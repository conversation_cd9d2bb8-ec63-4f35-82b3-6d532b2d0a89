name: CI/CD Pipeline

on:
  push:
    branches: [ staging, prod ]
    paths:
      - 'massive-discover-backend/**'
      - 'docker-compose.yml'
      - 'ghost/**'
      - 'directus/**'
      - 'scripts/deploy.sh'
      - '.github/workflows/ci-cd.yml'
  pull_request:
    branches: [ staging, prod ]
    paths:
      - 'massive-discover-backend/**'
      - 'docker-compose.yml'
      - 'ghost/**'
      - 'directus/**'
      - 'scripts/deploy.sh'
      - '.github/workflows/ci-cd.yml'
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ope-backend

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/prod'
    
    outputs:
      image-tag: ${{ steps.tag.outputs.image-tag }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Set image tag
      id: tag
      run: |
        IMAGE_TAG="${{ github.ref_name }}"
        echo "image-tag=${IMAGE_TAG}" >> $GITHUB_OUTPUT
        echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_ENV

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}
        tags: |
          type=raw,value=${{ env.IMAGE_TAG }}
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Debug - Show generated tags
      run: |
        echo "Generated tags:"
        echo "${{ steps.meta.outputs.tags }}"
        echo "Consistent tag for deploy: ${{ env.IMAGE_TAG }}"

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: ./massive-discover-backend
        file: ./massive-discover-backend/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64

    - name: Verify image was pushed
      run: |
        echo "Verifying pushed image..."
        echo "Deploy will use tag: ${{ env.IMAGE_TAG }}"

        # Try to pull the specific tag that deploy will use
        docker pull ghcr.io/${{ github.repository_owner }}/ope-backend:${{ env.IMAGE_TAG }} || echo "Failed to pull expected tag"

  deploy:
    name: Deploy to AWS LightSail
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: ${{ github.ref_name }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup SSH key
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.LIGHTSAIL_SSH_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.LIGHTSAIL_HOST }} >> ~/.ssh/known_hosts

    - name: Create deployment directory
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.LIGHTSAIL_USER }}@${{ secrets.LIGHTSAIL_HOST }} "
          mkdir -p /home/<USER>/ope-backend
        "

    - name: Copy deployment files
      run: |
        # Copy unified compose file
        scp -i ~/.ssh/id_rsa docker-compose.yml \
          ${{ secrets.LIGHTSAIL_USER }}@${{ secrets.LIGHTSAIL_HOST }}:/home/<USER>/ope-backend/

        # Copy deployment script
        scp -i ~/.ssh/id_rsa scripts/deploy.sh \
          ${{ secrets.LIGHTSAIL_USER }}@${{ secrets.LIGHTSAIL_HOST }}:/home/<USER>/ope-backend/

        # Create directories for Ghost and Directus if they don't exist
        ssh -i ~/.ssh/id_rsa ${{ secrets.LIGHTSAIL_USER }}@${{ secrets.LIGHTSAIL_HOST }} "
          mkdir -p /home/<USER>/ope-backend/ghost/content
          mkdir -p /home/<USER>/ope-backend/directus/database
          mkdir -p /home/<USER>/ope-backend/directus/uploads
          mkdir -p /home/<USER>/ope-backend/directus/extensions
        "

    - name: Deploy application
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.LIGHTSAIL_USER }}@${{ secrets.LIGHTSAIL_HOST }} "
          cd /home/<USER>/ope-backend
          chmod +x deploy.sh

          # Login to GitHub Container Registry
          echo '${{ secrets.GITHUB_TOKEN }}' | docker login ghcr.io -u ${{ github.actor }} --password-stdin

          # Set environment variables for deployment
          export GITHUB_REPOSITORY_OWNER=${{ github.repository_owner }}
          export IMAGE_TAG=${{ needs.build-and-push.outputs.image-tag }}
          export ENVIRONMENT=${{ github.ref_name }}

          # Run deployment script
          ./deploy.sh
        "

    - name: Verify deployment
      run: |
        ssh -i ~/.ssh/id_rsa ${{ secrets.LIGHTSAIL_USER }}@${{ secrets.LIGHTSAIL_HOST }} "
          # Show final container status after deployment
          echo '=== Final Container Status ==='
          docker ps --filter 'name=ope-' --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'

          # Show recent logs for troubleshooting if needed
          echo '=== Recent Container Logs ==='
          docker compose -f /home/<USER>/ope-backend/docker-compose.yml logs --tail=10 || echo 'Could not retrieve logs'
        "
