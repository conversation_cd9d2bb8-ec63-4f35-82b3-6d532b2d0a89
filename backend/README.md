# Open Portal Expo Backend

A comprehensive backend system for the Open Portal Expo mobile experience, providing APIs for event management, user interactions, gamification, and administrative functions.

## 🏗️ Architecture Overview

This repository contains a microservices-based backend system built with Spring Boot and Java 11, designed to support a mobile expo experience with QR code scanning, interactive maps, gamification, and content management.

### System Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Open Portal Expo Backend                 │
├─────────────────────────────────────────────────────────────┤
│  📱 Mobile App APIs          │  🔧 Admin Interface          │
│  (Port 8082)                 │  (Directus - Port 8055)      │
├─────────────────────────────────────────────────────────────┤
│                    Core Services                            │
│  • User Management          │  • Event Management          │
│  • Game Endpoints           │  • Content Management         │
│  • Reward System            │  • Map & Location Services    │
├─────────────────────────────────────────────────────────────┤
│                   Infrastructure                            │
│  • MySQL Database           │  • Redis Cache               │
│  • AWS Services             │  • Firebase Integration       │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Repository Structure

### `/directus`

**Content Management System & Admin Interface**

- **Purpose**: Provides a headless CMS for managing expo content, events, exhibitors, and administrative functions
- **Technology**: Directus v9+ with MySQL backend
- **Port**: 8055
- **Features**:
  - Event and exhibition management
  - User content administration
  - File and media management
  - RESTful API for admin interfaces
  - Real-time collaboration tools

**Key Files**:

- `docker-compose.yml` - Directus container configuration
- `database/` - Persistent database storage
- `uploads/` - File storage for images and media
- `extensions/` - Custom Directus extensions

### `/massive-discover-backend`

**Core Microservices Platform**

A Spring Boot-based microservices architecture providing the main business logic and APIs for the mobile application.

#### Service Modules

##### `discover-backend-common`

**Shared Libraries & Utilities**

- **Purpose**: Common utilities, configurations, and shared components across all services
- **Key Features**:
  - Database configuration and multi-tenancy support
  - Common exception handling and response models
  - Utility classes for token generation and validation
  - Shared enums and constants
  - Multi-tenant architecture support

##### `discover-backend-web`

**Main API Gateway & Mobile Services**

- **Purpose**: Primary API endpoint for mobile applications
- **Port**: 8082 (configurable)
- **Key Features**:
  - Event and map data APIs
  - User authentication and session management
  - Game interaction endpoints
  - Reward system APIs
  - QR code processing
  - Firebase integration for push notifications

##### `discover-backend-user`

**User Management Service**

- **Purpose**: Handles user authentication, profiles, and user-related operations
- **Key Features**:
  - Phone number verification and SMS integration
  - User registration and profile management
  - Authentication token management
  - User session handling
  - Redis-based caching for user data

##### `discover-backend-operations`

**Administrative Operations Service**

- **Purpose**: Backend operations and administrative functions
- **Port**: 8083 (admin APIs)
- **Key Features**:
  - Administrative API endpoints
  - System monitoring and health checks
  - Operational data management
  - Integration with external services

##### `discover-backend-feed`

**Content & Feed Management**

- **Purpose**: Manages content feeds, announcements, and dynamic content
- **Key Features**:
  - Bulletin and announcement management
  - Content feed generation
  - Dynamic content delivery
  - Content scheduling and publishing

##### `discover-backend-redis-lock`

**Distributed Locking Service**

- **Purpose**: Provides distributed locking mechanisms for concurrent operations
- **Key Features**:
  - Redis-based distributed locks
  - Reentrant lock implementation
  - Thread-safe operations for critical sections
  - Lock timeout and cleanup mechanisms

### `/mock-data`

**Development & Testing Data**

Comprehensive mock data system for development, testing, and demonstration purposes.

**Key Features**:

- **Complete Dataset**: 500+ records across 16 tables
- **Realistic Content**: Tech companies, events, games, rewards
- **Automated Scripts**: Loading, verification, and cleanup tools
- **API Testing**: Ready-to-use test scenarios

**Data Coverage**:

- 5 Events with different themes
- 65 Interactive map markers
- 51 Exhibitor companies
- 32 Gamification elements
- 22 Reward items with point system
- Hotels, restaurants, shops, and shows

**Management Scripts**:

```bash
# Load all mock data
./mock-data/scripts/load-all-data.sh

# Reset database
./mock-data/scripts/reset-data.sh

# Verify data integrity
./mock-data/scripts/verify-data.sh
```

## 🛠️ Technology Stack

### Backend Services

- **Java 11** - Primary programming language
- **Spring Boot 2.6.7** - Application framework
- **Spring Cloud** - Microservices architecture
- **MyBatis Plus** - ORM and database operations
- **Maven** - Dependency management and build tool

### Databases & Caching

- **MySQL 8.0** - Primary database
- **Redis 7.0** - Caching and session storage
- **Multi-tenancy** - Tenant-aware data isolation

### External Integrations

- **Firebase** - Push notifications and authentication
- **AWS Services** - Cloud infrastructure and deployment
- **Swagger 2** - API documentation
- **JWT** - Token-based authentication

### Development Tools

- **Docker** - Containerization
- **Kubernetes** - Container orchestration
- **AWS CodeBuild** - CI/CD pipeline
- **ECR** - Container registry

## 🚀 Quick Start

### Prerequisites

```bash
# Required services
- Java 11+
- Maven 3.6+
- Staging Database Access (ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com:3306, ope_backend/ope_backend)
- Redis 7.0 (localhost:6379, no auth)
- Docker & Docker Compose
```

### 1. Start Infrastructure Services

```bash
# Start Directus (Admin Interface)
cd backend/directus
docker-compose up -d

# Verify Directus is running
curl http://localhost:8055/server/health
```

### 2. Setup Database

**Note:** Using staging database for local development. No local database setup required.

```bash
# Load mock data to staging database
cd backend/mock-data/scripts
./load-all-data.sh
```

### 3. Start Backend Services

```bash
# Set environment variables
export AWS_ENV=local
export JDBC_URL=********************************************
export MYSQL_USER=root
export MYSQL_PASSWORD=root
export REDIS_CLUSTER_NODES=127.0.0.1:6379

# Build and run main service
cd backend/massive-discover-backend
mvn clean package -DskipTests
java -jar discover-backend-web/target/discover-backend-web.jar

# Run operations service (separate terminal)
java -jar discover-backend-operations/target/discover-backend-operations.jar
```

### 4. Verify Installation

```bash
# Test main API
curl -X POST http://localhost:8082/event/v1/event \
  -H "Content-Type: application/json" \
  -d '{"eventId": 1}'

# Test admin API
curl http://localhost:8083/swagger-ui.html

# Test Directus admin
curl http://localhost:8055/admin
```

## 📚 API Documentation

### Mobile App APIs (Port 8082)

**Event Management**

```bash
POST /event/v1/event          # Get event details
GET  /mark/v1/marks           # Get map markers
POST /game/v1/play            # Play games
GET  /reward/v1/rewards       # Get available rewards
```

**User Management**

```bash
POST /user/v1/login           # User authentication
POST /user/v1/verify          # Phone verification
GET  /user/v1/profile         # User profile
```

### Admin APIs (Port 8083)

**Administrative Functions**

```bash
GET  /admin/event/page        # List events
POST /admin/user/create       # Create users
GET  /admin/stats/dashboard   # System statistics
```

### Directus APIs (Port 8055)

**Content Management**

```bash
GET  /items/events            # Manage events
GET  /items/exhibitors        # Manage exhibitors
POST /files                   # Upload files
GET  /users/me                # Current user info
```

**Access Directus Admin**: http://localhost:8055/admin

- Email: <EMAIL>
- Password: directus

## 🔧 Development

### Building Services

```bash
# Build all modules
cd backend/massive-discover-backend
mvn clean package

# Build specific module
cd discover-backend-web
mvn clean package

# Skip tests for faster builds
mvn clean package -DskipTests
```

### Running in Development

```bash
# Run with hot reload (if using Spring Boot DevTools)
mvn spring-boot:run

# Run with specific profile
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# Debug mode
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"
```

### Database Management

```bash
# Reset to clean state
cd backend/mock-data/scripts
./reset-data.sh

# Load specific data sets
mysql -u root -proot open_portal_expo < ../sql/01-enhanced-events.sql

# Verify data integrity
./verify-data.sh
```

## 🚢 Deployment

### AWS Infrastructure

The system is designed for deployment on AWS using:

- **EKS (Elastic Kubernetes Service)** - Container orchestration
- **ECR (Elastic Container Registry)** - Docker image storage
- **RDS** - Managed MySQL database
- **ElastiCache** - Managed Redis cluster
- **CodeBuild** - CI/CD pipeline

### Build Pipeline

```bash
# Automated build process (buildspec.yml)
1. Maven build and test
2. Docker image creation
3. Push to ECR
4. Deploy to EKS cluster
5. Health check verification
```

### Environment Configuration

```bash
# Production environment variables
AWS_ENV=prod
JDBC_URL=***********************************************************************************
REDIS_CLUSTER_NODES=prod-redis.xxx.cache.amazonaws.com:6379
```

### Kubernetes Deployment

```bash
# Update EKS configuration
aws eks update-kubeconfig --region ap-northeast-1 --name massive-eks-0fj47BAg

# Deploy services
kubectl apply -f k8s/
kubectl get pods -n massive-develop

# Rolling update
kubectl set image deployment/discover-backend-web discover-backend-web=new-image:tag
```

## 📊 Monitoring & Operations

### Health Checks

```bash
# Service health endpoints
curl http://localhost:8082/actuator/health
curl http://localhost:8083/actuator/health
curl http://localhost:8055/server/health
```

### Logging

```bash
# View application logs
kubectl logs -f deployment/discover-backend-web -n massive-develop

# Local development logs
tail -f logs/application.log
```

### Database Monitoring

```bash
# Check database connections
mysql -u root -proot -e "SHOW PROCESSLIST;"

# Monitor Redis
redis-cli info stats
```

## 🔐 Security

### Authentication & Authorization

- **JWT Tokens** - Stateless authentication for mobile APIs
- **Firebase Auth** - Social login integration
- **Phone Verification** - SMS-based user verification
- **Multi-tenancy** - Tenant-aware data isolation

### API Security

- **CORS Configuration** - Cross-origin request handling
- **Rate Limiting** - Request throttling (Redis-based)
- **Input Validation** - Request payload validation
- **SQL Injection Protection** - MyBatis parameterized queries

## 🧪 Testing

### Mock Data Testing

```bash
# Load test data
cd backend/mock-data/scripts
./load-all-data.sh

# API integration tests
curl -X POST http://localhost:8082/event/v1/event -d '{"eventId": 1}'
curl -X GET "http://localhost:8082/mark/v1/marks?eventId=1"
```

### Unit Testing

```bash
# Run all tests
mvn test

# Run specific module tests
cd discover-backend-web
mvn test

# Generate test reports
mvn surefire-report:report
```

## 📈 Performance

### Optimization Features

- **Redis Caching** - Frequently accessed data caching
- **Connection Pooling** - Database connection optimization
- **Distributed Locking** - Concurrent operation handling
- **Lazy Loading** - On-demand data loading

### Monitoring Metrics

- **Response Times** - API endpoint performance
- **Database Queries** - Query execution monitoring
- **Cache Hit Rates** - Redis cache effectiveness
- **Error Rates** - System reliability metrics

## 🤝 Contributing

### Development Workflow

1. **Feature Development** - Create feature branches
2. **Code Review** - Pull request reviews
3. **Testing** - Comprehensive test coverage
4. **Documentation** - Update relevant documentation
5. **Deployment** - Automated CI/CD pipeline

### Code Standards

- **Java Code Style** - Follow Google Java Style Guide
- **API Design** - RESTful API principles
- **Database Design** - Normalized schema design
- **Documentation** - Comprehensive API documentation

## 📞 Support

### Documentation

- **API Docs**: http://localhost:8082/swagger-ui.html
- **Admin Docs**: http://localhost:8083/swagger-ui.html
- **Mock Data**: [MOCK_DATA.md](mock-data/MOCK_DATA.md)
- **Quick Start**: [QUICK_START.md](mock-data/QUICK_START.md)

### Troubleshooting

**Common Issues**:

- Database connection failures → Check MySQL service and credentials
- Redis connection errors → Verify Redis service status
- Port conflicts → Ensure ports 8055, 8082, 8083 are available
- Build failures → Check Java 11+ and Maven installation

**Support Channels**:

- Technical documentation in `/docs` folder
- API testing with provided mock data
- Comprehensive logging for debugging

---

**Ready to power amazing expo experiences!** 🎯
