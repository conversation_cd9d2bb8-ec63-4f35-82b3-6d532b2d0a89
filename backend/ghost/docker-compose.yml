services:
  ghost:
    image: ghost:5-alpine
    restart: always
    ports:
      - 8888:2368
    environment:
      # see https://ghost.org/docs/config/#configuration-options
      database__client: mysql
      database__connection__host: ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com
      database__connection__user: ghost
      database__connection__password: ghost
      database__connection__database: open_portal_expo_ghost
      url: http://localhost:8888
      # url: https://ope-ghost-stg.zafar.dev
      # contrary to the default mentioned in the linked documentation, this image defaults to NODE_ENV=production (so development mode needs to be explicitly specified if desired)
      NODE_ENV: development
    volumes:
      - ghost:/var/lib/ghost/content

volumes:
  ghost:
