services:
  redis:
    image: redis:7.0-alpine
    container_name: ope-redis-${ENVIRONMENT:-local}
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    mem_limit: 512m
    command: redis-server --appendonly yes --appendfsync everysec --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  backend:
    image: ${BACKEND_IMAGE:-ghcr.io/${GITHUB_REPOSITORY_OWNER}/ope-backend:${IMAGE_TAG:-latest}}
    build:
      context: ./massive-discover-backend
      dockerfile: Dockerfile
    container_name: ope-backend-${ENVIRONMENT:-local}
    restart: unless-stopped
    ports:
      - "8082:8082"
    mem_limit: 1g
    environment:
      # Spring Profile
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES_ACTIVE:-local}

      # Database Configuration
      - JDBC_URL=${JDBC_URL}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}

      # Redis Configuration
      - REDIS_CLUSTER_NODES=redis:6379

      # AWS Configuration
      - AWS_ENV=${AWS_ENV:-local}
      - STATIC_IMAGE_HOST=${STATIC_IMAGE_HOST}
      - BUCKET_NAME=${BUCKET_NAME}

      # Authentication Configuration
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRED_DAYS=${JWT_EXPIRED_DAYS:-30}

      # LINE OAuth Configuration
      # - LINE_CLIENT_ID=${LINE_CLIENT_ID}
      # - LINE_CLIENT_SECRET=${LINE_CLIENT_SECRET}
      # - LINE_CALLBACK_URL=${LINE_CALLBACK_URL}

      # Server Configuration
      - SERVER_PORT=8082

      # JVM Configuration
      - JAVA_OPTS=-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication

    depends_on:
      redis:
        condition: service_healthy

    volumes:
      - app_logs:/app/logs

    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/rank/v1/showTopN?n=1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

  ghost:
    image: ghost:5-alpine
    container_name: ope-ghost-${ENVIRONMENT:-local}
    restart: unless-stopped
    ports:
      - "8888:2368"
    environment:
      # Ghost Configuration
      database__client: ${GHOST_DB_CLIENT:-mysql}
      database__connection__host: ${GHOST_DB_HOST}
      database__connection__user: ${GHOST_DB_USER}
      database__connection__password: ${GHOST_DB_PASSWORD}
      database__connection__database: ${GHOST_DB_DATABASE}
      url: ${GHOST_URL}
      NODE_ENV: ${GHOST_NODE_ENV:-development}
    volumes:
      - ./ghost/content:/var/lib/ghost/content
    mem_limit: 512m
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  directus:
    image: directus/directus
    container_name: ope-directus-${ENVIRONMENT:-local}
    restart: unless-stopped
    ports:
      - "8055:8055"
    volumes:
      - ./directus/database:/directus/database
      - ./directus/uploads:/directus/uploads
      - ./directus/extensions:/directus/extensions
    environment:
      SECRET: ${DIRECTUS_SECRET}
      ADMIN_EMAIL: ${DIRECTUS_ADMIN_EMAIL}
      ADMIN_PASSWORD: ${DIRECTUS_ADMIN_PASSWORD}
      DB_CLIENT: ${DIRECTUS_DB_CLIENT:-mysql}
      DB_HOST: ${DIRECTUS_DB_HOST}
      DB_PORT: ${DIRECTUS_DB_PORT:-3306}
      DB_DATABASE: ${DIRECTUS_DB_DATABASE}
      DB_USER: ${DIRECTUS_DB_USER}
      DB_PASSWORD: ${DIRECTUS_DB_PASSWORD}
      CORS_ENABLED: true
      CORS_ORIGIN: ${DIRECTUS_CORS_ORIGIN:-"*"}
    mem_limit: 512m
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  redis_data:
    driver: local
    name: ope-redis-data-${ENVIRONMENT:-local}
  app_logs:
    driver: local
    name: ope-app-logs-${ENVIRONMENT:-local}
