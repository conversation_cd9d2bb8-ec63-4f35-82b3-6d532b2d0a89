# Multi-stage build for local development
FROM maven:3.8.6-amazoncorretto-11 AS builder

# Set working directory
WORKDIR /app

# Copy pom files first for better caching
COPY pom.xml .
COPY discover-backend-common/pom.xml discover-backend-common/
COPY discover-backend-web/pom.xml discover-backend-web/
COPY discover-backend-operations/pom.xml discover-backend-operations/
COPY discover-backend-user/pom.xml discover-backend-user/
COPY discover-backend-feed/pom.xml discover-backend-feed/
COPY discover-backend-redis-lock/pom.xml discover-backend-redis-lock/

# Download dependencies
RUN mvn dependency:go-offline -B

# Copy source code
COPY . .

# Build the application
RUN mvn clean package -DskipTests

# Runtime stage
FROM amazoncorretto:11.0.11

# Install curl for health checks
RUN yum update -y && yum install -y curl && yum clean all

# Create app directory
WORKDIR /app

# Copy the built JAR
COPY --from=builder /app/discover-backend-web/target/discover-backend-web.jar app.jar

# Create logs directory
RUN mkdir -p /app/logs

# Expose port 8082 (matching the run script)
EXPOSE 8082

# Add health check endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8082/rank/v1/showTopN?n=1 || exit 1

# Run the application
CMD ["java", "-jar", "-Dserver.port=8082", "app.jar"]
