version: 0.2

env:
  exported-variables:
    - NAMESPACE
    - ENVIRONMENT
    - APPROVAL_MESSAGE
    - IMAGE
    - ECR_URL
    - BUILD_PROJECT
    - DEPLOY_TYPE

phases:
  install:
    runtime-versions:
      java: corretto17
#    commands:
#      - echo Retrieving mvn settings...
#      - aws s3 cp s3://massive-maven-global/settings-massive.xml settings.xml
#      - ls -lhr settings.xml

  pre_build:
    commands:
      - echo Logging into Amazon ECR...
      - aws ecr get-login-password --region ap-northeast-1 | docker login --username AWS --password-stdin 559118964552.dkr.ecr.ap-northeast-1.amazonaws.com
      - |
        HEAD_REF=$(basename $CODEBUILD_WEBHOOK_HEAD_REF)
        COMMIT_ID=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c1-7)
        
        # set environment based on BRANCH
        ENVIRONMENT=prod

  build:
    commands:
      # Always use clean to prevent stale artifacts
      # Force clean compilation to ensure all annotations are properly compiled
      - echo "Cleaning and compiling with fresh build..."
      - mvn clean compile -DskipTests
      - mvn package -DskipTests

      # Verify critical entity classes are properly compiled
      - echo "Verifying compiled entity classes..."
      - find . -name "*.class" -path "*/entity/*" | head -10

      - docker build -f $BUILD_PROJECT/Dockerfile -t $ECR_URL:$COMMIT_ID -t $ECR_URL:$HEAD_REF -t $ECR_URL:latest --build-arg env=$ENVIRONMENT .

  post_build:
    commands:
      - |
        echo Pushing Docker images... 
        docker push $ECR_URL:$COMMIT_ID 
        docker push $ECR_URL:$HEAD_REF 
        docker push $ECR_URL:latest
        IMAGE=$ECR_URL:$COMMIT_ID

      - echo Install kubectl
      - curl -LO https://storage.googleapis.com/kubernetes-release/release/v1.20.2/bin/linux/amd64/kubectl
      - chmod +x ./kubectl
      - mv ./kubectl /usr/local/bin/kubectl
      - echo Prepare kubectl
      - aws eks --region ap-northeast-1 update-kubeconfig --name massive-eks-0fj47BAg
      - aws sts get-caller-identity
      - kubectl config view --minify
      - kubectl set image -n $NAMESPACE $DEPLOY_TYPE/$BUILD_PROJECT $BUILD_PROJECT=$IMAGE


artifacts:
  files:
    - temp

cache:
  paths:
    - "/root/.m2/**/*"
