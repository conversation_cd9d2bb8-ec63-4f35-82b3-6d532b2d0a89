#!/bin/bash

# Build script for independent massive-discover-backend submodules
# This script builds only the modules that don't depend on discover-backend-user
# which currently has compilation issues

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo -e "${BLUE}=== Building independent massive-discover-backend modules ===${NC}"
echo "Working directory: $(pwd)"
echo

# Function to print section headers
print_section() {
    echo -e "${YELLOW}=== $1 ===${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Check if <PERSON><PERSON> is installed
if ! command -v mvn &> /dev/null; then
    print_error "<PERSON><PERSON> is not installed or not in PATH"
    exit 1
fi

print_success "Maven found: $(mvn --version | head -n 1)"
echo

# Build only the independent modules that should work
INDEPENDENT_MODULES=(
    "discover-backend-common"
    "discover-backend-redis-lock"
)

print_section "Building independent modules"
echo "These modules don't depend on discover-backend-user and should build successfully:"
for module in "${INDEPENDENT_MODULES[@]}"; do
    echo "  • $module"
done
echo

# Clean and build each independent module
for module in "${INDEPENDENT_MODULES[@]}"; do
    print_section "Building $module"
    
    # Clean the specific module
    mvn clean -pl "$module"
    
    # Compile the module
    mvn compile -pl "$module"
    if [ $? -eq 0 ]; then
        print_success "$module compiled successfully"
        
        # Package the module
        mvn package -pl "$module" -DskipTests
        if [ $? -eq 0 ]; then
            print_success "$module packaged successfully"
        else
            print_error "$module packaging failed"
        fi
    else
        print_error "$module compilation failed"
    fi
    echo
done

# Check what was built
print_section "Build Results"

BUILT_JARS=()
FAILED_MODULES=()

for module in "${INDEPENDENT_MODULES[@]}"; do
    jar_file="$module/target/$module-0.0.1-SNAPSHOT.jar"
    if [ -f "$jar_file" ]; then
        size=$(du -h "$jar_file" | cut -f1)
        print_success "$module.jar - $size"
        echo "  Location: $jar_file"
        BUILT_JARS+=("$jar_file")
    else
        print_error "$module.jar - NOT FOUND"
        FAILED_MODULES+=("$module")
    fi
done

echo
print_section "Summary"

if [ ${#BUILT_JARS[@]} -gt 0 ]; then
    echo -e "${GREEN}Successfully built ${#BUILT_JARS[@]} independent modules!${NC}"
    echo
    echo "Built JARs:"
    for jar in "${BUILT_JARS[@]}"; do
        echo "  ✓ $jar"
    done
    echo
    echo "These modules can be used as dependencies for other projects."
else
    echo -e "${RED}No modules were built successfully.${NC}"
    exit 1
fi

if [ ${#FAILED_MODULES[@]} -gt 0 ]; then
    echo -e "${YELLOW}Failed modules: ${FAILED_MODULES[*]}${NC}"
    echo
fi

echo "Note: The following modules have dependencies on discover-backend-user"
echo "and cannot be built until the compilation issues are fixed:"
echo "  • discover-backend-feed (depends on discover-backend-user)"
echo "  • discover-backend-web (depends on discover-backend-user)"
echo "  • discover-backend-operations (depends on discover-backend-user)"
echo
echo "To fix the compilation issues, check the type compatibility problems"
echo "in discover-backend-user/src/main/java/com/massive/discover/user/component/RankListComponent.java"
