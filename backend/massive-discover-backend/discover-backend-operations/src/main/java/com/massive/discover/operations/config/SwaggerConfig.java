package com.massive.discover.operations.config;

import com.google.common.collect.Lists;
import com.massive.discover.user.model.UserCacheInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.ApiKey;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.servlet.ServletContext;

@Configuration
@EnableSwagger2
public class SwaggerConfig {

    ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("Massive Discover Operations API Spec")
                .description("API definition for massive discover operations apis")
                .license("")
                .licenseUrl("https://unlicense.org")
                .termsOfServiceUrl("")
                .version("2.0.0")
                .contact(new Contact("", "", ""))
                .build();
    }

    private ApiKey apiKey() {
        return new ApiKey("bearerAuth", "Authorization", "header");
    }

    @Bean
    public Docket customImplementation(ServletContext servletContext, @Value("${openapi.massiveServerAPISpec.base-path:}") String basePath) {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.massive.discover.web.controller"))
                .build()
                .ignoredParameterTypes(UserCacheInfo.class)
                .apiInfo(apiInfo())
                .securitySchemes(Lists.newArrayList(apiKey()));
    }
}