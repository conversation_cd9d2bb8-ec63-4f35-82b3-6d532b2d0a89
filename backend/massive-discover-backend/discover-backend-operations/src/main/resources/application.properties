springfox.documentation.swagger.v2.path=/api-docs
server.port=8080
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.date-format=com.massive.discover.common.format.RFC3339DateFormat



#datasource - Using staging database for local development
spring.datasource.url=${JDBC_URL:****************************************************************************************************************************}
spring.datasource.username=${MYSQL_USER:ope_backend}
spring.datasource.password=${MYSQL_PASSWORD:ope_backend}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.redis.cluster.nodes=${REDIS_CLUSTER_NODES:localhost:6379}

# actable
actable.table.auto=update
actable.model.pack=com.massive.discover.common.entity
actable.database.type=mysql
actable.index.prefix=idx_
actable.unique.prefix=idx_uni_
mybatis-plus.mapper-locations[0]=classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*/*.xml

# mybatis config
mybatis-plus.mapper-locations[1]=classpath*:mapper/**/*Mapper.xml
mybatis-plus.type-aliases-package=com.massive.discover.common.entity
mybatis-plus.global-config.db-config.table-underline=true
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
#mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

#firebase
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#line oauth
idp.line.clientId=${LINE_CLIENT_ID:}
idp.line.clientSecret=${LINE_CLIENT_SECRET:}
idp.line.callbackUrl=${LINE_CALLBACK_URL:}

#aws
aws.client.region=ap-northeast-1
aws.static.host=${STATIC_IMAGE_HOST:https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com}
aws.s3.signed.expiration.minutes=30
aws.s3.bucket.name=${BUCKET_NAME:open-portal-expo-prod}
aws.run.env=${AWS_ENV:dev}
