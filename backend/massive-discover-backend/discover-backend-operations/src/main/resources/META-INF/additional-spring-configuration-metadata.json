{"properties": [{"name": "springfox.documentation.swagger.v2.path", "type": "java.lang.String", "description": "Description for springfox.documentation.swagger.v2.path."}, {"name": "actable.model.pack", "type": "java.lang.String", "description": "Description for actable.model.pack."}, {"name": "actable.database.type", "type": "java.lang.String", "description": "Description for actable.database.type."}, {"name": "actable.index.prefix", "type": "java.lang.String", "description": "Description for actable.index.prefix."}, {"name": "actable.unique.prefix", "type": "java.lang.String", "description": "Description for actable.unique.prefix."}, {"name": "idp.line.clientId", "type": "java.lang.String", "description": "Description for idp.line.clientId."}, {"name": "idp.line.clientSecret", "type": "java.lang.String", "description": "Description for idp.line.clientSecret."}, {"name": "idp.line.callbackUrl", "type": "java.lang.String", "description": "Description for idp.line.callbackUrl."}, {"name": "aws.client.region", "type": "java.lang.String", "description": "Description for aws.client.region."}, {"name": "aws.static.host", "type": "java.lang.String", "description": "Description for aws.static.host."}, {"name": "aws.s3.signed.expiration.minutes", "type": "java.lang.String", "description": "Description for aws.s3.signed.expiration.minutes."}, {"name": "aws.s3.bucket.name", "type": "java.lang.String", "description": "Description for aws.s3.bucket.name."}, {"name": "aws.run.env", "type": "java.lang.String", "description": "Description for aws.run.env."}]}