# README

#### 相关技术

- 前端:Vue3、Vite3、TypeScript、NaiveUI、Pinia 和 UnoCSS
  - https://docs.soybean.pro
- 后端:springCloud + springboot + spring + springmvc + mybatis-plus + mysql + redis
  - 项目部署: docker + k8s
  - 中间件:nacos(微服务治理)
  - 消息队列: kafka
  - 测试:swagger2
  - 持久化框架
    - https://baomidou.com
  - 数据库表自动生成框架
    - https://www.yuque.com/sunchenbin/actable
- 开发风格：分布式系统架构，前后分离开发风格，RESTful api

#### 代码介绍

- [discover-backend-web](discover-backend-web) 客户端(web)后台

#### 架构设计

![架构图](./img/level.png "massive-discover-backend")

#### 系统微服务服务架构

![系统微服务服务](./img/system-component.jpeg "massive-discover-backend")

#### develop

```shell
# run local envs
export AWS_ENV=local
export JDBC_URL=********************************************
export MYSQL_USER=root
export MYSQL_PASSWORD=root
export REDIS_CLUSTER_NODES=127.0.0.1:6379
```

#### OPS

```shell
aws eks update-kubeconfig --region ap-northeast-1 --name massive-eks-0fj47BAg
kubectl get pods -n massive-develop
```
