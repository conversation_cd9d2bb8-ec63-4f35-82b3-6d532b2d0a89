package com.massive.discover.user.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/11
 */

@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
public class UserProfileVO {

    // 用户昵称
    private String nickname;

    // 用户头像URL
    private String profileImg;

    // 用户背景头像URL
    private String profileBgImg;

    // 用户个人介绍
    private String signature;

    // 用户等级
    private Integer rank;

    // 用户经验值
    private String experienceValue;
}
