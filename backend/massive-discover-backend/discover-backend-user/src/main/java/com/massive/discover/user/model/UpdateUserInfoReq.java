package com.massive.discover.user.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UpdateUserInfoReq {

    private String nickname;

    private Integer gender;

    @ApiModelProperty(value = "birthday for user's birthday as YYYY-MM-DD")
    private String birthday;

    @ApiModelProperty(value = "country")
    private String location;

}
