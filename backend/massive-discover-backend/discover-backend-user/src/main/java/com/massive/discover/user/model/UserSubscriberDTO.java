package com.massive.discover.user.model;

import com.massive.discover.user.enums.ThirdPartyLoginType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserSubscriberDTO {

    private String token;

    private String name;

    private String profileImg;

    private String uid;

    private String email;

    private Integer emailVerified;

    private String phoneNumber;

    private ThirdPartyLoginType loginType;
}
