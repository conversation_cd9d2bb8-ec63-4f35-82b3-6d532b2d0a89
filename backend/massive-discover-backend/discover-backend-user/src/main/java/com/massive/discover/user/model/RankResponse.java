package com.massive.discover.user.model;

import com.massive.discover.common.model.RankResultDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RankResponse implements Serializable {

    private static final long serialVersionUID = 4804922606006935590L;

    private List<RankResultDTO> rankResults;

}
