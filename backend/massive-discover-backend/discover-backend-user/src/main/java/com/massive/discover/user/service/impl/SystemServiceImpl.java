package com.massive.discover.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.PersonalInfoMapper;
import com.massive.discover.common.dao.SystemSettingsMapper;
import com.massive.discover.common.dao.UserMapper;
import com.massive.discover.common.entity.PersonalInfo;
import com.massive.discover.common.entity.SystemSettings;
import com.massive.discover.common.entity.User;
import com.massive.discover.user.model.AccountVO;
import com.massive.discover.user.model.PrivateSystemSettingsVO;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.user.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class SystemServiceImpl extends ServiceImpl<SystemSettingsMapper, SystemSettings> implements SystemService {

    @Resource
    private SystemSettingsMapper systemSettingsMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private PersonalInfoMapper personalInfoMapper;

    /**
     * 取得账号信息
     * @param userId user id
     * @return AccountVO account info
     */
    @Override
    public AccountVO queryAccountInfo(Long userId) {
        User user = userMapper.selectById(userId);
        PersonalInfo personalInfo = personalInfoMapper.selectById(user.getPersonalInfoId());
        return AccountVO.builder()
                .phone(personalInfo.getPhone())
                .build();
    }

    /**
     * 取得隐私设置信息
     * @param userInfo user info
     * @return PrivateSystemSettingsVO private system settings info
     */
    @Override
    public PrivateSystemSettingsVO queryPrivateSystemSettings(UserCacheInfo userInfo) {
        SystemSettings systemSettings = systemSettingsMapper.selectOne(new QueryWrapper<SystemSettings>()
                .lambda().eq(SystemSettings::getUserId, userInfo.getId()));
        return PrivateSystemSettingsVO.builder()
                .pushNotificationAccept(systemSettings.getPushNotificationAccept())
                .build();

    }

    /**
     * 更新隐私设置信息
     * @param userId user id
     * @param privateSystemSettings private system settings info
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePrivateSystemSettings(Long userId, PrivateSystemSettingsVO privateSystemSettings) {
        SystemSettings systemSettings = systemSettingsMapper.selectOne(new QueryWrapper<SystemSettings>()
                .lambda().eq(SystemSettings::getUserId, userId));
        systemSettings.setPushNotificationAccept(privateSystemSettings.getPushNotificationAccept());
        systemSettingsMapper.updateById(systemSettings);
    }

}
