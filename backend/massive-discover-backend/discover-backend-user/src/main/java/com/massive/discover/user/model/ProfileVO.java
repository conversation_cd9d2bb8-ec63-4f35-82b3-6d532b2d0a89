package com.massive.discover.user.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/11
 */

@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Data
public class ProfileVO {

    // 用户头像URL
    private String profileImg;

    // 用户昵称
    private String nickname;

    // 邮箱
    private String mail;

    // 性别
    private Integer gender;

    // 城市
    private String city;

    // 地域
    private String area;

    // 自我介绍
    private String signature;

    // Facebook
    private String facebookNickName;

    // Google
    private String googleNickName;

    // Line
    private String lineNickName;

    // Twitter
    private String twitterNickName;

}
