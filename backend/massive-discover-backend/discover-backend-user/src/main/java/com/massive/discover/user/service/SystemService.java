package com.massive.discover.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.user.model.PrivateSystemSettingsVO;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.common.entity.SystemSettings;
import com.massive.discover.user.model.AccountVO;

public interface SystemService extends IService<SystemSettings> {

    /**
     * 取得账号信息
     * @param userId user id
     * @return AccountVO
     */
    AccountVO queryAccountInfo(Long userId);

    /**
     * 取得隐私设置信息
     * @param userInfo user info
     * @return PrivateSystemSettingsVO private system settings info
     */
    PrivateSystemSettingsVO queryPrivateSystemSettings(UserCacheInfo userInfo);

    /**
     * 更新隐私设置信息
     * @param userId user id
     * @param PrivateSystemSettingsVO private system settings info
     */
    void updatePrivateSystemSettings(Long userId, PrivateSystemSettingsVO privateSystemSettings);

}

