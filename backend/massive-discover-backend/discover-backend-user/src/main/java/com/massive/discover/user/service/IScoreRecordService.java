package com.massive.discover.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.ScoreRecord;
import com.massive.discover.common.model.ScoreRecordDetailAddReq;

/**
 * <AUTHOR>
 */
public interface IScoreRecordService extends IService<ScoreRecord> {


    /**
     * 保存积分记录
     * @param scoreRecordDetailAddReq
     * @return
     */
    boolean saveScoreRecordDetail(ScoreRecordDetailAddReq scoreRecordDetailAddReq);
}
