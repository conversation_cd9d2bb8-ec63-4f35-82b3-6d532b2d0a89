package com.massive.discover.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.ScoreRecordMapper;
import com.massive.discover.common.entity.ScoreRecord;
import com.massive.discover.common.model.ScoreRecordDetailAddReq;
import com.massive.discover.common.utils.BeanCopierUtil;
import com.massive.discover.user.service.IScoreRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class ScoreRecordServiceImpl extends ServiceImpl<ScoreRecordMapper, ScoreRecord> implements IScoreRecordService {


    @Override
    @Transactional(rollbackFor = {Exception.class})
    public boolean saveScoreRecordDetail(ScoreRecordDetailAddReq scoreRecordDetailAddReq) {
        ScoreRecord scoreRecord = BeanCopierUtil.copy(scoreRecordDetailAddReq, ScoreRecord.class);
        return this.save(scoreRecord);
    }
}
