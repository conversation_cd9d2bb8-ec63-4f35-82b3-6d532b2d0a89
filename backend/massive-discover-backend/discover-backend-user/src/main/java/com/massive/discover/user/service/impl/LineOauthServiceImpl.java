package com.massive.discover.user.service.impl;

import com.massive.discover.common.exception.AuthException;
import com.massive.discover.user.enums.ThirdPartyLoginType;
import com.massive.discover.user.enums.UserEmailVerified;
import com.massive.discover.user.model.UserLoginInfo;
import com.massive.discover.user.model.UserSubscriberDTO;
import com.massive.discover.user.model.line.v2.AccessToken;
import com.massive.discover.user.model.line.v2.IdToken;
import com.massive.discover.user.model.line.v2.Verify;
import com.massive.discover.user.service.LineService;
import com.massive.discover.user.service.OauthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("lineOauthService")
public class LineOauthServiceImpl implements OauthService {

    @Resource
    private LineService lineService;

    @Override
    public UserSubscriberDTO verifyAndExchangeToken(UserLoginInfo userLoginInfo) {
        // oauth code
        AccessToken token = lineService.accessToken(userLoginInfo.getLineIdToken());
        if (token == null) {
            throw new AuthException("expired login error token ,please retry");
        }
        Verify verify = lineService.verify(token);
        IdToken lineToken = lineService.idToken(token.getId_token());

        // 过期时间,用于判断ttl
        long expirationTime = verify.expires_in * 1000L + lineToken.iat * 1000L;
        return getUserSubscriberDTO(userLoginInfo, lineToken, expirationTime);

    }

    protected UserSubscriberDTO getUserSubscriberDTO(UserLoginInfo userLoginInfo, IdToken lineToken, long expirationTime) {
        long currentTime = System.currentTimeMillis();
        // token无效，已过期
        if (expirationTime < currentTime) {
            throw new AuthException("expired login error token ,please retry");
        }
        return UserSubscriberDTO.builder()
                .token(userLoginInfo.getLineIdToken())
                .name(StringUtils.isNotBlank(userLoginInfo.getNickname()) ? userLoginInfo.getNickname() : lineToken.name)
                .profileImg(StringUtils.isNotBlank(userLoginInfo.getProfileImg()) ? userLoginInfo.getProfileImg() : "")
                .uid(lineToken.sub)
                .email("")
                .emailVerified(UserEmailVerified.NOT_VERIFIED.getState())
                .phoneNumber("")
                .loginType(ThirdPartyLoginType.LINE).build();
    }
}
