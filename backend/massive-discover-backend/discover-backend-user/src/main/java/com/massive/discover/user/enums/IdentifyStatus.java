package com.massive.discover.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 22:38
 */

@AllArgsConstructor
public enum IdentifyStatus {

    /**
     * not yet
     */
    NO(0),

    /**
     * confirmed
     */
    YES(1);

    @Getter
    private final int status;

    private static final Map<Integer, IdentifyStatus> MAP;

    static {
        MAP = Arrays.stream(IdentifyStatus.values()).collect(Collectors.toMap(IdentifyStatus::getStatus, status -> status));
    }

    public static IdentifyStatus parse(Integer status) {
        return MAP.get(status);
    }
}
