package com.massive.discover.user.service.impl;

import com.massive.discover.user.model.UserLoginInfo;
import com.massive.discover.user.model.UserSubscriberDTO;
import com.massive.discover.user.model.line.v2.IdToken;
import com.massive.discover.user.service.LineService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("lineNativeService")
public class LineNativeServiceImpl extends LineOauthServiceImpl {

    private static final Long EXPIRES_IN = 2592000L;

    @Resource
    private LineService lineService;

    @Override
    public UserSubscriberDTO verifyAndExchangeToken(UserLoginInfo userLoginInfo) {
        IdToken lineToken = lineService.idToken(userLoginInfo.getLineIdToken());
        // 过期时间,用于判断ttl
        long expirationTime = EXPIRES_IN * 1000L + lineToken.iat * 1000L;
        return getUserSubscriberDTO(userLoginInfo, lineToken, expirationTime);
    }
}
