package com.massive.discover.user.model;


import com.massive.discover.common.model.ScoreRecordVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HistoryScoreDTO {

    private String date;

    private List<ScoreRecordVO> scoreRecordList;
}
