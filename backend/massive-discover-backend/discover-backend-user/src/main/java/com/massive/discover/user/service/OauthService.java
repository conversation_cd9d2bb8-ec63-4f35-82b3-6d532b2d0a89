package com.massive.discover.user.service;

import com.massive.discover.user.model.UserLoginInfo;
import com.massive.discover.user.model.UserSubscriberDTO;

/**
 * 定义第三方登陆所需要的一些公共方法
 *
 * <AUTHOR>
 */
public interface OauthService {

    /**
     * cache时间设置为30天
     */
    Long EXPIRE_TIME_SEC = 30 * 24 * 3600L;

    /**
     * 从客户端获取idToken（jwt），并向对应的idp校验token真实性，如果校验成功后对我方服务器进行映射和缓存
     *
     * @param userLoginInfo 第三方获取的jwtToken以及用户信息
     * @return UserSubscriberEntity, null if not verified
     */
    UserSubscriberDTO verifyAndExchangeToken(UserLoginInfo userLoginInfo);
}
