package com.massive.discover.user.service.impl;

import com.massive.discover.common.exception.SystemException;
import com.massive.discover.user.enums.ThirdPartyLoginType;
import com.massive.discover.user.enums.UserEmailVerified;
import com.massive.discover.user.model.UserLoginInfo;
import com.massive.discover.user.model.UserSubscriberDTO;
import com.massive.discover.user.model.line.v2.IdToken;
import com.massive.discover.user.service.LineService;
import com.massive.discover.user.service.OauthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("phoneNumberService")
public class PhoneNumberServiceImpl implements OauthService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public UserSubscriberDTO verifyAndExchangeToken(UserLoginInfo userLoginInfo) {
        if(StringUtils.isEmpty(userLoginInfo.getVerifyCode())){
            throw new SystemException("verify code can not empty");
        }

        String key = "verificationCode:" + userLoginInfo.getPhoneNumber();
        //根据电话号码去缓存查询五分钟内是否已经发送过验证码
        String verificationCode = stringRedisTemplate.opsForValue().get(key);

        //如果不能从缓存里面获取到验证码说明已经失效了，需要重新发送
        if(StringUtils.isEmpty(verificationCode)){
            throw new SystemException("The verification code is expired.");
        }
        //输入的验证码和缓存里面的验证码
        if(!userLoginInfo.getVerifyCode().equals(verificationCode)){
            throw new SystemException("The verification code is incorrect.");
        }

        return UserSubscriberDTO.builder()
                .token(UUID.randomUUID().toString())
                .name(StringUtils.isNotBlank(userLoginInfo.getNickname()) ? userLoginInfo.getNickname() : null)
                .profileImg(StringUtils.isNotBlank(userLoginInfo.getProfileImg()) ? userLoginInfo.getProfileImg() : "")
                .uid(UUID.randomUUID().toString())

                .email("")
                .emailVerified(UserEmailVerified.NOT_VERIFIED.getState())
                .phoneNumber(userLoginInfo.getPhoneNumber())
                .loginType(ThirdPartyLoginType.PHONE).build();
    }
}
