package com.massive.discover.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.User;
import com.massive.discover.common.model.Result;
import com.massive.discover.common.model.SmsLoginRequest;
import com.massive.discover.user.model.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:28
 */

public interface UserService extends IService<User> {

    Integer LIMIT_COUNT = 20;

    /**
     * login service
     *
     * @param userLoginInfo user login info
     */
    UserInfo login(UserLoginInfo userLoginInfo, OauthService oauthService);

    /**
     * bing third party login
     *
     * @param userId user id
     */
    UserInfo bindThirdPartyLogin(Long userId, UserLoginInfo userLoginInfo, OauthService oauthService);

    /**
     * 从通过token缓存读取user
     *
     * @param idToken token
     * @return null if not exist in cache
     */
    UserCacheInfo getUserInfoFromCache(String idToken);

    /**
     * 注册界面取得用户年龄
     *
     * @param userBirthday user birthday in YYYY-MM-DD format
     * @return 1 when success or message if failed
     */
    String bindUserBirthday(Long userId, UserBirthday userBirthday);

    String sendVerifyCode(UserLoginInfo userLoginInfo);

    UserInfo updateUserInfo(UserCacheInfo loginUserInfo, UpdateUserInfoReq updateUserInfoReq);

    String sendVerifyCodeMock(UserLoginInfo userLoginInfo);

    /**
     * 注销用户信息
     * @param userId
     * @return
     */
    boolean unregister(Long userId, String idToken);
}
