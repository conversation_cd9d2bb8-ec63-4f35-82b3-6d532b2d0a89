package com.massive.discover.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.massive.discover.common.entity.*;
import com.massive.discover.common.enums.ScoreRecordSourceEnum;
import com.massive.discover.common.utils.*;
import com.massive.discover.user.constants.UserCacheConstant;
import com.massive.discover.user.model.*;
import com.massive.discover.user.service.*;
import com.massive.discover.common.dao.UserMapper;
import com.massive.discover.common.dao.UserProfileImgMapper;
import com.massive.discover.common.enums.AuditStatusEnum;
import com.massive.discover.common.exception.AuthException;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.user.enums.Gender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserLoginService userLoginService;

    @Resource
    private SystemService systemService;

    @Resource
    private UserProfileImgMapper userProfileImgMapper;

    @Resource
    private AwsSnsUtils awsSnsUtils;

    @Resource
    private IUserScoreService userScoreService;

    @Value("${phone.login.whiteList}")
    private String phoneLoginWhiteList;

    @Resource
    private SendVerifyCodeRecordService sendVerifyCodeRecordService;

    /**
     * 短信模板: Your verification code is:%s
     */
    @Value("${aws.sns.template}")
    public String template;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UserInfo login(UserLoginInfo userLoginInfo, OauthService oauthService) {
        UserSubscriberDTO userSubscriber = oauthService.verifyAndExchangeToken(userLoginInfo);

        UserLogin loginRecord = new UserLogin();
        if(StringUtils.isNotEmpty(userLoginInfo.getPhoneNumber())){
            loginRecord = userLoginService.getOne(
                    new QueryWrapper<UserLogin>().lambda()
                            .eq(UserLogin::getPhone, userSubscriber.getPhoneNumber()));
        }else{
            loginRecord = userLoginService.getOne(
                    new QueryWrapper<UserLogin>().lambda()
                            .eq(UserLogin::getThirdPartyId, userSubscriber.getUid()));
        }

        User user;
        // 不存在的新用户，映射并创建用户
        if (loginRecord == null) {
            loginRecord = UserLogin.builder()
                    .isThirdParty(1)
                    .thirdPartyType(userSubscriber.getLoginType().getType())
                    .thirdPartyId(userSubscriber.getUid())
                    .email(userSubscriber.getEmail()).
                    phone(userSubscriber.getPhoneNumber())
                    .build();

            user = User.builder()
                    .nickname(StringUtils.isNotBlank(userSubscriber.getName()) ? userSubscriber.getName() : null)
                    .gender(Gender.MALE.getGender())
                    .authToken(userSubscriber.getToken())
                    .build();
            save(user);
            loginRecord.setUserId(user.getId());
            // 保存用户头像
            userProfileImgMapper.insert(UserProfileImg.builder()
                    .profileImg(userSubscriber.getProfileImg())
                    .status(AuditStatusEnum.NORMAL.getStatus())
                    .build());
            // 初始化系统设置信息(userid会自动填充)
            systemService.save(new SystemSettings());
            userLoginService.save(loginRecord);
            userScoreService.addScoreByUserId(user.getId(), 500, ScoreRecordSourceEnum.INIT.getType(), null, "Registration reward");
        } else {
            user = getById(loginRecord.getUserId());
            if (user.getCreateAt() == null) {
                user.setCreateAt(LocalDateTime.now());
            }
            user.setAuthToken(userSubscriber.getToken());
            updateById(user);
        }

        // 已存在的用户，直接缓存
        // 在redis中缓存token
        cacheIdToken(userSubscriber.getToken(), user, Duration.ofMillis(UserCacheConstant.EXPIRE_TIME_SEC * 1000));

        UserInfo userInfo = BeanCopierUtil.copy(user, UserInfo.class);
        userInfo.setBirthdayStr(user.getBirthday() == null ?
                "" : user.getBirthday().toString());
        userInfo.setRegisterTime(user.getCreateAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        return userInfo;
    }

    @Override
    public UserInfo bindThirdPartyLogin(Long userId, UserLoginInfo userLoginInfo, OauthService oauthService) {
        UserSubscriberDTO userSubscriber = oauthService.verifyAndExchangeToken(userLoginInfo);
        User user = getById(userId);
        if (user == null) {
            throw new SystemException("binding user not found");
        }

        UserLogin loginRecord = userLoginService.getOne(
                new QueryWrapper<UserLogin>().lambda()
                        .eq(UserLogin::getThirdPartyId, userSubscriber.getUid()));
        // 不存在的新用户，映射并创建用户
        if (loginRecord == null) {
            loginRecord = UserLogin.builder()
                    .userId(userId)
                    .nickname(userLoginInfo.getNickname())
                    .isThirdParty(1)
                    .thirdPartyType(userSubscriber.getLoginType().getType())
                    .thirdPartyId(userSubscriber.getUid())
                    .email(userSubscriber.getEmail())
                    .phone(userSubscriber.getPhoneNumber())
                    .build();
            userLoginService.save(loginRecord);
        }
        UserInfo userInfo = BeanCopierUtil.copy(user, UserInfo.class);
        userInfo.setBirthdayStr(user.getBirthday() == null ?
                "" : user.getBirthday().toString());
        userInfo.setRegisterTime(user.getCreateAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        return userInfo;
    }

    /**
     * 将映射好的token和UserInfo存入redis备用
     *
     * @param idToken  jwt token
     * @param userInfo 映射后的userInfo
     * @param ttl      缓存存活时间，根据jwt expire时间来确定
     */
    private void cacheIdToken(String idToken, User userInfo, Duration ttl) {
        try {
            UserCacheInfo cacheInfo = BeanCopierUtil.copy(userInfo, UserCacheInfo.class);
            stringRedisTemplate.opsForValue().set(
                    String.format(UserCacheConstant.JWT_KEY_FORMAT, idToken),
                    new ObjectMapper().writeValueAsString(cacheInfo), ttl
            );
        } catch (Exception ex) {
            throw new SystemException(ex);
        }
    }

    @Override
    public UserCacheInfo getUserInfoFromCache(String idToken) {
        try {
            String userCacheJson = stringRedisTemplate.opsForValue().get(String.format(UserCacheConstant.JWT_KEY_FORMAT, idToken));
            if (StringUtils.isEmpty(userCacheJson)) {
                log.warn("idToken not found in cache {}", idToken);
                throw new AuthException("idToken not found in cache");
            }
            return new ObjectMapper().readValue(userCacheJson, UserCacheInfo.class);
        } catch (Exception ex) {
            throw new SystemException(ex);
        }
    }

    /**
     * 注册页面时记录用户生日
     *
     * @param userBirthday 用户生日YYYY-MM-DD
     * @return Success when it's ok, or error message
     */
    @Override
    public String bindUserBirthday(Long userID, UserBirthday userBirthday) {
        try {
            String birthday = userBirthday.getBirthday();
            Pattern p = Pattern.compile("\\d{4}-\\d{2}-\\d{2}");
            if (p.matcher(birthday).matches()) {
                User user = getById(userID);
                user.setBirthday(LocalDate.parse(birthday, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                updateById(user);
                return "Success";
            } else {
                throw new SystemException("birthday format is not right");
            }
        } catch (Exception ex) {
            throw new SystemException(ex);
        }
    }

    @Override
    public String sendVerifyCode(UserLoginInfo userLoginInfo) {

        if(StringUtils.isEmpty(userLoginInfo.getPhoneNumber())){
            throw new SystemException("phone number can not empty");
        }

        //如果手机号在白名单里面，模拟发送验证码。
        List<String> phoneWhiteList = Arrays.stream(phoneLoginWhiteList.split(";")).collect(Collectors.toList());
        if(phoneWhiteList.contains(userLoginInfo.getPhoneNumber())){
            return sendVerifyCodeMock(userLoginInfo);
        }

        String key = "verificationCode:" + userLoginInfo.getPhoneNumber();
        //根据电话号码去缓存查询五分钟内是否已经发送过验证码
        String verificationCode = stringRedisTemplate.opsForValue().get(key);
        if(StringUtils.isNotEmpty(verificationCode)){
            return "success";
        }

        String code = String.format("%06d", ThreadLocalRandom.current().nextInt(1000000));
        String result = awsSnsUtils.sendMessage(userLoginInfo.getPhoneNumber(), code);
        if("success".equals(result)){
            stringRedisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
        }

        SendVerifyCodeRecord sendVerifyCodeRecord = new SendVerifyCodeRecord().builder()
                .sendType(result.equals("success") ? 1 : 2)
                .sendInfo(result.equals("success") ? String.format(template,code) : "")
                .phoneNum(userLoginInfo.getPhoneNumber())
                .errorInfo(!result.equals("success") ? result : "")
                .build();
        sendVerifyCodeRecordService.saveSendRecord(sendVerifyCodeRecord);

        return result;
    }

    @Override
    public UserInfo updateUserInfo(UserCacheInfo loginUserInfo, UpdateUserInfoReq updateUserInfoReq) {

        //需要用userid再查询一次用户信息，确保用户是存在的
        User user = this.getById(loginUserInfo.getId());
        AssertUtils.notNull(user, "The user does not exist");

        if(StringUtils.isNotEmpty(updateUserInfoReq.getBirthday())){
            LocalDate beginDateTime = LocalDate.parse(updateUserInfoReq.getBirthday(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            user.setBirthday(beginDateTime);
        }
        if(null != updateUserInfoReq.getGender()){
            user.setGender(updateUserInfoReq.getGender());
        }
        if(StringUtils.isNotEmpty(updateUserInfoReq.getNickname())){
            user.setNickname(updateUserInfoReq.getNickname());
        }
        if(StringUtils.isNotEmpty(updateUserInfoReq.getLocation())){
            user.setLocation(updateUserInfoReq.getLocation());
        }
        user.setUpdateAt(LocalDateTime.now());
        AssertUtils.isTrue(this.updateById(user), "User information failed to update");

        //更新完成
        UserInfo userInfo = BeanCopierUtil.copy(user, UserInfo.class);
        userInfo.setBirthdayStr(user.getBirthday() == null ?
                "" : user.getBirthday().toString());
        userInfo.setRegisterTime(user.getCreateAt().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        return userInfo;
    }

    @Override
    public String sendVerifyCodeMock(UserLoginInfo userLoginInfo) {

        if(StringUtils.isEmpty(userLoginInfo.getPhoneNumber())){
            throw new SystemException("phone number can not empty");
        }
        String key = "verificationCode:" + userLoginInfo.getPhoneNumber();
        //根据电话号码去缓存查询五分钟内是否已经发送过验证码
        String verificationCode = stringRedisTemplate.opsForValue().get(key);
        if(StringUtils.isNotEmpty(verificationCode)){
            return "success";
        }
        String code = "123456";
        stringRedisTemplate.opsForValue().set(key, code, 5, TimeUnit.MINUTES);
        return "success";
    }

    @Override
    public boolean unregister(Long userId, String idToken) {
        // 清除token
        String userCacheJson = stringRedisTemplate.opsForValue().get(String.format(UserCacheConstant.JWT_KEY_FORMAT, idToken));
        if (StringUtils.isNotBlank(userCacheJson)) {
            stringRedisTemplate.delete(String.format(UserCacheConstant.JWT_KEY_FORMAT, idToken));
        }

        QueryWrapper<UserLogin> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        userLoginService.remove(queryWrapper);
        return this.removeById(userId);
    }
}
