package com.massive.discover.user.service;

import com.massive.discover.user.model.line.v2.AccessToken;
import com.massive.discover.user.model.line.v2.IdToken;
import com.massive.discover.user.model.line.v2.Verify;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.io.IOException;
import java.util.List;
import java.util.function.Function;

/**
 * <p>LINE v2 API Access</p>
 */
public interface LineService {

    String GRANT_TYPE_AUTHORIZATION_CODE = "authorization_code";
    String GRANT_TYPE_REFRESH_TOKEN = "refresh_token";

    default <T, R> R getClient(
            final String url,
            final Class<T> service,
            final Function<T, Call<R>> function) {

        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor();
        interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        OkHttpClient client = new OkHttpClient.Builder().addInterceptor(interceptor).build();

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(url)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        T t = retrofit.create(service);
        Call<R> call = function.apply(t);
        try {
            return call.execute().body();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    AccessToken accessToken(String code);

    AccessToken refreshToken(final AccessToken accessToken);

    Verify verify(final AccessToken accessToken);

    void revoke(final AccessToken accessToken);

    IdToken idToken(String id_token);

    String getLineWebLoginUrl(String state, String nonce, List<String> scopes);

    boolean verifyIdToken(String id_token, String nonce);

}
