package com.massive.discover.user.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RewardDTO implements Serializable {
    @NonNull
    private Long id;
    @NonNull
    private String name;

    private Long userRewardId;

    private String description;

    private String detailDescription;

    private String imageUrl;

    private Integer inventory;

    private List<String> detailImageUrls;

    private Integer pointPrice;

    private Long userId;

    private LocalDateTime boughtAt;

    private LocalDateTime exchangedAt;
}
