package com.massive.discover.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.massive.discover.common.dao.UserScoreMapper;
import com.massive.discover.common.entity.ScoreRecord;
import com.massive.discover.common.entity.UserScore;
import com.massive.discover.common.enums.ScoreTypeEnum;
import com.massive.discover.common.model.ScoreRecordDetailAddReq;
import com.massive.discover.common.model.ScoreRecordVO;
import com.massive.discover.common.utils.AssertUtils;
import com.massive.discover.user.component.RankListComponent;
import com.massive.discover.user.model.HistoryScoreDTO;
import com.massive.discover.user.service.IScoreRecordService;
import com.massive.discover.user.service.IUserScoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserScoreServiceImpl extends ServiceImpl<UserScoreMapper, UserScore> implements IUserScoreService {

    @Autowired
    private RankListComponent rankListComponent;

    @Autowired
    private IScoreRecordService scoreRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean subtractScoreByUserId(Long userId, Integer score, Integer source, Long sourceId) {
        //根据userId，获取用户积分
        QueryWrapper<UserScore> userScoreQueryWrapper = new QueryWrapper<>();
        userScoreQueryWrapper.eq("user_id", userId);
        UserScore userScore = this.baseMapper.selectOne(userScoreQueryWrapper);
        AssertUtils.notNull(userScore, "The points are insufficient and cannot be deducted");
        AssertUtils.isFalse(score > userScore.getTotalScore(), "The points are insufficient and cannot be deducted");

        UserScore updateUserScore = new UserScore();
        updateUserScore.setId(userScore.getId());
        updateUserScore.setTotalScore(userScore.getTotalScore() - score);
        this.updateById(updateUserScore);

        scoreRecordService.saveScoreRecordDetail(new ScoreRecordDetailAddReq().builder()
                .score("-" + score)
                .addOrSubtract(ScoreTypeEnum.SUBTRACT.getType())
                .sourceId(null != sourceId ? sourceId : null)
                .scoreSource(source)
                .build());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addScoreByUserId(Long userId, Integer score, Integer source, Long sourceId, String sourceName) {

        // Query user information by userId to get the user's current points
        QueryWrapper<UserScore> userScoreQueryWrapper = new QueryWrapper<>();
        userScoreQueryWrapper.eq("user_id", userId);
        UserScore userScore = this.baseMapper.selectOne(userScoreQueryWrapper);

        if(null == userScore){
            // Add new user's total score
            UserScore insertUserScore = new UserScore();
            insertUserScore.setTotalScore(score);
            insertUserScore.setRankingScore(score);
            insertUserScore.setUserId(userId);
            this.save(insertUserScore);
            // ===== MYSQL-BASED RANKING: No separate ranking update needed =====
            // Rankings are calculated on-demand from t_user_score table

            // ===== REDIS-BASED RANKING (COMMENTED OUT) =====
            // Update ranking data (ranking data is only updated when points are added, not when points are deducted)
            // rankListComponent.updateRank(userId, score);
        }else{
            // Update user's total score
            UserScore updateUserScore = new UserScore();
            updateUserScore.setId(userScore.getId());
            updateUserScore.setTotalScore(userScore.getTotalScore() + score);
            updateUserScore.setRankingScore(userScore.getRankingScore() + score);
            this.updateById(updateUserScore);
            // ===== MYSQL-BASED RANKING: No separate ranking update needed =====
            // Rankings are calculated on-demand from t_user_score table

            // ===== REDIS-BASED RANKING (COMMENTED OUT) =====
            // Update ranking data (ranking data is only updated when points are added, not when points are deducted)
            // rankListComponent.updateRank(userId, userScore.getRankingScore() + score);
        }

        scoreRecordService.saveScoreRecordDetail(new ScoreRecordDetailAddReq().builder()
                .score("+" + score)
                .addOrSubtract(ScoreTypeEnum.ADD.getType())
                .sourceId(sourceId)
                .scoreSource(source)
                .sourceName(StringUtils.isNotEmpty(sourceName) ? sourceName : "")
                .build());

        return true;
    }

    @Override
    public List<HistoryScoreDTO> getUserHistoryScore(Long userId) {
        List<ScoreRecord> userScoreList = scoreRecordService.list(new QueryWrapper<ScoreRecord>()
                .lambda().eq(ScoreRecord::getUserId, userId));

        List<String> createDateList = Lists.newArrayList();
        userScoreList.forEach(userScore -> {
            if(!createDateList.contains(userScore.getCreateAt().toLocalDate().toString())){
                createDateList.add(userScore.getCreateAt().toLocalDate().toString());
            }
        });

        List<HistoryScoreDTO> historyScoreList = Lists.newArrayList();

        createDateList.forEach(createDate -> {
            HistoryScoreDTO historyScoreDTO = new HistoryScoreDTO();
            List<ScoreRecordVO> scoreRecordList = Lists.newArrayList();
//            ScoreRecordVO mock = new ScoreRecordVO();
//            mock.setReasons("Admission");
//            scoreRecordList.add(mock);
            userScoreList.forEach(userScore -> {
               if (createDate.equals(userScore.getCreateAt().toLocalDate().toString())) {
                   ScoreRecordVO scoreRecordVO = new ScoreRecordVO();
                   scoreRecordVO.setScore(userScore.getScore());
                   scoreRecordVO.setFinishTime(userScore.getCreateAt());
                   scoreRecordVO.setSourceName(userScore.getSourceName());
                   scoreRecordVO.setReasons("Reward deduct!");
                   if(userScore.getAddOrSubtract().equals(ScoreTypeEnum.ADD.getType())){
                       scoreRecordVO.setReasons("Reward get!");
                   }
                   scoreRecordList.add(scoreRecordVO);
               }
            });
            historyScoreDTO.setDate(createDate);
            historyScoreDTO.setScoreRecordList(scoreRecordList);

            historyScoreList.add(historyScoreDTO);
        });

        return historyScoreList;
    }

    @Override
    public Integer getCurrentScore(Long userId) {
        QueryWrapper<UserScore> userScoreQueryWrapper = new QueryWrapper<>();
        userScoreQueryWrapper.eq("user_id", userId);
        UserScore userScore = this.baseMapper.selectOne(userScoreQueryWrapper);
        return userScore != null ? userScore.getTotalScore() : 0;
    }
}
