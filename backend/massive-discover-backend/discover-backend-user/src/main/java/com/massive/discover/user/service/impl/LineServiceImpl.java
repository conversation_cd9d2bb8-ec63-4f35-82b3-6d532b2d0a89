package com.massive.discover.user.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.massive.discover.common.exception.AuthException;
import com.massive.discover.user.model.line.v2.AccessToken;
import com.massive.discover.user.model.line.v2.IdToken;
import com.massive.discover.user.model.line.v2.Verify;
import com.massive.discover.user.service.LineApi;
import com.massive.discover.user.service.LineService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import retrofit2.Call;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.function.Function;

/**
 * <p>LINE v2 API Access</p>
 * <AUTHOR>
 */
@Service
public class LineServiceImpl implements LineService {

    private static final String GRANT_TYPE_AUTHORIZATION_CODE = "authorization_code";
    private static final String GRANT_TYPE_REFRESH_TOKEN = "refresh_token";

    @Value("${idp.line.clientId}")
    private String channelId;
    @Value("${idp.line.clientSecret}")
    private String channelSecret;
    @Value("${idp.line.callbackUrl}")
    private String callbackUrl;

    @Override
    public AccessToken accessToken(String code) {
        return getClient(t -> t.accessToken(
                GRANT_TYPE_AUTHORIZATION_CODE,
                channelId,
                channelSecret,
                callbackUrl,
                code));
    }

    @Override
    public AccessToken refreshToken(final AccessToken accessToken) {
        return getClient(t -> t.refreshToken(
                GRANT_TYPE_REFRESH_TOKEN,
                accessToken.getRefresh_token(),
                channelId,
                channelSecret));
    }

    @Override
    public Verify verify(final AccessToken accessToken) {
        return getClient(t -> t.verify(
                accessToken.getAccess_token()));
    }

    @Override
    public void revoke(final AccessToken accessToken) {
        getClient(t -> t.revoke(
                accessToken.getAccess_token(),
                channelId,
                channelSecret));
    }

    @Override
    public IdToken idToken(String id_token) {
        try {
            DecodedJWT jwt = JWT.decode(id_token);
            return new IdToken(
                    jwt.getClaim("iss").asString(),
                    jwt.getClaim("sub").asString(),
                    jwt.getClaim("aud").asString(),
                    jwt.getClaim("ext").asLong(),
                    jwt.getClaim("iat").asLong(),
                    jwt.getClaim("nonce").asString(),
                    jwt.getClaim("name").asString(),
                    jwt.getClaim("picture").asString());
        } catch (JWTDecodeException e) {
            throw new AuthException(e);
        }
    }

    @Override
    public String getLineWebLoginUrl(String state, String nonce, List<String> scopes) {
        final String encodedCallbackUrl;
        final String scope = String.join("%20", scopes);

        try {
            encodedCallbackUrl = URLEncoder.encode(callbackUrl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        return "https://access.line.me/oauth2/v2.1/authorize?response_type=code"
                + "&client_id=" + channelId
                + "&redirect_uri=" + encodedCallbackUrl
                + "&state=" + state
                + "&scope=" + scope
                + "&nonce=" + nonce;
    }

    @Override
    public boolean verifyIdToken(String idToken, String nonce) {
        try {
            JWT.require(
                            Algorithm.HMAC256(channelSecret))
                    .withIssuer("https://access.line.me")
                    .withAudience(channelId)
                    .withClaim("nonce", nonce)
                    // add 60 seconds leeway to handle clock skew between client and server sides.
                    .acceptLeeway(60)
                    .build()
                    .verify(idToken);
            return true;
        } catch (Exception ex) {
            throw new AuthException(ex);
        }
    }

    private <R> R getClient(final Function<LineApi, Call<R>> function) {
        return getClient("https://api.line.me/", LineApi.class, function);
    }
}
