package com.massive.discover.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@AllArgsConstructor
public enum Gender {

    /**
     * male
     */
    MALE(1),

    /**
     * female
     */
    FEMALE(2),

    /**
     * other
     */
    OTHER(3);

    @Getter
    private final int gender;

    private static final Map<Integer, Gender> MAP;

    static {
        MAP = Arrays.stream(Gender.values()).collect(Collectors.toMap(Gender::getGender, gender -> gender));
    }

    public static Gender parse(Integer gender) {
        return MAP.get(gender);
    }
}
