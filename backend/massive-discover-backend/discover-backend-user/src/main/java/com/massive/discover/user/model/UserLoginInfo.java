package com.massive.discover.user.model;

import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 14:19
 */

@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UserLoginInfo {

    @ApiModelProperty(value = "firebase generated JWT, field of 'idToken'")
    private String firebaseJwt;

    @ApiModelProperty(value = "line generated idToken, field of 'id_token', or oauth code")
    private String lineIdToken;

    @ApiModelProperty(value = "nickname of login user")
    private String nickname;

    @ApiModelProperty(value = "nickname of login user")
    private String profileImg;

    @ApiModelProperty("手机号")
    private String phoneNumber;

    @ApiModelProperty("验证码")
    private String verifyCode;
}
