package com.massive.discover.user.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 20:57
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCacheInfo {

    private Long id;

    private String nickname;

    private Integer gender;

    private String profileImg;

    private String profileBgImg;

    private Timestamp birthday;

    private String location;

    private String signature;

    private Integer point;

    private Integer personalInfoId;

    private Integer systemSettingId;

    private Integer addressListId;

    private Integer blacklistId;

    private String authToken;

    //是否管理员
    private Integer adminLevelId;
}
