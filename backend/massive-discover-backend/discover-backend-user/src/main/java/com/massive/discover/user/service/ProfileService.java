package com.massive.discover.user.service;


import com.massive.discover.user.model.ProfileDTO;
import com.massive.discover.user.model.ProfileVO;
import com.massive.discover.user.model.UserProfileVO;

public interface ProfileService {

    /**
     * 个人页面
     *
     * @param loginUserId login user id
     * @param userId      user id
     * @return UserProfileVO user profile info
     */
    UserProfileVO queryUserProfile(Long loginUserId, Long userId);

    /**
     * 取得个人信息
     *
     * @param userId user id
     * @return profile info
     */
    ProfileVO queryProfile(Long userId);

    /**
     * 更新个人信息
     *
     * @param userId     user id
     * @param profileDTO profile info
     */
    void updateProfile(Long userId, ProfileDTO profileDTO);

}

