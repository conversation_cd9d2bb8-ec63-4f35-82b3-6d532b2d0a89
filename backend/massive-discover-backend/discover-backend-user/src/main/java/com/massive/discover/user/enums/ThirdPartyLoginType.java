package com.massive.discover.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/4 18:03
 */

@AllArgsConstructor
public enum ThirdPartyLoginType {

    /**
     * email
     */
    FIREBASE_MAIL(1, "email"),

    /**
     * password
     */
    FIREBASE_PASSWORD(2, "password"),

    /**
     * phone
     */
    PHONE(3, "phone"),

    /**
     * google
     */
    FIREBASE_GOOGLE(4, "google.com"),

    /**
     * apple
     */
    FIREBASE_APPLE(5, "apple.com"),

    /**
     * facebook
     */
    FIREBASE_FACEBOOK(6, "facebook.com"),

    /**
     * twitter
     */
    FIREBASE_TWITTER(7, "twitter.com"),

    /**
     * anonymous
     */
    FIREBASE_ANONYMOUS(8, "anonymous"),

    /**
     * line
     */
    LINE(9, "line");

    @Getter
    private final Integer type;

    @Getter
    private final String desc;

    private static final Map<String, ThirdPartyLoginType> MAP;

    static {
        MAP = Arrays.stream(ThirdPartyLoginType.values()).collect(Collectors.toMap(ThirdPartyLoginType::getDesc, type -> type));
    }

    public static ThirdPartyLoginType parse(String provider) {
        return MAP.get(provider);
    }
}
