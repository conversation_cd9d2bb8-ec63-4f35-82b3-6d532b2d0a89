package com.massive.discover.user.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * User
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {

    private Long id;

    private String nickname;

    private Integer gender;

    private String profileImg;

    private String profileBgImg;

    private String birthdayStr;

    private String location;

    private String signature;

    private Integer point;

    private Integer shopId;

    private String authToken;

    private Long registerTime;

}

