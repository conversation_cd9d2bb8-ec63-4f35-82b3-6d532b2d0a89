package com.massive.discover.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.UserScore;
import com.massive.discover.user.model.HistoryScoreDTO;

import java.util.List;


public interface IUserScoreService extends IService<UserScore> {

    //根据userId，扣减积分
    boolean subtractScoreByUserId(Long userId, Integer score, Integer source, Long sourceId);

    //根据userId，增加积分
    boolean addScoreByUserId(Long userId, Integer score, Integer source, Long sourceId, String sourceName);

    List<HistoryScoreDTO> getUserHistoryScore(Long userId);

    //根据userId，获取当前可用积分
    Integer getCurrentScore(Long userId);
}

