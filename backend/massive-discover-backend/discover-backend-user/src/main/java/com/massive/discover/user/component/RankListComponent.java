package com.massive.discover.user.component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.massive.discover.common.dao.UserMapper;
import com.massive.discover.common.dao.UserScoreMapper;
import com.massive.discover.common.entity.User;

import com.massive.discover.common.model.RankDTO;
import com.massive.discover.common.model.RankResultDTO;
import com.massive.discover.user.model.RankResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Component
public class RankListComponent {

    // ===== REDIS-BASED IMPLEMENTATION (COMMENTED OUT) =====
    /*
    @Autowired
    private RedisComponent redisComponent;
    private static final String RANK_PREFIX = "global_rank";
    */

    // ===== MYSQL-BASED IMPLEMENTATION =====
    @Autowired
    private UserScoreMapper userScoreMapper;

    @Autowired
    private UserMapper userMapper;

    // ===== REDIS HELPER METHODS (COMMENTED OUT) =====
    /*
    private List<RankDTO> buildRedisRankToBizDO(Set<ZSetOperations.TypedTuple<String>> result, long offset) {
        List<RankDTO> rankList = new ArrayList<>(result.size());
        long rank = offset;
        for (ZSetOperations.TypedTuple<String> sub : result) {
            rankList.add(new RankDTO(rank++, Math.abs(sub.getScore().intValue()), Long.parseLong(sub.getValue())));
        }
        return rankList;
    }
    */

    /**
     * 获取前n名的排行榜数据
     *
     * @param n
     * @return
     */
    public RankResponse getTopNRanks(int n) {
        // ===== MYSQL-BASED IMPLEMENTATION =====
        List<RankResultDTO> rankResultDTOList = userScoreMapper.getTopNRankings(n);
        return new RankResponse(rankResultDTOList);

        // ===== REDIS-BASED IMPLEMENTATION (COMMENTED OUT) =====
        /*
        Set<ZSetOperations.TypedTuple<String>> result = redisComponent.rangeWithScore(RANK_PREFIX, 0, n - 1);
        List<RankDTO> rankDTOList = buildRedisRankToBizDO(result, 1);
        List<Long> userIds = rankDTOList.stream().map(RankDTO::getUserId).collect(Collectors.toList());
        //根据userid获取用户信息
        List<User> userList = userMapper.selectList(new QueryWrapper<User>().lambda().in(User::getId, userIds));
        List<RankResultDTO> rankResultDTOList = getRankResults(rankDTOList, userList);
        return new RankResponse(rankResultDTOList);
        */
    }

    /**
     * 获取用户所在排行榜的位置，以及排行榜中其前后n个用户的排行信息
     *
     * @param userId
     * @param n
     * @return
     */
    public List<RankDTO> getRankAroundUser(Long userId, int n) {
        // ===== MYSQL-BASED IMPLEMENTATION =====
        List<RankDTO> result = userScoreMapper.getRankingsAroundUser(userId, n);
        return result != null ? result : Collections.emptyList();

        // ===== REDIS-BASED IMPLEMENTATION (COMMENTED OUT) =====
        /*
        // 首先是获取用户对应的排名
        RankDTO rank = getRank(userId);
        if (rank.getRank() <= 0) {
            // fixme 用户没有上榜时，不返回
            return Collections.emptyList();
        }
        // 因为实际的排名是从0开始的，所以查询周边排名时，需要将n-1
        Set<ZSetOperations.TypedTuple<String>> result =
                redisComponent.rangeWithScore(RANK_PREFIX, Math.max(0, rank.getRank() - n - 1), rank.getRank() + n - 1);
        return buildRedisRankToBizDO(result, rank.getRank() - n);
        */
    }


    /**
     * 获取用户的排行榜位置
     *
     * @param userId
     * @return
     */
    public RankDTO getRank(Long userId) {
        // ===== MYSQL-BASED IMPLEMENTATION =====
        RankDTO result = userScoreMapper.getUserRank(userId);
        if (result == null) {
            // 没有排行时，直接返回一个默认的
            return new RankDTO(-1L, 0, userId);
        }
        return result;

        // ===== REDIS-BASED IMPLEMENTATION (COMMENTED OUT) =====
        /*
        // 获取排行， 因为默认是0为开头，因此实际的排名需要+1
        Long rank = redisComponent.rank(RANK_PREFIX, String.valueOf(userId));
        if (rank == null) {
            // 没有排行时，直接返回一个默认的
            return new RankDTO(-1L, 0, userId);
        }

        // 获取积分
        Double score = redisComponent.score(RANK_PREFIX, String.valueOf(userId));
        return new RankDTO(rank + 1, Math.abs(score.intValue()), userId);
        */
    }

    /**
     * 更新用户积分，并获取最新的个人所在排行榜信息
     *
     * @param userId
     * @param score
     * @return
     */
    public RankDTO updateRank(Long userId, Integer score) {
        // ===== MYSQL-BASED IMPLEMENTATION =====
        // With MySQL-based ranking, we don't need to maintain a separate ranking store
        // Rankings are calculated on-demand from the t_user_score table
        // Just return the current rank for the user
        return getRank(userId);

        // ===== REDIS-BASED IMPLEMENTATION (COMMENTED OUT) =====
        /*
        // 因为zset默认积分小的在前面，所以我们对score进行取反，这样用户的积分越大，对应的score越小，排名越高
        redisComponent.add(RANK_PREFIX, String.valueOf(userId), -score);
        Long rank = redisComponent.rank(RANK_PREFIX, String.valueOf(userId));
        return new RankDTO(rank + 1, score, userId);
        */
    }

    // ===== REDIS HELPER METHODS (COMMENTED OUT) =====
    /*
    //拼接返回信息
    private static List<RankResultDTO> getRankResults(List<RankDTO> rankDTOList, List<User> userList) {
        List<RankResultDTO> rankResultDTOList = Lists.newArrayList();
        for (RankDTO rankDTO : rankDTOList) {
            RankResultDTO  rankResultDTO = new RankResultDTO();
            for (User user : userList) {
                if(rankDTO.getUserId().equals(user.getId())){
                    rankResultDTO.setRank(rankDTO.getRank());
                    rankResultDTO.setScore(rankDTO.getScore());
                    rankResultDTO.setNickName(user.getNickname());
                    rankResultDTOList.add(rankResultDTO);
                    break;
                }
            }
        }
        return rankResultDTOList;
    }
    */

}
