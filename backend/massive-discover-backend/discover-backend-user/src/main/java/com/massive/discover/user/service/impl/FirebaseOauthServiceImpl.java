package com.massive.discover.user.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.client.util.ArrayMap;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.massive.discover.common.exception.AuthException;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.user.model.UserLoginInfo;
import com.massive.discover.user.model.UserSubscriberDTO;
import com.massive.discover.user.enums.ThirdPartyLoginType;
import com.massive.discover.user.enums.UserEmailVerified;
import com.massive.discover.user.service.OauthService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FirebaseOauthServiceImpl implements OauthService {

    private final ObjectMapper objectMapper;

    public FirebaseOauthServiceImpl() {
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public UserSubscriberDTO verifyAndExchangeToken(UserLoginInfo userLoginInfo) {
        FirebaseToken firebaseToken;
        try {
            firebaseToken = FirebaseAuth.getInstance().verifyIdToken(userLoginInfo.getFirebaseJwt());
        } catch (FirebaseAuthException e) {
            throw new AuthException("id token expired，please retry login");
        }

        if (firebaseToken == null) {
            throw new SystemException("login error,please retry");
        }
        val firebaseAuths = objectMapper.convertValue(firebaseToken.getClaims().get("firebase"), ArrayMap.class);
        String provider = objectMapper.convertValue(firebaseAuths.get("sign_in_provider"), String.class);
        ThirdPartyLoginType loginType = ThirdPartyLoginType.parse(provider);
        if (loginType == null) {
            throw new SystemException("third party login not support " + provider);
        }

        // 过期时间,用于判断ttl
        long expirationTime = Long.parseLong(firebaseToken.getClaims().get("exp").toString()) * 1000L;
        long currentTime = System.currentTimeMillis();

        // token无效，已过期
        if (expirationTime < currentTime) {
            throw new AuthException("expired login error token ,please retry");
        }

        val phoneNumber = firebaseToken.getClaims().get("phone_number");
        return UserSubscriberDTO.builder()
                .token(userLoginInfo.getFirebaseJwt())
                .name(StringUtils.isNotBlank(userLoginInfo.getNickname()) ? userLoginInfo.getNickname() : firebaseToken.getName())
                .profileImg(StringUtils.isNotBlank(userLoginInfo.getProfileImg()) ? userLoginInfo.getProfileImg() : "")
                .uid(firebaseToken.getUid())
                .email(firebaseToken.getEmail())
                .emailVerified(firebaseToken.isEmailVerified() ?
                        UserEmailVerified.VERIFIED.getState() : UserEmailVerified.NOT_VERIFIED.getState())
                .phoneNumber(phoneNumber != null ? phoneNumber.toString() : "")
                .loginType(loginType).build();
    }
}
