package com.massive.discover.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.SendVerifyCodeRecordMapper;
import com.massive.discover.common.entity.SendVerifyCodeRecord;
import com.massive.discover.user.service.SendVerifyCodeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SendVerifyCodeRecordServiceImpl extends ServiceImpl<SendVerifyCodeRecordMapper, SendVerifyCodeRecord> implements SendVerifyCodeRecordService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSendRecord(SendVerifyCodeRecord sendVerifyCodeRecord) {
        this.save(sendVerifyCodeRecord);
    }
}
