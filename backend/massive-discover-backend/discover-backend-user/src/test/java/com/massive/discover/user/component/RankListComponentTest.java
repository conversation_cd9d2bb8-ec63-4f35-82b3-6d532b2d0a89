package com.massive.discover.user.component;

import com.massive.discover.common.dao.UserScoreMapper;
import com.massive.discover.common.model.RankDTO;
import com.massive.discover.common.model.RankResultDTO;
import com.massive.discover.user.model.RankResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Test for MySQL-based ranking system
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class RankListComponentTest {

    @Mock
    private UserScoreMapper userScoreMapper;

    @InjectMocks
    private RankListComponent rankListComponent;

    private List<RankResultDTO> mockTopRankings;
    private RankDTO mockUserRank;

    @BeforeEach
    void setUp() {
        // Setup mock data
        mockTopRankings = Arrays.asList(
            createRankResult(1L, 1500, "Alice"),
            createRankResult(2L, 1200, "Bob"),
            createRankResult(3L, 800, "Charlie")
        );

        mockUserRank = new RankDTO(2L, 1200, 123L);
    }

    @Test
    void testGetTopNRanks_Success() {
        // Given
        when(userScoreMapper.getTopNRankings(anyInt())).thenReturn(mockTopRankings);

        // When
        RankResponse result = rankListComponent.getTopNRanks(3);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRankResults());
        assertEquals(3, result.getRankResults().size());
        
        RankResultDTO firstPlace = result.getRankResults().get(0);
        assertEquals(1L, firstPlace.getRank());
        assertEquals(1500, firstPlace.getScore());
        assertEquals("Alice", firstPlace.getNickName());
    }

    @Test
    void testGetTopNRanks_EmptyResult() {
        // Given
        when(userScoreMapper.getTopNRankings(anyInt())).thenReturn(Collections.emptyList());

        // When
        RankResponse result = rankListComponent.getTopNRanks(10);

        // Then
        assertNotNull(result);
        assertNotNull(result.getRankResults());
        assertTrue(result.getRankResults().isEmpty());
    }

    @Test
    void testGetRank_UserExists() {
        // Given
        Long userId = 123L;
        when(userScoreMapper.getUserRank(userId)).thenReturn(mockUserRank);

        // When
        RankDTO result = rankListComponent.getRank(userId);

        // Then
        assertNotNull(result);
        assertEquals(2L, result.getRank());
        assertEquals(1200, result.getScore());
        assertEquals(userId, result.getUserId());
    }

    @Test
    void testGetRank_UserNotFound() {
        // Given
        Long userId = 999L;
        when(userScoreMapper.getUserRank(userId)).thenReturn(null);

        // When
        RankDTO result = rankListComponent.getRank(userId);

        // Then
        assertNotNull(result);
        assertEquals(-1L, result.getRank());
        assertEquals(0, result.getScore());
        assertEquals(userId, result.getUserId());
    }

    @Test
    void testGetRankAroundUser_Success() {
        // Given
        Long userId = 123L;
        List<RankDTO> mockAroundRankings = Arrays.asList(
            new RankDTO(1L, 1500, 111L),
            new RankDTO(2L, 1200, 123L),
            new RankDTO(3L, 800, 333L)
        );
        when(userScoreMapper.getRankingsAroundUser(userId, 1)).thenReturn(mockAroundRankings);

        // When
        List<RankDTO> result = rankListComponent.getRankAroundUser(userId, 1);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(123L, result.get(1).getUserId()); // User should be in the middle
    }

    @Test
    void testGetRankAroundUser_EmptyResult() {
        // Given
        Long userId = 999L;
        when(userScoreMapper.getRankingsAroundUser(eq(userId), anyInt())).thenReturn(null);

        // When
        List<RankDTO> result = rankListComponent.getRankAroundUser(userId, 1);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testUpdateRank_ReturnsCurrentRank() {
        // Given
        Long userId = 123L;
        Integer score = 1500;
        when(userScoreMapper.getUserRank(userId)).thenReturn(mockUserRank);

        // When
        RankDTO result = rankListComponent.updateRank(userId, score);

        // Then
        assertNotNull(result);
        assertEquals(mockUserRank.getRank(), result.getRank());
        assertEquals(mockUserRank.getScore(), result.getScore());
        assertEquals(mockUserRank.getUserId(), result.getUserId());
    }

    private RankResultDTO createRankResult(Long rank, Integer score, String nickName) {
        RankResultDTO result = new RankResultDTO();
        result.setRank(rank);
        result.setScore(score);
        result.setNickName(nickName);
        return result;
    }
}
