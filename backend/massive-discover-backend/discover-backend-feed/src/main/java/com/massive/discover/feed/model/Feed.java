package com.massive.discover.feed.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 23:02
 */

@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Data
public class Feed {

    @ApiModelProperty(name = "id of media")
    private Long id;

    @ApiModelProperty(name = "url of cover")
    private String coverUrl;

    @ApiModelProperty(name = "length of cover")
    private Double coverLength;

    @ApiModelProperty(name = "width of cover")
    private Double coverWidth;

    @ApiModelProperty(name = "text of media title")
    private String title;

    @ApiModelProperty(name = "text of media description")
    private String description;

    @ApiModelProperty(name = "create timestamp")
    private Long createTimeStamp;
}
