package com.massive.discover.feed.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 22:50
 */

@Builder
@Data
public class HomeBanner {

    @ApiModelProperty(name = "image url of banner")
    private String imageUrl;

    @ApiModelProperty(name = "jump Link of banner(native/H5)")
    private String linkUrl;

    @ApiModelProperty(name = "length url of banner")
    private Double length;

    @ApiModelProperty(name = "width url of banner")
    private Double width;
}
