package com.massive.discover.feed.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 23:48
 */

@Getter
@AllArgsConstructor
public enum MediaStatusEnum {

    /**
     * normal
     */
    NORMAL(1),

    /**
     * invisible
     */
    INVISIBLE(2),

    /**
     * disable
     */
    DISABLE(0);

    private final Integer status;

    private static final Map<Integer, MediaStatusEnum> MAP;

    static {
        MAP = Arrays.stream(MediaStatusEnum.values()).collect(Collectors.toMap(MediaStatusEnum::getStatus, status -> status));
    }

    public static MediaStatusEnum parse(Integer status) {
        return MAP.get(status);
    }
}
