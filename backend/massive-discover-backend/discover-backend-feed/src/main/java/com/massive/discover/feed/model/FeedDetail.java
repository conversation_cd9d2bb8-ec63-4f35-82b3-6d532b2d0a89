package com.massive.discover.feed.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 23:02
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class FeedDetail extends Feed {

    @ApiModelProperty(name = "title of media")
    private String title;

    @ApiModelProperty(name = "description of media")
    private String description;

    List<FeedDetail> more;
}
