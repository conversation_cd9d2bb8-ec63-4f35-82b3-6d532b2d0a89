package com.massive.discover.feed.service.impl;

import com.google.common.collect.Lists;
import com.massive.discover.feed.model.Home;
import com.massive.discover.feed.service.FeedService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 23:55
 */

@Service
public class FeedServiceImpl implements FeedService {

    @Override
    public Home feedHome(Long viewerId) {

        return Home.builder().banners(Lists.newArrayList()).homeCategories(Lists.newArrayList()).build();
    }
}
