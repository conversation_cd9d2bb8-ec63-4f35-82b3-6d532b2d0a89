package com.massive.discover.lock.redis.impl;

import com.google.common.collect.Lists;
import com.massive.discover.lock.redis.IRedisLock;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/12/23
 * <p>
 * 简单的分布式非阻塞式锁实现,优化解锁，使其无法释放其他应用/线程的加的锁
 * 缺点：
 * 1. 无法重入
 */

public class SimpleRedisLock implements IRedisLock {

    private StringRedisTemplate stringRedisTemplate;

    public SimpleRedisLock(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Override
    public Boolean tryLock(String lockName, String request, long leaseTime, TimeUnit unit) {
        // 注意该方法是在 spring-boot-starter-data-redis 2.1 版本新增加的，若是之前版本 可以执行下面的方法
        return stringRedisTemplate.opsForValue().setIfAbsent(lockName, request, leaseTime, unit);
    }

    /**
     * 适用于 spring-boot-starter-data-redis 2.1 之前的版本
     *
     * @param lockName
     * @param request
     * @param leaseTime
     * @param unit
     * @return
     */
    public Boolean doOldTryLock(String lockName, String request, long leaseTime, TimeUnit unit) {
        Boolean result = stringRedisTemplate.execute((RedisCallback<Boolean>) connection -> {
            RedisSerializer valueSerializer = stringRedisTemplate.getValueSerializer();
            RedisSerializer keySerializer = stringRedisTemplate.getKeySerializer();

            Boolean innerResult = connection.set(keySerializer.serialize(lockName), valueSerializer.serialize(request), Expiration.from(leaseTime, unit), RedisStringCommands.SetOption.SET_IF_ABSENT);
            return innerResult;
        });
        return result;
    }

    @Override
    public Boolean unlock(String lockName, String request) {
        DefaultRedisScript<Boolean> unlockScript = new DefaultRedisScript<>();
        unlockScript.setLocation(new ClassPathResource("simple_unlock.lua"));
        unlockScript.setResultType(Boolean.class);
        return stringRedisTemplate.execute(unlockScript, Lists.newArrayList(lockName), request);
    }

    @Override
    public Boolean forceUnlock(String lockName) {
        return stringRedisTemplate.delete(lockName);
    }

}
