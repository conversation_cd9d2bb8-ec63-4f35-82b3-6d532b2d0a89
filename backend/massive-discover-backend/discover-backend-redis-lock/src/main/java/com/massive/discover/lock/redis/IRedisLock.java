package com.massive.discover.lock.redis;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/23 10:55 PM
 */

public interface IRedisLock {

    /**
     * 非阻塞式加锁，若锁存在，直接返回
     *
     * @param lockName  锁名称
     * @param request   唯一标识，防止其他应用/线程解锁，可以使用 UUID 生成
     * @param leaseTime 超时时间
     * @param unit      时间单位
     * @return Boolean
     */
    Boolean tryLock(String lockName, String request, long leaseTime, TimeUnit unit);

    /**
     * 解锁
     * 如果传入应用标识与之前加锁一致，解锁成功
     * 否则直接返回
     *
     * @param lockName 锁
     * @param request  唯一标识
     * @return Boolean
     */
    Boolean unlock(String lockName, String request);

    /**
     * 强制解锁
     *
     * @param lockName
     * @return true:解锁成功，false：锁不存在，或者锁已经超时，
     */
    Boolean forceUnlock(String lockName);
}
