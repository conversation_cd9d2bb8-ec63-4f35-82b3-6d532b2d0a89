package com.massive.discover.lock.redis;

import com.massive.discover.lock.redis.impl.RedisReentrancyLock;
import com.massive.discover.lock.redis.impl.RedisReentrancyThreadLocalLock;
import com.massive.discover.lock.redis.impl.SimpleRedisLock;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/12/23 6:19 PM
 */
@Configuration(proxyBeanMethods = false)
@ComponentScan("com.massive.discover.lock.redis")
public class RedisLockAutoConfiguration {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Bean
    @ConditionalOnMissingBean(name = "simpleRedisLock")
    public SimpleRedisLock simpleRedisLock() {
        return new SimpleRedisLock(stringRedisTemplate);
    }

    @Bean
    @ConditionalOnMissingBean(name = "redisReentrancyLock")
    public RedisReentrancyLock redisReentrancyLock() {
        RedisReentrancyLock redisReentrancyLock = new RedisReentrancyLock(stringRedisTemplate);
        redisReentrancyLock.init();
        return redisReentrancyLock;
    }

    @Bean
    @ConditionalOnMissingBean(name = "redisReentrancyThreadLocalLock")
    @ConditionalOnSingleCandidate()
    public RedisReentrancyThreadLocalLock redisReentrancyThreadLocalLock(SimpleRedisLock simpleRedisLock) {
        return new RedisReentrancyThreadLocalLock(simpleRedisLock);
    }
}
