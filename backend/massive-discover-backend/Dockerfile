# Multi-stage build for production deployment
FROM maven:3.8.6-amazoncorretto-11 AS builder

# Set working directory
WORKDIR /app

# Copy pom files first for better Docker layer caching
COPY pom.xml .
COPY discover-backend-common/pom.xml discover-backend-common/
COPY discover-backend-web/pom.xml discover-backend-web/
COPY discover-backend-operations/pom.xml discover-backend-operations/
COPY discover-backend-user/pom.xml discover-backend-user/
COPY discover-backend-feed/pom.xml discover-backend-feed/
COPY discover-backend-redis-lock/pom.xml discover-backend-redis-lock/

# Download dependencies (cached layer if pom.xml files haven't changed)
RUN mvn dependency:go-offline -B

# Copy source code
COPY . .

# Build the application (skip tests for faster builds - tests run in CI)
RUN mvn clean package -DskipTests -B

# Production runtime stage
FROM amazoncorretto:11.0.11-alpine

# Install curl for health checks and create non-root user
RUN apk add --no-cache curl && \
    addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Create app directory and set ownership
WORKDIR /app
RUN chown appuser:appgroup /app

# Copy the built JAR from builder stage
COPY --from=builder --chown=appuser:appgroup /app/discover-backend-web/target/discover-backend-web.jar app.jar

# Create logs directory with proper permissions
RUN mkdir -p /app/logs && chown appuser:appgroup /app/logs

# Switch to non-root user
USER appuser

# Expose port 8082
EXPOSE 8082

# Add health check endpoint
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8082/rank/v1/showTopN?n=1 || exit 1

# JVM optimization for containerized environments
ENV JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+UseG1GC -XX:+UseStringDeduplication"

# Run the application
CMD ["sh", "-c", "java $JAVA_OPTS -jar -Dserver.port=8082 app.jar"]
