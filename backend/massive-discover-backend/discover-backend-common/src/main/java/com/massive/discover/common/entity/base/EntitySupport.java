package com.massive.discover.common.entity.base;

import com.baomidou.mybatisplus.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-11-24 11:06
 * @version: V1.0
 * @description：y
 * modifyTime           author              description
 * -------------------------------------------------------------------
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class EntitySupport implements Serializable {

    private static final long serialVersionUID = -2419880638782525925L;

    @TableId(type = IdType.AUTO)
    @Column(isAutoIncrement = true, isKey = true)
    private Long id;

    /**
     * 更新时间
     */
    @Column(type = MySqlTypeConstant.DATETIME, defaultValue = "NULL ON UPDATE CURRENT_TIMESTAMP")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateAt;

    /**
     * 创建时间
     */
    @Column(type = MySqlTypeConstant.DATETIME, defaultValue = "CURRENT_TIMESTAMP")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createAt;

    @Column(comment = "用户ID")
    @TableField(fill = FieldFill.INSERT)
    private Long userId;
}
