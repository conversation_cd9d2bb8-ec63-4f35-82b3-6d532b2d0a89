package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 登录设备类型
 */
@Getter
@AllArgsConstructor
public enum SysLoginDeviceEnum {

    PC(1, "电脑端"),

    ANDROID(2, "安卓"),

    APPLE(3, "苹果"),

    H5(4, "H5"),

    WEIXIN_MP(5, "微信小程序");


    private final Integer value;

    @Getter
    private final String description;

    private static final Map<Integer, SysLoginDeviceEnum> MAP;

    static {
        MAP = Arrays.stream(SysLoginDeviceEnum.values()).collect(Collectors.toMap(SysLoginDeviceEnum::getValue, type -> type));
    }

    public static SysLoginDeviceEnum parse(Integer type) {
        return MAP.get(type);
    }
}
