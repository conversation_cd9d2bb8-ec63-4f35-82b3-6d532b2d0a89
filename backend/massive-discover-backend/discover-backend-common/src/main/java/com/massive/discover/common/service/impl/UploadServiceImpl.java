package com.massive.discover.common.service.impl;

import com.massive.discover.common.constant.FileConstant;
import com.massive.discover.common.service.UploadService;
import com.massive.discover.common.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.time.Duration;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/06/07 14:21
 */
@Slf4j
public class UploadServiceImpl implements UploadService {

    private final Long preSignedExpiration;

    private final S3Presigner s3Presigner;

    public UploadServiceImpl(Long preSignedExpiration, S3Presigner s3Presigner) {
        this.preSignedExpiration = preSignedExpiration;
        this.s3Presigner = s3Presigner;
    }

    @Override
    public String preSignedFileUrl(String bucketName, String keyName) {
        return preSignedFileUrl(bucketName, keyName, FileConstant.DEFAULT_CONTENT_TYPE);
    }

    @Override
    public String preSignedFileUrl(String bucketName, String keyName, String contentType) {
        try {
            PutObjectRequest objectRequest = PutObjectRequest.builder().bucket(bucketName).key(keyName).contentType(contentType).build();

            PutObjectPresignRequest preSignRequest = PutObjectPresignRequest.builder().signatureDuration(Duration.ofMinutes(preSignedExpiration)).putObjectRequest(objectRequest).build();
            PresignedPutObjectRequest preSignedRequest = s3Presigner.presignPutObject(preSignRequest);
            String signedUrl = preSignedRequest.url().toString();
            log.info("get signedUrl success:fileKey-{},signedUrl-{}", keyName, signedUrl);
            return signedUrl;
        } catch (Exception ex) {
            log.error("get signedUrl failed:fileKey-{}", keyName, ex);
            throw new SystemException(ex);
        }
    }
}
