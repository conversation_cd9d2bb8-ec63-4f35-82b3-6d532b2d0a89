package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_show", comment = "t_show", isSimple = true)
@TableName("t_show")
public class Show extends EntitySupport {

    @NonNull
    private String markId;

    @NonNull
    private String name;

    private String thumbnailUrl;

    private String description;

    private LocalDateTime beginTime;

    private LocalDateTime endTime;

}
