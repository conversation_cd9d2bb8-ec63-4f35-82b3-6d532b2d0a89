package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 20:11
 */
@Getter
@AllArgsConstructor
public enum ResponseStatusEnum {

    /**
     * success
     */
    SUCCESS(200, "success"),


    /**
     * no auth
     */
    UNAUTHORIZED(401, "unauthorized"),


    /**
     * bad request
     */
    BAD_REQUEST(400, "bad request"),

    /**
     * bad request
     */
    INTERNAL_ERROR(500, "internal error");

    private final Integer status;

    @Getter
    private final String description;

    private static final Map<Integer, ResponseStatusEnum> MAP;

    static {
        MAP = Arrays.stream(ResponseStatusEnum.values()).collect(Collectors.toMap(ResponseStatusEnum::getStatus, state -> state));
    }

    public static ResponseStatusEnum parse(Integer status) {
        return MAP.get(status);
    }
}
