package com.massive.discover.common.utils;

import com.massive.discover.common.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentials;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sns.SnsClient;
import software.amazon.awssdk.services.sns.model.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * AWS SNS SMS Utility Class
 * Replaces deprecated AWS Pinpoint for SMS functionality
 * 
 * <AUTHOR> Team
 */
@Slf4j
@Component
public class AwsSnsUtils {

    /**
     * AWS Access Key ID
     */
    @Value("${aws.sns.accessKeyId}")
    private String accessKeyId;

    /**
     * AWS Secret Access Key
     */
    @Value("${aws.sns.secretAccessKey}")
    private String secretAccessKey;

    /**
     * AWS Region (e.g., ap-northeast-1, ap-northeast-1)
     */
    @Value("${aws.sns.region}")
    private String region;

    /**
     * SMS message template with placeholder for verification code
     * Example: "Your verification code is: %s. Valid for 5 minutes."
     */
    @Value("${aws.sns.template}")
    private String template;

    /**
     * Sender ID for SMS (optional, not supported in all regions)
     * Used in regions that support Sender ID (not US/Canada)
     */
    @Value("${aws.sns.senderId:}")
    private String senderId;

    /**
     * SMS Type: Promotional or Transactional
     * Use "Transactional" for verification codes
     */
    @Value("${aws.sns.smsType:Transactional}")
    private String smsType;

    /**
     * Maximum price willing to pay for SMS in USD
     * Helps prevent unexpected charges
     */
    @Value("${aws.sns.maxPrice:0.50}")
    private String maxPrice;

    /**
     * Send SMS message using AWS SNS
     * 
     * @param destinationNumber Phone number in E.164 format (e.g., +1234567890)
     * @param code Verification code to send
     * @return "success" if sent successfully, error message otherwise
     */
    public String sendMessage(String destinationNumber, String code) {
        try {
            log.info("🚀 Starting SMS send process to {} using AWS SNS", destinationNumber);
            log.info("📋 SMS Configuration - Region: {}, Template: {}, SenderId: '{}', SmsType: {}, MaxPrice: {}",
                    region, template, senderId, smsType, maxPrice);
            log.info("🔑 AWS Credentials - AccessKeyId: {}..., SecretKey: {}...",
                    accessKeyId != null ? accessKeyId.substring(0, Math.min(8, accessKeyId.length())) : "null",
                    secretAccessKey != null ? secretAccessKey.substring(0, Math.min(4, secretAccessKey.length())) : "null");

            // Validate phone number
            if (!isValidPhoneNumber(destinationNumber)) {
                log.error("❌ Invalid phone number format: {}", destinationNumber);
                return "Invalid phone number format. Must be in E.164 format (e.g., +817014601880)";
            }

            // Format the message with the verification code
            String message = String.format(template, code);
            log.info("📱 Formatted message: {}", message);

            // Create SNS client
            log.info("🔧 Creating SNS client...");
            SnsClient snsClient = createSnsClient();
            log.info("✅ SNS client created successfully");

            // Send SMS
            log.info("📤 Sending SMS request to AWS SNS...");
            String messageId = sendSms(snsClient, destinationNumber, message);

            log.info("🎉 SMS sent successfully to {}. MessageId: {}, Content: {}, Time: {}",
                    destinationNumber, messageId, message, LocalDateTime.now());

            return "success";

        } catch (SnsException e) {
            log.error("❌ AWS SNS error sending SMS to {}: Code: {}, Message: {}, Details: {}",
                    destinationNumber, e.awsErrorDetails().errorCode(), e.awsErrorDetails().errorMessage(), e.awsErrorDetails());
            return "AWS SNS Error: " + e.awsErrorDetails().errorMessage();
        } catch (Exception e) {
            log.error("💥 Unexpected error sending SMS to {}: {}", destinationNumber, e.getMessage(), e);
            return "Unexpected error: " + e.getMessage();
        }
    }

    /**
     * Create SNS client with credentials
     */
    private SnsClient createSnsClient() {
        try {
            log.info("🔧 Creating SNS client with region: {}", region);

            if (accessKeyId == null || accessKeyId.trim().isEmpty()) {
                log.error("❌ AWS Access Key ID is null or empty!");
                throw new IllegalArgumentException("AWS Access Key ID is required");
            }

            if (secretAccessKey == null || secretAccessKey.trim().isEmpty()) {
                log.error("❌ AWS Secret Access Key is null or empty!");
                throw new IllegalArgumentException("AWS Secret Access Key is required");
            }

            log.info("✅ AWS credentials are configured");

            SnsClient client = SnsClient.builder()
                    .credentialsProvider(AwsCredentialsProviderChain.builder()
                            .addCredentialsProvider(new AwsCredentialsProvider() {
                                @Override
                                public AwsCredentials resolveCredentials() {
                                    log.debug("🔑 Resolving AWS credentials...");
                                    return AwsBasicCredentials.create(accessKeyId, secretAccessKey);
                                }
                            }).build())
                    .region(Region.of(region))
                    .build();

            log.info("✅ SNS client created successfully for region: {}", region);
            return client;

        } catch (Exception e) {
            log.error("💥 Failed to create SNS client: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Send SMS using SNS
     */
    private String sendSms(SnsClient snsClient, String phoneNumber, String message) {
        try {
            log.info("🔧 Building SMS attributes...");

            // Set SMS attributes
            Map<String, MessageAttributeValue> smsAttributes = new HashMap<>();

            // SMS Type (Promotional or Transactional)
            smsAttributes.put("AWS.SNS.SMS.SMSType",
                MessageAttributeValue.builder()
                    .stringValue(smsType)
                    .dataType("String")
                    .build());
            log.info("📝 Added SMS Type: {}", smsType);

            // Maximum price willing to pay
            smsAttributes.put("AWS.SNS.SMS.MaxPrice",
                MessageAttributeValue.builder()
                    .stringValue(maxPrice)
                    .dataType("Number")
                    .build());
            log.info("💰 Added Max Price: {}", maxPrice);

            // Sender ID (if configured and supported in region)
            if (senderId != null && !senderId.trim().isEmpty()) {
                smsAttributes.put("AWS.SNS.SMS.SenderID",
                    MessageAttributeValue.builder()
                        .stringValue(senderId)
                        .dataType("String")
                        .build());
                log.info("🏷️ Added Sender ID: {}", senderId);
            } else {
                log.info("🚫 No Sender ID configured (good for sandbox mode)");
            }

            log.info("📦 Building publish request...");
            // Create publish request
            PublishRequest publishRequest = PublishRequest.builder()
                    .phoneNumber(phoneNumber)
                    .message(message)
                    .messageAttributes(smsAttributes)
                    .build();

            log.info("📤 Publishing to AWS SNS - Phone: {}, Message Length: {}, Attributes Count: {}",
                    phoneNumber, message.length(), smsAttributes.size());

            // Send the message
            PublishResponse publishResponse = snsClient.publish(publishRequest);

            log.info("✅ AWS SNS publish successful - MessageId: {}, ResponseMetadata: {}",
                    publishResponse.messageId(), publishResponse.responseMetadata());

            return publishResponse.messageId();

        } catch (SnsException e) {
            log.error("❌ SNS publish failed - Error Code: {}, Message: {}, StatusCode: {}, RequestId: {}",
                    e.awsErrorDetails().errorCode(),
                    e.awsErrorDetails().errorMessage(),
                    e.statusCode(),
                    e.requestId());
            throw e;
        } catch (Exception e) {
            log.error("💥 Unexpected error in sendSms: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Validate phone number format (basic E.164 validation)
     */
    public boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }
        
        // Basic E.164 format validation: +[country code][number]
        // Should start with + and be 7-15 digits total
        return phoneNumber.matches("^\\+[1-9]\\d{6,14}$");
    }

    /**
     * Format phone number to E.164 format
     * This is a basic implementation - you might want to use a library like libphonenumber for production
     */
    public String formatPhoneNumber(String phoneNumber, String defaultCountryCode) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return null;
        }
        
        // Remove all non-digit characters except +
        String cleaned = phoneNumber.replaceAll("[^+\\d]", "");
        
        // If it already starts with +, return as is
        if (cleaned.startsWith("+")) {
            return cleaned;
        }
        
        // If it starts with country code without +, add +
        if (cleaned.length() > 10) {
            return "+" + cleaned;
        }
        
        // Otherwise, add default country code
        return defaultCountryCode + cleaned;
    }
}
