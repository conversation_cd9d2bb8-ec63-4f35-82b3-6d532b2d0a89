package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_user_score", comment = "用户积分表", isSimple = true)
@TableName("t_user_score")
public class UserScore extends EntitySupport {

    //需要做加分、减分操作
    @Column(isNull = false, defaultValue = "0")
    private Integer totalScore;

    //也是总分-该总分不参与加减积分，只用作排行榜用
    @Column(isNull = false, defaultValue = "0")
    private Integer rankingScore;
}