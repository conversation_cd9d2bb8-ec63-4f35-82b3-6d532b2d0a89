package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.LocalDateTime;

/**
 * 展会的分区，如：1F、2F
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_bulletin", comment = "t_bulletin", isSimple = true)
@TableName("t_bulletin")
public class Bulletin extends EntitySupport {

    @NonNull
    private Long eventId;

    @NonNull
    private String title;

    private String brief;

    private String pageUrl;

    private LocalDateTime publishAt;

    @Column(defaultValue = "false")
    private boolean published;
}
