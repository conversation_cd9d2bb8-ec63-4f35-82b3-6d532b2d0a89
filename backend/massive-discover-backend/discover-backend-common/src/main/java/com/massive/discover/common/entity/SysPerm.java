package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "sys_perm", comment = "系统权限", isSimple = true)
@TableName("sys_perm")
public class SysPerm extends EntitySupport {

    /**
     * 权限值，权限控制表达式
     */
    @Unique(columns = {"pval"})
    private String pval;

    /**
     * 父节点
     */
    @Column(defaultValue = "0")
    private Integer parentId;

    /**
     * 权限名称
     */
    private String pname;

    /**
     * 权限排序,用于菜单权限
     */
    private Integer ranking;

    /**
     * 权限类型：1.菜单; 2.api; 3.按钮; 4.数据
     */
    @Column(comment = "权限类型：1.菜单; 2.api; 3.按钮; 4.数据")
    private Integer ptype;

    /**
     * 是否叶子节点
     */
    @Column(defaultValue = "1")
    private Boolean leaf;

    private String icon;

}
