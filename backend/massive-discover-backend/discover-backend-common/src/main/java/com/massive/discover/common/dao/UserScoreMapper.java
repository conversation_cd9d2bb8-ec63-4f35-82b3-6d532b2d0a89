package com.massive.discover.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.massive.discover.common.entity.UserScore;
import com.massive.discover.common.model.RankDTO;
import com.massive.discover.common.model.RankResultDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 23:16
 */

public interface UserScoreMapper extends BaseMapper<UserScore> {

    /**
     * Get top N rankings with user details
     * @param limit number of top users to return
     * @return list of ranking results with user info
     */
    List<RankResultDTO> getTopNRankings(@Param("limit") int limit);

    /**
     * Get specific user's rank and score
     * @param userId user ID to get rank for
     * @return user's rank information
     */
    RankDTO getUserRank(@Param("userId") Long userId);

    /**
     * Get rankings around a specific user
     * @param userId target user ID
     * @param offset number of users before/after to include
     * @return list of rankings around the user
     */
    List<RankDTO> getRankingsAroundUser(@Param("userId") Long userId, @Param("offset") int offset);

}
