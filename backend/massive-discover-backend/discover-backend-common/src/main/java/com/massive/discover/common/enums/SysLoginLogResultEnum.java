package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 登录类型
 */
@Getter
@AllArgsConstructor
public enum SysLoginLogResultEnum {

    LOGIN_SUCCESS(0, "登录成功"), LOGIN_FAIL(1, "登录失败"), LOGIN_OUT(2, "退出登录");

    private final Integer status;

    @Getter
    private final String description;

    private static final Map<Integer, SysLoginLogResultEnum> MAP;

    static {
        MAP = Arrays.stream(SysLoginLogResultEnum.values()).collect(Collectors.toMap(SysLoginLogResultEnum::getStatus, status -> status));
    }

    public static SysLoginLogResultEnum parse(Integer status) {
        return MAP.get(status);
    }
}
