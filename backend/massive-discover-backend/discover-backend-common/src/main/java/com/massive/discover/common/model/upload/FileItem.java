package com.massive.discover.common.model.upload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class FileItem {

    private Long id;

    private String fileName;

    private Long fileSize;

    private String contentType;

    @ApiModelProperty(notes = "unique file key")
    private String fileKey;

    @ApiModelProperty(notes = "file type : 1 video, 2 image, 3 other")
    private Integer mediaType;

    private String url;

    private String signedUrl;

    @ApiModelProperty(notes = "file status : 0 init, 1 finished, 98 failed, 99 timeout")
    private Integer status;

    private Long timestamp;
}