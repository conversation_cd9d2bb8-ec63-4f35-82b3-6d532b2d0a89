package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_mark_status", comment = "t_mark_status", isSimple = true)
@TableName("t_mark_status")
public class MarkStatus extends EntitySupport {
    @Unique(value = "markId_userId", columns = {"mark_id", "user_id"})
    @NonNull
    private Long markId;

    @NonNull
    private Long userId;

    @Column(defaultValue = "false")
    private boolean gotScore;

    @Column(defaultValue = "false")
    private boolean collected;
}