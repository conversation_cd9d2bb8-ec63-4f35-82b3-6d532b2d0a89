package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    /**
     * 审核通过
     */
    NORMAL(1),

    /**
     * 审核中
     */
    AUDITING(2),

    /**
     * 审核未通过
     */
    INVISIBLE(-1);

    private final Integer status;

    private static final Map<Integer, AuditStatusEnum> MAP;

    static {
        MAP = Arrays.stream(AuditStatusEnum.values()).collect(Collectors.toMap(AuditStatusEnum::getStatus, status -> status));
    }

    public static AuditStatusEnum parse(Integer status) {
        return MAP.get(status);
    }
}
