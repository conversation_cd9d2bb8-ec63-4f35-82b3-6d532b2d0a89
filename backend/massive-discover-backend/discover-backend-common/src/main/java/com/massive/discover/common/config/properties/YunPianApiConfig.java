package com.massive.discover.common.config.properties;

import com.massive.discover.common.exception.SystemException;

import java.util.Properties;

public class YunPianApiConfig {

    private static final Properties properties = new Properties();

    static {
        try {
            properties.load(Thread.currentThread().getContextClassLoader().getResourceAsStream("application.properties"));
        } catch (Exception e) {
            throw new SystemException("加载application.properties异常");
        }
    }
    public static  final String getApiKey(){
        return properties.getProperty("APIKEY");
    }

    public static final String getSendSmsApi() {
        return properties.getProperty("send.sms.api");
    }

    public static final String getTplSendSmsApi() {
        return properties.getProperty("send.tpl.api");
    }

    public static final String getEncoding() {
        return properties.getProperty("api.encoding");
    }
}
