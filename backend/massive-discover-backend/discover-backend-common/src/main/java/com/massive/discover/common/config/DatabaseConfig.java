package com.massive.discover.common.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.*;
import com.massive.discover.common.config.properties.TenantProperties;
import com.massive.discover.common.context.TenantContextHolder;
import lombok.AllArgsConstructor;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.NullValue;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = {
        "com.massive.discover.common.dao",
        "com.gitee.sunchenbin.mybatis.actable.dao.*"})
@ComponentScan(basePackages = {
        "com.massive.discover",
        "com.gitee.sunchenbin.mybatis.actable.manager.*"})
@AutoConfigureBefore(DatabaseConfig.class)
@AllArgsConstructor
@EnableConfigurationProperties(TenantProperties.class)
public class DatabaseConfig {

    private TenantProperties tenantProperties;
    /**
     * mybatis-plus分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {

        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        //动态表名插件
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> {
            Long tenantId = TenantContextHolder.getTenantId();
            //符合的表名拼接租户号
            if (tenantProperties.getDynamicTables().stream().anyMatch(
                    (t) -> t.equalsIgnoreCase(tableName))) {
                return tableName + "_" + tenantId;
            }
            return tableName;
        });
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);


        // 新多租户插件配置,一缓和二缓遵循mybatis的规则,需要设置 MybatisConfiguration#useDeprecatedExecutor = false 避免缓存万一出现问题
        //租户拦截器
        interceptor.addInnerInterceptor(new TenantLineInnerInterceptor(new TenantLineHandler() {

            /**
             * 获取租户ID
             *
             * @return
             */
            @Override
            public Expression getTenantId() {
                Long tenantId = TenantContextHolder.getTenantId();
                if (null != tenantId) {
                    return new LongValue(tenantId);
                }
                return new NullValue();
            }

            /**
             * 获取多租户的字段名
             *
             * @return String
             */
            @Override
            public String getTenantIdColumn() {
                return tenantProperties.getColumn();
            }

            /**
             * 过滤不需要根据租户隔离的表
             * 这是 default 方法,默认返回 false 表示所有表都需要拼多租户条件
             *
             * @param tableName 表名
             */
            @Override
            public boolean ignoreTable(String tableName) {
                Long tenantId = TenantContextHolder.getTenantId();

                //0表示超级管理员，不需要多租户校验
                if(null != tenantId && 0 == tenantId){
                    return true;
                }
                return tenantProperties.getIgnoreTables().stream().anyMatch(
                        (t) -> tableName.startsWith(t) || tableName.equalsIgnoreCase(t)
                );
            }
        }));
        // 如果用了分页插件注意先 add TenantLineInnerInterceptor 再 add PaginationInnerInterceptor
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
}