package com.massive.discover.common.utils;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/2 22:00
 */

public class TextUtil {

    public static String withSuffix(long count) {
        if (count < 1000) return String.valueOf(count);
        int exp = (int) (Math.log(count) / Math.log(1000));
        return String.format("%.1f %c",
                count / Math.pow(1000, exp),
                "kMGTPE".charAt(exp-1));
    }
}
