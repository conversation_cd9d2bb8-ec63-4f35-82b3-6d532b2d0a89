package com.massive.discover.common.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 登录查询日志
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LoginLogQueryParams extends BasePageParams {

    @ApiModelProperty("开始日期")
    private String startDate;

    @ApiModelProperty("结束日期")
    private String endDate;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("ip")
    private String ip;
}
