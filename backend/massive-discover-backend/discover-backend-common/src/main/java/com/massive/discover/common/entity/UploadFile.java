package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_upload_file", comment = "上传文件信息", isSimple = true)
@TableName("t_upload_file")
public class UploadFile extends EntitySupport {

    private String fileName;

    private Long fileSize;

    private String contentType;

    @Unique
    private String fileKey;

    @Unique
    private String signedUrl;

    @Column(type = MySqlTypeConstant.TINYINT, comment = "1 video, 2 image, 3 other")
    private Integer mediaType;

    private Integer uploadState;
}