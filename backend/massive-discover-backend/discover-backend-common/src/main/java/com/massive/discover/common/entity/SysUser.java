package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@Table(name = "sys_user", comment = "系统用户", isSimple = true)
@TableName("sys_user")
public class SysUser extends EntitySupport {

    /**
     * 登录名，不可改
     */
    private String uname;

    /**
     * 用户昵称，可改
     */
    private String nick;

    /**
     * 已加密的登录密码
     */
    private String pwd;

    /**
     * 加密盐值
     */
    private String salt;

    /**
     * 是否锁定
     */
    @Column(defaultValue = "0")
    private Boolean isLock;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 头像
     */
    private String avatar;
}
