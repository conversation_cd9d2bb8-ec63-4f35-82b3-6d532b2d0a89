package com.massive.discover.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.massive.discover.common.entity.SysRole;
import com.massive.discover.common.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    List<SysRole> queryUserRoles(@Param("userId") Long userId);
}
