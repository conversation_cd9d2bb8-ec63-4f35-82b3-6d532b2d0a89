package com.massive.discover.common.properties;

import lombok.Data;
import lombok.NonNull;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties("idp.firebase")
public class FirebaseCredentialProperties {
    private String credential;
}
