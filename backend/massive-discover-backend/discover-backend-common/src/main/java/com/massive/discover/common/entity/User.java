package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_user", comment = "用户信息", isSimple = true)
@TableName("t_user")
public class User{

    @TableId(type = IdType.AUTO)
    @Column(isAutoIncrement = true, isKey = true)
    private Long id;

    /**
     * 更新时间
     */
    @Column(type = MySqlTypeConstant.DATETIME, defaultValue = "NULL ON UPDATE CURRENT_TIMESTAMP")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateAt;

    /**
     * 创建时间
     */
    @Column(type = MySqlTypeConstant.DATETIME, defaultValue = "CURRENT_TIMESTAMP")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createAt;

    private String nickname;

    private Integer gender;

    private LocalDate birthday;

    private String location;

    private String signature;

    private Integer point;

    private Integer couponCollectorId;

    private Integer permissionId;

    private Integer shopId;

    private Integer personalInfoId;

    private Integer systemSettingId;

    private Integer interestListId;

    private Integer addressListId;

    private Integer blacklistId;

    //是否管理员，0：是 1：否
    private Integer adminLevelId;

    private Integer status;

    @Column(type = MySqlTypeConstant.TEXT)
    private String authToken;
}