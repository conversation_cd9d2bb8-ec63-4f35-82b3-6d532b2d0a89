package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 23:44
 */

@Getter
@AllArgsConstructor
public enum MediaTypeEnum {

    /**
     * video
     */
    VIDEO(1),

    /**
     * image
     */
    IMAGE(2),

    /**
     * other
     */
    OTHER(3);

    private final Integer type;

    private static final Map<Integer, MediaTypeEnum> MAP;

    static {
        MAP = Arrays.stream(MediaTypeEnum.values()).collect(Collectors.toMap(MediaTypeEnum::getType, type -> type));
    }

    public static MediaTypeEnum parse(Integer type) {
        return MAP.get(type);
    }
}
