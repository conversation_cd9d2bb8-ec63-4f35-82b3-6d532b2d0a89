package com.massive.discover.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScoreRecordDetailAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 积分(1000, -1000)
     */
    private String score;

    /**
     * （mark（1：游戏，2：展会 3：演出），2：兑换ID）
     */
    private Long sourceId;

    /**
     * 来源ID（mark-（1：游戏，2：展会 3：演出（+）），4：兑换积分（-））
     * {@link com.massive.discover.common.enums.MarkTypeEnum}
     */
    private Integer scoreSource;

    /**
     * 加分或者扣分（1：加分，2：扣分）
     * {@link com.massive.discover.common.enums.ScoreTypeEnum}
     */
    private Integer addOrSubtract;

    /**
     * 来源名称
     */
    private String sourceName;

}
