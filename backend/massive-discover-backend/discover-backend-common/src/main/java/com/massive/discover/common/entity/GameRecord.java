package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_game_record", comment = "t_game_record", isSimple = true)
@TableName("t_game_record")
public class GameRecord extends EntitySupport {

    @NonNull
    private Long gameId;

    @NonNull
    private String gameName;

    @NonNull
    private Integer gameLevel;

    //完成时间
    private LocalDateTime finishedAt;

    private Integer isPublic;

    //得分
    private Integer score;

    //锚点ID
    private Long markId;

}