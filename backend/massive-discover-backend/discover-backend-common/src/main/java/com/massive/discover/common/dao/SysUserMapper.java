package com.massive.discover.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.massive.discover.common.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    List<SysUser> selectUserIncludeRoles(Page<?> page,
                                         @Param("uname") String uname,
                                         @Param("tel") String tel,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime);


    List<SysUser> queryUser(IPage<?> page, @Param("type")Integer type, @Param("uname")String uname,
                            @Param("linkman")String linkName, @Param("tel")String tel, @Param("pass")Integer pass,
                            @Param("starttime")Long start, @Param("endtime")Long end, @Param("poorList")List<Integer> poorList);
}
