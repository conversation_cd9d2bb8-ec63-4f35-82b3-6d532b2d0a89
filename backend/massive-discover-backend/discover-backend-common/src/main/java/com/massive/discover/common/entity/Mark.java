package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_mark", comment = "t_mark", isSimple = true)
@TableName("t_mark")
public class Mark extends EntitySupport {

    @NonNull
    private Long eventId;

    @NonNull
    private String name;

    private Long areaId;

    //游戏或者展会（1:游戏，2:展会，3：演出）
    @NonNull
    private Integer markType;

    private Integer xAxis;

    private Integer yAxis;

    //该积分在锚点上面，如果大于0，则表示该锚点可以直接获取积分，不需要玩游戏等互动操作。
    @Column(defaultValue = "0")
    private Integer markScore;

    //展位号码，用于标识展位位置（如：A-101, Hall-B-25, Booth-42）
    @Column(length = 50, comment = "Booth number for markers (e.g., A-101, Hall-B-25, Booth-42)")
    private String boothNumber;

}