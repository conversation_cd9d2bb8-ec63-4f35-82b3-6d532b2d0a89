package com.massive.discover.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.massive.discover.common.entity.SysPerm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysPermMapper extends BaseMapper<SysPerm> {

    List<SysPerm> getPermsByUserId(@Param("userId") Long userId);

    List<SysPerm> getPermsByRoleId(@Param("roleId") Integer roleId);

    void saveOrUpdate(@Param("perms") List<SysPerm> perms);
}
