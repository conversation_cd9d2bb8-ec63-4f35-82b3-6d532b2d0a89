package com.massive.discover.common.utils;

import org.apache.commons.lang3.time.DateUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-06-25 11:13
 **/
public class TimeUtil {

    private static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final String DEFAULT_TIME_FORMAT = "yyyy-MM-dd";

    private static final String DEFAULT_ZONE_ID = ZoneId.systemDefault().getId();

    /**
     * @return yyyy-MM-dd hh:mm:ss
     */
    public static String getTimeString(Long timestamp) {
        return DateTimeFormatter.ofPattern(DEFAULT_PATTERN)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of(DEFAULT_ZONE_ID)));
    }

    public static Date getDateTimeFromTimestamp(Long timestamp) {
        return Date.from(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of(DEFAULT_ZONE_ID))
                .atZone(ZoneId.of(DEFAULT_ZONE_ID)).toInstant());
    }

    public static String getDateString(Long timestamp) {
        return DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of(DEFAULT_ZONE_ID)));
    }

    public static String getDateString(Date date) {
        return DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.of(DEFAULT_ZONE_ID)));
    }

    /**
     * @return yyyy-MM-dd hh:mm:ss
     */
    public static String getTimeString(Date date) {
        return DateTimeFormatter.ofPattern(DEFAULT_PATTERN)
                .format(LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.of(DEFAULT_ZONE_ID)));
    }

    public static Date getTimeFromTimeString(String timeString) {
        return Date.from(LocalDateTime.parse(timeString,
                        DateTimeFormatter.ofPattern(DEFAULT_PATTERN)).atZone(ZoneId.of(DEFAULT_ZONE_ID))
                .toInstant());
    }

    public static Date getTimeFromTimeString(String timeString, String timePattern) {
        return Date.from(LocalDateTime.parse(timeString,
                        DateTimeFormatter.ofPattern(timePattern)).atZone(ZoneId.of(DEFAULT_ZONE_ID))
                .toInstant());
    }

    public static Date getDateFromDateString(String timeString) {
        return Date.from(LocalDate.parse(timeString, DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT))
                .atStartOfDay().atZone(ZoneId.of(DEFAULT_ZONE_ID)).toInstant());
    }

    public static Date getDateFromDateString(String timeString, String datePattern) {
        return Date.from(LocalDate.parse(timeString, DateTimeFormatter.ofPattern(datePattern))
                .atStartOfDay().atZone(ZoneId.of(DEFAULT_ZONE_ID)).toInstant());
    }

    /**
     * 获得当天0点时间
     */
    public static Long getTimesMorning(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获得当天24点时间
     */
    public static long getTimesNight(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(Calendar.HOUR_OF_DAY, 24);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取指定日期所在月份开始的时间戳
     *
     * @param date 指定日期
     * @return millis
     */
    public static Long getMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间戳
        return c.getTimeInMillis();
    }

    /**
     * 获取指定日期所在月份结束的时间戳
     *
     * @param date 指定日期
     * @return millis
     */
    public static Long getMonthEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return c.getTimeInMillis();
    }

    /**
     * 获取上一个月结束的时间戳
     *
     * @param date 指定日期
     * @return long
     */
    public static Long getLastMonthEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为上一个月最后一天
        c.add(Calendar.MONTH, -1);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return c.getTimeInMillis();
    }

    /**
     * 获取上一个月开始的时间戳
     *
     * @param date 指定日期
     * @return millis
     */
    public static Long getLastMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为1号,当前日期既为本月第一天
        c.add(Calendar.MONTH, -1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND, 0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间戳
        return c.getTimeInMillis();
    }

    /***
     *
     * 获取上周一0点时间戳
     * @param date date
     * @return millis
     */
    public static Long getLastWeekMonday(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date(getThisWeekMonday()));
        cal.add(Calendar.DATE, -7);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        cal.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        cal.set(Calendar.MINUTE, 0);
        //将秒至0
        cal.set(Calendar.SECOND, 0);
        //将毫秒至0
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /***
     * 获取上周日24点时间戳
     * @param date date
     * @return millis
     */
    public static Long getLastWeekSunday(Date date) {
        Date a = DateUtils.addDays(date, -1);
        Calendar cal = Calendar.getInstance();
        cal.setTime(a);
        cal.set(Calendar.DAY_OF_WEEK, 1);
        //将小时至23
        cal.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        cal.set(Calendar.MINUTE, 59);
        //将秒至59
        cal.set(Calendar.SECOND, 59);
        //将毫秒至999
        cal.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return cal.getTimeInMillis();
    }

    /***
     *获取本周一0点时间戳
     * @return millis
     */
    public static Long getThisWeekMonday() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return cal.getTimeInMillis();
    }

    /**
     * end
     * 本周结束时间戳
     */
    public static Long getWeekEndTime() {
        Calendar cal = Calendar.getInstance();
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获取星期日结束时间戳
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        //将小时至23
        cal.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        cal.set(Calendar.MINUTE, 59);
        //将秒至59
        cal.set(Calendar.SECOND, 59);
        //将毫秒至999
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTimeInMillis();
    }


    /**
     * 获取上一个星期结束的时间戳
     *
     * @param date 指定日期
     * @return millis
     */
    public static Long getThisWeekEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为上一个月最后一天
        c.set(Calendar.DAY_OF_WEEK, 7);
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND, 59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间戳
        return c.getTimeInMillis();
    }

    /***
     * 获取前一天凌晨0点时间戳
     */
    public static Long getYesterdayStat(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取前一天24点时间戳
     */
    public static Long getYesterdayEnd(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DAY_OF_MONTH, -1);
        cal.set(Calendar.HOUR_OF_DAY, 24);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取过去第n天的开始
     *
     * @param past day of past
     * @return millis
     */
    public static Long getPastDateBegin(int past) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.DAY_OF_YEAR, cal.get(Calendar.DAY_OF_YEAR) - past);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取过去第n天的结束
     *
     * @param past day of past
     * @return millis
     */
    public static Long getPastDateEnd(int past) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.DAY_OF_YEAR, cal.get(Calendar.DAY_OF_YEAR) - past);
        //将小时至23
        cal.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        cal.set(Calendar.MINUTE, 59);
        //将秒至59
        cal.set(Calendar.SECOND, 59);
        //将毫秒至999
        cal.set(Calendar.MILLISECOND, 999);
        return cal.getTimeInMillis();
    }
}
