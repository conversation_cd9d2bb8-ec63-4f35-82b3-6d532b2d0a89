package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_reward_image", comment = "t_reward_image", isSimple = true)
@TableName("t_reward_image")
public class RewardImage extends EntitySupport {

    @NonNull
    private Long rewardId;

    @NonNull
    private String imageUrl;

}