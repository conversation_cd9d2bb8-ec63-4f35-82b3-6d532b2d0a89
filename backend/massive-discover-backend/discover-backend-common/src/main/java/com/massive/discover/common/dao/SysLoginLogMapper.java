package com.massive.discover.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.massive.discover.common.entity.SysLoginLog;
import com.massive.discover.common.model.LoginLogQueryParams;
import com.massive.discover.common.model.LoginLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 登录日志
 */
@Mapper
public interface SysLoginLogMapper extends BaseMapper<SysLoginLog> {

    /**
     * 分页查询
     *
     * @param page      query
     * @param queryForm query params
     * @return LoginLogVO
     */
    List<LoginLogVO> queryByPage(Page<?> page, @Param("query") LoginLogQueryParams queryForm);

    /**
     * 查询上一个登录记录
     *
     * @param userId   user id
     * @param userType user type
     * @return LoginLogVO
     */
    LoginLogVO queryLastByUserId(@Param("userId") Long userId, @Param("userType") Integer userType);
}
