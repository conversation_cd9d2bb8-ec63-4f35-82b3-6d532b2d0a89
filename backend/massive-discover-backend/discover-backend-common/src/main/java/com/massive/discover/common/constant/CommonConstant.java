package com.massive.discover.common.constant;

public interface CommonConstant {

    String LEFT_PARENTHESIS = "(";

    String RIGHT_PARENTHESIS = ")";

    String BLANK = "";

    String COLON_CONSTANT = ":";

    String HALF_WIDTH_SPACE = " ";

    String FULL_WIDTH_SPACE = "　";

    String MINUS = "-";

    String TILDE_SYMBOL = "~";

    String HHMM_FORMAT = "HHmm";

    String WITH_COLON_CONSTANT_HHMM_FORMAT = "HH:mm";

    String WITH_MINUS_YYYYMMDD_FORMAT = "yyyy-MM-dd";

    String WITH_KANJI_YYYYMMDD_FORMAT = "yyyy年MM月dd日";

    String WITH_KANJI_MMDD_FORMAT = "MM月dd日";

    String YYYYMMDD_FORMAT ="yyyyMMdd";

    String WITH_MINUS_YYYYMMDD_HHMM_FORMAT ="yyyy-MM-dd HH:mm";

    String TIME_ZONE = "Asia/Tokyo";

    String SQL_LIMIT = "LIMIT";

    String SQL_OFFSET = "OFFSET";

    String SQL_LIMIT_ONE = "LIMIT 1";

    String SQL_LIMIT_TEN = "LIMIT 10";

    String SQL_SEARCH_KEY_ID = "ID";

    int ONE_DAY_HOURS = 24;

    String BUSINESS_START_TEXT = "営業開始";

    String BEYOND_OPERATING_HOURS_TEXT = "営業時間外";

    String BUSINESS_END_TIME_TEXT = "営業終了";

    String BUSINESS_REOPEN = "再開";

    String VALID = "有効";

    String TODAY = "今日";
    String TOMORROW = "明日";

}
