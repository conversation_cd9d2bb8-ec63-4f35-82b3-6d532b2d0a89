package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/9 01:56
 */

@Getter
@AllArgsConstructor
public enum UploadUsageScenarioEnum {

    /**
     * user profile
     */
    USER_PROFILE(1),

    /**
     * user profile background
     */
    USER_PROFILE_BACKGROUND(2),

    /**
     * video
     */
    VIDEO(3);

    private final int type;

    private static final Map<Integer, UploadUsageScenarioEnum> MAP;

    static {
        MAP = Arrays.stream(UploadUsageScenarioEnum.values()).collect(Collectors.toMap(UploadUsageScenarioEnum::getType, state -> state));
    }

    public static UploadUsageScenarioEnum parse(Integer type) {
        return MAP.get(type);
    }
}
