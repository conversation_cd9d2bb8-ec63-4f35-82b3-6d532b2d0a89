package com.massive.discover.common.model;

import com.massive.discover.common.enums.ResponseStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/14 17:52
 */

@Getter
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class Result<T> {

    /**
     * 响应状态
     */
    private Integer code;

    /**
     * 响应编码
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    public Result(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Result(ResponseStatusEnum responseStatus) {
        this(responseStatus.getStatus(), responseStatus.getDescription(), null);
    }

    public Result(ResponseStatusEnum responseStatus, T data) {
        this(responseStatus.getStatus(), responseStatus.getDescription(), data);
    }

    public Result(ResponseStatusEnum responseStatus, String msg, T data) {
        this.code = responseStatus.getStatus();
        this.msg = msg;
        this.data = data;
    }

    /**
     * @return 默认成功响应
     */
    public static Result<Void> success() {
        return new Result<>(ResponseStatusEnum.SUCCESS);
    }

    /**
     * 自定义信息的成功响应
     * <p>通常用作插入成功等并显示具体操作通知如: return BasicResultVO.success("发送信息成功")</p>
     *
     * @param msg 信息
     * @return 自定义信息的成功响应
     */
    public static <T> Result<T> success(String msg) {
        return new Result<>(ResponseStatusEnum.SUCCESS, msg, null);
    }

    /**
     * 带数据的成功响应
     *
     * @param data 数据
     * @return 带数据的成功响应
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(ResponseStatusEnum.SUCCESS, data);
    }

    /**
     * @return 默认失败响应
     */
    public static <T> Result<T> fail() {
        return new Result<>(ResponseStatusEnum.BAD_REQUEST, ResponseStatusEnum.BAD_REQUEST.getDescription(), null);
    }

    /**
     * 自定义错误信息的失败响应
     *
     * @param msg 错误信息
     * @return 自定义错误信息的失败响应
     */
    public static <T> Result<T> fail(String msg) {
        return fail(ResponseStatusEnum.BAD_REQUEST, msg);
    }

    /**
     * 自定义状态的失败响应
     *
     * @param responseStatus 状态
     * @return 自定义状态的失败响应
     */
    public static <T> Result<T> fail(ResponseStatusEnum responseStatus) {
        return fail(responseStatus, responseStatus.getDescription());
    }

    /**
     * 自定义状态和信息的失败响应
     *
     * @param msg 信息
     * @return 自定义状态和信息的失败响应
     */
    public static <T> Result<T> fail(ResponseStatusEnum responseStatus, String msg) {
        return new Result<>(responseStatus, msg, null);
    }

    public boolean isSuccess(){
        return ResponseStatusEnum.SUCCESS.getStatus().equals(this.code);
    }
}
