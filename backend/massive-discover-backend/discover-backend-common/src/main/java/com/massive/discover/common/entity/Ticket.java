package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.LocalDateTime;

/**
 * 展会的分区，如：1F、2F
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_ticket", comment = "t_ticket", isSimple = true)
@TableName("t_ticket")
public class Ticket extends EntitySupport {

    @Unique(value = "eventId_code", columns = {"event_id", "code"})
    @NonNull
    private Long eventId;

    private String name;

    @NonNull
    private String code;

    private LocalDateTime boundAt;

    private LocalDateTime checkedAt;
}
