package com.massive.discover.common.config.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 租户配置属性(可以写在yml配置文件里,也可以在这儿写死)
 * @date 2022/08/30 13:57
 **/
@Getter
@Setter
@ConfigurationProperties(prefix = "tenant")
public class TenantProperties {
    /**
     * 是否开启租户模式
     */
    private Boolean enable = true;

    /**
     * 需要排除的多租户的表(根据自己需要修改)
     */
    private List<String> ignoreTables = Arrays.asList(
            "t_user",
            "t_sys_user",
            "t_user_login",
            "t_event",
            "t_game",
            "t_game_status",
            "t_mission",
            "t_play_games",
            "t_mark",
            "t_reward",
            "t_event_map",
            "t_reward_image",
            "t_area",
            "t_exhibitor",
            "t_hotel",
            "t_other",
            "t_restaurant",
            "t_shop",
            "t_show",
            "t_bulletin",
            "t_ticket",
            "t_send_verify_code_record"
    );
    /**
     * 动态表名的表(根据自己需要修改)
     */
    private List<String> dynamicTables = Arrays.asList("sys_login_log");

    /**
     * 多租户字段名称(根据实际项目修改)
     */
    private String column = "user_id";

    /**
     * 排除不进行租户隔离的sql
     */
    private List<String> ignoreSqls = new ArrayList<>();

}

