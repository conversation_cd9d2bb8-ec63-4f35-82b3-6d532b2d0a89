package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.*;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_event_map", comment = "t_event_map", isSimple = true)
@TableName("t_event_map")
public class EventMap extends EntitySupport {

    private String name;

    private String description;

    private Long eventId;

    private String mapUrl;

    private Integer mapWidth;

    private Integer mapHeight;

}