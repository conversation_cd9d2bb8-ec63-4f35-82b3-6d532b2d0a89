package com.massive.discover.common.model;

import com.google.common.collect.Lists;
import com.massive.discover.common.entity.SysRole;
import com.massive.discover.common.entity.SysUser;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/26 08:42
 */

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SysUserVo extends SysUser {

    /** 用户所有角色值，在管理后台显示用户的角色 */
    private List<SysRole> roleList = Lists.newArrayList();
}
