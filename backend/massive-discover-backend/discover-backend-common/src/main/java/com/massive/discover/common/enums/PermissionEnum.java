package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/7/19
 */

@Getter
@AllArgsConstructor
public enum PermissionEnum {

    /**
     * 任意
     */
    ANY(0),

    /**
     * 相互关注
     */
    MUTUAL_FOLLOW(1),

    /**
     * 只自己可见
     */
    PRIVATE(-1);

    private final Integer type;

    private static final Map<Integer, PermissionEnum> MAP;

    static {
        MAP = Arrays.stream(PermissionEnum.values()).collect(Collectors.toMap(PermissionEnum::getType, type -> type));
    }

    public static PermissionEnum parse(Integer type) {
        return MAP.get(type);
    }
}
