package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.LocalDateTime;

/**
 * <AUTHOR> wang
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_user_reward", comment = "t_user_reward", isSimple = true)
@TableName("t_user_reward")
public class UserReward extends EntitySupport {
    @NonNull
    private Long rewardId;

    private LocalDateTime exchangedAt;
}