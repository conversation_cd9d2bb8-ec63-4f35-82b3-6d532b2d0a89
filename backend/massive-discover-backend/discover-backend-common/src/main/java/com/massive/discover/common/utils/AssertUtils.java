package com.massive.discover.common.utils;

import cn.hutool.core.lang.Assert;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.model.Result;

import java.util.Collection;

/**
 *
 *
 * <AUTHOR>
 * @date 2024/4/17 15:44
 * @version V1.0
 * modifyTime           author              description
 * -------------------------------------------------------------------
 */
public class AssertUtils extends Assert {

	public static void throwException(Result<?> r) {
		if (r.getCode() != 0) {
			throw new SystemException(r.getMsg());
		}
	}

	public static void isTrue(boolean expression, String errorMsgTemplate, Object... params) {
		try {
			Assert.isTrue(expression, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static void isTrue(boolean expression) {
		try {
			Assert.isTrue(expression);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static void isFalse(boolean expression, String errorMsgTemplate, Object... params) {
		try {
			Assert.isFalse(expression, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static void isFalse(boolean expression) {
		try {
			Assert.isFalse(expression);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static void isNull(Object object, String errorMsgTemplate, Object... params) {
		try {
			Assert.isNull(object, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static void isNull(Object object) {
		try {
			Assert.isNull(object);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static <T> T notNull(T object, String errorMsgTemplate, Object... params) {
		try {
			return Assert.notNull(object, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static <T> T notNull(T object) {
		try {
			return Assert.notNull(object);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static String notEmpty(String text, String errorMsgTemplate, Object... params) {
		try {
			return Assert.notEmpty(text, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static String notEmpty(String text) {
		try {
			return Assert.notEmpty(text);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static String notBlank(String text, String errorMsgTemplate, Object... params) {
		try {
			return Assert.notBlank(text, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static String notBlank(String text) {
		try {
			return Assert.notBlank(text);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static String notContain(String textToSearch, String substring, String errorMsgTemplate, Object... params) {
		try {
			return Assert.notContain(textToSearch, substring, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static String notContain(String textToSearch, String substring) {
		try {
			return Assert.notContain(textToSearch, substring);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static Object[] notEmpty(Object[] array, String errorMsgTemplate, Object... params) {
		try {
			return Assert.notEmpty(array, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static Object[] notEmpty(Object[] array) {
		try {
			return Assert.notEmpty(array);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static <T> T[] noNullElements(T[] array, String errorMsgTemplate, Object... params) {
		try {
			return Assert.noNullElements(array, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static <T> T[] noNullElements(T[] array) {
		try {
			return Assert.noNullElements(array);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static <T> Collection<T> notEmpty(Collection<T> collection, String errorMsgTemplate, Object... params) {
		try {
			return Assert.notEmpty(collection, errorMsgTemplate, params);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}

	public static <T> Collection<T> notEmpty(Collection<T> collection) {
		try {
			return Assert.notEmpty(collection);
		} catch (IllegalArgumentException e){
			throw new SystemException(e.getMessage());
		}
	}
}
