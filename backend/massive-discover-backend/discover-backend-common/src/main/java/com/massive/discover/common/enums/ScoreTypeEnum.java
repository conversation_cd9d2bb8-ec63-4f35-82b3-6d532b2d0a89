package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统用户类型
 */
@Getter
@AllArgsConstructor
public enum ScoreTypeEnum {

    /**
     * 加分
     */
    ADD(1, "加分"),

    /**
     * 减分
     */
    SUBTRACT(2, "减分");;

    private final Integer type;

    @Getter
    private final String description;

    private static final Map<Integer, ScoreTypeEnum> MAP;

    static {
        MAP = Arrays.stream(ScoreTypeEnum.values()).collect(Collectors.toMap(ScoreTypeEnum::getType, sysUserType -> sysUserType));
    }

    public static ScoreTypeEnum parse(Integer type) {
        return MAP.get(type);
    }
}
