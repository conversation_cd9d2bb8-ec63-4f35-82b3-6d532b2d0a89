package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_send_info", comment = "手机发送信息", isSimple = true)
@TableName("t_send_info")
public class SendInfo {

    private Integer count;

    private Double fee;

    private String sid;
}
