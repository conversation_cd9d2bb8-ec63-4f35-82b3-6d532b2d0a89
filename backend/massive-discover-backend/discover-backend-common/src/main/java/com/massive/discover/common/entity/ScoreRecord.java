package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_score_record", comment = "积分记录表", isSimple = true)
@TableName("t_score_record")
public class ScoreRecord extends EntitySupport {

    /**
     * 积分(1000, -1000)
     */
    private String score;

    /**
     * （mark（1：游戏，2：展会 3：演出），4：兑换ID）
     */
    private Long sourceId;

    /**
     * 来源ID（mark-（1：游戏，2：展会 3：演出（+）），4：兑换积分（-））
     * {@link com.massive.discover.common.enums.ScoreRecordSourceEnum}
     */
    private Integer scoreSource;

    /**
     * 加分或者扣分（1：加分，2：扣分）
     * {@link com.massive.discover.common.enums.ScoreTypeEnum}
     */
    private Integer addOrSubtract;

    /**
     * 来源名称
     */
    private String sourceName;
}