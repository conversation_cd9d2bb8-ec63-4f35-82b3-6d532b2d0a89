package com.massive.discover.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScoreRecordVO {

    /**
     * 积分(1000, -1000)
     */
    private String score;

    /**
     * 来源名称
     */
    private String sourceName;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 获取积分或者扣减积分文案
     */
    private String reasons;
}
