package com.massive.discover.common.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.massive.discover.common.entity.SysRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysRoleMapper extends BaseMapper<SysRole> {

    List<SysRole> getRolesByUserId(@Param("userId") Integer userId);

    List<Integer> getRoleIdsByUserId(@Param("userId") Integer userId);

    Boolean checkRidsContainRval(@Param("rids") List<Integer> rids, @Param("rval") String rval);

    Boolean checkUidContainRval(@Param("uid") Long uid, @Param("rval") String rval);

}
