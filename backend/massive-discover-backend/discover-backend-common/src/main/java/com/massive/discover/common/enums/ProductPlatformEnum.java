package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 支付平台
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ProductPlatformEnum {

    /**
     * google 和安卓支付
     */
    GOOGLE("google"),
    /**
     * 苹果支付
     */
    APPLE("apple"),
    /**
     * web 支付，Stripe
     */
    WEB("web");

    private final String platform;


    private static final Map<String, ProductPlatformEnum> MAP;

    static {
        MAP = Arrays.stream(ProductPlatformEnum.values()).collect(Collectors.toMap(ProductPlatformEnum::getPlatform, productPlatformEnum -> productPlatformEnum));
    }

    public static ProductPlatformEnum parsePlatform(String platform) {
        return MAP.get(platform);
    }

}