package com.massive.discover.common.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_send_verify_code_record", comment = "手机发送记录", isSimple = true)
@TableName("t_send_verify_code_record")
public class SendVerifyCodeRecord extends EntitySupport {

    private String phoneNum;

    /**
     * 发送内容
     */
    private String sendInfo;

    /**
     * 发送状态：1：成功；2：失败
     */
    private Integer sendType;


    /**
     * 如果发送失败，记录发送失败原因
     */
    private String errorInfo;
}
