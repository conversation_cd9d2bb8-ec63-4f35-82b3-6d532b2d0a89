package com.massive.discover.common.utils;

import com.massive.discover.common.config.properties.YunPianApiConfig;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.model.SendSmsResultDTO;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.http.HttpEntity;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

public class YunpianSmsUtil {
    //通用发送接口的https地址
    private static String URI_SEND_SMS = YunPianApiConfig.getSendSmsApi();

    // 模板发送接口的https地址
    private static String URI_TPL_SEND_SMS = YunPianApiConfig.getTplSendSmsApi();

    //编码格式。发送编码格式统一用UTF-8
    private static String ENCODING = YunPianApiConfig.getEncoding();

    private static String API_KEY = YunPianApiConfig.getApiKey();

    //thread safe
    private static final CloseableHttpClient httpClient = HttpClients.createDefault();

    /**
     * 通用接口发短信
     *
     * @param text   　短信内容
     * @param mobile 　接受的手机号
     * @return json格式字符串
     * @throws SystemException
     */
    public static SendSmsResultDTO sendSms(String text, String mobile) throws SystemException {
        Map<String, String> params = new HashMap<String, String>();
        params.put("apikey", API_KEY);
        params.put("text", text);
        params.put("mobile", mobile);
        try {
            String post = post(URI_SEND_SMS, params);
            return JsonUtil.fromJson(post, SendSmsResultDTO.class);
        } catch (Exception e) {
            throw new SystemException("Invoke Api Failed" + e.getMessage());
        }
    }

    /**
     * 通过模板发送短信(不推荐)
     *
     * @param tpl_id    　模板id
     * @param tpl_value 　模板变量值
     * @param mobile    　接受的手机号
     * @return json格式字符串
     * @throws SystemException
     */
    public static SendSmsResultDTO tplSendSms(long tpl_id, Map<String, String> tpl_value,
                                           String mobile) throws SystemException {
        Map<String, String> params = new HashMap<String, String>();
        params.put("apikey", API_KEY);
        params.put("tpl_id", String.valueOf(tpl_id));
        StringBuilder sb = new StringBuilder();
        params.put("mobile", mobile);
        try {
            for (Map.Entry<String, String> m : tpl_value.entrySet()) {
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(URLEncoder.encode(m.getKey(), ENCODING));
                sb.append("=");
                sb.append(URLEncoder.encode(m.getValue(), ENCODING));

            }
            params.put("tpl_value", sb.toString());
            String post = post(URI_TPL_SEND_SMS, params);
            return JsonUtil.fromJson(post, SendSmsResultDTO.class);
        } catch (Exception e) {
            throw new SystemException("Invoke Api Failed" + e.getMessage());
        }
    }

    /**
     * 基于HttpClient 4.3的通用POST方法
     *
     * @param url       提交的URL
     * @param paramsMap 提交<参数，值>Map
     * @return 提交响应
     */
    public static String post(String url, Map<String, String> paramsMap) throws Exception {
        //reuse httpclient to keepalive to the server
        //keepalive in https will save time on tcp handshaking.
        CloseableHttpClient client = httpClient;
        String responseText = "";
        HttpPost method = new HttpPost(url);
        CloseableHttpResponse response = null;
        try {
            if (paramsMap != null) {
                List<NameValuePair> paramList = new ArrayList<NameValuePair>();
                for (Map.Entry<String, String> param : paramsMap.entrySet()) {
                    NameValuePair pair = new BasicNameValuePair(param.getKey(), param.getValue());
                    paramList.add(pair);
                }
                method.setEntity(new UrlEncodedFormEntity(paramList, ENCODING));
            }
            response = client.execute(method);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                responseText = EntityUtils.toString(entity);
            }
        } finally {
            //must close the response or will lead to next request hang.
            if (response != null) {
                response.close();
            }
        }
        return responseText;
    }


    public static void main(String[] args) {

        String text = "your verification code is  " +
                String.format("%06d", ThreadLocalRandom.current().nextInt(1000000))
                + "if not your own operation, please ignore this message.";

        SendSmsResultDTO sendSmsResultDTO = sendSms(text, "8107083930729");
        System.out.println("x");
    }
}
