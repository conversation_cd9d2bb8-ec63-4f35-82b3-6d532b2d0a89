package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "sys_role", comment = "权限角色", isSimple = true)
@TableName("sys_role")
public class SysRole extends EntitySupport {

    /**
     * 角色名，用于显示
     */
    private String rname;

    /**
     * 角色描述
     */
    private String rdesc;

    /**
     * 角色值，用于权限判断
     */
    @Unique(columns = {"rval"})
    private String rval;
}
