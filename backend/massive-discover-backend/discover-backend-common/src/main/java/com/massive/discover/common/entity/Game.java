package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_game", comment = "t_game", isSimple = true)
@TableName("t_game")
public class Game extends EntitySupport {

    //展会ID
    @NonNull
    private Long eventId;

    //游戏名称
    @NonNull
    private String name;

    //游戏说明
    private String description;

    //游戏可用ID - 引用t_game_available表
    private Long gameAvailableId;

    //游戏难度等级（1:Easy, 2:Medium, 3:Hard）
    private Integer gameLevel;

    @Unique
    private Long markId;

    private Long areaId;
}