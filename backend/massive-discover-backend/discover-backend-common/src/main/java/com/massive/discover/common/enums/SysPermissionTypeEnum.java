package com.massive.discover.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 权限类型枚举
 */
@Getter
@AllArgsConstructor
public enum SysPermissionTypeEnum {

    /**
     * 菜单
     */
    MENU(1, "菜单"),

    /**
     * api
     */
    API(2, "接口"),

    /**
     * button
     */
    BUTTON(3, "按钮"),

    /**
     * button
     */
    DATA(4, "数据");

    private final Integer type;

    @Getter
    private final String description;

    private static final Map<Integer, SysPermissionTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SysPermissionTypeEnum.values()).collect(Collectors.toMap(SysPermissionTypeEnum::getType, type -> type));
    }

    public static SysPermissionTypeEnum parse(Integer type) {
        return MAP.get(type);
    }
}
