package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.gitee.sunchenbin.mybatis.actable.constants.MySqlTypeConstant;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_user_login", comment = "登录信息", isSimple = true)
@TableName("t_user_login")
public class UserLogin extends EntitySupport {

    private Long userId;

    private String nickname;

    private Integer isThirdParty;

    private Integer thirdPartyType;

    @Unique
    private String thirdPartyId;

    private String email;

    private String phone;
}