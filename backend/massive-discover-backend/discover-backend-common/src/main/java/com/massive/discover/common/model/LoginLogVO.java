package com.massive.discover.common.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * 登录日志
 */
@Data
public class LoginLogVO {

    private Long loginLogId;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户类型")
    private Integer userType;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("登录ip")
    private String loginIp;

    @ApiModelProperty("user-agent")
    private String userAgent;

    @ApiModelProperty("remark")
    private String remark;

    private Integer loginResult;

    private Timestamp createAt;
}
