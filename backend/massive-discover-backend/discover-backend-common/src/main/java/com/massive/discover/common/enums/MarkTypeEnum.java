package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum MarkTypeEnum {

    /**
     * 游戏
     */
    GAME(1),

    /**
     * 展会
     */
    BOOTH(2),

    /**
     * 演出
     */
    STAGE(3),

    /**
     * 餐厅
     */
    RESTAURANT(4),

    /**
     * 商店
     */
    SHOP(5),

    /**
     * 酒店
     */
    HOTEL(6),

    /**
     * 其他
     */
    OTHER(7);

    private final Integer status;

    private static final Map<Integer, MarkTypeEnum> MAP;

    static {
        MAP = Arrays.stream(MarkTypeEnum.values()).collect(Collectors.toMap(MarkTypeEnum::getStatus, status -> status));
    }

    public static MarkTypeEnum parse(Integer status) {
        return MAP.get(status);
    }
}
