package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统用户类型
 */
@Getter
@AllArgsConstructor
public enum SysUserTypeEnum {

    /**
     * 管理端 员工用户
     */
    ADMIN_EMPLOYEE(1, "员工"),

    /**
     * 管理端 机构用户
     */
    ADMIN_MCN(2, "机构");;

    private final Integer type;

    @Getter
    private final String description;

    private static final Map<Integer, SysUserTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SysUserTypeEnum.values()).collect(Collectors.toMap(SysUserTypeEnum::getType, sysUserType -> sysUserType));
    }

    public static SysUserTypeEnum parse(Integer type) {
        return MAP.get(type);
    }
}
