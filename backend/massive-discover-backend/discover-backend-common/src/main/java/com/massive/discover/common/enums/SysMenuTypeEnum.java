package com.massive.discover.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 菜单类型枚举
 *
 */
@Getter
@AllArgsConstructor
public enum SysMenuTypeEnum {
    /**
     * 目录
     */
    CATALOG(1, "目录"),
    /**
     * 菜单
     */
    MENU(2, "菜单");

    private final Integer type;

    @Getter
    private final String description;

    private static final Map<Integer, SysMenuTypeEnum> MAP;

    static {
        MAP = Arrays.stream(SysMenuTypeEnum.values()).collect(Collectors.toMap(SysMenuTypeEnum::getType, type -> type));
    }

    public static SysMenuTypeEnum parse(Integer type) {
        return MAP.get(type);
    }
}
