package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.gitee.sunchenbin.mybatis.actable.annotation.Unique;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_restaurant", comment = "t_restaurant", isSimple = true)
@TableName("t_restaurant")
public class Restaurant extends EntitySupport {

    @NonNull
    @Unique
    private Long markId;

    @NonNull
    private String name;

    private String thumbnailUrl;

    private String description;

    private String tags;

    private String websiteUrl;

}
