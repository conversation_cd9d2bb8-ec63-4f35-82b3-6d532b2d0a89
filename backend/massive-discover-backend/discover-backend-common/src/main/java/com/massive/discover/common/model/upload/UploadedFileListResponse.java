package com.massive.discover.common.model.upload;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * uploaded file list response
 */
@ApiModel(description = "uploaded file list response")
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class UploadedFileListResponse {
    private List<FileItem> uploadedFileList;

    private Integer page;

    private Integer size;

    private Long total;
}

