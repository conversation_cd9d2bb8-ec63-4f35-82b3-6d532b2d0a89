package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 积分记录来源
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ScoreRecordSourceEnum {

    /**
     * 游戏
     */
    GAME(1, "游戏"),

    /**
     * 展会
     */
    BOOTH(2, "展会"),

    /**
     * 演出
     */
    STAGE(3, "演出"),

    /**
     * 积分兑换
     */
    EXCHANGE(4, "兑换"),


    /**
     * 初始化积分
     */
    INIT(5, "初始化");
    private final Integer type;

    @Getter
    private final String description;

    private static final Map<Integer, ScoreRecordSourceEnum> MAP;

    static {
        MAP = Arrays.stream(ScoreRecordSourceEnum.values()).collect(Collectors.toMap(ScoreRecordSourceEnum::getType, sysUserType -> sysUserType));
    }

    public static ScoreRecordSourceEnum parse(Integer type) {
        return MAP.get(type);
    }
}
