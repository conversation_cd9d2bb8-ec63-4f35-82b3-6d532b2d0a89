package com.massive.discover.common.service;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.massive.discover.common.entity.UploadFile;
import com.massive.discover.common.enums.FileStatusEnum;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.model.upload.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.transaction.annotation.Transactional;

import java.time.ZoneOffset;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/9 02:34
 */

public class FileManager {

    private final String staticHost;

    private final String bucketName;

    private final UploadFileService uploadFileService;

    private final UploadService uploadService;

    public FileManager(String staticHost, String bucketName, UploadFileService uploadFileService, UploadService uploadService) {
        this.staticHost = staticHost;
        this.bucketName = bucketName;
        this.uploadFileService = uploadFileService;
        this.uploadService = uploadService;
    }

    @Transactional(rollbackFor = Exception.class)
    public PreSignUploadFileResponse generatePreSignedUrl(Long userId, PreSignUploadFileRequest preSignRequest) {
        String fileKey = NanoIdUtils.randomNanoId(NanoIdUtils.DEFAULT_NUMBER_GENERATOR, NanoIdUtils.DEFAULT_ALPHABET, 10) + "." + preSignRequest.getContentType();
        String preSignedUrl = uploadService.preSignedFileUrl(bucketName, fileKey, preSignRequest.getContentType());
        UploadFile uploadFile = UploadFile.builder().fileName(preSignRequest.getFileName()).fileSize(preSignRequest.getFileSize()).fileKey(fileKey).mediaType(preSignRequest.getMediaType()).signedUrl(preSignedUrl).uploadState(FileStatusEnum.INITIAL.getState()).build();
        uploadFileService.save(uploadFile);

        return PreSignUploadFileResponse.builder().fileKey(fileKey).fileName(uploadFile.getFileName()).fileSize(uploadFile.getFileSize()).contentType(uploadFile.getContentType()).mediaType(uploadFile.getMediaType()).preSignedUrl(uploadFile.getSignedUrl()).build();
    }

    @Transactional(rollbackFor = Exception.class)
    public FinishSignedFileResponse changeFileStatus(Long userId, String fileKey, FileStatusEnum fileStatus) {
        boolean rowAffect = uploadFileService.update(new UpdateWrapper<UploadFile>().eq("file_key", fileKey).eq("user_id", userId).set("file_key", fileStatus.getState()));
        if (rowAffect) {
            UploadFile uploadFile = uploadFileService.getOne(new QueryWrapper<UploadFile>().lambda().eq(UploadFile::getFileKey, fileKey));
            String url = String.format("%s/%s.%s", staticHost, uploadFile.getFileKey(), uploadFile.getContentType());
            return FinishSignedFileResponse.builder().fileKey(uploadFile.getFileKey()).fileName(uploadFile.getFileName()).contentType(uploadFile.getContentType()).mediaType(uploadFile.getMediaType()).preSignedUrl(uploadFile.getSignedUrl()).url(url).fileStatus(uploadFile.getUploadState()).createTime(uploadFile.getCreateAt().toInstant(ZoneOffset.of("+8")).toEpochMilli()).build();
        }
        throw new SystemException("upload file confirm fail");
    }

    public UploadedFileListResponse getFileItems(Long userId, UploadedFileListRequest uploadedFileListRequest) {
        UploadedFileListResponse response = new UploadedFileListResponse();
        response.setPage(uploadedFileListRequest.getPage());
        response.setSize(uploadedFileListRequest.getSize());

        QueryWrapper<UploadFile> query = new QueryWrapper<>();
        query.lambda().eq(UploadFile::getUserId, userId);
        if (uploadedFileListRequest.getStatus() != null) {
            query.lambda().eq(UploadFile::getUploadState, uploadedFileListRequest.getStatus());
        }
        if (uploadedFileListRequest.getMediaType() != null) {
            query.lambda().eq(UploadFile::getMediaType, uploadedFileListRequest.getMediaType());
        }
        query.orderByDesc("update_at");
        IPage<UploadFile> filePage = new Page<>(uploadedFileListRequest.getPage(), uploadedFileListRequest.getSize());
        filePage = uploadFileService.page(filePage, query);

        if (CollectionUtils.isNotEmpty(filePage.getRecords())) {
            response.setUploadedFileList(filePage.getRecords().stream().map(fileItem -> FileItem.builder().id(fileItem.getId()).fileName(fileItem.getFileName()).fileSize(fileItem.getFileSize()).contentType(fileItem.getContentType()).fileKey(fileItem.getFileKey()).mediaType(fileItem.getMediaType()).url(String.format("%s/%s.%s", staticHost, fileItem.getFileKey(), fileItem.getContentType())).signedUrl(fileItem.getSignedUrl()).timestamp(fileItem.getCreateAt().toInstant(ZoneOffset.of("+8")).toEpochMilli()).build()).collect(Collectors.toList()));
            response.setTotal(filePage.getTotal());
        } else {
            response.setUploadedFileList(Lists.newArrayList());
            response.setTotal(0L);
        }
        return response;
    }
}
