package com.massive.discover.common.service;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/06/07 14:14
 */

public interface UploadService {

    /**
     * @param bucketName bucket
     * @param keyName    fileKey
     * @return signedUrl
     */
    String preSignedFileUrl(String bucketName, String keyName);

    /**
     * @param bucketName  bucket
     * @param keyName     fileKey
     * @param contentType content type
     * @return signedUrl
     */
    String preSignedFileUrl(String bucketName, String keyName, String contentType);
}
