package com.massive.discover.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum FileStatusEnum {

    /**
     * 初始
     */
    INITIAL(0),


    /**
     * 已上传
     */
    FINISHED_UPLOAD(1),


    /**
     * 失败
     */
    FAIL(98),

    /**
     * 超时
     */
    TIMEOUT(99);

    private final int state;

    private static final Map<Integer, FileStatusEnum> MAP;

    static {
        MAP = Arrays.stream(FileStatusEnum.values()).collect(Collectors.toMap(FileStatusEnum::getState, state -> state));
    }

    public static FileStatusEnum parse(Integer state) {
        return MAP.get(state);
    }
}
