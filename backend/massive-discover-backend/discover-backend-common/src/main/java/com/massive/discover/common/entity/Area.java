package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * 展会的分区，如：1F、2F
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_area", comment = "t_area", isSimple = true)
@TableName("t_area")
public class Area extends EntitySupport {

    @NonNull
    private Long eventId;

    private String name;

    private Integer width;

    private Integer height;

    private Integer xAxis;

    private Integer yAxis;

}
