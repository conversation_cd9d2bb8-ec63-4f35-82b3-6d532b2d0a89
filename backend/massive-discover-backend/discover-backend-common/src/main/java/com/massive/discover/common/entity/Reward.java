package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Column;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_reward", comment = "t_reward", isSimple = true)
@TableName("t_reward")
public class Reward extends EntitySupport {

    @NonNull
    private Long eventId;

    @NonNull
    private String name;

    private String description;

    private String detailDescription;

    private String imageUrl;

    @NonNull
    private Integer pointPrice;

    @NonNull
    @Column(defaultValue = "0")
    private Integer inventory;

    @NonNull
    @Column(defaultValue = "0")
    private Integer bought;
}