package com.massive.discover.common.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.gitee.sunchenbin.mybatis.actable.annotation.Table;
import com.massive.discover.common.entity.base.EntitySupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "t_game_available", comment = "t_game_available", isSimple = true)
@TableName("t_game_available")
public class GameAvailable extends EntitySupport {

    //游戏名称
    @NonNull
    private String name;

    //游戏说明
    private String description;

    //游戏基础链接
    @NonNull
    private String gameUrl;

}
