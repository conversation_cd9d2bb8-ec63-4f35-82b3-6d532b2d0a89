package com.massive.discover.common.model.upload;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * UploadedFileListRequest
 */
@Data
public class UploadedFileListRequest {

    private Integer page = 1;

    private Integer size = 10;

    @ApiModelProperty(notes = "file type : 1 video, 2 image, 3 other")
    private Integer mediaType;

    @ApiModelProperty(notes = "file status : 0 init, 1 finished, 98 failed, 99 timeout")
    private Integer status;
}

