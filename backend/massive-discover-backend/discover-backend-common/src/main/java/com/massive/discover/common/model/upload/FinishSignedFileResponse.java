package com.massive.discover.common.model.upload;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * UploadSignedFileResponse
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FinishSignedFileResponse {

    private String fileKey;

    private String fileName;

    private String contentType;

    @ApiModelProperty(notes = "file type : 1 video, 2 image, 3 other")
    private Integer mediaType;

    private String preSignedUrl;

    private String url;

    private Integer fileStatus;

    private Long createTime;
}

