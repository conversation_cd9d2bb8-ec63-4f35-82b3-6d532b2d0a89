<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.massive.discover.common.dao.SysPermMapper">

    <select id="getPermsByUserId" resultType="com.massive.discover.common.entity.SysPerm">
        SELECT p.*
        FROM sys_perm p,
             sys_role_perm rp,
             sys_user_role ur
        WHERE ur.role_id = rp.role_id
          AND ur.user_id = #{userId}
        ORDER BY p.rank, p.create_at DESC
    </select>

    <select id="getPermsByRoleId" resultType="com.massive.discover.common.entity.SysPerm">
        SELECT p.*
        FROM sys_perm p,
             sys_role_perm rp
        WHERE p.id = rp.perm_id
          AND rp.role_id = #{roleId}
    </select>

    <insert id="saveOrUpdate">
        INSERT INTO sys_perm (pval,parent_id,pname,`rank`,ptype,leaf,icon) VALUES
        <foreach collection="perms" item="p" separator=",">
            (#{p.pval},#{p.parent_id},#{p.pname},#{rank},#{p.ptype},#{p.leaf},#{p.icon}
        </foreach>
        ON DUPLICATE KEY UPDATE
        parent_id = VALUES(parent_id), pname = VALUES(pname), `rank` = VALUES(`rank`), ptype = VALUES(ptype), leaf = VALUES(leaf),icon = VALUES(icon)
    </insert>

</mapper>
