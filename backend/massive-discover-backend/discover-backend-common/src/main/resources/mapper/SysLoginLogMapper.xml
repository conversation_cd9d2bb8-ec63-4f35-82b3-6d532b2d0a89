<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.massive.discover.common.dao.SysLoginLogMapper">

    <select id="queryByPage" resultType="com.massive.discover.common.model.LoginLogVO">
        select
        *
        from sys_login_log
        <where>
            <if test="query.startDate != null and query.startDate != ''">
                AND DATE_FORMAT(create_at, '%Y-%m-%d') &gt;= #{query.startDate}
            </if>
            <if test="query.endDate != null and query.endDate != ''">
                AND DATE_FORMAT(create_at, '%Y-%m-%d') &lt;= #{query.endDate}
            </if>
            <if test="query.userName != null and query.userName != ''">
                AND INSTR(user_name,#{query.userName})
            </if>
            <if test="query.ip != null">
                AND INSTR(login_ip,#{query.ip})
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="queryLastByUserId" resultType="com.massive.discover.common.model.LoginLogVO">
        select *
        from sys_login_log
        where user_id = #{userId}
          and user_type = #{userType}
        order by create_at desc
        limit 1
    </select>

</mapper>