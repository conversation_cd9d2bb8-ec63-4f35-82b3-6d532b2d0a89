<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.massive.discover.common.dao.UserScoreMapper">

    <!-- Get top N rankings with user details -->
    <select id="getTopNRankings" resultType="com.massive.discover.common.model.RankResultDTO">
        SELECT
            ranked_users.`rank`,
            ranked_users.ranking_score as score,
            u.nickname as nickName
        FROM (
            SELECT
                us.user_id,
                us.ranking_score,
                ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as `rank`
            FROM t_user_score us
            WHERE us.ranking_score > 0
            ORDER BY us.ranking_score DESC
            LIMIT #{limit}
        ) ranked_users
        JOIN t_user u ON ranked_users.user_id = u.id
        ORDER BY ranked_users.`rank`
    </select>

    <!-- Get specific user's rank and score -->
    <select id="getUserRank" resultType="com.massive.discover.common.model.RankDTO">
        SELECT
            ranked_users.`rank`,
            ranked_users.ranking_score as score,
            ranked_users.user_id as userId
        FROM (
            SELECT
                us.user_id,
                us.ranking_score,
                ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as `rank`
            FROM t_user_score us
            WHERE us.ranking_score > 0
        ) ranked_users
        WHERE ranked_users.user_id = #{userId}
    </select>

    <!-- Get rankings around a specific user -->
    <select id="getRankingsAroundUser" resultType="com.massive.discover.common.model.RankDTO">
        WITH user_rank AS (
            SELECT
                us.user_id,
                us.ranking_score,
                ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as `rank`
            FROM t_user_score us
            WHERE us.ranking_score > 0
        ),
        target_user AS (
            SELECT `rank` FROM user_rank WHERE user_id = #{userId}
        )
        SELECT
            ur.`rank`,
            ur.ranking_score as score,
            ur.user_id as userId
        FROM user_rank ur, target_user tu
        WHERE ur.`rank` BETWEEN (tu.`rank` - #{offset}) AND (tu.`rank` + #{offset})
        AND ur.`rank` > 0
        ORDER BY ur.`rank`
    </select>

</mapper>