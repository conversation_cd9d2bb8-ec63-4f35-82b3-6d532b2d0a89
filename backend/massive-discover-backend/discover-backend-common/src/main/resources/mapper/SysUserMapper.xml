<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.massive.discover.common.dao.SysUserMapper">

    <resultMap id="userMap" type="com.massive.discover.common.entity.SysUser">
        <id property="id" column="id"/>
        <result property="uname" column="uname"/>
        <result property="nick" column="nick"/>
        <result property="pwd" column="pwd"/>
        <result property="salt" column="salt"/>
        <result property="tel" column="tel"/>
        <result property="isLock" column="is_lock"/>
        <result property="avatar" column="avatar"/>
        <result property="createAt" column="create_at" jdbcType="TIMESTAMP"/>
        <result property="updateAt" column="update_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 一对多：role -->
    <resultMap id="userIncludeRolesMap" extends="userMap" type="com.massive.discover.common.model.SysUserVo">
        <collection property="roleList" columnPrefix="role_" resultMap="com.massive.discover.common.dao.SysRoleMapper.roleMap">
        </collection>
    </resultMap>

    <select id="selectUserIncludeRoles" resultMap="userIncludeRolesMap">
        select
        u.*,
        r.id as role_rid,
        r.rname as role_rname,
        r.rdesc as role_rdesc,
        r.rval as role_rval,
        r.create_at as role_created,
        r.update_at as role_updated
        from sys_user u
        left join sys_user_role ur on u.id = ur.user_id
        left join sys_role r on ur.role_id = r.id
        <where>
            <if test="uname != null and uname != ''">
                u.`uname` like concat('%',#{uname},'%') and
            </if>

            <if test="tel != null and tel != ''">
                u.`tel` = #{tel} and
            </if>

            <if test="startTime != null or endTime != null">
                <![CDATA[
                    AND u.create_at >= CONCAT(#{startTime},' 00:00:00')

                    AND u.create_at <= CONCAT(#{endTime},' 23:59:59')
                ]]>
                AND
            </if>
            1 = 1
        </where>
        order by u.create_at desc
    </select>

    <select id="queryUser" resultType="com.massive.discover.common.entity.SysUser">
        select
        u.*
        from sys_user u
        <where>
            <if test="uname != null and uname != ''">
                u.`uname` like concat('%',#{uname},'%') and
            </if>

            <if test="uname != null and uname != ''">
                u.`uname` like concat('%',#{uname},'%') and
            </if>

            <if test="tel != null and tel != ''">
                u.`tel` = #{tel} and
            </if>
            1 = 1
        </where>

        order by u.create_at desc
    </select>

</mapper>
