<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.massive.discover.common.dao.SysRoleMapper">

    <resultMap id="roleMap" type="com.massive.discover.common.entity.SysRole">
        <id property="id" column="id"/>
        <result property="rname" column="rname"/>
        <result property="rdesc" column="rdesc"/>
        <result property="rval" column="rval"/>
        <result property="createAt" column="create_at" jdbcType="TIMESTAMP"/>
        <result property="updateAt" column="update_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="getRolesByUserId" resultType="com.massive.discover.common.entity.SysRole">
        SELECT r.rname, r.rval
        FROM sys_role r,
             sys_user_role ur
        WHERE r.id = ur.role_id
          AND ur.user_id = #{userId}
    </select>

    <select id="getRoleIdsByUserId" resultType="java.lang.Integer">
        SELECT r.id
        FROM sys_role r,
             sys_user_role ur
        WHERE r.id = ur.role_id
          AND ur.user_id = #{userId}
    </select>

    <select id="checkRidsContainRval" resultType="java.lang.Boolean">
        SELECT
        CONCAT(',',GROUP_CONCAT(rval SEPARATOR ','),',')
        REGEXP CONCAT(',',#{rval},',{1}') AS result FROM sys_role
        WHERE id IN
        <foreach collection="rids" item="rid" separator="," open="(" close=")">
            #{rid}
        </foreach>
    </select>

    <select id="checkUidContainRval" resultType="java.lang.Boolean">
        SELECT CONCAT(',', GROUP_CONCAT(r.rval SEPARATOR ','), ',')
                   REGEXP CONCAT(',', #{rval}, ',{1}') AS result
        FROM sys_role r,
             sys_user_role ur
        WHERE ur.role_id = r.id
          AND ur.user_id = #{uid}
    </select>

</mapper>
