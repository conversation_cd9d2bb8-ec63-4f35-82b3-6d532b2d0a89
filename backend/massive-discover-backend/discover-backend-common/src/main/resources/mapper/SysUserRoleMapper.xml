<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.massive.discover.common.dao.SysUserRoleMapper">

    <select id="queryUserRoles" resultType="com.massive.discover.common.entity.SysRole">
        SELECT sysRole.*
        FROM sys_user_role userRole
                 LEFT JOIN sys_role sysRole ON userRole.role_id = sysRole.id
        WHERE userRole.user_id = #{userId}
    </select>
</mapper>
