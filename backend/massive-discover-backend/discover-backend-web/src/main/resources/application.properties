springfox.documentation.swagger.v2.path=/api-docs
server.port=8082
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.date-format=com.massive.discover.common.format.RFC3339DateFormat



#datasource - Using staging database for local development
spring.datasource.url=${JDBC_URL:****************************************************************************************************************************}
spring.datasource.username=${MYSQL_USER:ope_backend}
spring.datasource.password=${MYSQL_PASSWORD:ope_backend}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.redis.cluster.nodes=${REDIS_CLUSTER_NODES:localhost:6379}

# actable - disabled to prevent unwanted table changes
actable.table.auto=none
actable.model.pack=com.massive.discover.common.entity
actable.database.type=mysql
actable.index.prefix=idx_
actable.unique.prefix=idx_uni_
mybatis-plus.mapper-locations[0]=classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*/*.xml

# mybatis config
mybatis-plus.mapper-locations[1]=classpath*:mapper/**/*Mapper.xml
mybatis-plus.type-aliases-package=com.massive.discover.common.entity
mybatis-plus.global-config.db-config.table-underline=true
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

#firebase
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

#line oauth
idp.line.clientId=${LINE_CLIENT_ID:}
idp.line.clientSecret=${LINE_CLIENT_SECRET:}
idp.line.callbackUrl=${LINE_CALLBACK_URL:}

#aws
aws.client.region=ap-northeast-1
aws.static.host=${STATIC_IMAGE_HOST:https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com}
aws.s3.signed.expiration.minutes=30
aws.s3.bucket.name=${BUCKET_NAME:open-portal-expo-prod}
aws.run.env=${AWS_ENV:dev}
#aws.sns.accessKeyId=********************
#aws.sns.secretKey=C+INAlVXZMg6I95Ty+SP71Ho19Q5CEU7F/16EzTQ

# AWS SNS for SMS (replaces deprecated Pinpoint)
# TODO: Replace with your actual AWS credentials
aws.sns.accessKeyId=${AWS_SNS_ACCESS_KEY_ID:********************}
aws.sns.secretAccessKey=${AWS_SNS_SECRET_ACCESS_KEY:F+olw+Vf9Miiijl1S25+zms40Ve/hFMEFe3+VoSx}
aws.sns.region=${AWS_SNS_REGION:ap-northeast-1}
aws.sns.template=${AWS_SNS_TEMPLATE:Your verification code is: %s. Valid for 15 minutes.}
aws.sns.senderId=${AWS_SNS_SENDER_ID:}
aws.sns.smsType=${AWS_SNS_SMS_TYPE:Transactional}
aws.sns.maxPrice=${AWS_SNS_MAX_PRICE:0.50}

#Yun Pian
APIKEY=3a4d664601a023768c936a874ee6a19e
send.sms.api=https://us.yunpian.com/v1/sms/send.json
send.tpl.api=https://us.yunpian.com/v1/sms/tpl_send.json
api.encoding=UTF-8

# Login WhiteList
phone.login.whiteList=+8617600229099;+8615958145623