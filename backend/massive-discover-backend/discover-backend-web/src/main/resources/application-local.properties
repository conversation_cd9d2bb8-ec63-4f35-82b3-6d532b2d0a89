# Local development configuration for Discover Service
springfox.documentation.swagger.v2.path=/api-docs
server.port=8082
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.date-format=com.massive.discover.common.format.RFC3339DateFormat



# Staging database for local development - respects environment variables
spring.datasource.url=${JDBC_URL:*********************************************************************************************************************************************************************}
spring.datasource.username=${MYSQL_USER:ope_backend}
spring.datasource.password=${MYSQL_PASSWORD:ope_backend}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Local Redis - respects environment variables
spring.redis.cluster.nodes=${REDIS_CLUSTER_NODES:localhost:6379}
spring.redis.database=0

# MyBatis Plus configuration
mybatis-plus.mapper-locations[0]=classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*/*.xml
mybatis-plus.mapper-locations[1]=classpath*:mapper/**/*Mapper.xml
mybatis-plus.type-aliases-package=com.massive.discover.common.entity
mybatis-plus.global-config.db-config.table-underline=true
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Enable actable for automatic table creation
actable.table.auto=update
actable.model.pack=com.massive.discover.common.entity
actable.database.type=mysql
actable.index.prefix=idx_
actable.unique.prefix=idx_uni_

# AWS - respects environment variables
aws.client.region=ap-northeast-1
aws.static.host=${STATIC_IMAGE_HOST:http://localhost:8082}
aws.s3.bucket.name=${BUCKET_NAME:test-bucket}
aws.run.env=${AWS_ENV:local}

# Logging
logging.level.com.massive.discover=DEBUG
logging.level.org.springframework.web=DEBUG

# Phone whitelist for local development (mock SMS)
phone.login.whiteList=1234567890;+1234567890;+8617600229099;+8615958145623

# AWS SNS Configuration (for local development - replace with real credentials for production)
aws.sns.accessKeyId=********************
aws.sns.secretAccessKey=F+olw+Vf9Miiijl1S25+zms40Ve/hFMEFe3+VoSx
aws.sns.region=ap-northeast-1
aws.sns.template=Your verification code is: %s. Valid for 5 minutes.
aws.sns.senderId=
aws.sns.smsType=Transactional
aws.sns.maxPrice=0.50

# AWS Pinpoint removed - deprecated service, using SNS instead
