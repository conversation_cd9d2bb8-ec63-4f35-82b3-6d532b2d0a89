# Staging environment configuration for Discover Service
springfox.documentation.swagger.v2.path=/api-docs
server.port=8082
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.date-format=com.massive.discover.common.format.RFC3339DateFormat



# Database configuration - uses environment variables
spring.datasource.url=${JDBC_URL}
spring.datasource.username=${MYSQL_USER}
spring.datasource.password=${MYSQL_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Redis configuration
spring.redis.cluster.nodes=${REDIS_CLUSTER_NODES:localhost:6379}

# Disable actable in staging to prevent unwanted table changes
actable.table.auto=none
actable.model.pack=com.massive.discover.common.entity
actable.database.type=mysql
actable.index.prefix=idx_
actable.unique.prefix=idx_uni_
mybatis-plus.mapper-locations[0]=classpath*:com/gitee/sunchenbin/mybatis/actable/mapping/*/*.xml

# MyBatis configuration
mybatis-plus.mapper-locations[1]=classpath*:mapper/**/*Mapper.xml
mybatis-plus.type-aliases-package=com.massive.discover.common.entity
mybatis-plus.global-config.db-config.table-underline=true
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Firebase configuration
firebase.config.path=${FIREBASE_CONFIG_PATH:}

# LINE OAuth configuration
idp.line.clientId=${LINE_CLIENT_ID:}
idp.line.clientSecret=${LINE_CLIENT_SECRET:}
idp.line.callbackUrl=${LINE_CALLBACK_URL:}

# AWS configuration
aws.client.region=ap-northeast-1
aws.static.host=${STATIC_IMAGE_HOST:https://staging-bucket.s3.ap-northeast-1.amazonaws.com}
aws.s3.signed.expiration.minutes=30
aws.s3.bucket.name=${BUCKET_NAME:staging-bucket}
aws.run.env=${AWS_ENV:staging}

# JWT configuration
jwt.secret=${JWT_SECRET}
jwt.expired.days=${JWT_EXPIRED_DAYS:30}

# Logging configuration for staging
logging.level.com.massive.discover=INFO
logging.level.org.springframework.web=WARN
logging.level.org.springframework.security=WARN

# Phone whitelist for staging (can include test numbers)
phone.login.whiteList=${PHONE_WHITELIST:+1234567890;+8617600229099}
