{"properties": [{"name": "idp.firebase.credential", "type": "java.lang.String", "description": "Description for idp.firebase.credential."}, {"name": "springfox.documentation.swagger.v2.path", "type": "java.lang.String", "description": "Description for springfox.documentation.swagger.v2.path."}, {"name": "actable.database.type", "type": "java.lang.String", "description": "Description for actable.database.type."}, {"name": "actable.index.prefix", "type": "java.lang.String", "description": "Description for actable.index.prefix."}, {"name": "actable.model.pack", "type": "java.lang.String", "description": "Description for actable.model.pack."}, {"name": "actable.unique.prefix", "type": "java.lang.String", "description": "Description for actable.unique.prefix."}, {"name": "aws.static.host", "type": "java.lang.String", "description": "Description for aws.static.host."}, {"name": "aws.s3.bucket.name", "type": "java.lang.String", "description": "Description for aws.s3.bucket.name."}]}