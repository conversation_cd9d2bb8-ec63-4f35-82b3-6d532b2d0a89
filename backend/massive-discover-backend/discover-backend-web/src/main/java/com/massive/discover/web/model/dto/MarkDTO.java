package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Area;
import com.massive.discover.common.entity.Mark;
import com.massive.discover.common.entity.MarkStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MarkDTO {

    @NonNull
    private Long eventId;

    @NonNull
    private Long id;

    private Long areaId;

    private String name;

    private String areaName;

    private String contentName;

    @NonNull
    private Integer markType;

    @NonNull
    private Integer xAxis;

    @NonNull
    private Integer yAxis;

    private boolean showPoint;

    private boolean collected;

    private String boothNumber;

    public static MarkDTO fromEntity(@NonNull Mark mark, @NonNull MarkStatus markStatus, @NonNull Area area) {
        return MarkDTO.builder()
                .id(mark.getId())
                .name(mark.getName())
                .eventId(mark.getEventId())
                .xAxis(mark.getXAxis())
                .yAxis(mark.getYAxis())
                .markType(mark.getMarkType())
                .areaId(mark.getAreaId())
                .areaName(area.getName())
                .showPoint(mark.getMarkScore() > 0 && !markStatus.isGotScore())
                .collected(markStatus.isCollected())
                .boothNumber(mark.getBoothNumber())
                .build();
    }
}
