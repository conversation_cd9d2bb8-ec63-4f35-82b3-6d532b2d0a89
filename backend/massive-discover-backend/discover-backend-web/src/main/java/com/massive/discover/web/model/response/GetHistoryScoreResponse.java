package com.massive.discover.web.model.response;


import com.massive.discover.common.entity.ScoreRecord;
import com.massive.discover.user.model.HistoryScoreDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GetHistoryScoreResponse {

    List<HistoryScoreDTO> historyScores;
}
