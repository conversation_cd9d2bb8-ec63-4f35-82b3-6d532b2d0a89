package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.HotelMapper;
import com.massive.discover.common.entity.Hotel;
import com.massive.discover.web.model.dto.HotelDTO;
import com.massive.discover.web.service.IHotelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class HotelServiceImpl extends ServiceImpl<HotelMapper, Hotel> implements IHotelService {

    @Override
    public Hotel findByMarkId(Long markId) {
        return this.getOne(new QueryWrapper<Hotel>().eq("mark_id", markId));
    }

    public HotelDTO findByMarkIdAsDTO(Long markId) {
        Hotel hotel = this.getOne(new QueryWrapper<Hotel>().eq("mark_id", markId));
        if (hotel == null) {
            return null;
        }
        return HotelDTO.fromEntity(hotel);
    }
}
