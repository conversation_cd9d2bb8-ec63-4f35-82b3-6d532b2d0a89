package com.massive.discover.web.model.response;

import com.massive.discover.user.model.RewardDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public final class GetMyRewardsResponse implements Serializable {
    List<RewardDTO> myRewards;
}
