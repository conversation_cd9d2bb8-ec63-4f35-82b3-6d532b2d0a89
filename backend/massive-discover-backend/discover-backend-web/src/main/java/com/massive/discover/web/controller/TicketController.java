package com.massive.discover.web.controller;

import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.entity.Ticket;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.web.model.request.BindTicketRequest;
import com.massive.discover.web.service.ITicketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;

@RestController
@Api(tags = "ticket")
@RequestMapping("/ticket")
public class TicketController {

    @Resource
    private ITicketService ticketService;

    @GetMapping("/v1/ticket")
    @ApiOperation(value = "根据userId获取ticket信息", notes = "ticket", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Ticket> getUserTikcet(
            @RequestParam @Validated @Min(1L) Long eventId,
            @LoginUser UserCacheInfo loginUserInfo
    ) {
        return Result.success(ticketService.getUserTicket(eventId, loginUserInfo.getId()));
    }

    @GetMapping("/v1/ticket/refresh")
    @ApiOperation(value = "根据userId获取ticket信息", notes = "ticket", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> refreshTikcet(
            @RequestParam @Validated @Min(1L) Long eventId,
            @RequestParam String code
    ) {
        return Result.success(ticketService.refreshTicket(eventId, code));
    }

    @PostMapping("/v1/ticket")
    @ApiOperation(value = "user绑定ticket", notes = "ticket", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> bindTicket(@LoginUser UserCacheInfo loginUserInfo,
                                      @RequestBody @Validated BindTicketRequest request) {
        boolean flag;
        try {
            flag = ticketService.bindTicket(request.getEventId(), request.getCode(), loginUserInfo.getId());
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
        return Result.success(flag);
    }

    @PostMapping("/v1/ticket/check")
    @ApiOperation(value = "user check ticket", notes = "ticket", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> checkTicket(@LoginUser UserCacheInfo loginUserInfo,
                                       @RequestBody @Validated BindTicketRequest request) {
        boolean flag;
        try {
            flag = ticketService.checkTicket(request.getEventId(), request.getCode(), loginUserInfo.getId());
        } catch (Exception e) {
            return Result.fail(e.getMessage());
        }
        return Result.success(flag);
    }
}
