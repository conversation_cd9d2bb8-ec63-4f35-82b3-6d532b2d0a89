package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.GameAvailableMapper;
import com.massive.discover.common.entity.GameAvailable;
import com.massive.discover.web.service.IGameAvailableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GameAvailableServiceImpl extends ServiceImpl<GameAvailableMapper, GameAvailable> implements IGameAvailableService {

    @Override
    public GameAvailable getGameAvailableById(Long gameAvailableId) {
        return this.getById(gameAvailableId);
    }

}
