package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.ShopMapper;
import com.massive.discover.common.entity.Shop;
import com.massive.discover.web.model.dto.ShopDTO;
import com.massive.discover.web.service.IShopService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class ShopServiceImpl extends ServiceImpl<ShopMapper, Shop> implements IShopService {

    @Override
    public Shop findByMarkId(Long markId) {
        return this.getOne(new QueryWrapper<Shop>().eq("mark_id", markId));
    }

    public ShopDTO findByMarkIdAsDTO(Long markId) {
        Shop shop = this.getOne(new QueryWrapper<Shop>().eq("mark_id", markId));
        if (shop == null) {
            return null;
        }
        return ShopDTO.fromEntity(shop);
    }
}
