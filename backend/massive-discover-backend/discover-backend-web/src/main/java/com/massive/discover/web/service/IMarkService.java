package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.entity.Mark;
import com.massive.discover.web.model.dto.MarkDTO;
import com.massive.discover.web.model.request.ChangeMarkCollectRequest;
import com.massive.discover.web.model.response.GetMarksResponse;
import com.massive.discover.web.model.response.MarkDetailResponse;


public interface IMarkService extends IService<Mark> {

    GetMarksResponse getMarks(Long eventId, Long userId);

    MarkDetailResponse getMarkDetail(Long markId, Long userId);

    <PERSON> getMarkById(Long markId);

    Boolean changeCollected(ChangeMarkCollectRequest request, Long userId);

    Boolean addMarkScore(Long markId, Long userId);

    PageInfo<MarkDTO> collectedMark(int pageNum, int pageSize, long userId);
}

