package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Shop;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopDTO {

    @NonNull
    private Long id;

    private String name;

    private String thumbnailUrl;

    private String description;

    private List<String> tags;

    private String websiteUrl;

    public static ShopDTO fromEntity(@NonNull Shop shop) {
        return ShopDTO.builder()
                .id(shop.getId())
                .name(shop.getName())
                .thumbnailUrl(shop.getThumbnailUrl())
                .description(shop.getDescription())
                // Convert comma-separated tags string to List<String>
                .tags(shop.getTags() != null && !shop.getTags().trim().isEmpty() ?
                    Arrays.asList(shop.getTags().split(",")) :
                    Collections.emptyList())
                .websiteUrl(shop.getWebsiteUrl())
                .build();
    }
}
