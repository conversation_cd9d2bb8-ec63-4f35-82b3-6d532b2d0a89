package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Restaurant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RestaurantDTO {

    @NonNull
    private Long id;

    private String name;

    private String thumbnailUrl;

    private String description;

    private List<String> tags;

    private String websiteUrl;

    public static RestaurantDTO fromEntity(@NonNull Restaurant restaurant) {
        return RestaurantDTO.builder()
                .id(restaurant.getId())
                .name(restaurant.getName())
                .thumbnailUrl(restaurant.getThumbnailUrl())
                .description(restaurant.getDescription())
                // Convert comma-separated tags string to List<String>
                .tags(restaurant.getTags() != null && !restaurant.getTags().trim().isEmpty() ?
                    Arrays.asList(restaurant.getTags().split(",")) :
                    Collections.emptyList())
                .websiteUrl(restaurant.getWebsiteUrl())
                .build();
    }
}
