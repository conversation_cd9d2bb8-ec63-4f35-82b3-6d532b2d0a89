package com.massive.discover.web.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MarkDetailResponse {

    @NonNull
    private Long id;

    @NonNull
    private String name;

    @NonNull
    private Long eventId;

    private Long areaId;

    private String areaName;

    //游戏或者展会（1:游戏，2:展会, 3:舞台）
    @NonNull
    private Integer markType;

    private boolean showPoint;

    private boolean collected;

    private Integer markScore;

    private String boothNumber;

    private Object content;
}
