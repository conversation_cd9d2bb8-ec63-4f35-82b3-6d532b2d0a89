package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Other;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class OtherDTO {

    @NonNull
    private Long id;

    private String name;

    private String thumbnailUrl;

    private String description;

    private List<String> tags;

    private String websiteUrl;

    public static OtherDTO fromEntity(@NonNull Other other) {
        return OtherDTO.builder()
                .id(other.getId())
                .name(other.getName())
                .thumbnailUrl(other.getThumbnailUrl())
                .description(other.getDescription())
                // Convert comma-separated tags string to List<String>
                .tags(other.getTags() != null && !other.getTags().trim().isEmpty() ?
                    Arrays.asList(other.getTags().split(",")) :
                    Collections.emptyList())
                .websiteUrl(other.getWebsiteUrl())
                .build();
    }
}
