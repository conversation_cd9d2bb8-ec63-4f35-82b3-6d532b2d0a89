package com.massive.discover.web.config;

import com.massive.discover.common.constant.AwsConstant;
import com.massive.discover.common.service.FileManager;
import com.massive.discover.common.service.UploadFileService;
import com.massive.discover.common.service.UploadService;
import com.massive.discover.common.service.impl.UploadServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.dynamodb.DynamoDbClient;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class AwsConfig {

    @Value("${aws.run.env}")
    private String awsEnv;

    @Value("${aws.client.region}")
    private String awsRegion;

    @Value("${aws.s3.signed.expiration.minutes:10}")
    private Long preSignedExpiration;

    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${aws.static.host}")
    private String staticHost;

    private AwsCredentialsProvider credentialsProvider() {
        if (AwsConstant.AWS_LOCAL_ENV.equals(awsEnv)) {
            return ProfileCredentialsProvider.create();
        }
        return DefaultCredentialsProvider.create();
    }

    @Bean
    public S3Presigner s3Presigner() {
        try {
            return S3Presigner.builder().region(Region.of(awsRegion)).credentialsProvider(credentialsProvider()).build();
        } catch (Exception ex) {
            log.error("init S3 preSigner error", ex);
            return S3Presigner.builder().region(Region.of(awsRegion)).build();
        }
    }

    @Bean
    public DynamoDbClient dynamoDbClient() {
        try {
            return DynamoDbClient.builder().region(Region.of(awsRegion)).credentialsProvider(credentialsProvider()).build();
        } catch (Exception ex) {
            log.error("init dynamoDbClient error", ex);
            return DynamoDbClient.builder().region(Region.of(awsRegion)).build();
        }
    }

    @Bean
    public UploadService uploadService(S3Presigner s3Presigner) {
        return new UploadServiceImpl(preSignedExpiration, s3Presigner);
    }

    @Bean
    public FileManager fileManager(
            UploadFileService uploadFileService,
            UploadService uploadService) {
        return new FileManager(
                staticHost,
                bucketName,
                uploadFileService, uploadService);
    }
}
