package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.OtherMapper;
import com.massive.discover.common.entity.Other;
import com.massive.discover.web.model.dto.OtherDTO;
import com.massive.discover.web.service.IOtherService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class OtherServiceImpl extends ServiceImpl<OtherMapper, Other> implements IOtherService {

    @Override
    public Other findByMarkId(Long markId) {
        return this.getOne(new QueryWrapper<Other>().eq("mark_id", markId));
    }

    public OtherDTO findByMarkIdAsDTO(Long markId) {
        Other other = this.getOne(new QueryWrapper<Other>().eq("mark_id", markId));
        if (other == null) {
            return null;
        }
        return OtherDTO.fromEntity(other);
    }
}
