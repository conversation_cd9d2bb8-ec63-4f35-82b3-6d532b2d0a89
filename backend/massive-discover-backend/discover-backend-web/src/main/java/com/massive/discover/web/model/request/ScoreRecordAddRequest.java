package com.massive.discover.web.model.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ScoreRecordAddRequest {

    @ApiModelProperty(name = "积分", value = "score", required = true)
    private Integer score;

    @ApiModelProperty(name = "来源", value = "source", required = true)
    private Integer source;

    @ApiModelProperty(name = "锚点ID或兑换ID", value = "sourceId")
    private Long sourceId;

    @ApiModelProperty(name = "来源名称", value = "sourceName")
    private String sourceName;
}
