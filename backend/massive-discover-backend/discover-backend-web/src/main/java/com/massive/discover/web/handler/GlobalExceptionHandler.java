package com.massive.discover.web.handler;

import com.massive.discover.common.enums.ResponseStatusEnum;
import com.massive.discover.common.exception.AuthException;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.http.HttpServletRequest;

/**
 * 统一捕捉异常，返回给前台一个json信息，前台根据这个信息显示对应的提示，或者做页面的跳转。
 *
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(value = AuthException.class)
    @ResponseBody
    public Result<?> handleAuthenticationException(HttpServletRequest req, AuthException authException) {
        log.error("GlobalExceptionHandler.handleAuthenticationException:{}", authException.getMessage());
        return Result.fail(ResponseStatusEnum.UNAUTHORIZED, authException.getMessage());
    }

    @ExceptionHandler(value = SystemException.class)
    @ResponseBody
    public Result<?> handleSystemException(HttpServletRequest req, SystemException systemException) {
        log.error("GlobalExceptionHandler.handleAuthenticationException:{}", systemException.getMessage());
        return Result.fail(ResponseStatusEnum.BAD_REQUEST, systemException.getMessage());
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public Result<?> handleException(HttpServletRequest req, Exception ex) {
        log.error("GlobalExceptionHandler.handleException", ex);
        return Result.fail(ResponseStatusEnum.INTERNAL_ERROR, ex.getMessage());
    }
}