package com.massive.discover.web.controller;

import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.model.HistoryScoreDTO;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.user.service.IUserScoreService;
import com.massive.discover.web.model.request.ScoreRecordAddRequest;
import com.massive.discover.web.model.response.GetHistoryScoreResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "UserScoreController", tags = "用户积分接口")
@RequestMapping("/userScore")
public class UserScoreController {

    @Resource
    private IUserScoreService userScoreService;

    @PostMapping("/v1/subtractScoreByUserId")
    @ApiOperation(value = "扣减积分", notes = "userScore",
            authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> subtractScoreByUserId(@LoginUser UserCacheInfo loginUserInfo, @RequestBody ScoreRecordAddRequest scoreRecordAddRequest) {
        return Result.success(userScoreService.subtractScoreByUserId(loginUserInfo.getId(), scoreRecordAddRequest.getScore(),
                scoreRecordAddRequest.getSource(), scoreRecordAddRequest.getSourceId()));
    }

    @GetMapping("/v1/currentScore")
    @ApiOperation(value = "获取用户当前可用积分", notes = "userScore",
            authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Integer> getCurrentScore(@LoginUser UserCacheInfo loginUserInfo) {
        return Result.success(userScoreService.getCurrentScore(loginUserInfo.getId()));
    }

    @PostMapping("/v1/addScoreByUserId")
    @ApiOperation(value = "增加积分", notes = "userScore",
            authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> addScoreByUserId(@LoginUser UserCacheInfo loginUserInfo, @RequestBody ScoreRecordAddRequest scoreRecordAddRequest) {
        return Result.success(userScoreService.addScoreByUserId(loginUserInfo.getId(), scoreRecordAddRequest.getScore(),
                scoreRecordAddRequest.getSource(), scoreRecordAddRequest.getSourceId(), scoreRecordAddRequest.getSourceName()));
    }

    @GetMapping("/v1/getUserHistoryScore")
    @ApiOperation(value = "获取用户历史积分记录", notes = "userScore",
            authorizations = {@Authorization(value = "bearerAuth")})
    public Result<GetHistoryScoreResponse> getUserHistoryScore(@LoginUser UserCacheInfo loginUserInfo) {
        return Result.success(new GetHistoryScoreResponse().builder().
                historyScores(userScoreService.getUserHistoryScore(loginUserInfo.getId())).build());
    }

}
