package com.massive.discover.web.controller;

import com.github.pagehelper.PageInfo;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.web.model.dto.MarkDTO;
import com.massive.discover.web.model.request.ChangeMarkCollectRequest;
import com.massive.discover.web.model.request.MarkScoreAddRequest;
import com.massive.discover.web.model.response.GetMarksResponse;
import com.massive.discover.web.model.response.MarkDetailResponse;
import com.massive.discover.web.service.IMarkService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "mark")
@RequestMapping(value = "/mark", produces = "application/json")
public class MarkController {

    @Resource
    private IMarkService markService;

    @GetMapping("/v1/marks")
    @ApiOperation(value = "根据eventId获取Mark信息", notes = "mark", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<GetMarksResponse> getMarks(
            @LoginUser UserCacheInfo loginUserInfo,
            @RequestParam @Validated @Min(1L) Long eventId
    ) {
        return Result.success(markService.getMarks(eventId, loginUserInfo.getId()));
    }

    @GetMapping("/v1/mark")
    @ApiOperation(value = "根据markId获取Mark信息", notes = "mark", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<MarkDetailResponse> getMarkDetail(@LoginUser UserCacheInfo loginUserInfo,
                                                    @RequestParam @Validated @Min(1L) Long markId) {

        MarkDetailResponse markDetailResponse;
        try {
            markDetailResponse = markService.getMarkDetail(markId, loginUserInfo.getId());
        }catch (SystemException systemException){
            return Result.fail(systemException.getMessage());
        }
        return Result.success(markDetailResponse);
    }

    @PutMapping("/v1/mark/collect")
    @ApiOperation(value = "根据markId修改收藏状态", notes = "mark", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> changeMarkCollect(
            @LoginUser UserCacheInfo loginUserInfo,
            @RequestBody @Validated ChangeMarkCollectRequest request
    ) {
        return Result.success(markService.changeCollected(request, loginUserInfo.getId()));
    }

    @GetMapping("/v1/mark/collect")
    @ApiOperation(value = "获取收藏Mark", notes = "mark", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<PageInfo<MarkDTO>> collectedMark(int pageNum, int pageSize, @LoginUser UserCacheInfo loginUserInfo) {
        return Result.success(markService.collectedMark(pageNum, pageSize, loginUserInfo.getId()));
    }

    @PutMapping("/v1/addMarkScore")
    @ApiOperation(value = "根据markId增加积分", notes = "mark", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> addMarkScore(
            @LoginUser UserCacheInfo loginUserInfo,
            @RequestBody MarkScoreAddRequest markScoreAddRequest
            ) {
        try {
            markService.addMarkScore(markScoreAddRequest.getMarkId(), loginUserInfo.getId());
        }catch (SystemException systemException){
            return Result.fail(systemException.getMessage());
        }
        return Result.success(true);
    }
}
