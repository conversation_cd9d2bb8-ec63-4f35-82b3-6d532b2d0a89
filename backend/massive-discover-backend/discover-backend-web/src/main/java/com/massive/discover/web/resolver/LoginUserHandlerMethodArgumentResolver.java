package com.massive.discover.web.resolver;

import com.massive.discover.web.utils.JwtUtil;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.exception.AuthException;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.user.service.UserService;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023-06-01 22:21
 */
@Component
public class LoginUserHandlerMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Resource
    private UserService userService;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.getParameterType().isAssignableFrom(UserCacheInfo.class)
                && parameter.hasParameterAnnotation(LoginUser.class);
    }

    @Override
    public Object resolveArgument(@NonNull MethodParameter parameter,
                                  ModelAndViewContainer container,
                                  @NonNull NativeWebRequest request,
                                  WebDataBinderFactory factory) {
        // 获取token
        String jwtToken = JwtUtil.parseJwtFromRequest(Objects.requireNonNull(request.getNativeRequest(HttpServletRequest.class)));
        if (StringUtils.isEmpty(jwtToken)) {
            throw new AuthException("idToken null");
        }
        return userService.getUserInfoFromCache(jwtToken);
    }
}
