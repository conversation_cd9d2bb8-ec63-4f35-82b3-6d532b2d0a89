package com.massive.discover.web.model.response;

import com.massive.discover.web.model.dto.AreaDTO;
import com.massive.discover.web.model.dto.MarkDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public final class GetMarksResponse implements Serializable {

    List<AreaDTO> areas;

    List<MarkDTO> marks;

}
