package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.RewardImageMapper;
import com.massive.discover.common.entity.RewardImage;
import com.massive.discover.web.service.IRewardImageService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
@Transactional
public class RewardImageServiceImpl extends ServiceImpl<RewardImageMapper, RewardImage> implements IRewardImageService {

    @Override
    public Map<Long, List<String>> getRewardImageMap(@NonNull Collection<Long> rewardIds) {
        return list(new QueryWrapper<RewardImage>().lambda().in(RewardImage::getRewardId, rewardIds))
                .stream()
                .collect(Collectors.groupingBy(
                        RewardImage::getRewardId,
                        Collectors.mapping(RewardImage::getImageUrl, Collectors.toList())
                ));
    }
}
