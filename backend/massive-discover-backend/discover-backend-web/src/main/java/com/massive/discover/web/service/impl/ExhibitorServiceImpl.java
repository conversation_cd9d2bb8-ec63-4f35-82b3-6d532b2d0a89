package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.dao.AreaMapper;
import com.massive.discover.common.dao.ExhibitorMapper;
import com.massive.discover.common.dao.MarkMapper;
import com.massive.discover.common.entity.Area;
import com.massive.discover.common.entity.Exhibitor;
import com.massive.discover.common.entity.Mark;
import com.massive.discover.common.entity.MarkStatus;
import com.massive.discover.web.model.dto.ExhibitorDTO;
import com.massive.discover.web.model.dto.MarkDTO;
import com.massive.discover.web.service.IExhibitorService;
import com.massive.discover.web.service.IMarkStatusService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class ExhibitorServiceImpl extends ServiceImpl<ExhibitorMapper, Exhibitor> implements IExhibitorService {

    @Resource
    private MarkMapper markMapper;

    @Resource
    private IMarkStatusService markStatusService;

    @Resource
    private AreaMapper areaMapper;

    @Override
    public Exhibitor findByMarkId(Long markId) {
        return this.getOne(new QueryWrapper<Exhibitor>().eq("mark_id", markId));
    }

    public ExhibitorDTO findByMarkIdAsDTO(Long markId, Long userId) {
        val exhibitor = this.getOne(new QueryWrapper<Exhibitor>().eq("mark_id", markId));
        if (exhibitor == null) {
            return null;
        }

        val mark = markMapper.selectById(markId);
        val markStatus = markStatusService.getStatus(markId, userId);
        val area = areaMapper.selectById(mark.getAreaId());

        return ExhibitorDTO.fromEntity(exhibitor, mark, markStatus, area);
    }

    @Override
    public PageInfo<MarkDTO> search(int pageNum, int pageSize, String word, long userId) {
        val wrapper = new QueryWrapper<Exhibitor>().lambda();
        if (word != null && !word.trim().isEmpty()) {
            wrapper.and(x -> x.like(Exhibitor::getName, word).or().like(Exhibitor::getDescription, word));
        }
        PageHelper.startPage(pageNum, pageSize);
        val exhibitors = list(wrapper);
        if (exhibitors.isEmpty()) {
            return new PageInfo<>(List.of());
        }
        val markIds = exhibitors.stream().map(Exhibitor::getMarkId).collect(Collectors.toSet());

        val marks = markMapper.selectList((new QueryWrapper<Mark>().lambda().in(Mark::getId, markIds)));
        val markMap = marks.stream().collect(Collectors.toMap(Mark::getId, Function.identity()));
        val markStatusMap = markStatusService.list(
                new QueryWrapper<MarkStatus>().lambda()
                        .eq(MarkStatus::getUserId, userId)
                        .in(MarkStatus::getMarkId, markIds)
        ).stream().collect(Collectors.toMap(MarkStatus::getMarkId, Function.identity()));
        val areaMap = areaMapper.selectList((new QueryWrapper<Area>().lambda()
                        .in(Area::getId, marks.stream().map(Mark::getAreaId).collect(Collectors.toSet()))))
                .stream().collect(Collectors.toMap(Area::getId, Function.identity()));
        val newMarkStatuses = new ArrayList<MarkStatus>();

        val markDTOS = exhibitors.stream()
                .filter(x -> markMap.containsKey(x.getMarkId()))
                .filter(x -> areaMap.containsKey(markMap.get(x.getMarkId()).getAreaId()))
                .map(x -> {
                    val mark = markMap.get(x.getMarkId());
                    val area = areaMap.get(markMap.get(x.getMarkId()).getAreaId());
                    val markStatus = markStatusMap.getOrDefault(x.getMarkId(), markStatusService.defaultStatus(x.getMarkId(), userId));
                    newMarkStatuses.add(markStatus);
                    val markDTO = MarkDTO.fromEntity(mark, markStatus, area);
                    markDTO.setContentName(x.getName());
                    return markDTO;
                }).collect(Collectors.toList());
        markStatusService.saveOrUpdateBatch(newMarkStatuses);
        return new PageInfo<>(markDTOS);
    }
}
