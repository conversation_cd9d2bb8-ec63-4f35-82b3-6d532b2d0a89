package com.massive.discover.web.controller;

import com.massive.discover.common.annotation.IgnoreAuth;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.model.RankDTO;
import com.massive.discover.common.model.RankResultDTO;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.component.RankListComponent;
import com.massive.discover.user.model.RankResponse;
import com.massive.discover.user.model.UserCacheInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * user api
 * <AUTHOR>
 */
@RestController
@Slf4j
@Api(value = "RankController", tags = "积分排行接口")
@RequestMapping("/rank")
public class RankController {

    @Autowired
    private RankListComponent rankListComponent;

//    @Autowired
//    private IUserScoreService userScoreService;

    @GetMapping("/v1/showTopN")
    @ApiOperation(value = "Get points for top n", notes = "rank",
            authorizations = {@Authorization(value = "bearerAuth")})
    @IgnoreAuth
    public Result<RankResponse> showTopN(int n) {
        return Result.success(rankListComponent.getTopNRanks(n));
    }

    @GetMapping(path = "/v1/rank")
    @ApiOperation(value = "Get the current user's leaderboard position", notes = "rank",
            authorizations = {@Authorization(value = "bearerAuth")})
    public Result<RankResultDTO> queryRankByUserId(@LoginUser UserCacheInfo userCacheInfo) {
        RankDTO rankDTO = rankListComponent.getRank(userCacheInfo.getId());
        RankResultDTO rankResultDTO = new RankResultDTO();
        if(null != rankDTO && rankDTO.getScore() > 0){
            rankResultDTO.setRank(rankDTO.getRank());
            rankResultDTO.setScore(rankDTO.getScore());
            rankResultDTO.setNickName(userCacheInfo.getNickname());
        }else{
            rankResultDTO.setRank(1000L);
            rankResultDTO.setScore(0);
            rankResultDTO.setNickName(userCacheInfo.getNickname());
        }
        return Result.success(rankResultDTO);
    }

//    @GetMapping(path = "/v1/updateTest")
//    @ApiOperation(value = "更新个人积分测试专用", notes = "rank",
//            authorizations = {@Authorization(value = "bearerAuth")})
//    public RankDTO updateScore(long userId, Long markId) {
//        RankDTO rankDTO = rankListComponent.getRank(userId);
//        userScoreService.addScoreByUserId(userId, score);
//        return rankListComponent.updateRank(userId, score);
//    }

}