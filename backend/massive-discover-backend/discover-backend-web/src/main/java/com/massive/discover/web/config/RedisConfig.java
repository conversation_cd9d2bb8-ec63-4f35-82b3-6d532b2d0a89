package com.massive.discover.web.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

/**
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {

//    @Value("${aws.run.env}")
//    private String awsEnv;

    @Value("${spring.redis.cluster.nodes}")
    private String redisClusterNodes;

    @Bean
    public LettuceConnectionFactory redisConnectionFactory() {
//        if (AwsConstant.AWS_LOCAL_ENV.equals(awsEnv)) {
//            return new LettuceConnectionFactory(new RedisStandaloneConfiguration(redisClusterNodes.split(":")[0],
//                    Integer.parseInt(redisClusterNodes.split(":")[1])));
//        } else {
//            return new LettuceConnectionFactory(
//                    new RedisClusterConfiguration(Lists.newArrayList(redisClusterNodes.split(","))));
//        }
        return new LettuceConnectionFactory(new RedisStandaloneConfiguration(redisClusterNodes.split(":")[0], Integer.parseInt(redisClusterNodes.split(":")[1])));
    }
}
