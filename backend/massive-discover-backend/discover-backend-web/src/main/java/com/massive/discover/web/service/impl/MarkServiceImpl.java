package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.dao.AreaMapper;
import com.massive.discover.common.dao.MarkMapper;
import com.massive.discover.common.entity.Area;
import com.massive.discover.common.entity.Exhibitor;
import com.massive.discover.common.entity.Game;
import com.massive.discover.common.entity.Hotel;
import com.massive.discover.common.entity.Mark;
import com.massive.discover.common.entity.MarkStatus;
import com.massive.discover.common.entity.Restaurant;
import com.massive.discover.common.entity.Shop;
import com.massive.discover.common.entity.Other;
import com.massive.discover.common.enums.MarkTypeEnum;
import com.massive.discover.common.model.GameRecordAddReq;
import com.massive.discover.common.utils.AssertUtils;
import com.massive.discover.user.service.IUserScoreService;
import com.massive.discover.web.model.dto.AreaDTO;
import com.massive.discover.web.model.dto.MarkDTO;
import com.massive.discover.web.model.request.ChangeMarkCollectRequest;
import com.massive.discover.web.model.response.GetMarksResponse;
import com.massive.discover.web.model.response.MarkDetailResponse;
import com.massive.discover.web.service.IExhibitorService;
import com.massive.discover.web.service.IGameRecordService;
import com.massive.discover.web.service.IGameService;
import com.massive.discover.web.service.IHotelService;
import com.massive.discover.web.service.IMarkService;
import com.massive.discover.web.service.IMarkStatusService;
import com.massive.discover.web.service.IRestaurantService;
import com.massive.discover.web.service.IShopService;
import com.massive.discover.web.service.IShowService;
import com.massive.discover.web.service.IOtherService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class MarkServiceImpl extends ServiceImpl<MarkMapper, Mark> implements IMarkService {

    @Resource
    private AreaMapper areaMapper;

    @Resource
    private IRestaurantService restaurantService;

    @Resource
    private IShopService shopService;

    @Resource
    private IHotelService hotelService;

    @Resource
    private IOtherService otherService;

    @Resource
    private IMarkStatusService markStatusService;

    @Resource
    private IExhibitorService exhibitorService;

    @Resource
    private IGameService gameService;

    @Resource
    private IShowService showService;

    @Resource
    private IGameRecordService gameRecordService;

    @Resource
    private IUserScoreService userScoreService;

    @Override
    public GetMarksResponse getMarks(@NonNull Long eventId, @NonNull Long userId) {
        val areas = areaMapper.selectList(new QueryWrapper<Area>().lambda().eq(Area::getEventId, eventId));

        val marks = list(new QueryWrapper<Mark>().lambda().eq(Mark::getEventId, eventId));

        val markStatusMap = markStatusService.list(
                new QueryWrapper<MarkStatus>().lambda()
                        .eq(MarkStatus::getUserId, userId)
                        .in(MarkStatus::getMarkId, marks.stream().map(Mark::getId).collect(Collectors.toSet()))
        ).stream().collect(Collectors.toMap(MarkStatus::getMarkId, Function.identity()));
        val areaMap = areas.stream().collect(Collectors.toMap(Area::getId, Function.identity()));

        val newMarkStatuses = new ArrayList<MarkStatus>();

        val markDTOs = marks.stream().filter(x -> areaMap.containsKey(x.getAreaId()))
                .map(x -> {
                    val markStatus = markStatusMap.getOrDefault(x.getId(), markStatusService.defaultStatus(x.getId(), userId));
                    newMarkStatuses.add(markStatus);
                    return MarkDTO.fromEntity(x, markStatus, areaMap.get(x.getAreaId()));
        }).collect(Collectors.toList());

        //==start get areas
        val areaMarkDtoMap = markDTOs.stream().collect(Collectors.groupingBy(MarkDTO::getAreaId));

        val areaDTOs = areas.stream()
                .map(x -> {
                    val areaMarkDTOs = areaMarkDtoMap.getOrDefault(x.getId(), List.of());
                    val types = areaMarkDTOs.stream().map(MarkDTO::getMarkType).collect(Collectors.toSet());
                    val collected = areaMarkDTOs.stream().anyMatch(MarkDTO::isCollected);
                    val showPoint = areaMarkDTOs.stream().anyMatch(MarkDTO::isShowPoint);

                    return AreaDTO.builder()
                            .name(x.getName())
                            .xAxis(x.getXAxis())
                            .yAxis(x.getYAxis())
                            .width(x.getWidth())
                            .height(x.getHeight())
                            .id(x.getId())
                            .collected(collected)
                            .markCount(areaMarkDTOs.size())
                            .showPoint(showPoint)
                            .types(types)
                            .build();
                }).collect(Collectors.toList());

        markStatusService.saveOrUpdateBatch(newMarkStatuses);

        return new GetMarksResponse(areaDTOs, markDTOs);
    }

    @Override
    public MarkDetailResponse getMarkDetail(@NonNull Long markId, @NonNull Long userId) {
        val response = new MarkDetailResponse();
        val mark = getById(markId);
        AssertUtils.notNull(mark, "Can not found this mark!");
        val markStatus = markStatusService.getStatus(markId, userId);
        val area = areaMapper.selectById(mark.getAreaId());

        response.setId(mark.getId());
        response.setCollected(markStatus.isCollected());
        response.setMarkType(mark.getMarkType());
        response.setEventId(mark.getEventId());
        response.setName(mark.getName());
        response.setAreaId(mark.getAreaId());
        response.setAreaName(area.getName());
        response.setShowPoint(mark.getMarkScore() > 0 && !markStatus.isGotScore());
        response.setMarkScore(mark.getMarkScore());
        response.setBoothNumber(mark.getBoothNumber());

        val markType = MarkTypeEnum.parse(mark.getMarkType());

        if (MarkTypeEnum.GAME.equals(markType)) {
            response.setContent(gameService.getGameDetailByMarkId(markId));
        } else if (MarkTypeEnum.BOOTH.equals(markType)) {
            response.setContent(exhibitorService.findByMarkIdAsDTO(markId, userId));
        } else if (MarkTypeEnum.STAGE.equals(markType)) {
            response.setContent(Pair.of("shows", showService.getTodayShow(markId)));
        } else if (MarkTypeEnum.RESTAURANT.equals(markType)) {
            response.setContent(((RestaurantServiceImpl) restaurantService).findByMarkIdAsDTO(markId));
        } else if (MarkTypeEnum.SHOP.equals(markType)) {
            response.setContent(((ShopServiceImpl) shopService).findByMarkIdAsDTO(markId));
        } else if (MarkTypeEnum.HOTEL.equals(markType)) {
            response.setContent(((HotelServiceImpl) hotelService).findByMarkIdAsDTO(markId));
        } else {
            response.setContent(((OtherServiceImpl) otherService).findByMarkIdAsDTO(markId));
        }

        markStatusService.saveOrUpdate(markStatus);
        return response;
    }

    @Override
    public Mark getMarkById(Long markId) {
        return this.getById(markId);
    }

    @Override
    public Boolean changeCollected(@NonNull ChangeMarkCollectRequest request, @NonNull Long userId) {
        val markStatus = markStatusService.getStatus(request.getMarkId(), userId);
        markStatus.setCollected(request.getCollected());
        return markStatusService.saveOrUpdate(markStatus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addMarkScore(Long markId, Long userId) {
        //根据markId获取markId信息
        Mark mark = this.getById(markId);
        AssertUtils.notNull(mark, "Can not found this mark!");
        //获取锚点和用户绑定关系
        MarkStatus markStatus = markStatusService.getOne(new QueryWrapper<MarkStatus>()
                .lambda().eq(MarkStatus::getMarkId, markId).eq(MarkStatus::getUserId, userId));
        AssertUtils.notNull(markStatus, "Can not found this mark status!");
        AssertUtils.isFalse(markStatus.isGotScore(), "This mark already got score!");

        //更新用户积分
        userScoreService.addScoreByUserId(userId, mark.getMarkScore(), mark.getMarkType(), mark.getId(), mark.getName());
        if(MarkTypeEnum.GAME.getStatus().equals(mark.getMarkType())){
            //如果是游戏类型，根据markId获取游戏信息，并更新游戏记录
            Game game = gameService.getGameByMarkId(markId);
            AssertUtils.notNull(game, "Can not found this game!");
            gameRecordService.addPlayGameResult(userId, new GameRecordAddReq(game.getId(), mark.getMarkScore()));
        }

        markStatus.setGotScore(true);
        //更新锚点状态
        return markStatusService.updateById(markStatus);
    }

    @Override
    public PageInfo<MarkDTO> collectedMark(int pageNum, int pageSize, long userId) {
        val markStatuses = markStatusService.list(new QueryWrapper<MarkStatus>().lambda().eq(MarkStatus::getUserId, userId).eq(MarkStatus::isCollected, true));
        val markStatusMap = markStatuses.stream().collect(Collectors.toMap(MarkStatus::getMarkId, Function.identity()));
        val collectedMarkIds = markStatuses.stream().map(MarkStatus::getMarkId).collect(Collectors.toSet());

        PageHelper.startPage(pageNum, pageSize);
        val marks = list(new QueryWrapper<Mark>().lambda().in(Mark::getId, collectedMarkIds));
        if (marks.isEmpty()) {
            return new PageInfo<>(List.of());
        }
        val markIds = marks.stream().map(Mark::getId).collect(Collectors.toSet());
        val exhibitorMap = getContentsMap(exhibitorService, Exhibitor::getMarkId, markIds);
        val gameMap = getContentsMap(gameService, Game::getMarkId, markIds);
        val restaurantMap = getContentsMap(restaurantService, Restaurant::getMarkId, markIds);
        val shopMap = getContentsMap(shopService, Shop::getMarkId, markIds);
        val hotelMap = getContentsMap(hotelService, Hotel::getMarkId, markIds);
        val otherMap = getContentsMap(otherService, Other::getMarkId, markIds);
        val areaMap = areaMapper.selectList(
                        new QueryWrapper<Area>().lambda().in(Area::getId, marks.stream().map(Mark::getAreaId).collect(Collectors.toSet())))
                .stream().collect(Collectors.toMap(Area::getId, Function.identity()));

        val markDTOS = marks.stream()
                .filter(x -> {
                    val markType = MarkTypeEnum.parse(x.getMarkType());
                    if (MarkTypeEnum.GAME.equals(markType)) {
                        return gameMap.containsKey(x.getId());
                    } else if (MarkTypeEnum.BOOTH.equals(markType)) {
                        return exhibitorMap.containsKey(x.getId());
                    } else if (MarkTypeEnum.OTHER.equals(markType)) {
                        return otherMap.containsKey(x.getId());
                    } else if (MarkTypeEnum.RESTAURANT.equals(markType)) {
                        return restaurantMap.containsKey(x.getId());
                    } else if (MarkTypeEnum.SHOP.equals(markType)) {
                        return shopMap.containsKey(x.getId());
                    } else if (MarkTypeEnum.HOTEL.equals(markType)) {
                        return hotelMap.containsKey(x.getId());
                    } else {
                        return true;
                    }
                })
                .filter(x -> areaMap.containsKey(x.getAreaId()))
                .filter(x -> markStatusMap.containsKey(x.getId()))
                .map(x -> {
                    val markDTO = MarkDTO.fromEntity(x, markStatusMap.get(x.getId()), areaMap.get(x.getAreaId()));
                    val markType = MarkTypeEnum.parse(x.getMarkType());
                    if (MarkTypeEnum.GAME.equals(markType)) {
                        markDTO.setContentName(gameMap.get(x.getId()).getName());
                    } else if (MarkTypeEnum.BOOTH.equals(markType)) {
                        markDTO.setContentName(exhibitorMap.get(x.getId()).getName());
                    } else if (MarkTypeEnum.STAGE.equals(markType)) {
                        markDTO.setContentName("Stage--" + x.getName());
                    } else if (MarkTypeEnum.RESTAURANT.equals(markType)) {
                        markDTO.setContentName(restaurantMap.get(x.getId()).getName());
                    } else if (MarkTypeEnum.SHOP.equals(markType)) {
                        markDTO.setContentName(shopMap.get(x.getId()).getName());
                    } else if (MarkTypeEnum.HOTEL.equals(markType)) {
                        markDTO.setContentName(hotelMap.get(x.getId()).getName());
                    } else {
                        markDTO.setContentName(otherMap.get(x.getId()).getName());
                    }
                    return markDTO;
                }).collect(Collectors.toList());
        return new PageInfo<>(markDTOS);
    }

    private <T> Map<Long, T> getContentsMap(
            @NonNull IService<T> service,
            @NonNull SFunction<T, Long> markIdGetter,
            Collection<Long> markIds
    ) {
        return service.list(new QueryWrapper<T>().lambda().in(markIdGetter, markIds))
                .stream()
                .collect(Collectors.toMap(markIdGetter, Function.identity()));
    }
}
