package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.Ticket;


public interface ITicketService extends IService<Ticket> {
    Ticket getUserTicket(Long eventId, Long userId);

    Boolean bindTicket(Long eventId, String code, Long userId);

    Boolean checkTicket(Long eventId, String code, Long userId);

    Boolean refreshTicket(Long eventId, String code);
}

