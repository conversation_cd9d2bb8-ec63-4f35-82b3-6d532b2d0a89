package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.RewardMapper;
import com.massive.discover.common.entity.Reward;
import com.massive.discover.common.entity.UserReward;
import com.massive.discover.common.enums.ScoreRecordSourceEnum;
import com.massive.discover.common.utils.AssertUtils;
import com.massive.discover.lock.redis.impl.RedisReentrancyLock;
import com.massive.discover.user.model.RewardDTO;
import com.massive.discover.user.service.IUserScoreService;
import com.massive.discover.web.model.TaskType;
import com.massive.discover.web.model.response.GetMyRewardsResponse;
import com.massive.discover.web.model.response.GetRewardsResponse;
import com.massive.discover.web.service.IRewardImageService;
import com.massive.discover.web.service.IRewardService;
import com.massive.discover.web.service.IUserRewardService;
import com.massive.discover.web.utils.RedisLockUtil;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class RewardServiceImpl extends ServiceImpl<RewardMapper, Reward> implements IRewardService {

    @Resource
    private IUserRewardService userRewardService;

    @Resource
    private IRewardImageService rewardImageService;

    @Resource
    private IUserScoreService userScoreService;

    @Resource
    private RedisReentrancyLock redisReentrancyLock;

    @Override
    public GetRewardsResponse getEventReward(@NonNull Long eventId) {
        val rewards = list(new QueryWrapper<Reward>().lambda().eq(Reward::getEventId, eventId));
        if (rewards.isEmpty()) {
            return new GetRewardsResponse(List.of());
        }
        val rewardImageMap = rewardImageService.getRewardImageMap(
                rewards.stream().map(Reward::getId).collect(Collectors.toSet())
        );
        return new GetRewardsResponse(
                rewards.stream()
                        .map(x -> {
                            val images = rewardImageMap.getOrDefault(x.getId(), List.of());
                            return RewardDTO.builder()
                                    .id(x.getId())
                                    .name(x.getName())
                                    .description(x.getDescription())
                                    .imageUrl(x.getImageUrl())
                                    .detailDescription(x.getDetailDescription())
                                    .detailImageUrls(images)
                                    .pointPrice(x.getPointPrice())
                                    .inventory(x.getInventory())
                                    .userId(null)
                                    .boughtAt(null)
                                    .exchangedAt(null)
                                    .build();
                        }).collect(Collectors.toList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean buyReward(@NonNull Long rewardId, @NonNull Long userId) {
        return Boolean.TRUE.equals(
                RedisLockUtil.doAsyncTask(
                        () -> orderReward(rewardId, userId),
                        TaskType.BUY_REWARD,
                        30L,
                        redisReentrancyLock));
    }

    private boolean orderReward(@NonNull Long rewardId, @NonNull Long userId) {
        val reward = getById(rewardId);
        AssertUtils.notNull(reward, "Can not found this reward!");
        AssertUtils.isTrue(reward.getInventory() > 0, "Inventory is not enough!");

        userScoreService.subtractScoreByUserId(userId, reward.getPointPrice(), ScoreRecordSourceEnum.EXCHANGE.getType(), rewardId);

        reward.setBought(reward.getBought() + 1);
        reward.setInventory(reward.getInventory() - 1);
        val userReward = UserReward.builder()
                .rewardId(reward.getId())
                .build();
        val flag = userRewardService.save(userReward);
        return flag && updateById(reward);
    }

    @Override
    public GetMyRewardsResponse getMyRewards(@NonNull Long userId) {
        val myRewards = userRewardService.list(new QueryWrapper<UserReward>().lambda().eq(UserReward::getUserId, userId));
        if (myRewards.isEmpty()) {
            return new GetMyRewardsResponse(List.of());
        }
        val rewardIs = myRewards.stream().map(UserReward::getRewardId).collect(Collectors.toSet());
        val rewardMap = listByIds(rewardIs).stream().collect(Collectors.toMap(Reward::getId, Function.identity()));
        val rewardImageMap = rewardImageService.getRewardImageMap(rewardIs);
        return new GetMyRewardsResponse(
                myRewards.stream()
                        .filter(x -> rewardMap.containsKey(x.getRewardId()))
                        .map(x -> {
                            val reward = rewardMap.get(x.getRewardId());
                            val images = rewardImageMap.getOrDefault(x.getId(), List.of());
                            return RewardDTO.builder()
                                    .id(x.getId())
                                    .name(reward.getName())
                                    .description(reward.getDescription())
                                    .imageUrl(reward.getImageUrl())
                                    .detailDescription(reward.getDetailDescription())
                                    .detailImageUrls(images)
                                    .pointPrice(reward.getPointPrice())
                                    .userId(x.getUserId())
                                    .boughtAt(x.getCreateAt())
                                    .exchangedAt(x.getExchangedAt())
                                    .build();
                        }).collect(Collectors.toList()));
    }

    @Override
    public boolean exchangeReward(@NonNull Long userRewardId) {
        val myReward = userRewardService.getById(userRewardId);
        AssertUtils.notNull(myReward, "Can not found this user reward!");
        AssertUtils.isNull(myReward.getExchangedAt(), "The reward has already been exchanged!");
        myReward.setExchangedAt(LocalDateTime.now());
        return userRewardService.updateById(myReward);
    }
}
