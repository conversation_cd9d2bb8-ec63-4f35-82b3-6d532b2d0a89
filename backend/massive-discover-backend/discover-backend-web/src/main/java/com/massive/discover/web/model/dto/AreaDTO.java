package com.massive.discover.web.model.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AreaDTO {

    private Long id;
    private String name;
    private Integer markCount;
    private boolean collected;
    private boolean showPoint;
    private Set<Integer> types;
    private Integer width;
    private Integer height;
    private Integer xAxis;
    private Integer yAxis;
}
