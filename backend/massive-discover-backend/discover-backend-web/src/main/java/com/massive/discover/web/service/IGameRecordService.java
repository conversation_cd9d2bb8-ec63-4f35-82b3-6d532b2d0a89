package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.entity.GameRecord;
import com.massive.discover.common.model.GameRecordAddReq;
import com.massive.discover.user.model.UserCacheInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IGameRecordService extends IService<GameRecord> {

    boolean addPlayGameResult(Long userId, GameRecordAddReq gameRecordAddReq);

    PageInfo<GameRecord> getGameDetailPage(Long userId, int pageNum, int pageSize);

}

