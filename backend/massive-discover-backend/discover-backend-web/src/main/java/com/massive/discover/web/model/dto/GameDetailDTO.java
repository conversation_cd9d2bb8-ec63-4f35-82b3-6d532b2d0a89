package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Game;
import com.massive.discover.common.entity.GameAvailable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.LocalDateTime;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GameDetailDTO {

    @NonNull
    private Long id;

    @NonNull
    private Long eventId;

    @NonNull
    private String name;

    private String description;

    @NonNull
    private Long markId;

    private Long areaId;

    private Integer gameLevel;

    // Constructed game URL with parameters
    @NonNull
    private String gameUrl;

    private LocalDateTime createAt;

    private LocalDateTime updateAt;

    private Long userId;

    public static GameDetailDTO fromEntities(@NonNull Game game, @NonNull GameAvailable gameAvailable) {
        // Construct the game URL with markId parameter
        String constructedUrl = gameAvailable.getGameUrl() + "?markId=" + game.getMarkId();
        
        return GameDetailDTO.builder()
                .id(game.getId())
                .eventId(game.getEventId())
                .name(game.getName())
                .description(game.getDescription())
                .markId(game.getMarkId())
                .areaId(game.getAreaId())
                .gameLevel(game.getGameLevel())
                .gameUrl(constructedUrl)
                .createAt(game.getCreateAt())
                .updateAt(game.getUpdateAt())
                .userId(game.getUserId())
                .build();
    }
}
