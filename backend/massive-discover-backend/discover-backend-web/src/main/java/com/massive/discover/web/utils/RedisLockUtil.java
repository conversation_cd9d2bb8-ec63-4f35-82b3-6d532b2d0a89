package com.massive.discover.web.utils;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.utils.SupplierWrapper;
import com.massive.discover.lock.redis.impl.RedisReentrancyLock;
import com.massive.discover.web.model.TaskType;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * 使用Jwt从header中快速提取uid信息
 *
 * <AUTHOR>
 */
@Slf4j
public class RedisLockUtil {
    public static <T> T doAsyncTask(SupplierWrapper<T> supplier,
                                    TaskType type,
                                    Long lockTimeoutSeconds,
                                    RedisReentrancyLock redisReentrancyLock) {
        String lockId = NanoIdUtils.randomNanoId();
        try {
            if (redisReentrancyLock.tryLock(String.format("task:lock:%s",
                            type.getType()),
                    lockId,
                    lockTimeoutSeconds,
                    TimeUnit.SECONDS)) {
                return supplier.get();
            } else {
                log.error("unable get the distribute lock,lockType:{},lockId:{}", type.getType(), lockId);
            }
        } catch (Exception ex) {
                throw new SystemException(ex.getMessage());
        } finally {
            redisReentrancyLock.unlock(String.format("task:lock:%s", type.getType()), lockId);
        }
        return null;
    }
}
