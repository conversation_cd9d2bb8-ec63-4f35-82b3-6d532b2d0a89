package com.massive.discover.web.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * task 类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
public enum TaskType {

    /**
     * BuyReward
     */
    BUY_REWARD("BUY_REWARD", "Buy reward.");

    @Getter
    private final String type;

    @Getter
    private final String description;

    private static final Map<String, TaskType> MAP;

    static {
        MAP = Arrays.stream(TaskType.values()).collect(Collectors.toMap(TaskType::getType, type -> type));
    }

    public static TaskType parse(String type) {
        return MAP.get(type);
    }

}
