package com.massive.discover.web.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public final class ChangeMarkCollectRequest implements Serializable {
    @ApiModelProperty(value = "markId", required = true)
    @Min(1L)
    private Long markId;

    @ApiModelProperty(value = "collected", required = true)
    private Boolean collected;
}
