package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.entity.Exhibitor;
import com.massive.discover.web.model.dto.ExhibitorDTO;
import com.massive.discover.web.model.dto.MarkDTO;

public interface IExhibitorService extends IService<Exhibitor> {

    Exhibitor findByMarkId(Long markId);

    ExhibitorDTO findByMarkIdAsDTO(Long markId, Long userId);

    PageInfo<MarkDTO> search(int pageNum, int pageSize, String word, long userId);
}

