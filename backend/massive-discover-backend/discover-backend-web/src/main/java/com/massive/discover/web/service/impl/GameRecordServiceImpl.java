package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.dao.GameMapper;
import com.massive.discover.common.dao.GameRecordMapper;
import com.massive.discover.common.entity.Game;
import com.massive.discover.common.entity.GameRecord;
import com.massive.discover.common.model.GameRecordAddReq;
import com.massive.discover.web.service.IGameRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class GameRecordServiceImpl extends ServiceImpl<GameRecordMapper, GameRecord> implements IGameRecordService {

    @Autowired
    private GameMapper gameMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPlayGameResult(Long userId, GameRecordAddReq gameRecordAddReq) {

        GameRecord gameRecord = new GameRecord();
        //根据gameId去查询game信息
        Game game = gameMapper.selectById(gameRecordAddReq.getGameId());
        BeanUtils.copyProperties(gameRecordAddReq, gameRecord);
        gameRecord.setGameName(game.getName());
        gameRecord.setGameLevel(game.getGameLevel());
        gameRecord.setScore(gameRecordAddReq.getScore());
        gameRecord.setFinishedAt(LocalDateTime.now());
        //保存游戏记录
        return this.save(gameRecord);
    }

    @Override
    public PageInfo<GameRecord> getGameDetailPage(Long userId, int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        QueryWrapper<GameRecord> playGamesQueryWrapper = new QueryWrapper<>();
        playGamesQueryWrapper.eq("user_id", userId);
        List<GameRecord> gameRecordList = this.list(playGamesQueryWrapper);
        return new PageInfo<>(gameRecordList);
    }
}
