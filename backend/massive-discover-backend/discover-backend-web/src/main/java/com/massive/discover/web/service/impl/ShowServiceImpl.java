package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.ShowMapper;
import com.massive.discover.common.entity.Show;
import com.massive.discover.web.service.IShowService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class ShowServiceImpl extends ServiceImpl<ShowMapper, Show> implements IShowService {

    @Override
    public List<Show> getTodayShow(Long markId) {

        //Todo 暂时写死，等测试完成
        val startOfDay = LocalDateTime.of(2024,5,1,0,0,0);
        val endOfDay = startOfDay.plusDays(1).minusSeconds(1);

        return list(new QueryWrapper<Show>().lambda()
                .eq(Show::getMarkId, markId)
                .ge(Show::getBeginTime, startOfDay)
                .le(Show::getEndTime, endOfDay)
                .orderByAsc(Show::getBeginTime));
    }
}
