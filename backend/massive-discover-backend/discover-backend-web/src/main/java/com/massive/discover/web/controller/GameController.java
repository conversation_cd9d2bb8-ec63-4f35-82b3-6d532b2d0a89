package com.massive.discover.web.controller;

import com.massive.discover.common.annotation.IgnoreAuth;
import com.massive.discover.common.model.Result;
import com.massive.discover.web.model.response.GetGamesResponse;
import com.massive.discover.web.service.IGameService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;

@RestController
@Api(value = "game", tags = "游戏列表")
@RequestMapping("/game")
public class GameController {

    @Resource
    private IGameService gameService;

    @GetMapping("/v1/getGameList")
    @ApiOperation(value = "根据eventId获取所有游戏信息", notes = "game", authorizations = {@Authorization(value = "bearerAuth")})
    @IgnoreAuth
    public Result<GetGamesResponse> getGameList(
            @RequestParam @Validated @Min(1L) Long eventId
    ) {
        return Result.success(gameService.getGameList(eventId));
    }
}
