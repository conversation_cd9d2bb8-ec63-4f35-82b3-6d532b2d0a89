package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.Event;
import com.massive.discover.web.model.request.GetEventInfoRequest;
import com.massive.discover.web.model.response.EventInitResponse;

public interface IEventService extends IService<Event> {

    EventInitResponse getEvent(GetEventInfoRequest getEventInfoRequest);
}

