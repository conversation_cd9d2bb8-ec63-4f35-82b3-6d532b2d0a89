package com.massive.discover.web.interceptor;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.massive.discover.common.context.TenantContextHolder;
import com.massive.discover.web.utils.JwtUtil;
import com.massive.discover.common.annotation.IgnoreAuth;
import com.massive.discover.common.annotation.NoAnonymous;
import com.massive.discover.common.entity.UserLogin;
import com.massive.discover.common.exception.AuthException;
import com.massive.discover.user.enums.ThirdPartyLoginType;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.user.service.UserLoginService;
import com.massive.discover.user.service.UserService;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * {@code @date} 2023-61-06 01:04
 */
@Component
public class AuthorizationInterceptor implements HandlerInterceptor {

    @Resource
    private UserService userService;

    @Resource
    private UserLoginService userLoginService;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        if (checkIgnoreAuth(handler)) {
            return true;
        }
        UserCacheInfo userInfo = checkAuth(request);
        //登录拦截设置多租户ID
        if(userInfo.getAdminLevelId() != null && userInfo.getAdminLevelId() == 0){
            //设置租户ID为0，表示为超级管理员（    //是否管理员，0：是 1：否）
            TenantContextHolder.setTenantId(0L);
        }else{
            if(null != userInfo.getId()){
                TenantContextHolder.setTenantId(userInfo.getId());
            }
        }
        checkNoAnonymous(handler, userInfo);
        return true;
    }


    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        HandlerInterceptor.super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        TenantContextHolder.clear();
    }

    /**
     * 从request的header中解析jwt并检查是否已登陆
     *
     * @param request 请求
     */
    private UserCacheInfo checkAuth(HttpServletRequest request) {
        String jwtToken = JwtUtil.parseJwtFromRequest(request);
        if (StringUtils.isEmpty(jwtToken)) {
            throw new AuthException("idToken null");
        }
        return userService.getUserInfoFromCache(jwtToken);
    }

    /**
     * 验证ignore auth
     */
    protected boolean checkIgnoreAuth(Object handler) {
        if (handler instanceof HandlerMethod) {
            //如果有@IgnoreAuth注解，则不验证token
            return ((HandlerMethod) handler).getMethodAnnotation(IgnoreAuth.class) != null;
        } else {
            return true;
        }
    }

    /**
     * 验证No Anonymous auth
     */
    protected void checkNoAnonymous(Object handler, UserCacheInfo userInfo) {
        if (handler instanceof HandlerMethod) {
            //如果有@IgnoreAuth注解，则不验证token
            if (((HandlerMethod) handler).getMethodAnnotation(NoAnonymous.class) != null) {
                //检查是否匿名用户
                long noAnonymousCount = userLoginService.count(new QueryWrapper<UserLogin>().lambda().eq(UserLogin::getUserId, userInfo.getId()).ne(UserLogin::getThirdPartyType, ThirdPartyLoginType.FIREBASE_ANONYMOUS.getType()));
                if (noAnonymousCount == 0L) {
                    throw new AuthException("anonymous users are not allowed to operate");
                }
            }
        }
    }
}
