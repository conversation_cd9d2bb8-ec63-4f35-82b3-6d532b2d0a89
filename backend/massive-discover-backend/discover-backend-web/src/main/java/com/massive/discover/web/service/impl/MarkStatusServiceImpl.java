package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.MarkStatusMapper;
import com.massive.discover.common.entity.MarkStatus;
import com.massive.discover.common.utils.AssertUtils;
import com.massive.discover.web.service.IMarkStatusService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class MarkStatusServiceImpl extends ServiceImpl<MarkStatusMapper, MarkStatus> implements IMarkStatusService {

    @Override
    public MarkStatus defaultStatus(@NonNull Long markId, @NonNull Long userId) {
        return MarkStatus.builder()
                .markId(markId)
                .userId(userId)
                .gotScore(false)
                .collected(false)
                .build();
    }

    @Override
    public MarkStatus getStatus(@NonNull Long markId, @NonNull Long userId) {
        val status = getOne(new QueryWrapper<MarkStatus>().lambda().eq(MarkStatus::getUserId, userId).eq(MarkStatus::getMarkId, markId));
        if (status == null) {
            return defaultStatus(markId, userId);
        }
        return status;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMarkStatus(MarkStatus markStatus) {
        AssertUtils.notNull(markStatus, "markStatus is null");
        return this.save(markStatus);
    }

}
