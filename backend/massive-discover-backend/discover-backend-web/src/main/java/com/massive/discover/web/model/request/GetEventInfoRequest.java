package com.massive.discover.web.model.request;


import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class GetEventInfoRequest {

    @Min(value = 1)
    @NonNull
    private Long eventId;

    @ApiModelProperty(value = "yyyy-MM-dd HH:mm:ss")
    private String startDate;
}
