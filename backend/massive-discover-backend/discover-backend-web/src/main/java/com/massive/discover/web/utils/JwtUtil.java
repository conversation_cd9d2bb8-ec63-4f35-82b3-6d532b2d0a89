package com.massive.discover.web.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.user.service.OauthService;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.Map;

/**
 * 使用Jwt从header中快速提取uid信息
 *
 * <AUTHOR>
 */
@Slf4j
public class JwtUtil {
    public static final String TOKEN_PREFIX = "Bearer ";

    public static final String HEADER_STRING = "Authorization";

    /**
     * null for invalid or expired token
     *
     * @param request 请求
     * @return null or uid
     */
    public static String parseJwtFromRequest(HttpServletRequest request) {
        String header = request.getHeader(HEADER_STRING);
        if (header == null || !header.startsWith(TOKEN_PREFIX)) {
            return null;
        }
        return header.replace(TOKEN_PREFIX, "");
    }

    public static String generateToken(String secret, Map<String, ?> claims) {
        Algorithm algorithm = Algorithm.HMAC256(secret);
        try {
            return JWT.create().withIssuer("Nana").withSubject(new ObjectMapper().writeValueAsString(claims)).withExpiresAt(new Date(System.currentTimeMillis() + OauthService.EXPIRE_TIME_SEC * 1000)).sign(algorithm);
        } catch (Exception ex) {
            throw new SystemException(ex);
        }
    }

}
