package com.massive.discover.web.config;

import com.google.common.collect.Lists;
import com.massive.discover.web.interceptor.AuthorizationInterceptor;
import com.massive.discover.web.resolver.LoginUserHandlerMethodArgumentResolver;
import lombok.NonNull;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
public class WebConfiguration {

    private static final List<String> IGNORE_URLS = Lists.newArrayList(
            "/favicon.ico",
            "/error",
            "/swagger-resources/**",
            "/webjars/**",
            "/v2/**",
            "/doc.html",
            "**/swagger-ui.html",
            "/swagger-ui.html/**"
    );

    @Bean
    public WebMvcConfigurer corsConfigurer(AuthorizationInterceptor interceptor,
                                           LoginUserHandlerMethodArgumentResolver argumentResolver) {
        return new WebMvcConfigurer() {
            @Override
            public void addCorsMappings(@NonNull CorsRegistry registry) {
                registry.addMapping("/**").allowedOrigins("*").allowedHeaders("*").allowedMethods("*");
            }

            @Override
            public void addInterceptors(@NonNull InterceptorRegistry registry) {
                registry.addInterceptor(interceptor)
                        .addPathPatterns("/**")
                        .excludePathPatterns(IGNORE_URLS);
            }

            @Override
            public void addArgumentResolvers(@NonNull List<HandlerMethodArgumentResolver> argumentResolvers) {
                argumentResolvers.add(argumentResolver);
            }
        };
    }
}