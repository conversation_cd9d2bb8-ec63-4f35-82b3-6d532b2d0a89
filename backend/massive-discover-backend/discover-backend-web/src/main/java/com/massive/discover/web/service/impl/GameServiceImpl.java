package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.massive.discover.common.dao.GameMapper;
import com.massive.discover.common.entity.Game;
import com.massive.discover.common.entity.GameAvailable;
import com.massive.discover.web.model.dto.GameDetailDTO;
import com.massive.discover.web.model.response.GetGamesResponse;
import com.massive.discover.web.service.IGameAvailableService;
import com.massive.discover.web.service.IGameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class GameServiceImpl extends ServiceImpl<GameMapper, Game> implements IGameService {

    @Autowired
    private IGameAvailableService gameAvailableService;

    @Override
    public GetGamesResponse getGameList(Long eventId) {
        List<Game> result = Lists.newArrayList();
        QueryWrapper<Game> queryWrapperGame = new QueryWrapper<>();
        queryWrapperGame.eq("event_id", eventId);
        List<Game> gameList = this.list(queryWrapperGame);
        if(!gameList.isEmpty()){
            result.addAll(gameList);
        }
        return new GetGamesResponse(result);
    }

    @Override
    public List<Game> getGamesById(List<Long> gameIds) {
        return this.list(
                new QueryWrapper<Game>().lambda()
                        .in(Game::getId, gameIds)
        );
    }

    @Override
    public Game getGameByMarkId(Long markId) {
        return this.getOne(new QueryWrapper<Game>().lambda().eq(Game::getMarkId, markId));
    }

    @Override
    public GameDetailDTO getGameDetailByMarkId(Long markId) {
        // Get the game instance
        Game game = this.getGameByMarkId(markId);
        if (game == null) {
            return null;
        }

        // Get the game available details
        GameAvailable gameAvailable = gameAvailableService.getGameAvailableById(game.getGameAvailableId());
        if (gameAvailable == null) {
            log.warn("GameAvailable not found for gameAvailableId: {}", game.getGameAvailableId());
            return null;
        }

        // Create DTO with constructed URL
        return GameDetailDTO.fromEntities(game, gameAvailable);
    }

}
