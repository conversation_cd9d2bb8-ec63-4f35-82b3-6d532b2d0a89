package com.massive.discover.web.config;

import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.massive.discover.common.properties.FirebaseCredentialProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;

import static com.google.auth.oauth2.GoogleCredentials.fromStream;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(FirebaseCredentialProperties.class)
public class FirebaseConfig {

    @Resource
    private FirebaseCredentialProperties firebaseCredentialProperties;

    @PostConstruct
    public void init() {
        try {
            FirebaseApp.initializeApp(
                    FirebaseOptions.builder()
                            .setCredentials(
                                    fromStream(
                                            new ByteArrayInputStream(
                                                    firebaseCredentialProperties.getCredential().getBytes())))
                            .build());
        } catch (IOException e) {
            log.error("Failed to load firebase credential", e);
        }
    }
}
