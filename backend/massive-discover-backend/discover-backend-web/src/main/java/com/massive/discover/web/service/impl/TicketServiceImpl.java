package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.TicketMapper;
import com.massive.discover.common.entity.Ticket;
import com.massive.discover.common.utils.AssertUtils;
import com.massive.discover.web.service.ITicketService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class TicketServiceImpl extends ServiceImpl<TicketMapper, Ticket> implements ITicketService {

    @Override
    public Ticket getUserTicket(@NonNull Long eventId, @NonNull Long userId) {
        return getOne(new QueryWrapper<Ticket>().lambda()
                .eq(Ticket::getEventId, eventId)
                .eq(Ticket::getUserId, userId)
                .isNotNull(Ticket::getBoundAt));
    }

    @Override
    public Boolean bindTicket(@NonNull Long eventId, @NonNull String code, @NonNull Long userId) {
        val oldTicket = getOne(new QueryWrapper<Ticket>().lambda()
                .eq(Ticket::getEventId, eventId)
                .eq(Ticket::getUserId, userId)
                .isNotNull(Ticket::getBoundAt));
        AssertUtils.isNull(oldTicket, "You already bind a ticket!");
        val ticket = getOne(new QueryWrapper<Ticket>().lambda().eq(Ticket::getEventId, eventId).eq(Ticket::getCode, code));
        AssertUtils.notNull(ticket, "Can not found this ticket!");
        AssertUtils.isNull(ticket.getBoundAt(), "The ticket has already been bound!");
        ticket.setUserId(userId);
        ticket.setBoundAt(LocalDateTime.now());
        return updateById(ticket);
    }

    @Override
    public Boolean checkTicket(@NonNull Long eventId, @NonNull String code, @NonNull Long userId) {
        val ticket = getOne(new QueryWrapper<Ticket>().lambda()
                .eq(Ticket::getEventId, eventId)
                .eq(Ticket::getCode, code)
                .eq(Ticket::getUserId, userId)
                .isNotNull(Ticket::getBoundAt));
        AssertUtils.notNull(ticket, "Can not found this ticket!");
        AssertUtils.isNull(ticket.getCheckedAt(), "The ticket has already been checked!");
        ticket.setCheckedAt(LocalDateTime.now());
        return updateById(ticket);
    }

    @Override
    public Boolean refreshTicket(Long eventId, String code) {
        val ticket = getOne(new QueryWrapper<Ticket>().lambda()
                .eq(Ticket::getEventId, eventId)
                .eq(Ticket::getCode, code));
        AssertUtils.notNull(ticket, "Can not found this ticket!");
        ticket.setUserId(null);
        ticket.setBoundAt(null);
        ticket.setCheckedAt(null);
        return updateById(ticket);
    }
}
