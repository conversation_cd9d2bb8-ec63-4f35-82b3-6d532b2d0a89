package com.massive.discover.web.controller;

import com.massive.discover.common.annotation.IgnoreAuth;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.exception.SystemException;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.web.model.request.BuyRewardRequest;
import com.massive.discover.web.model.request.ExchangeRewardRequest;
import com.massive.discover.web.model.response.GetMyRewardsResponse;
import com.massive.discover.web.model.response.GetRewardsResponse;
import com.massive.discover.web.service.IRewardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;

@RestController
@Api(tags = "reward")
@RequestMapping("/reward")
public class RewardController {

    @Resource
    private IRewardService rewardService;

    @GetMapping("/v1/rewards")
    @ApiOperation(value = "根据eventId获取reward信息", notes = "reward", authorizations = {@Authorization(value = "bearerAuth")})
    @IgnoreAuth
    public Result<GetRewardsResponse> getRewards(
            @RequestParam @Validated @Min(1L) Long eventId
    ) {
        return Result.success(rewardService.getEventReward(eventId));
    }

    @GetMapping("/v1/my/rewards")
    @ApiOperation(value = "根据user获取reward信息", notes = "reward", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<GetMyRewardsResponse> getMyRewards(@LoginUser UserCacheInfo loginUserInfo) {
        return Result.success(rewardService.getMyRewards(loginUserInfo.getId()));
    }

    @PostMapping("/v1/my/reward")
    @ApiOperation(value = "user购买reward", notes = "reward", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> buyReward(@LoginUser UserCacheInfo loginUserInfo,
                                     @RequestBody @Validated BuyRewardRequest request) {
        boolean flag;
        try {
            flag = rewardService.buyReward(request.getRewardId(), loginUserInfo.getId());
        } catch (SystemException systemException) {
            return Result.fail(systemException.getMessage());
        }
        return Result.success(flag);
    }

    @PutMapping("/v1/my/reward")
    @ApiOperation(value = "user兑换reward", notes = "reward", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<Boolean> exchangeReward(@LoginUser UserCacheInfo loginUserInfo,
                                          @RequestBody @Validated ExchangeRewardRequest request) {
        boolean flag;
        try {
            flag = rewardService.exchangeReward(request.getUserRewardId());
        } catch (SystemException systemException) {
            return Result.fail(systemException.getMessage());
        }
        return Result.success(flag);
    }
}
