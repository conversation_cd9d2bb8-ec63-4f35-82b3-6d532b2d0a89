package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Hotel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HotelDTO {

    @NonNull
    private Long id;

    private String name;

    private String thumbnailUrl;

    private String description;

    private List<String> tags;

    private String websiteUrl;

    public static HotelDTO fromEntity(@NonNull Hotel hotel) {
        return HotelDTO.builder()
                .id(hotel.getId())
                .name(hotel.getName())
                .thumbnailUrl(hotel.getThumbnailUrl())
                .description(hotel.getDescription())
                // Convert comma-separated tags string to List<String>
                .tags(hotel.getTags() != null && !hotel.getTags().trim().isEmpty() ?
                    Arrays.asList(hotel.getTags().split(",")) :
                    Collections.emptyList())
                .websiteUrl(hotel.getWebsiteUrl())
                .build();
    }
}
