package com.massive.discover.web.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public final class BindTicketRequest implements Serializable {
    @ApiModelProperty(value = "eventId", required = true)
    @Min(1L)
    private Long eventId;

    @ApiModelProperty(value = "code", required = true)
    private String code;
}
