package com.massive.discover.web.controller;

import com.google.common.collect.Lists;
import com.massive.discover.common.annotation.IgnoreAuth;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.enums.ResponseStatusEnum;
import com.massive.discover.common.model.Result;
import com.massive.discover.common.utils.CommonUtils;
import com.massive.discover.user.model.*;
import com.massive.discover.user.service.LineService;
import com.massive.discover.user.service.UserService;
import com.massive.discover.user.service.impl.FirebaseOauthServiceImpl;
import com.massive.discover.user.service.impl.LineNativeServiceImpl;
import com.massive.discover.user.service.impl.LineOauthServiceImpl;
import com.massive.discover.user.service.impl.PhoneNumberServiceImpl;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

import static com.massive.discover.web.utils.JwtUtil.HEADER_STRING;
import static com.massive.discover.web.utils.JwtUtil.TOKEN_PREFIX;


/**
 * user api
 * <AUTHOR>
 */
@RestController
@Slf4j
public class UserController {

    @Resource
    private UserService userService;

    @Resource
    private FirebaseOauthServiceImpl firebaseOauthService;

    @Resource
    private LineOauthServiceImpl lineOauthService;

    @Resource
    private LineNativeServiceImpl lineNativeService;

    @Resource
    private LineService lineService;

    @Resource
    private PhoneNumberServiceImpl phoneNumberService;

    @ApiOperation(
            value = "登录",
            nickname = "登录",
            notes = "登录",
            response = UserInfo.class,
            tags = {"user-controller"})
    @IgnoreAuth
    @PostMapping("/user/firebase/login")
    public Result<UserInfo> login(@RequestBody UserLoginInfo userLoginInfo) {
        UserInfo userInfo = userService.login(userLoginInfo, firebaseOauthService);
        return Result.success(userInfo);
    }

    @ApiOperation(
            value = "bind third party Login method",
            nickname = "bind third party Login method",
            notes = "bind third party Login method",
            response = UserInfo.class,
            authorizations = {@Authorization(value = "bearerAuth")},
            tags = {"user-controller"})
    @PostMapping("/user/firebase/bindThirdPartyLogin")
    public Result<UserInfo> bindFirebaseThirdPartyLogin(
            @RequestBody UserLoginInfo userLoginInfo,
            @LoginUser UserCacheInfo loginUserInfo) {
        UserInfo userInfo = userService.bindThirdPartyLogin(
                loginUserInfo.getId(),
                userLoginInfo, firebaseOauthService);
        return Result.success(userInfo);
    }

    @ApiOperation(
            value = "get line oauth url",
            nickname = "get line oauth url",
            notes = "get line oauth url",
            response = UserInfo.class,
            tags = {"user-controller"})
    @GetMapping("/user/line/loginUrl")
    @IgnoreAuth
    public Result<String> lineLoginUrl() {
        final String state = CommonUtils.getToken();
        final String nonce = CommonUtils.getToken();
        return new Result<>(ResponseStatusEnum.SUCCESS, lineService.getLineWebLoginUrl(
                state,
                nonce,
                Lists.newArrayList("openid", "profile")));
    }

    @ApiOperation(
            value = "登陆(line)",
            nickname = "登陆(line)",
            notes = "登陆(line)",
            response = UserInfo.class,
            tags = {"user-controller"})
    @IgnoreAuth
    @PostMapping("/user/line/login")
    public Result<UserInfo> lineLogin(@RequestBody UserLoginInfo userLoginInfo) {
        UserInfo userInfo = userService.login(userLoginInfo, lineNativeService);
        return Result.success(userInfo);
    }

    @ApiOperation(
            value = "line login callback api.",
            nickname = "line login callback api",
            notes = "line login callback api",
            response = UserInfo.class,
            tags = {"user-controller"})
    @IgnoreAuth
    @GetMapping("/user/line/loginCallback")
    public Result<UserInfo> lineLoginCallback(@RequestParam String code) {
        UserInfo userInfo = userService.login(
                UserLoginInfo.builder().lineIdToken(code).build(),
                lineOauthService);
        return Result.success(userInfo);
    }

    @ApiOperation(
            value = "bind line third party Login method",
            nickname = "bind line third party Login method",
            notes = "bind line third party Login method",
            response = UserInfo.class,
            authorizations = {@Authorization(value = "bearerAuth")},
            tags = {"user-controller"})
    @PostMapping("/user/line/bindThirdPartyLogin")
    public Result<UserInfo> bindLineThirdPartyLogin(
            @RequestBody UserLoginInfo userLoginInfo,
            @LoginUser UserCacheInfo loginUserInfo) {
        UserInfo userInfo = userService.bindThirdPartyLogin(loginUserInfo.getId(),
                userLoginInfo, lineNativeService);
        return Result.success(userInfo);
    }

    @ApiOperation(
            value = "bind user birthday when user finished login",
            nickname = "bind user birthday when user login",
            notes = "bind user birthday when user finished login",
            response = UserBirthday.class,
            authorizations = {@Authorization(value = "bearerAuth")},
            tags = {"user-controller"})
    @PostMapping("/user/login/addBirthday")
    public Result<Integer> bindUserBirthdayOnLogin(
            @RequestBody UserBirthday userBirthday,
            @LoginUser UserCacheInfo loginUserInfo) {
        String result = userService.bindUserBirthday(loginUserInfo.getId(), userBirthday);
        if (Objects.equals(result, "Success")) {
            return Result.success(1);
        } else {
            return Result.success(result);
        }
    }

    @ApiOperation(
            value = "send verification code",
            nickname = "send verification code",
            tags = {"user-controller"})
    @PostMapping("/user/login/sendVerifyCode")
    @IgnoreAuth
    public Result<Integer> sendVerifyCode(
            @RequestBody UserLoginInfo userLoginInfo) {
        String result = userService.sendVerifyCode(userLoginInfo);
        if (Objects.equals(result, "success")) {
            return Result.success(1);
        } else {
            return Result.fail(result);
        }

    }

    @ApiOperation(
            value = "phone number login",
            nickname = "phone number login",
            tags = {"user-controller"})
    @PostMapping("/user/login/phoneNumberLogin")
    @IgnoreAuth
    public Result<UserInfo> phoneNumberLogin(
            @RequestBody UserLoginInfo userLoginInfo) {
        return Result.success(userService.login(userLoginInfo, phoneNumberService));
    }

    @ApiOperation(
            value = "update userinfo",
            nickname = "update userinfo",
            authorizations = {@Authorization(value = "bearerAuth")},
            tags = {"user-controller"})
    @PostMapping("/user/updateUserInfo")
    public Result<UserInfo> updateUserInfo(
            @LoginUser UserCacheInfo loginUserInfo,
            @RequestBody UpdateUserInfoReq updateUserInfo) {
        return Result.success(userService.updateUserInfo(loginUserInfo, updateUserInfo));
    }

    @ApiOperation(
            value = "send verification code mock",
            nickname = "send verification code mock",
            tags = {"user-controller"})
    @PostMapping("/user/login/sendVerifyCodeMock")
    @IgnoreAuth
    public Result<Integer> sendVerifyCodeMock(
            @RequestBody UserLoginInfo userLoginInfo) {
        String result = userService.sendVerifyCodeMock(userLoginInfo);
        if (Objects.equals(result, "success")) {
            return Result.success(1);
        } else {
            return Result.fail(result);
        }
    }

    @ApiOperation(
            value = "user unregister",
            nickname = "user unregister",
            authorizations = {@Authorization(value = "bearerAuth")},
            tags = {"user-controller"})
    @PostMapping("/user/unregister")
    public Result<String> unregister(
            @LoginUser UserCacheInfo loginUserInfo,
            HttpServletRequest request) {

        String header = request.getHeader(HEADER_STRING);
        if (header == null || !header.startsWith(TOKEN_PREFIX)) {
            return null;
        }
        String idToken = header.replace(TOKEN_PREFIX, "");

        boolean result = userService.unregister(loginUserInfo.getId(), idToken);
        if (result) {
            return Result.success("success");
        } else {
            return Result.fail("unregister fail");
        }
    }


}