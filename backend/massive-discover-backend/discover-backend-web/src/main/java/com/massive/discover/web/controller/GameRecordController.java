package com.massive.discover.web.controller;

import com.github.pagehelper.PageInfo;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.entity.GameRecord;
import com.massive.discover.common.model.GameRecordAddReq;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.web.service.IGameRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(value = "PlayGamesController", tags = "游戏结果")
@RequestMapping("/playGames")
public class GameRecordController {

    @Resource
    private IGameRecordService gameRecordService;

    @GetMapping("/v1/getGameDetailPage")
    @ApiOperation(value = "分页获取用户游戏结果", notes = "gameDetail",
            authorizations = {@Authorization(value = "bearerAuth")})
    public Result<PageInfo<GameRecord>> getGameDetailPage(@LoginUser UserCacheInfo loginUserInfo,
                                                          int pageNum, int pageSize) {
        return Result.success(gameRecordService.getGameDetailPage(loginUserInfo.getId(), pageNum, pageSize));
    }

}
