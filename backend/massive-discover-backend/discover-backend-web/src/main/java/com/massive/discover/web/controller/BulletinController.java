package com.massive.discover.web.controller;

import com.github.pagehelper.PageInfo;
import com.massive.discover.common.entity.Bulletin;
import com.massive.discover.common.model.Result;
import com.massive.discover.web.service.IBulletinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "bulletin")
@RequestMapping(value = "/bulletin", produces = "application/json")
public class BulletinController {

    @Resource
    private IBulletinService bulletinService;

    @GetMapping("/v1/bulletins")
    @ApiOperation(value = "分页获取bulletin列表", notes = "getExhibitorPage", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<PageInfo<Bulletin>> getEventBulletin(int pageNum, int pageSize, long eventId) {
        return Result.success(bulletinService.getEventBulletin(pageNum, pageSize, eventId));
    }
}
