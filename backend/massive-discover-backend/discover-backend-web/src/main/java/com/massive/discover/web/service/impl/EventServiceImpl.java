package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.EventMapMapper;
import com.massive.discover.common.dao.EventMapper;
import com.massive.discover.common.entity.Event;
import com.massive.discover.common.entity.EventMap;
import com.massive.discover.common.utils.AssertUtils;
import com.massive.discover.web.model.request.GetEventInfoRequest;
import com.massive.discover.web.model.response.EventInitResponse;
import com.massive.discover.web.service.IEventService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class EventServiceImpl extends ServiceImpl<EventMapper, Event> implements IEventService {

    @Autowired
    private EventMapMapper eventMapMapper;

    @Override
    public EventInitResponse getEvent(GetEventInfoRequest getEventInfoRequest) {
        //根据eventId获取event信息
        Event event = getById(getEventInfoRequest.getEventId());
        AssertUtils.notNull(event, "eventInfo not found");
        //根据eventId获取map信息
        List<EventMap> eventMapList = eventMapMapper.selectList(new QueryWrapper<EventMap>().lambda().eq(EventMap::getEventId, getEventInfoRequest.getEventId()));
        AssertUtils.notEmpty(eventMapList, "event map info not found");

        //获取event的开始时间，计算倒计时天数
        LocalDateTime startDate = event.getStartDate();
        if(StringUtils.isNotBlank(getEventInfoRequest.getStartDate())){
            startDate = LocalDateTime.parse(getEventInfoRequest.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        int countdown =  (int)Duration.between(LocalDateTime.now(), startDate).toDays();

        EventInitResponse response = new EventInitResponse();
        response.setEventInfo(event);
        response.setMap(eventMapList.get(0));
        response.setCountdown(countdown);

        return response;
    }
}
