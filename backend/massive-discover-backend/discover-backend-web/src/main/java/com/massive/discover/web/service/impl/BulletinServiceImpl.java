package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.massive.discover.common.dao.BulletinMapper;
import com.massive.discover.common.entity.Bulletin;
import com.massive.discover.web.service.IBulletinService;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class BulletinServiceImpl extends ServiceImpl<BulletinMapper, Bulletin> implements IBulletinService {

    @Override
    public PageInfo<Bulletin> getEventBulletin(int pageNum, int pageSize, long eventId) {
        PageHelper.startPage(pageNum, pageSize);
        val bulletins = list(new QueryWrapper<Bulletin>().lambda().eq(Bulletin::isPublished, true)
                .eq(Bulletin::getEventId, eventId)
                .orderByAsc(Bulletin::getPublishAt));
        return new PageInfo<>(bulletins);
    }
}
