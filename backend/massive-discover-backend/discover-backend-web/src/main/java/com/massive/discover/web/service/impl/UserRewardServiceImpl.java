package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.UserRewardMapper;
import com.massive.discover.common.entity.UserReward;
import com.massive.discover.web.service.IUserRewardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
@Transactional
public class UserRewardServiceImpl extends ServiceImpl<UserRewardMapper, UserReward> implements IUserRewardService {

}
