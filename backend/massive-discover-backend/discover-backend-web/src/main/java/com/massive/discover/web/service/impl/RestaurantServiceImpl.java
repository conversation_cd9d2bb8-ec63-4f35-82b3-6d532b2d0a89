package com.massive.discover.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.massive.discover.common.dao.RestaurantMapper;
import com.massive.discover.common.entity.Restaurant;
import com.massive.discover.web.model.dto.RestaurantDTO;
import com.massive.discover.web.service.IRestaurantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2023/6/6 13:30
 */

@Slf4j
@Service
public class RestaurantServiceImpl extends ServiceImpl<RestaurantMapper, Restaurant> implements IRestaurantService {

    @Override
    public Restaurant findByMarkId(Long markId) {
        return this.getOne(new QueryWrapper<Restaurant>().eq("mark_id", markId));
    }

    public RestaurantDTO findByMarkIdAsDTO(Long markId) {
        Restaurant restaurant = this.getOne(new QueryWrapper<Restaurant>().eq("mark_id", markId));
        if (restaurant == null) {
            return null;
        }
        return RestaurantDTO.fromEntity(restaurant);
    }
}
