package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.Reward;
import com.massive.discover.web.model.response.GetMyRewardsResponse;
import com.massive.discover.web.model.response.GetRewardsResponse;

public interface IRewardService extends IService<Reward> {

    GetRewardsResponse getEventReward(Long eventId);

    boolean buyReward(Long rewardId, Long userId);

    GetMyRewardsResponse getMyRewards(Long userId);

    boolean exchangeReward(Long userRewardId);
}

