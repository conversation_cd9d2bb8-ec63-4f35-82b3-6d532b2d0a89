package com.massive.discover.web.model.response;

import com.massive.discover.common.entity.Game;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public final class GetGamesResponse implements Serializable {

    List<Game> gameList;
}
