package com.massive.discover.web.controller;

import com.massive.discover.common.annotation.IgnoreAuth;
import com.massive.discover.common.entity.Event;
import com.massive.discover.common.model.Result;
import com.massive.discover.web.model.request.GetEventInfoRequest;
import com.massive.discover.web.model.response.EventInitResponse;
import com.massive.discover.web.service.IEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Min;

@RestController
@Api(tags = "event")
@RequestMapping("/event")
public class EventController {

    @Resource
    private IEventService eventService;

    @PostMapping("/v1/event")
    @ApiOperation(value = "event", notes = "event", authorizations = {@Authorization(value = "bearerAuth")})
    @IgnoreAuth
    public Result<EventInitResponse> getEvent(@RequestBody GetEventInfoRequest getEventInfoRequest) {
        return Result.success(eventService.getEvent(getEventInfoRequest));
    }
}
