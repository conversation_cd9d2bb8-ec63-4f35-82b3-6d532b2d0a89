package com.massive.discover.web.model.dto;

import com.massive.discover.common.entity.Area;
import com.massive.discover.common.entity.Exhibitor;
import com.massive.discover.common.entity.Mark;
import com.massive.discover.common.entity.MarkStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ExhibitorDTO {

    @NonNull
    private Long eventId;

    @NonNull
    private Long id;

    private String name;

    private String thumbnailUrl;

    private String description;

    private Long areaId;

    private String areaName;

    private Long markId;

    private String markName;

    @NonNull
    private Integer xAxis;

    @NonNull
    private Integer yAxis;

    private boolean gotScore;

    private boolean collected;

    private List<String> tags;

    private String websiteUrl;

    public static ExhibitorDTO fromEntity(@NonNull Exhibitor exhibitor,
                                          @NonNull Mark mark,
                                          @NonNull MarkStatus markStatus,
                                          @NonNull Area area) {
        return ExhibitorDTO.builder()
                .id(exhibitor.getId())
                .name(exhibitor.getName())
                .thumbnailUrl(exhibitor.getThumbnailUrl())
                .description(exhibitor.getDescription())
                .eventId(mark.getEventId())
                .markId(mark.getId())
                .xAxis(mark.getXAxis())
                .yAxis(mark.getYAxis())
                .areaId(area.getId())
                .areaName(area.getName())
                .gotScore(mark.getMarkScore() > 0 && markStatus.isGotScore())
                .collected(markStatus.isCollected())
                // Convert comma-separated tags string to List<String>
                .tags(exhibitor.getTags() != null && !exhibitor.getTags().trim().isEmpty() ?
                    Arrays.asList(exhibitor.getTags().split(",")) :
                    Collections.emptyList())
                .websiteUrl(exhibitor.getWebsiteUrl())
                .build();
    }
}
