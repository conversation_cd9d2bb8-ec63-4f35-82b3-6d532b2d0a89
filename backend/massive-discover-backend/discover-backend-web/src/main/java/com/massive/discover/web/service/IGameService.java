package com.massive.discover.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.massive.discover.common.entity.Game;
import com.massive.discover.web.model.dto.GameDetailDTO;
import com.massive.discover.web.model.response.GetGamesResponse;

import java.util.List;


public interface IGameService extends IService<Game> {

    GetGamesResponse getGameList(Long eventId);

    List<Game> getGamesById(List<Long> gameIds);

    Game getGameByMarkId(Long markId);

    GameDetailDTO getGameDetailByMarkId(Long markId);

}

