package com.massive.discover.web.controller;

import com.github.pagehelper.PageInfo;
import com.massive.discover.common.annotation.LoginUser;
import com.massive.discover.common.model.Result;
import com.massive.discover.user.model.UserCacheInfo;
import com.massive.discover.web.model.dto.MarkDTO;
import com.massive.discover.web.service.IExhibitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Authorization;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@Api(tags = "exhibitor")
@RequestMapping(value = "/exhibitor", produces = "application/json")
public class ExhibitorController {

    @Resource
    private IExhibitorService exhibitorService;

    @GetMapping("/v1/search")
    @ApiOperation(value = "分页获取Exhibitor列表", notes = "getExhibitorPage", authorizations = {@Authorization(value = "bearerAuth")})
    public Result<PageInfo<MarkDTO>> getExhibitorPage(int pageNum, int pageSize, String word, @LoginUser UserCacheInfo loginUserInfo) {
        return Result.success(exhibitorService.search(pageNum, pageSize, word, loginUserInfo.getId()));
    }
}
