package com.massive.discover.web.model.response;

import com.massive.discover.common.entity.Event;
import com.massive.discover.common.entity.EventMap;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class EventInitResponse implements Serializable {

    private Event eventInfo;

    private EventMap map;

    private Integer countdown;
}
