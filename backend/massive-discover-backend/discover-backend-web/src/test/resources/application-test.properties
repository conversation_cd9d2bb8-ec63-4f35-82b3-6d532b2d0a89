# Test configuration for Discover Service
springfox.documentation.swagger.v2.path=/api-docs
server.port=0
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.jackson.serialization.WRITE_DATES_AS_TIMESTAMPS=false
spring.jackson.date-format=com.massive.discover.common.format.RFC3339DateFormat

# Spring Boot Actuator Configuration for Testing
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=always
management.health.redis.enabled=false
management.health.db.enabled=false

# Disable database for testing
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# Disable Redis for testing
spring.data.redis.repositories.enabled=false

# Disable actable for testing
actable.table.auto=none

# Logging for testing
logging.level.com.massive.discover=INFO
logging.level.org.springframework.web=INFO
