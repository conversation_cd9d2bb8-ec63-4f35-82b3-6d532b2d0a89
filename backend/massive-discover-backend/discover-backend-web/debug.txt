[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.massive:discover-backend-web:jar:0.0.1-SNAPSHOT
[WARNING] 'version' contains an expression but should be a constant. @ com.massive:discover-backend-web:${discover-backend-web-version}, /Users/<USER>/dev/open-portal-expo/backend/massive-discover-backend/discover-backend-web/pom.xml, line 14, column 14
[WARNING] 'dependencyManagement.dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: software.amazon.awssdk:bom:pom -> version 2.16.60 vs ${aws.java.sdk.version} @ com.massive:massive-discover-backend:0.0.1-SNAPSHOT, /Users/<USER>/dev/open-portal-expo/backend/massive-discover-backend/pom.xml, line 231, column 25
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] 
[INFO] ------------------< com.massive:discover-backend-web >------------------
[INFO] Building discover-backend-web 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] Parameter 'systemPropertyVariables' is unknown for plugin 'maven-compiler-plugin:3.8.1:compile (default-compile)'
[WARNING] Parameter 'systemPropertyVariables' is unknown for plugin 'maven-compiler-plugin:3.8.1:testCompile (default-testCompile)'
[INFO] 
[INFO] >>> spring-boot:2.5.5:run (default-cli) > test-compile @ discover-backend-web >>>
[INFO] 
[INFO] --- resources:3.2.0:resources (default-resources) @ discover-backend-web ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] Copying 76 resources
[INFO] Copying 2 resources
[INFO] 
[INFO] --- compiler:3.8.1:compile (default-compile) @ discover-backend-web ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] --- resources:3.2.0:testResources (default-testResources) @ discover-backend-web ---
[INFO] Using 'UTF-8' encoding to copy filtered resources.
[INFO] Using 'UTF-8' encoding to copy filtered properties files.
[INFO] skip non existing resourceDirectory /Users/<USER>/dev/open-portal-expo/backend/massive-discover-backend/discover-backend-web/src/test/resources
[INFO] 
[INFO] --- compiler:3.8.1:testCompile (default-testCompile) @ discover-backend-web ---
[INFO] Nothing to compile - all classes are up to date
[INFO] 
[INFO] <<< spring-boot:2.5.5:run (default-cli) < test-compile @ discover-backend-web <<<
[INFO] 
[INFO] 
[INFO] --- spring-boot:2.5.5:run (default-cli) @ discover-backend-web ---
[INFO] Attaching agents: []

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::                (v2.6.7)

2025-06-19 13:06:37.012  INFO 45197 --- [           main] c.m.d.web.MassiveDiscoverWebApplication  : Starting MassiveDiscoverWebApplication using Java 11.0.27 on Ahmads-MacBook-Air.local with PID 45197 (/Users/<USER>/dev/open-portal-expo/backend/massive-discover-backend/discover-backend-web/target/classes started by rafazafar in /Users/<USER>/dev/open-portal-expo/backend/massive-discover-backend/discover-backend-web)
2025-06-19 13:06:37.012  INFO 45197 --- [           main] c.m.d.web.MassiveDiscoverWebApplication  : No active profile set, falling back to 1 default profile: "default"
2025-06-19 13:06:37.337  INFO 45197 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-19 13:06:37.338  INFO 45197 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-19 13:06:37.350  INFO 45197 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-19 13:06:37.557  INFO 45197 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8082 (http)
2025-06-19 13:06:37.560  INFO 45197 --- [           main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-06-19 13:06:37.560  INFO 45197 --- [           main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.62]
2025-06-19 13:06:37.612  INFO 45197 --- [           main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-06-19 13:06:37.612  INFO 45197 --- [           main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 586 ms
Logging initialized using 'class org.apache.ibatis.logging.stdout.StdOutImpl' adapter.
Registered plugin: 'com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor@349312d5'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/gitee/sunchenbin/mybatis/actable/mybatis-enhance-actable/1.5.0.RELEASE/mybatis-enhance-actable-1.5.0.RELEASE.jar!/com/gitee/sunchenbin/mybatis/actable/mapping/common/BaseCRUDMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/gitee/sunchenbin/mybatis/actable/mybatis-enhance-actable/1.5.0.RELEASE/mybatis-enhance-actable-1.5.0.RELEASE.jar!/com/gitee/sunchenbin/mybatis/actable/mapping/common/BaseMysqlCRUDMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/gitee/sunchenbin/mybatis/actable/mybatis-enhance-actable/1.5.0.RELEASE/mybatis-enhance-actable-1.5.0.RELEASE.jar!/com/gitee/sunchenbin/mybatis/actable/mapping/system/CreateMysqlTablesMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/RestaurantMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/PersonalInfoMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/ScoreRecordMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UserProfileImgMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SysPermMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SysUserMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UploadFileMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SysRoleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/AreaMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/RewardMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SystemSettingsMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UserScoreMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SysLoginLogMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SysRolePermMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/MarkStatusMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UserLoginDeviceMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UserMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/TicketMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/EventMapMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/HotelMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/SysUserRoleMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UserRewardMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/ShopMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/GameRecordMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/BulletinMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/OtherMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/GameMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/EventMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/MarkMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/RewardImageMapper.xml]'
Parsed mapper file: 'URL [jar:file:/Users/<USER>/.m2/repository/com/massive/discover-backend-common/0.0.1-SNAPSHOT/discover-backend-common-0.0.1-SNAPSHOT.jar!/mapper/UserLoginMapper.xml]'
 _ _   |_  _ _|_. ___ _ |    _ 
| | |\/|_)(_| | |_\  |_)||_|_\ 
     /               |         
                        ******* 
2025-06-19 13:06:38.226  INFO 45197 --- [           main] c.g.s.m.a.m.handler.StartUpHandlerImpl   : databaseType=mysql，开始执行mysql的处理方法
2025-06-19 13:06:38.230  INFO 45197 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-06-19 13:06:38.807  INFO 45197 --- [           main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
Creating a new SqlSession
Registering transaction synchronization for SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
JDBC Connection [HikariProxyConnection@298711368 wrapping com.mysql.cj.jdbc.ConnectionImpl@523f3c29] will be managed by Spring
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: sys_user(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, sys_user, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 1, 2025-06-19 04:02:39, null, null, utf8mb4_0900_ai_ci, null, , 系统用户
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: sys_user(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, sys_user, avatar, 7, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, is_lock, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 1, null, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, nick, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, pwd, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, salt, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, tel, 6, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, uname, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user, user_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 11
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: sys_user(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: sys_user_role(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, sys_user_role, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 1, 2025-06-19 04:02:42, null, null, utf8mb4_0900_ai_ci, null, , 用户角色
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: sys_user_role(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, sys_user_role, create_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user_role, id, 2, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user_role, role_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user_role, update_at, 3, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_user_role, user_id, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 5
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: sys_user_role(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_personalinfo(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_personalinfo, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 16384, 0, 1, 2025-06-19 04:02:41, null, null, utf8mb4_0900_ai_ci, null, , 用户个人信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_personalinfo(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_personalinfo, address, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, area, 5, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, city, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, country, 6, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, create_at, 12, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, id, 10, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, identify_status, 9, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, name, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, phone, 8, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, postcode, 7, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, province, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, update_at, 11, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_personalinfo, user_id, 13, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 13
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_personalinfo(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_phone
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_area(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_area, BASE TABLE, InnoDB, 10, Dynamic, 8, 2048, 16384, 0, 0, 0, 75, 2025-06-19 04:02:41, 2025-06-18 10:48:27, null, utf8mb4_0900_ai_ci, null, , t_area
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_area(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_area, create_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, event_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, height, 4, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, id, 7, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, update_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, user_id, 10, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, width, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, x_axis, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_area, y_axis, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==      Total: 10
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_area(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_send_verify_code_record(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_send_verify_code_record, BASE TABLE, InnoDB, 10, Dynamic, 7, 2340, 16384, 0, 0, 0, 15, 2025-06-19 04:02:38, 2025-06-18 10:39:03, null, utf8mb4_0900_ai_ci, null, , 手机发送记录
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_send_verify_code_record(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_send_verify_code_record, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, error_info, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, phone_num, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, send_info, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, send_type, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_verify_code_record, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 8
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_send_verify_code_record(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_shop(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_shop, BASE TABLE, InnoDB, 10, Dynamic, 11, 1489, 16384, 0, 16384, 0, 12, 2025-06-19 04:02:38, 2025-06-17 02:38:17, null, utf8mb4_0900_ai_ci, null, , t_shop
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_shop(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_shop, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, thumbnail_url, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_shop, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 8
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_shop(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_mark_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: sys_login_log(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, sys_login_log, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 1, 2025-06-19 04:02:41, null, null, utf8mb4_0900_ai_ci, null, , 系统登录日志
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: sys_login_log(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, sys_login_log, create_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, id, 7, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, login_ip, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, login_result, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, remark, 5, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, update_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, user_agent, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, user_id, 10, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, user_name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_login_log, user_type, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==      Total: 10
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: sys_login_log(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: sys_perm(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, sys_perm, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 16384, 0, 1, 2025-06-19 04:02:38, null, null, utf8mb4_0900_ai_ci, null, , 系统权限
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: sys_perm(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, sys_perm, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, icon, 7, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, leaf, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 1, null, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, parent_id, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, pname, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, ptype, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 权限类型：1.菜单; 2.api; 3.按钮; 4.数据, <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, pval, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, ranking, 4, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_perm, user_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 11
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: sys_perm(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_pval
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_reward(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_reward, BASE TABLE, InnoDB, 10, Dynamic, 22, 744, 16384, 0, 0, 0, 23, 2025-06-19 04:02:40, 2025-06-15 09:15:07, null, utf8mb4_0900_ai_ci, null, , t_reward
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_reward(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_reward, bought, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, create_at, 11, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, description, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, detail_description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, event_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, id, 9, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, image_url, 5, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, inventory, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, point_price, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, update_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward, user_id, 12, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 12
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_reward(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_game_record(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_game_record, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 1, 2025-06-19 04:02:40, null, null, utf8mb4_0900_ai_ci, null, , t_game_record
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_game_record(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_game_record, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, finished_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, game_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, game_level, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, game_name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, is_public, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, mark_id, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, score, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game_record, user_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 11
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_game_record(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_user_reward(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_user_reward, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 3, 2025-06-19 04:02:38, 2025-06-18 08:30:35, null, utf8mb4_0900_ai_ci, null, , t_user_reward
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_user_reward(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_user_reward, create_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_reward, exchanged_at, 2, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_reward, id, 3, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_reward, reward_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_reward, update_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_reward, user_id, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 6
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_user_reward(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_system_settings(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_system_settings, BASE TABLE, InnoDB, 10, Dynamic, 2, 8192, 16384, 0, 0, 0, 7, 2025-06-19 04:02:41, 2025-06-18 12:20:43, null, utf8mb4_0900_ai_ci, null, , 用户系统设置信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_system_settings(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_system_settings, create_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_system_settings, id, 2, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_system_settings, push_notification_accept, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_system_settings, update_at, 3, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_system_settings, user_id, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 5
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_system_settings(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_send_info(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_send_info, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, null, 2025-06-19 04:02:38, null, null, utf8mb4_0900_ai_ci, null, , 手机发送信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_send_info(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_send_info, count, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_info, fee, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_send_info, sid, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==      Total: 3
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_send_info(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_reward_image(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_reward_image, BASE TABLE, InnoDB, 10, Dynamic, 54, 303, 16384, 0, 0, 0, 55, 2025-06-19 04:02:41, 2025-06-15 09:15:07, null, utf8mb4_0900_ai_ci, null, , t_reward_image
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_reward_image(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_reward_image, create_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward_image, id, 3, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward_image, image_url, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward_image, reward_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward_image, update_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_reward_image, user_id, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 6
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_reward_image(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_mark(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_mark, BASE TABLE, InnoDB, 10, Dynamic, 37, 442, 16384, 0, 0, 0, 144, 2025-06-19 04:02:41, 2025-06-17 05:15:57, null, utf8mb4_0900_ai_ci, null, , t_mark
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_mark(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_mark, area_id, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, event_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, mark_score, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, mark_type, 4, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, user_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, x_axis, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark, y_axis, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==      Total: 11
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_mark(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_user_profile_img(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_user_profile_img, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 7, 2025-06-19 04:02:41, 2025-06-18 12:20:43, null, utf8mb4_0900_ai_ci, null, , 用户头像列表
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_user_profile_img(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_user_profile_img, create_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_profile_img, file_name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_profile_img, id, 4, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_profile_img, profile_img, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_profile_img, status, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_profile_img, update_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_profile_img, user_id, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 7
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_user_profile_img(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_upload_file(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_upload_file, BASE TABLE, InnoDB, 10, Dynamic, 40, 409, 16384, 0, 32768, 0, 41, 2025-06-19 04:02:39, 2025-06-15 09:15:08, null, utf8mb4_0900_ai_ci, null, , 上传文件信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_upload_file(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_upload_file, content_type, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, file_key, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, file_name, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, file_size, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, media_type, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 3, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 1 video, 2 image, 3 other, <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, signed_url, 5, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, upload_state, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_upload_file, user_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 11
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_upload_file(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_file_key
<==        Row: idx_uni_signed_url
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_ticket(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_ticket, BASE TABLE, InnoDB, 10, Dynamic, 80, 204, 16384, 0, 16384, 0, 81, 2025-06-19 04:02:39, 2025-06-15 09:15:08, null, utf8mb4_0900_ai_ci, null, , t_ticket
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_ticket(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_ticket, bound_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, checked_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, code, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, create_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, event_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, MUL, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, id, 6, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, update_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_ticket, user_id, 9, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 9
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_ticket(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_eventId_code
<==        Row: idx_uni_eventId_code
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_bulletin(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_bulletin, BASE TABLE, InnoDB, 10, Dynamic, 25, 655, 16384, 0, 0, 0, 26, 2025-06-19 04:02:40, 2025-06-15 09:15:05, null, utf8mb4_0900_ai_ci, null, , t_bulletin
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_bulletin(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_bulletin, brief, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, create_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, event_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, id, 7, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, page_url, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, publish_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, published, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 1, null, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, title, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, update_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_bulletin, user_id, 10, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 10
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_bulletin(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_user(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_user, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 8, 2025-06-19 04:02:39, 2025-06-18 12:52:08, null, utf8mb4_0900_ai_ci, null, , 用户信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_user(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_user, address_list_id, 16, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, admin_level_id, 18, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, auth_token, 20, <<BLOB>>, YES, <<BLOB>>, 65535, 65535, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, birthday, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, blacklist_id, 17, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, coupon_collector_id, 10, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, create_at, 3, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, gender, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, id, 1, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, interest_list_id, 15, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, location, 7, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, nickname, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, permission_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, personal_info_id, 13, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, point, 9, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, shop_id, 12, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, signature, 8, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, status, 19, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, system_setting_id, 14, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user, update_at, 2, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==      Total: 20
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_user(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_event_map(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_event_map, BASE TABLE, InnoDB, 10, Dynamic, 1, 16384, 16384, 0, 0, 0, 13, 2025-06-19 04:02:42, 2025-06-18 10:56:07, null, utf8mb4_0900_ai_ci, null, , t_event_map
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_event_map(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_event_map, create_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, description, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, event_id, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, id, 7, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, map_height, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, map_url, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, map_width, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, name, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, update_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event_map, user_id, 10, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 10
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_event_map(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_user_score(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_user_score, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 7, 2025-06-19 04:02:40, 2025-06-18 12:20:43, null, utf8mb4_0900_ai_ci, null, , 用户积分表
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_user_score(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_user_score, create_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_score, id, 3, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_score, ranking_score, 2, <<BLOB>>, NO, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_score, total_score, 1, <<BLOB>>, NO, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_score, update_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_score, user_id, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 6
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_user_score(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_restaurant(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_restaurant, BASE TABLE, InnoDB, 10, Dynamic, 6, 2730, 16384, 0, 16384, 0, 15, 2025-06-19 04:02:40, 2025-06-15 09:15:07, null, utf8mb4_0900_ai_ci, null, , t_restaurant
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_restaurant(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_restaurant, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, thumbnail_url, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_restaurant, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 8
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_restaurant(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_mark_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_event(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_event, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 16384, 0, 7, 2025-06-19 04:02:42, 2025-06-18 10:49:16, null, utf8mb4_0900_ai_ci, null, , t_event
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_event(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_event, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, description, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, end_date, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, name, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, start_date, 3, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_event, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 8
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_event(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_name
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_other(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_other, BASE TABLE, InnoDB, 10, Dynamic, 24, 682, 16384, 0, 16384, 0, 27, 2025-06-19 04:02:38, 2025-06-15 09:15:09, null, utf8mb4_0900_ai_ci, null, , t_other
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_other(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_other, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, thumbnail_url, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_other, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 8
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_other(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_mark_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_hotel(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_hotel, BASE TABLE, InnoDB, 10, Dynamic, 7, 2340, 16384, 0, 16384, 0, 16, 2025-06-19 04:02:42, 2025-06-15 09:15:06, null, utf8mb4_0900_ai_ci, null, , t_hotel
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_hotel(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_hotel, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, thumbnail_url, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_hotel, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 8
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_hotel(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_mark_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_game(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_game, BASE TABLE, InnoDB, 10, Dynamic, 8, 2048, 16384, 0, 16384, 0, 47, 2025-06-19 04:02:37, 2025-06-17 05:12:26, null, utf8mb4_0900_ai_ci, null, , t_game
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_game(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_game, area_id, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, description, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, event_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, game_level, 5, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, game_url, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, mark_id, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_game, user_id, 11, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 11
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_game(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_mark_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: sys_role(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, sys_role, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 16384, 0, 1, 2025-06-19 04:02:39, null, null, utf8mb4_0900_ai_ci, null, , 权限角色
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: sys_role(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, sys_role, create_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role, id, 4, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role, rdesc, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role, rname, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role, rval, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role, update_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role, user_id, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 7
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: sys_role(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_rval
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_user_device(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_user_device, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 1, 2025-06-19 04:02:39, null, null, utf8mb4_0900_ai_ci, null, , 设备信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_user_device(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_user_device, create_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_device, device_id, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_device, device_type, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, device type, 1 iOS 2 android 3 other, <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_device, id, 3, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_device, update_at, 4, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_device, user_id, 6, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 6
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_user_device(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_mark_status(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_mark_status, BASE TABLE, InnoDB, 10, Dynamic, 29, 564, 16384, 0, 16384, 0, 104, 2025-06-19 04:02:38, 2025-06-18 12:50:37, null, utf8mb4_0900_ai_ci, null, , t_mark_status
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_mark_status(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_mark_status, collected, 4, <<BLOB>>, YES, <<BLOB>>, null, null, 1, null, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark_status, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark_status, got_score, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 1, null, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark_status, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark_status, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, MUL, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark_status, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_mark_status, user_id, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==      Total: 7
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_mark_status(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_markId_userId
<==        Row: idx_uni_markId_userId
<==      Total: 2
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_user_login(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_user_login, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 16384, 0, 7, 2025-06-19 04:02:42, 2025-06-18 12:20:43, null, utf8mb4_0900_ai_ci, null, , 登录信息
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_user_login(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_user_login, create_at, 10, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, email, 6, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, id, 8, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, is_third_party, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, nickname, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, phone, 7, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, third_party_id, 5, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, third_party_type, 4, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, update_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_user_login, user_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==      Total: 10
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_user_login(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_third_party_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_show(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_show, BASE TABLE, InnoDB, 10, Dynamic, 9, 1820, 16384, 0, 0, 0, 11, 2025-06-19 04:02:38, 2025-06-17 05:16:11, null, utf8mb4_0900_ai_ci, null, , t_show
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_show(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_show, begin_time, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, create_at, 9, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, end_time, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, id, 7, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, thumbnail_url, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, update_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_show, user_id, 10, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 10
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_show(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_exhibitor(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_exhibitor, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, null, 2025-06-19 04:06:28, null, null, utf8mb4_0900_ai_ci, null, , t_exhibitor
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_exhibitor(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_exhibitor, create_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, description, 4, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, id, 5, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, mark_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, UNI, , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, name, 2, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, tags, 9, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, thumbnail_url, 3, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, update_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_exhibitor, user_id, 8, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 9
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_exhibitor(String)
<==    Columns: INDEX_NAME
<==        Row: idx_uni_mark_id
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: t_score_record(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, t_score_record, BASE TABLE, InnoDB, 10, Dynamic, 4, 4096, 16384, 0, 0, 0, 9, 2025-06-19 04:02:42, 2025-06-18 12:20:43, null, utf8mb4_0900_ai_ci, null, , 积分记录表
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: t_score_record(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, t_score_record, add_or_subtract, 4, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, create_at, 8, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, id, 6, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, score, 1, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, score_source, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, source_id, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, source_name, 5, <<BLOB>>, YES, <<BLOB>>, 255, 1020, null, null, null, utf8mb4, utf8mb4_0900_ai_ci, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, update_at, 7, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, t_score_record, user_id, 9, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 9
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: t_score_record(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.tables where table_name = ? and table_schema = (select database())
==> Parameters: sys_role_perm(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, TABLE_TYPE, ENGINE, VERSION, ROW_FORMAT, TABLE_ROWS, AVG_ROW_LENGTH, DATA_LENGTH, MAX_DATA_LENGTH, INDEX_LENGTH, DATA_FREE, AUTO_INCREMENT, CREATE_TIME, UPDATE_TIME, CHECK_TIME, TABLE_COLLATION, CHECKSUM, CREATE_OPTIONS, TABLE_COMMENT
<==        Row: def, open_portal_expo, sys_role_perm, BASE TABLE, InnoDB, 10, Dynamic, 0, 0, 16384, 0, 0, 0, 1, 2025-06-19 04:02:42, null, null, utf8mb4_0900_ai_ci, null, , 角色权限
<==      Total: 1
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select * from information_schema.columns where table_name = ? and table_schema = (select database())
==> Parameters: sys_role_perm(String)
<==    Columns: TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME, ORDINAL_POSITION, COLUMN_DEFAULT, IS_NULLABLE, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, CHARACTER_OCTET_LENGTH, NUMERIC_PRECISION, NUMERIC_SCALE, DATETIME_PRECISION, CHARACTER_SET_NAME, COLLATION_NAME, COLUMN_TYPE, COLUMN_KEY, EXTRA, PRIVILEGES, COLUMN_COMMENT, GENERATION_EXPRESSION, SRS_ID
<==        Row: def, open_portal_expo, sys_role_perm, create_at, 6, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , DEFAULT_GENERATED, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role_perm, id, 4, <<BLOB>>, NO, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, PRI, auto_increment, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role_perm, perm_id, 2, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role_perm, perm_type, 3, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 权限类型：1.菜单；2.api 3.数据, <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role_perm, role_id, 1, <<BLOB>>, YES, <<BLOB>>, null, null, 10, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role_perm, update_at, 5, <<BLOB>>, YES, <<BLOB>>, null, null, null, null, 0, null, null, <<BLOB>>, , on update CURRENT_TIMESTAMP, select,insert,update,references, , <<BLOB>>, null
<==        Row: def, open_portal_expo, sys_role_perm, user_id, 7, <<BLOB>>, YES, <<BLOB>>, null, null, 19, 0, null, null, null, <<BLOB>>, , , select,insert,update,references, 用户ID, <<BLOB>>, null
<==      Total: 7
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: select index_name from information_schema.statistics where table_name = ? and table_schema = (select database()) and lower(index_name) !='primary' and ( index_name like 'idx_uni_%' or index_name like 'idx_%')
==> Parameters: sys_role_perm(String)
<==      Total: 0
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:39.943  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始删除表t_exhibitor中的字段tags
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_exhibitor` drop `tags`
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:39.987  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成删除表t_exhibitor中的字段tags
2025-06-19 13:06:39.987  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段area_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `area_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.050  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段area_id
2025-06-19 13:06:40.050  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.079  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段event_id
2025-06-19 13:06:40.079  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段game_level
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `game_level` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.117  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段game_level
2025-06-19 13:06:40.117  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.145  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段id
2025-06-19 13:06:40.145  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.176  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段mark_id
2025-06-19 13:06:40.176  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.207  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段update_at
2025-06-19 13:06:40.208  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.237  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game中的字段user_id
2025-06-19 13:06:40.237  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_shop中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_shop` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.265  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_shop中的字段id
2025-06-19 13:06:40.265  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_shop中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_shop` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.296  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_shop中的字段mark_id
2025-06-19 13:06:40.296  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_shop中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_shop` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.326  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_shop中的字段update_at
2025-06-19 13:06:40.326  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_shop中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_shop` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.355  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_shop中的字段user_id
2025-06-19 13:06:40.356  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_send_verify_code_record中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_send_verify_code_record` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.387  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_send_verify_code_record中的字段id
2025-06-19 13:06:40.388  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_send_verify_code_record中的字段send_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_send_verify_code_record` modify `send_type` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.417  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_send_verify_code_record中的字段send_type
2025-06-19 13:06:40.418  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_send_verify_code_record中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_send_verify_code_record` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.446  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_send_verify_code_record中的字段update_at
2025-06-19 13:06:40.447  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_send_verify_code_record中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_send_verify_code_record` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.478  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_send_verify_code_record中的字段user_id
2025-06-19 13:06:40.479  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_perm中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_perm` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.509  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_perm中的字段id
2025-06-19 13:06:40.509  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_perm中的字段parent_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_perm` modify `parent_id` int(11) NULL DEFAULT 0
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.538  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_perm中的字段parent_id
2025-06-19 13:06:40.539  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_perm中的字段ptype
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_perm` modify `ptype` int(11) NULL COMMENT ?
==> Parameters: 权限类型：1.菜单; 2.api; 3.按钮; 4.数据(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.569  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_perm中的字段ptype
2025-06-19 13:06:40.569  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_perm中的字段ranking
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_perm` modify `ranking` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.607  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_perm中的字段ranking
2025-06-19 13:06:40.607  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_perm中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_perm` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.637  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_perm中的字段update_at
2025-06-19 13:06:40.638  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_perm中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_perm` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.668  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_perm中的字段user_id
2025-06-19 13:06:40.668  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_show中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_show` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.701  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_show中的字段id
2025-06-19 13:06:40.702  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_show中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_show` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.733  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_show中的字段update_at
2025-06-19 13:06:40.733  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_show中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_show` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.763  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_show中的字段user_id
2025-06-19 13:06:40.763  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_reward中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_reward` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.793  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_reward中的字段id
2025-06-19 13:06:40.793  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_reward中的字段reward_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_reward` modify `reward_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.822  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_reward中的字段reward_id
2025-06-19 13:06:40.822  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_reward中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_reward` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.851  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_reward中的字段update_at
2025-06-19 13:06:40.851  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_reward中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_reward` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.879  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_reward中的字段user_id
2025-06-19 13:06:40.880  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_exhibitor中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_exhibitor` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.909  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_exhibitor中的字段id
2025-06-19 13:06:40.910  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_exhibitor中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_exhibitor` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.941  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_exhibitor中的字段mark_id
2025-06-19 13:06:40.941  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_exhibitor中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_exhibitor` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:40.969  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_exhibitor中的字段update_at
2025-06-19 13:06:40.969  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_exhibitor中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_exhibitor` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.001  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_exhibitor中的字段user_id
2025-06-19 13:06:41.001  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_other中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_other` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.031  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_other中的字段id
2025-06-19 13:06:41.031  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_other中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_other` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.061  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_other中的字段mark_id
2025-06-19 13:06:41.061  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_other中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_other` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.091  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_other中的字段update_at
2025-06-19 13:06:41.092  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_other中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_other` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.122  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_other中的字段user_id
2025-06-19 13:06:41.122  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark_status中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark_status` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.153  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark_status中的字段id
2025-06-19 13:06:41.154  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark_status中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark_status` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.191  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark_status中的字段mark_id
2025-06-19 13:06:41.191  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark_status中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark_status` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.222  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark_status中的字段update_at
2025-06-19 13:06:41.222  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark_status中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark_status` modify `user_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.252  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark_status中的字段user_id
2025-06-19 13:06:41.252  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_send_info中的字段count
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_send_info` modify `count` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.280  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_send_info中的字段count
2025-06-19 13:06:41.280  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.309  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role中的字段id
2025-06-19 13:06:41.309  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.340  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role中的字段update_at
2025-06-19 13:06:41.341  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.370  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role中的字段user_id
2025-06-19 13:06:41.370  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_upload_file中的字段file_size
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_upload_file` modify `file_size` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.404  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_upload_file中的字段file_size
2025-06-19 13:06:41.404  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_upload_file中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_upload_file` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.435  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_upload_file中的字段id
2025-06-19 13:06:41.436  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_upload_file中的字段media_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_upload_file` modify `media_type` tinyint(4) NULL COMMENT ?
==> Parameters: 1 video, 2 image, 3 other(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.467  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_upload_file中的字段media_type
2025-06-19 13:06:41.467  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_upload_file中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_upload_file` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.496  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_upload_file中的字段update_at
2025-06-19 13:06:41.496  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_upload_file中的字段upload_state
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_upload_file` modify `upload_state` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.528  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_upload_file中的字段upload_state
2025-06-19 13:06:41.528  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_upload_file中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_upload_file` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.558  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_upload_file中的字段user_id
2025-06-19 13:06:41.558  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_device中的字段device_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_device` modify `device_type` int(11) NULL COMMENT ?
==> Parameters: device type, 1 iOS 2 android 3 other(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.590  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_device中的字段device_type
2025-06-19 13:06:41.590  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_device中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_device` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.619  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_device中的字段id
2025-06-19 13:06:41.620  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_device中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_device` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.649  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_device中的字段update_at
2025-06-19 13:06:41.649  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_device中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_device` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.679  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_device中的字段user_id
2025-06-19 13:06:41.679  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.708  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user中的字段id
2025-06-19 13:06:41.708  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.737  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user中的字段update_at
2025-06-19 13:06:41.737  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.767  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user中的字段user_id
2025-06-19 13:06:41.767  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段address_list_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `address_list_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.796  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段address_list_id
2025-06-19 13:06:41.796  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段admin_level_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `admin_level_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.829  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段admin_level_id
2025-06-19 13:06:41.830  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段blacklist_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `blacklist_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.862  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段blacklist_id
2025-06-19 13:06:41.862  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段coupon_collector_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `coupon_collector_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.891  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段coupon_collector_id
2025-06-19 13:06:41.891  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段gender
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `gender` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.924  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段gender
2025-06-19 13:06:41.924  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.955  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段id
2025-06-19 13:06:41.955  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段interest_list_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `interest_list_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:41.985  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段interest_list_id
2025-06-19 13:06:41.985  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段permission_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `permission_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.016  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段permission_id
2025-06-19 13:06:42.017  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段personal_info_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `personal_info_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.052  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段personal_info_id
2025-06-19 13:06:42.053  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段point
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `point` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.096  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段point
2025-06-19 13:06:42.096  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段shop_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `shop_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.124  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段shop_id
2025-06-19 13:06:42.124  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段status
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `status` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.154  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段status
2025-06-19 13:06:42.154  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段system_setting_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `system_setting_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.185  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段system_setting_id
2025-06-19 13:06:42.185  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.213  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user中的字段update_at
2025-06-19 13:06:42.213  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_ticket中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_ticket` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.245  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_ticket中的字段event_id
2025-06-19 13:06:42.245  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_ticket中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_ticket` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.275  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_ticket中的字段id
2025-06-19 13:06:42.275  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_ticket中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_ticket` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.309  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_ticket中的字段update_at
2025-06-19 13:06:42.309  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_ticket中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_ticket` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.337  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_ticket中的字段user_id
2025-06-19 13:06:42.337  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_bulletin中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_bulletin` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.370  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_bulletin中的字段event_id
2025-06-19 13:06:42.370  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_bulletin中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_bulletin` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.401  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_bulletin中的字段id
2025-06-19 13:06:42.401  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_bulletin中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_bulletin` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.429  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_bulletin中的字段update_at
2025-06-19 13:06:42.429  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_bulletin中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_bulletin` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.463  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_bulletin中的字段user_id
2025-06-19 13:06:42.463  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段bought
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `bought` int(11) NULL DEFAULT 0
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.495  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段bought
2025-06-19 13:06:42.496  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.527  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段event_id
2025-06-19 13:06:42.527  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.559  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段id
2025-06-19 13:06:42.559  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段inventory
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `inventory` int(11) NULL DEFAULT 0
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.586  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段inventory
2025-06-19 13:06:42.586  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段point_price
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `point_price` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.615  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段point_price
2025-06-19 13:06:42.615  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.643  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段update_at
2025-06-19 13:06:42.643  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.671  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward中的字段user_id
2025-06-19 13:06:42.671  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_restaurant中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_restaurant` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.705  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_restaurant中的字段id
2025-06-19 13:06:42.705  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_restaurant中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_restaurant` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.733  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_restaurant中的字段mark_id
2025-06-19 13:06:42.733  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_restaurant中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_restaurant` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.762  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_restaurant中的字段update_at
2025-06-19 13:06:42.762  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_restaurant中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_restaurant` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.791  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_restaurant中的字段user_id
2025-06-19 13:06:42.791  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段game_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `game_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.819  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段game_id
2025-06-19 13:06:42.820  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段game_level
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `game_level` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.849  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段game_level
2025-06-19 13:06:42.849  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.881  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段id
2025-06-19 13:06:42.882  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段is_public
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `is_public` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.914  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段is_public
2025-06-19 13:06:42.914  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.944  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段mark_id
2025-06-19 13:06:42.945  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段score
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `score` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:42.979  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段score
2025-06-19 13:06:42.979  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.014  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段update_at
2025-06-19 13:06:43.014  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_game_record中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_game_record` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.045  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_game_record中的字段user_id
2025-06-19 13:06:43.046  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_score中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_score` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.073  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_score中的字段id
2025-06-19 13:06:43.073  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_score中的字段ranking_score
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_score` modify `ranking_score` int(11) NOT NULL DEFAULT 0
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.104  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_score中的字段ranking_score
2025-06-19 13:06:43.104  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_score中的字段total_score
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_score` modify `total_score` int(11) NOT NULL DEFAULT 0
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.134  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_score中的字段total_score
2025-06-19 13:06:43.134  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_score中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_score` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.162  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_score中的字段update_at
2025-06-19 13:06:43.162  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_score中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_score` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.194  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_score中的字段user_id
2025-06-19 13:06:43.194  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.225  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段event_id
2025-06-19 13:06:43.225  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段height
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `height` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.256  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段height
2025-06-19 13:06:43.257  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.290  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段id
2025-06-19 13:06:43.290  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.320  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段update_at
2025-06-19 13:06:43.321  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.348  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段user_id
2025-06-19 13:06:43.349  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段width
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `width` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.382  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段width
2025-06-19 13:06:43.382  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段x_axis
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `x_axis` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.411  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段x_axis
2025-06-19 13:06:43.411  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_area中的字段y_axis
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_area` modify `y_axis` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.440  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_area中的字段y_axis
2025-06-19 13:06:43.441  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_personalinfo中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_personalinfo` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.474  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_personalinfo中的字段id
2025-06-19 13:06:43.474  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_personalinfo中的字段identify_status
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_personalinfo` modify `identify_status` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.504  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_personalinfo中的字段identify_status
2025-06-19 13:06:43.504  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_personalinfo中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_personalinfo` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.536  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_personalinfo中的字段update_at
2025-06-19 13:06:43.536  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_personalinfo中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_personalinfo` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.568  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_personalinfo中的字段user_id
2025-06-19 13:06:43.569  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward_image中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward_image` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.596  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward_image中的字段id
2025-06-19 13:06:43.596  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward_image中的字段reward_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward_image` modify `reward_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.624  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward_image中的字段reward_id
2025-06-19 13:06:43.625  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward_image中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward_image` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.657  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward_image中的字段update_at
2025-06-19 13:06:43.657  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_reward_image中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_reward_image` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.690  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_reward_image中的字段user_id
2025-06-19 13:06:43.690  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_profile_img中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_profile_img` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.720  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_profile_img中的字段id
2025-06-19 13:06:43.721  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_profile_img中的字段status
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_profile_img` modify `status` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.749  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_profile_img中的字段status
2025-06-19 13:06:43.749  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_profile_img中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_profile_img` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.782  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_profile_img中的字段update_at
2025-06-19 13:06:43.782  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_profile_img中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_profile_img` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.819  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_profile_img中的字段user_id
2025-06-19 13:06:43.819  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_login_log中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_login_log` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.847  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_login_log中的字段id
2025-06-19 13:06:43.847  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_login_log中的字段login_result
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_login_log` modify `login_result` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.875  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_login_log中的字段login_result
2025-06-19 13:06:43.876  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_login_log中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_login_log` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.906  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_login_log中的字段update_at
2025-06-19 13:06:43.906  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_login_log中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_login_log` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.934  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_login_log中的字段user_id
2025-06-19 13:06:43.934  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_login_log中的字段user_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_login_log` modify `user_type` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.965  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_login_log中的字段user_type
2025-06-19 13:06:43.965  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段area_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `area_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:43.996  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段area_id
2025-06-19 13:06:43.996  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.024  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段event_id
2025-06-19 13:06:44.024  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.053  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段id
2025-06-19 13:06:44.053  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段mark_score
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `mark_score` int(11) NULL DEFAULT 0
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.083  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段mark_score
2025-06-19 13:06:44.083  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段mark_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `mark_type` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.112  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段mark_type
2025-06-19 13:06:44.112  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.140  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段update_at
2025-06-19 13:06:44.140  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.171  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段user_id
2025-06-19 13:06:44.171  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段x_axis
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `x_axis` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.204  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段x_axis
2025-06-19 13:06:44.204  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_mark中的字段y_axis
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_mark` modify `y_axis` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.231  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_mark中的字段y_axis
2025-06-19 13:06:44.231  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_system_settings中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_system_settings` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.258  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_system_settings中的字段id
2025-06-19 13:06:44.258  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_system_settings中的字段push_notification_accept
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_system_settings` modify `push_notification_accept` int(11) NULL DEFAULT 1
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.285  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_system_settings中的字段push_notification_accept
2025-06-19 13:06:44.285  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_system_settings中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_system_settings` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.315  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_system_settings中的字段update_at
2025-06-19 13:06:44.316  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_system_settings中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_system_settings` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.347  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_system_settings中的字段user_id
2025-06-19 13:06:44.347  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event_map中的字段event_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event_map` modify `event_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.379  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event_map中的字段event_id
2025-06-19 13:06:44.380  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event_map中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event_map` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.411  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event_map中的字段id
2025-06-19 13:06:44.412  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event_map中的字段map_height
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event_map` modify `map_height` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.449  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event_map中的字段map_height
2025-06-19 13:06:44.449  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event_map中的字段map_width
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event_map` modify `map_width` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.480  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event_map中的字段map_width
2025-06-19 13:06:44.480  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event_map中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event_map` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.512  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event_map中的字段update_at
2025-06-19 13:06:44.512  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event_map中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event_map` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.540  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event_map中的字段user_id
2025-06-19 13:06:44.540  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_login中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_login` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.572  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_login中的字段id
2025-06-19 13:06:44.572  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_login中的字段is_third_party
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_login` modify `is_third_party` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.602  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_login中的字段is_third_party
2025-06-19 13:06:44.602  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_login中的字段third_party_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_login` modify `third_party_type` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.631  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_login中的字段third_party_type
2025-06-19 13:06:44.632  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_login中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_login` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.662  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_login中的字段update_at
2025-06-19 13:06:44.662  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_user_login中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_user_login` modify `user_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.695  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_user_login中的字段user_id
2025-06-19 13:06:44.696  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_score_record中的字段add_or_subtract
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_score_record` modify `add_or_subtract` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.723  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_score_record中的字段add_or_subtract
2025-06-19 13:06:44.723  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_score_record中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_score_record` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.752  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_score_record中的字段id
2025-06-19 13:06:44.752  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_score_record中的字段score_source
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_score_record` modify `score_source` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.782  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_score_record中的字段score_source
2025-06-19 13:06:44.782  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_score_record中的字段source_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_score_record` modify `source_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.812  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_score_record中的字段source_id
2025-06-19 13:06:44.812  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_score_record中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_score_record` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.841  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_score_record中的字段update_at
2025-06-19 13:06:44.841  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_score_record中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_score_record` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.872  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_score_record中的字段user_id
2025-06-19 13:06:44.872  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role_perm中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role_perm` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.902  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role_perm中的字段id
2025-06-19 13:06:44.903  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role_perm中的字段perm_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role_perm` modify `perm_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.932  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role_perm中的字段perm_id
2025-06-19 13:06:44.933  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role_perm中的字段perm_type
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role_perm` modify `perm_type` int(11) NULL COMMENT ?
==> Parameters: 权限类型：1.菜单；2.api 3.数据(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.961  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role_perm中的字段perm_type
2025-06-19 13:06:44.962  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role_perm中的字段role_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role_perm` modify `role_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:44.991  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role_perm中的字段role_id
2025-06-19 13:06:44.992  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role_perm中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role_perm` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.031  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role_perm中的字段update_at
2025-06-19 13:06:45.031  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_role_perm中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_role_perm` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.061  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_role_perm中的字段user_id
2025-06-19 13:06:45.061  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user_role中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user_role` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.090  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user_role中的字段id
2025-06-19 13:06:45.090  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user_role中的字段role_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user_role` modify `role_id` int(11) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.121  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user_role中的字段role_id
2025-06-19 13:06:45.121  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user_role中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user_role` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.150  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user_role中的字段update_at
2025-06-19 13:06:45.151  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表sys_user_role中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `sys_user_role` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.179  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表sys_user_role中的字段user_id
2025-06-19 13:06:45.180  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_hotel中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_hotel` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.208  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_hotel中的字段id
2025-06-19 13:06:45.209  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_hotel中的字段mark_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_hotel` modify `mark_id` bigint(20) NULL
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.242  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_hotel中的字段mark_id
2025-06-19 13:06:45.242  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_hotel中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_hotel` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.274  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_hotel中的字段update_at
2025-06-19 13:06:45.274  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_hotel中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_hotel` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.304  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_hotel中的字段user_id
2025-06-19 13:06:45.305  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event中的字段id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event` modify `id` bigint(20) NOT NULL AUTO_INCREMENT
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.338  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event中的字段id
2025-06-19 13:06:45.339  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event中的字段update_at
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event` modify `update_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
==> Parameters: 
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.367  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event中的字段update_at
2025-06-19 13:06:45.367  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 开始修改表t_event中的字段user_id
Fetched SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9] from current transaction
==>  Preparing: alter table `t_event` modify `user_id` bigint(20) NULL COMMENT ?
==> Parameters: 用户ID(String)
Releasing transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.399  INFO 45197 --- [           main] s.m.a.m.s.SysMysqlCreateTableManagerImpl : 完成修改表t_event中的字段user_id
Transaction synchronization committing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Transaction synchronization deregistering SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
Transaction synchronization closing SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5cc152f9]
2025-06-19 13:06:45.599  INFO 45197 --- [           main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]


,------.                           ,--.  ,--.         ,--.                         
|  .--. '  ,--,--.  ,---.   ,---.  |  '--'  |  ,---.  |  |  ,---.   ,---.  ,--.--. 
|  '--' | ' ,-.  | | .-. | | .-. : |  .--.  | | .-. : |  | | .-. | | .-. : |  .--' 
|  | --'  \ '-'  | ' '-' ' \   --. |  |  |  | \   --. |  | | '-' ' \   --. |  |    
`--'       `--`--' .`-  /   `----' `--'  `--'  `----' `--' |  |-'   `----' `--'    
                   `---'                                   `--'                        is intercepting.

2025-06-19 13:06:45.754  INFO 45197 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8082 (http) with context path ''
2025-06-19 13:06:45.754  INFO 45197 --- [           main] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-06-19 13:06:45.759  INFO 45197 --- [           main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-06-19 13:06:45.771  INFO 45197 --- [           main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-06-19 13:06:45.853  INFO 45197 --- [           main] c.m.d.web.MassiveDiscoverWebApplication  : Started MassiveDiscoverWebApplication in 8.991 seconds (JVM running for 9.169)
2025-06-19 13:06:57.592  INFO 45197 --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-06-19 13:06:57.680  INFO 45197 --- [ionShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  22.099 s
[INFO] Finished at: 2025-06-19T13:06:57+09:00
[INFO] ------------------------------------------------------------------------
