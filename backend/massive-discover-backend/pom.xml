<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.6.7</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <modules>
        <module>discover-backend-common</module>
        <module>discover-backend-web</module>
        <module>discover-backend-operations</module>
        <module>discover-backend-user</module>
        <module>discover-backend-feed</module>
        <module>discover-backend-redis-lock</module>
    </modules>

    <groupId>com.massive</groupId>
    <artifactId>massive-discover-backend</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <name>massive-discover-backend</name>
    <description>massive discover backend service</description>
    <packaging>pom</packaging>

    <properties>
        <discover-backend-common-version>0.0.1-SNAPSHOT</discover-backend-common-version>
        <discover-backend-user-version>0.0.1-SNAPSHOT</discover-backend-user-version>
        <discover-backend-web-version>0.0.1-SNAPSHOT</discover-backend-web-version>
        <discover-backend-feed-version>0.0.1-SNAPSHOT</discover-backend-feed-version>
        <discover-backend-redis-lock-version>0.0.1-SNAPSHOT</discover-backend-redis-lock-version>

        <!-- plugin -->
        <maven-compiler-plugin-version>3.7.0</maven-compiler-plugin-version>
        <maven-jar-plugin-version>3.2.0</maven-jar-plugin-version>
        <spring-boot-maven-plugin-version>2.5.5</spring-boot-maven-plugin-version>

        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <aws-sns.version>1.12.5</aws-sns.version>
        <aws.java.sdk.version>2.20.43</aws.java.sdk.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- business module -->
            <dependency>
                <groupId>com.massive</groupId>
                <artifactId>discover-backend-common</artifactId>
                <version>0.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.massive</groupId>
                <artifactId>discover-backend-user</artifactId>
                <version>${discover-backend-user-version}</version>
            </dependency>
            <dependency>
                <groupId>com.massive</groupId>
                <artifactId>discover-backend-feed</artifactId>
                <version>${discover-backend-feed-version}</version>
            </dependency>
            <dependency>
                <groupId>com.massive</groupId>
                <artifactId>discover-backend-redis-lock</artifactId>
                <version>${discover-backend-redis-lock-version}</version>
            </dependency>

            <!-- framework -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.3.1</version>
            </dependency>
            <dependency>
                <groupId>com.gitee.sunchenbin.mybatis.actable</groupId>
                <artifactId>mybatis-enhance-actable</artifactId>
                <version>1.5.0.RELEASE</version>
            </dependency>

            <!-- firebase -->
            <dependency>
                <groupId>com.google.firebase</groupId>
                <artifactId>firebase-admin</artifactId>
                <version>8.1.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-context</artifactId>
                <version>1.39.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>2.7.1</version>
            </dependency>
            <dependency>
                <groupId>com.aventrix.jnanoid</groupId>
                <artifactId>jnanoid</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.14.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>

            <!-- line -->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-gson</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.6.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>2.3.2</version>
            </dependency>

            <!-- aws client -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.16.60</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>30.1.1-android</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>5.7.22</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>1.12.0</version>
            </dependency>
            <dependency>
                <artifactId>commons-codec</artifactId>
                <groupId>commons-codec</groupId>
                <version>1.13</version>
            </dependency>
            <dependency>
                <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
                <artifactId>concurrentlinkedhashmap-lru</artifactId>
                <version>1.4.2</version>
            </dependency>

            <!--SpringFox dependencies -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-core</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.22</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.22</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>0.11.1</version>
            </dependency>
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>jwks-rsa</artifactId>
                <version>0.9.0</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>0.11.1</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>4.5.13</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${aws.java.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.4.6</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>