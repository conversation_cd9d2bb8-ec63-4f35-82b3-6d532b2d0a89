#!/bin/bash

# Build Verification Script
# This script verifies that critical entity classes are properly compiled with all annotations

set -e

echo "🔍 Verifying build integrity..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to check if a class file contains expected annotations
check_entity_annotations() {
    local entity_name=$1
    local class_file="discover-backend-common/target/classes/com/massive/discover/common/entity/${entity_name}.class"
    
    echo -e "${BLUE}Checking ${entity_name} entity...${NC}"
    
    if [[ ! -f "$class_file" ]]; then
        echo -e "${RED}❌ ${entity_name}.class not found at $class_file${NC}"
        return 1
    fi
    
    # Use javap to inspect the compiled class for annotations
    if command -v javap >/dev/null 2>&1; then
        echo -e "${BLUE}  Inspecting compiled annotations...${NC}"
        javap -cp "discover-backend-common/target/classes" -v "com.massive.discover.common.entity.${entity_name}" | grep -E "(Column|Table|TableName)" || {
            echo -e "${YELLOW}  ⚠️  No database annotations found in compiled class${NC}"
        }
    else
        echo -e "${YELLOW}  ⚠️  javap not available, skipping annotation inspection${NC}"
    fi
    
    echo -e "${GREEN}  ✅ ${entity_name}.class exists${NC}"
    return 0
}

# Function to verify Maven compilation
verify_maven_build() {
    echo -e "${BLUE}Verifying Maven build...${NC}"
    
    # Check if target directories exist
    if [[ ! -d "discover-backend-common/target/classes" ]]; then
        echo -e "${RED}❌ Common module not compiled${NC}"
        return 1
    fi
    
    if [[ ! -d "discover-backend-web/target/classes" ]]; then
        echo -e "${RED}❌ Web module not compiled${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Maven build directories exist${NC}"
    return 0
}

# Function to check critical entity classes
verify_entity_classes() {
    echo -e "${BLUE}Verifying critical entity classes...${NC}"
    
    local entities=("Mark" "User" "Event" "Area" "Exhibitor")
    local failed=0
    
    for entity in "${entities[@]}"; do
        if ! check_entity_annotations "$entity"; then
            ((failed++))
        fi
    done
    
    if [[ $failed -gt 0 ]]; then
        echo -e "${RED}❌ $failed entity classes failed verification${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ All entity classes verified${NC}"
    return 0
}

# Function to check for common build issues
check_build_issues() {
    echo -e "${BLUE}Checking for common build issues...${NC}"
    
    # Check for empty target directories
    local empty_dirs=0
    for module in "discover-backend-common" "discover-backend-web" "discover-backend-operations" "discover-backend-user"; do
        if [[ -d "$module/target/classes" ]]; then
            local class_count=$(find "$module/target/classes" -name "*.class" | wc -l)
            if [[ $class_count -eq 0 ]]; then
                echo -e "${YELLOW}  ⚠️  $module has no compiled classes${NC}"
                ((empty_dirs++))
            else
                echo -e "${GREEN}  ✅ $module has $class_count compiled classes${NC}"
            fi
        fi
    done
    
    if [[ $empty_dirs -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  $empty_dirs modules have no compiled classes${NC}"
    fi
    
    # Check for annotation processor issues
    if find . -name "*.java" -exec grep -l "@Column\|@Table" {} \; | head -5 | while read -r file; do
        echo -e "${BLUE}  Found annotated source: $file${NC}"
    done
    
    return 0
}

# Main verification process
main() {
    echo -e "${BLUE}🚀 Starting build verification...${NC}"
    
    # Change to the Maven project directory
    if [[ ! -f "pom.xml" ]]; then
        echo -e "${RED}❌ Not in Maven project root (no pom.xml found)${NC}"
        exit 1
    fi
    
    # Run verification steps
    verify_maven_build || exit 1
    verify_entity_classes || exit 1
    check_build_issues
    
    echo -e "${GREEN}🎉 Build verification completed successfully!${NC}"
    echo -e "${BLUE}📊 Build Summary:${NC}"
    echo -e "  • Total .class files: $(find . -name "*.class" | wc -l)"
    echo -e "  • Entity classes: $(find . -name "*.class" -path "*/entity/*" | wc -l)"
    echo -e "  • JAR files: $(find . -name "*.jar" | wc -l)"
}

# Handle command line arguments
case "${1:-verify}" in
    verify)
        main
        ;;
    entities)
        verify_entity_classes
        ;;
    maven)
        verify_maven_build
        ;;
    *)
        echo "Usage: $0 [verify|entities|maven]"
        echo "  verify   - Full verification (default)"
        echo "  entities - Check entity classes only"
        echo "  maven    - Check Maven build only"
        exit 1
        ;;
esac
