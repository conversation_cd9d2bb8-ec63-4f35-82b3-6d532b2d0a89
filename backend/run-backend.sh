#!/bin/bash
echo "🚀 Starting Backend Services (AWS Pinpoint removed, using SNS)"

# Set up local environment variables
echo "Setting up local environment variables..."
export SPRING_PROFILES_ACTIVE="local"
export JDBC_URL="****************************************************************************************************************************"
export MYSQL_USER="ope_backend"
export MYSQL_PASSWORD="ope_backend"
export REDIS_CLUSTER_NODES="localhost:6379"
export AWS_ENV="local"
export LINE_CLIENT_ID=""
export LINE_CLIENT_SECRET=""
export LINE_CALLBACK_URL=""
export JWT_SECRET="massive123qwe#$"
export JWT_EXPIRED_DAYS="7"
export STATIC_IMAGE_HOST="https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com"
export BUCKET_NAME="open-portal-expo-prod"

echo "✅ Environment variables set:"
echo "   Spring Profile: $SPRING_PROFILES_ACTIVE"
echo "   Database: $JDBC_URL"
echo "   Redis: $REDIS_CLUSTER_NODES"
echo "   Environment: $AWS_ENV"
echo ""

# Check database connectivity
echo "🔍 Checking staging database connectivity..."
if /opt/homebrew/opt/mysql@8.0/bin/mysql --default-auth=mysql_native_password -h ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com -u ope_backend -pope_backend -e "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Staging database connection successful"
else
    echo "❌ Staging database connection failed. Please check your network connection and credentials."
    echo "   Database: ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com:3306"
    exit 1
fi

# Check Redis connectivity
echo "🔍 Checking Redis connectivity..."
if redis-cli ping > /dev/null 2>&1; then
    echo "✅ Redis connection successful"
else
    echo "❌ Redis connection failed. Please ensure Redis is running on localhost:6379"
    echo "   Run: ./run-database.sh"
    exit 1
fi
echo ""

echo "🔧 Building all module JARs first..."
cd massive-discover-backend
echo "📍 Working directory: $(pwd)"
mvn clean package -DskipTests
echo ""
echo "🚀 Starting Discover Backend Web on port 8082..."
mvn spring-boot:run -pl discover-backend-web
