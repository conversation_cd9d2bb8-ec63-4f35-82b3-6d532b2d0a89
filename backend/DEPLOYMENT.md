# Deployment Guide - Open Portal Expo Backend

This guide covers the complete setup for deploying the Spring Boot backend application to AWS LightSail using GitHub Actions CI/CD pipeline with Docker Compose.

## Overview

The deployment pipeline includes:
- **Build & Test**: Maven build and unit tests
- **Docker Build**: Multi-stage Docker build optimized for production
- **Container Registry**: Push to GitHub Container Registry (GHCR)
- **Deploy**: Zero-downtime deployment to AWS LightSail using Docker Compose
- **Health Check**: Automated verification of deployment success

## Prerequisites

### 1. AWS LightSail Instance Setup

#### Instance Requirements
- **OS**: Ubuntu 20.04 LTS or later
- **Instance Size**: At least 2GB RAM (recommended: 4GB for production)
- **Storage**: Minimum 20GB SSD
- **Networking**: Open ports 22 (SSH), 80 (HTTP), 443 (HTTPS), 8082 (API)

#### Install Required Software

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y curl jq net-tools

# Logout and login again to apply docker group changes
```

#### Create Deployment Directory

```bash
sudo mkdir -p /home/<USER>/ope-backend
sudo chown $USER:$USER /home/<USER>/ope-backend
```

### 2. GitHub Repository Secrets

Configure the following secrets in your GitHub repository (`Settings > Secrets and variables > Actions`):

#### Required Secrets

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `LIGHTSAIL_SSH_KEY` | Private SSH key for LightSail access | `-----BEGIN OPENSSH PRIVATE KEY-----...` |
| `LIGHTSAIL_HOST` | LightSail instance IP address | `***********` |
| `LIGHTSAIL_USER` | SSH username for LightSail | `ubuntu` |

#### Environment Variables for Production

Create a `.env` file on your LightSail instance at `/home/<USER>/ope-backend/.env`:

```bash
# Database Configuration
JDBC_URL=*************************************************
MYSQL_USER=your_db_user
MYSQL_PASSWORD=your_db_password

# AWS Configuration
AWS_ENV=prod
STATIC_IMAGE_HOST=https://your-s3-bucket.s3.region.amazonaws.com
BUCKET_NAME=your-s3-bucket

# Authentication
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_EXPIRED_DAYS=30

# Spring Profile
SPRING_PROFILES_ACTIVE=prod
```

### 3. SSH Key Setup

#### Generate SSH Key Pair

```bash
# On your local machine
ssh-keygen -t rsa -b 4096 -C "<EMAIL>" -f ~/.ssh/lightsail_deploy

# Copy public key to LightSail instance
ssh-copy-id -i ~/.ssh/lightsail_deploy.pub ubuntu@YOUR_LIGHTSAIL_IP
```

#### Add Private Key to GitHub Secrets

```bash
# Copy private key content
cat ~/.ssh/lightsail_deploy
```

Copy the entire output (including `-----BEGIN OPENSSH PRIVATE KEY-----` and `-----END OPENSSH PRIVATE KEY-----`) and add it as the `LIGHTSAIL_SSH_KEY` secret in GitHub.

## Deployment Process

### Automatic Deployment

The CI/CD pipeline automatically triggers on:
- Push to `prod` branch
- Changes to backend code or deployment files
- Manual workflow dispatch

### Manual Deployment

You can also deploy manually using the deployment script:

```bash
# On LightSail instance
cd /home/<USER>/ope-backend

# Set required environment variables
export GITHUB_REPOSITORY_OWNER=your-github-username
export IMAGE_TAG=main-latest

# Run deployment
./deploy.sh
```

### Deployment Commands

```bash
# Full deployment
./deploy.sh deploy

# Stop containers
./deploy.sh stop

# Start containers
./deploy.sh start

# Restart containers
./deploy.sh restart

# Check status
./deploy.sh status

# View logs
./deploy.sh logs
```

## Monitoring and Troubleshooting

### Health Checks

The application includes built-in health checks:
- **Docker Health Check**: `curl http://localhost:8082/actuator/health`
- **Container Status**: `docker ps --filter 'name=ope-'`

### Log Monitoring

```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f backend

# View Redis logs
docker-compose -f docker-compose.prod.yml logs -f redis

# View deployment logs
tail -f /home/<USER>/ope-backend/deploy.log
```

### Common Issues

#### 1. Container Won't Start
```bash
# Check container logs
docker logs ope-backend

# Check environment variables
docker exec ope-backend env | grep -E "(JDBC|REDIS|JWT)"
```

#### 2. Database Connection Issues
```bash
# Test database connectivity from container
docker exec ope-backend curl -f http://localhost:8082/actuator/health
```

#### 3. Redis Connection Issues
```bash
# Check Redis container
docker exec ope-redis redis-cli ping

# Check Redis logs
docker logs ope-redis
```

### Performance Monitoring

```bash
# Container resource usage
docker stats ope-backend ope-redis

# System resource usage
htop

# Disk usage
df -h
docker system df
```

## Security Considerations

1. **Environment Variables**: Never commit sensitive data to Git
2. **SSH Keys**: Use dedicated deployment keys with minimal permissions
3. **Container Security**: Containers run as non-root users
4. **Network Security**: Use Docker networks for inter-container communication
5. **Log Security**: Logs are rotated and size-limited

## Backup and Recovery

### Database Backup
```bash
# Create database backup (adjust connection details)
mysqldump -h your-rds-endpoint -u username -p database_name > backup.sql
```

### Container Data Backup
```bash
# Backup Redis data
docker run --rm -v ope-redis-data:/data -v $(pwd):/backup alpine tar czf /backup/redis-backup.tar.gz -C /data .

# Backup application logs
docker run --rm -v ope-app-logs:/logs -v $(pwd):/backup alpine tar czf /backup/logs-backup.tar.gz -C /logs .
```

## Scaling Considerations

For production scaling:
1. **Load Balancer**: Use AWS Application Load Balancer
2. **Multiple Instances**: Deploy to multiple LightSail instances
3. **Database**: Use AWS RDS with read replicas
4. **Redis**: Consider AWS ElastiCache for Redis
5. **Monitoring**: Implement CloudWatch monitoring

## Support

For issues with deployment:
1. Check GitHub Actions logs
2. Review LightSail instance logs
3. Verify environment variables
4. Test connectivity between services
