{"name": "directus-extension-generate-types", "version": "0.6.1", "license": "MPL-2.0", "keywords": ["directus", "directus-extension", "directus-custom-module"], "directus:extension": {"type": "module", "path": "dist/index.js", "source": "src/index.ts", "host": "^9.0.0-rc.95", "hidden": false}, "scripts": {"build": "directus-extension build", "watch": "directus-extension build --watch", "dev": "./dev.sh"}, "devDependencies": {"@directus/extensions-sdk": "11.0.1", "@types/prismjs": "^1.26.3", "axios": "^1.6.8", "sass": "^1.72.0", "typescript": "^5.4.2", "vue": "^3.4.21"}, "dependencies": {"@directus/shared": "^9.24.0", "prismjs": "^1.29.0"}, "files": ["dist"]}