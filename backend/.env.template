# Open Portal Expo Backend - Environment Configuration Template
# Copy this file to .env on your LightSail instance and fill in the values
# DO NOT commit the actual .env file to the repository

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
# Environment name: local, staging, prod
ENVIRONMENT=staging

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================
# GitHub repository owner (for Docker image)
GITHUB_REPOSITORY_OWNER=cis-gk-dev

# Image tag (usually set by CI/CD pipeline)
IMAGE_TAG=staging

# Backend image override (optional, defaults to GHCR image)
# BACKEND_IMAGE=ghcr.io/cis-gk-dev/ope-backend:staging

# =============================================================================
# SPRING CONFIGURATION
# =============================================================================
# Spring Boot profile
SPRING_PROFILES_ACTIVE=staging

# Health check endpoint (consistent across all environments)
# Uses ranking API endpoint which doesn't require authentication
# HEALTH_CHECK_PATH=/rank/v1/showTopN?n=1

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# MySQL/RDS database connection details
JDBC_URL=*******************************************************************************************************************
MYSQL_USER=your_database_username
MYSQL_PASSWORD=your_database_password

# =============================================================================
# AWS CONFIGURATION
# =============================================================================
# Environment (prod, staging, dev)
AWS_ENV=staging

# S3 bucket for static assets
STATIC_IMAGE_HOST=https://your-s3-bucket.s3.ap-northeast-1.amazonaws.com
BUCKET_NAME=your-s3-bucket-name

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# JWT secret key (generate a strong random key)
# You can generate one with: openssl rand -base64 32
JWT_SECRET=your-super-secure-jwt-secret-key-minimum-32-characters

# JWT token expiration in days
JWT_EXPIRED_DAYS=30

# =============================================================================
# LINE OAUTH CONFIGURATION (Optional)
# =============================================================================
# LINE Login integration (leave empty if not using)
LINE_CLIENT_ID=
LINE_CLIENT_SECRET=
LINE_CALLBACK_URL=

# =============================================================================
# GHOST CMS CONFIGURATION
# =============================================================================
# Ghost database configuration
GHOST_DB_CLIENT=mysql
GHOST_DB_HOST=your-rds-endpoint
GHOST_DB_USER=ghost_user
GHOST_DB_PASSWORD=ghost_password
GHOST_DB_DATABASE=ghost_database

# Ghost URL (where Ghost will be accessible)
GHOST_URL=https://your-domain.com/blog

# Ghost Node environment (development for staging, production for prod)
GHOST_NODE_ENV=development

# =============================================================================
# DIRECTUS CMS CONFIGURATION
# =============================================================================
# Directus secret key (generate a strong random key)
# You can generate one with: openssl rand -base64 32
DIRECTUS_SECRET=your-directus-secret-key

# Directus admin user
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=your-admin-password

# Directus database configuration
DIRECTUS_DB_CLIENT=mysql
DIRECTUS_DB_HOST=your-rds-endpoint
DIRECTUS_DB_PORT=3306
DIRECTUS_DB_DATABASE=directus_database
DIRECTUS_DB_USER=directus_user
DIRECTUS_DB_PASSWORD=directus_password

# Directus CORS configuration
DIRECTUS_CORS_ORIGIN=*

# =============================================================================
# EXAMPLE VALUES FOR DIFFERENT ENVIRONMENTS
# =============================================================================

# STAGING ENVIRONMENT:
# ENVIRONMENT=staging
# SPRING_PROFILES_ACTIVE=staging
# AWS_ENV=staging
# GHOST_NODE_ENV=development

# PRODUCTION ENVIRONMENT:
# ENVIRONMENT=prod
# SPRING_PROFILES_ACTIVE=prod
# AWS_ENV=prod
# GHOST_NODE_ENV=production
