#!/bin/bash

# AWS LightSail Deployment Script for Open Portal Expo Backend
# This script handles zero-downtime deployment using Docker Compose

set -e  # Exit on any error

# Configuration
COMPOSE_FILE="docker-compose.yml"  # Will be copied from appropriate environment file
ENV_FILE=".env"
BACKUP_DIR="/home/<USER>/ope-backend/backups"
LOG_FILE="/home/<USER>/ope-backend/deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if required environment variables are set
check_env_vars() {
    log "Checking required environment variables..."

    required_vars=(
        "GITHUB_REPOSITORY_OWNER"
        "IMAGE_TAG"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "Required environment variable $var is not set"
        fi
    done

    success "All required environment variables are set"
}

# Create backup of current deployment
create_backup() {
    log "Creating backup of current deployment..."

    mkdir -p "$BACKUP_DIR"

    # Create timestamped backup directory
    BACKUP_TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
    CURRENT_BACKUP_DIR="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    mkdir -p "$CURRENT_BACKUP_DIR"

    # Backup current compose file and env file
    if [[ -f "$COMPOSE_FILE" ]]; then
        cp "$COMPOSE_FILE" "$CURRENT_BACKUP_DIR/"
    fi

    if [[ -f "$ENV_FILE" ]]; then
        cp "$ENV_FILE" "$CURRENT_BACKUP_DIR/"
    fi

    # Keep only last 5 backups
    cd "$BACKUP_DIR"
    ls -t | tail -n +6 | xargs -r rm -rf
    cd - > /dev/null

    success "Backup created at $CURRENT_BACKUP_DIR"
}

# Pull latest Docker images
pull_images() {
    log "Pulling latest Docker images..."

    # Login to GitHub Container Registry (using GitHub token from environment)
    if [[ -n "$GITHUB_TOKEN" ]]; then
        echo "$GITHUB_TOKEN" | docker login ghcr.io -u "$GITHUB_REPOSITORY_OWNER" --password-stdin
    fi

    # Pull the specific backend image tag
    docker pull "ghcr.io/$GITHUB_REPOSITORY_OWNER/ope-backend:$IMAGE_TAG" || error "Failed to pull backend image"

    # Pull other service images
    docker pull redis:7.0-alpine || warning "Failed to pull Redis image (may already exist)"
    docker pull ghost:5-alpine || warning "Failed to pull Ghost image (may already exist)"
    docker pull directus/directus || warning "Failed to pull Directus image (may already exist)"

    success "Docker images pulled successfully"
}

# Deploy application
deploy_application() {
    log "Deploying application..."

    # Export environment variables for docker compose
    export GITHUB_REPOSITORY_OWNER
    export IMAGE_TAG
    export ENVIRONMENT

    # Load environment variables from .env file if it exists
    if [[ -f "$ENV_FILE" ]]; then
        log "Loading environment variables from $ENV_FILE"
        set -a  # automatically export all variables
        source "$ENV_FILE"
        set +a
    else
        warning "No .env file found. Make sure all required environment variables are set."
    fi

    # Stop existing containers gracefully
    log "Stopping existing containers..."
    docker compose -f "$COMPOSE_FILE" down --timeout 30 || warning "No existing containers to stop"

    # Start new containers
    log "Starting new containers..."
    docker compose -f "$COMPOSE_FILE" up -d || error "Failed to start containers"

    success "Application deployed successfully"
}

# Health check for all services
health_check() {
    log "Performing health checks for all services..."

    local max_attempts=30
    local services_healthy=0
    local total_services=3
    local backend_healthy=false
    local ghost_healthy=false
    local directus_healthy=false

    # Check Backend
    log "Checking Backend health..."
    local attempt=1

    # Use consistent health check endpoint
    local health_endpoint="/rank/v1/showTopN?n=1"

    while [[ $attempt -le $max_attempts ]]; do
        log "Backend health check attempt $attempt/$max_attempts"
        if curl -f -s "http://localhost:8082${health_endpoint}" > /dev/null 2>&1; then
            success "Backend health check passed!"
            ((services_healthy++))
            backend_healthy=true
            break
        fi
        sleep 10
        ((attempt++))
    done

    if [[ $backend_healthy == false ]]; then
        warning "Backend health check failed after $max_attempts attempts"
    fi

    # Check Ghost
    log "Checking Ghost health..."
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        log "Ghost health check attempt $attempt/$max_attempts"
        if curl -f -s http://localhost:8888 > /dev/null 2>&1; then
            success "Ghost health check passed!"
            ((services_healthy++))
            ghost_healthy=true
            break
        fi
        sleep 10
        ((attempt++))
    done

    if [[ $ghost_healthy == false ]]; then
        warning "Ghost health check failed after $max_attempts attempts"
    fi

    # Check Directus
    log "Checking Directus health..."
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        log "Directus health check attempt $attempt/$max_attempts"
        if curl -f -s http://localhost:8055 > /dev/null 2>&1; then
            success "Directus health check passed!"
            ((services_healthy++))
            directus_healthy=true
            break
        fi
        sleep 10
        ((attempt++))
    done

    if [[ $directus_healthy == false ]]; then
        warning "Directus health check failed after $max_attempts attempts"
    fi

    # Summary
    log "Health check summary: $services_healthy/$total_services services are healthy"
    log "Backend: $([ $backend_healthy == true ] && echo "✓ Healthy" || echo "✗ Failed")"
    log "Ghost: $([ $ghost_healthy == true ] && echo "✓ Healthy" || echo "✗ Failed")"
    log "Directus: $([ $directus_healthy == true ] && echo "✓ Healthy" || echo "✗ Failed")"

    if [[ $services_healthy -eq $total_services ]]; then
        success "All services are healthy!"
        return 0
    elif [[ $backend_healthy == true ]]; then
        warning "Backend is healthy, but some other services failed. Deployment considered successful."
        return 0
    else
        warning "Backend health check failed. Deployment may have issues."
        return 0
    fi
}

# Cleanup old images
cleanup_images() {
    log "Cleaning up old Docker images..."

    # Remove dangling images (don't fail if none exist)
    if docker image prune -f > /dev/null 2>&1; then
        log "Removed dangling images"
    else
        warning "No dangling images to remove or prune failed"
    fi

    # Remove old backend images (keep last 3) - since you only keep staging/prod tags, this likely won't find much
    local old_images
    old_images=$(docker images "ghcr.io/$GITHUB_REPOSITORY_OWNER/ope-backend" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | \
        tail -n +2 | sort -k2 -r | tail -n +4 | awk '{print $1}' 2>/dev/null || true)

    if [[ -n "$old_images" ]]; then
        if echo "$old_images" | xargs -r docker rmi > /dev/null 2>&1; then
            log "Removed old backend images"
        else
            warning "Failed to remove some old images (they may be in use)"
        fi
    else
        log "No old backend images to remove"
    fi

    log "Image cleanup completed"
}

# Main deployment process
main() {
    log "Starting deployment process..."
    log "Repository Owner: $GITHUB_REPOSITORY_OWNER"
    log "Image Tag: $IMAGE_TAG"

    check_env_vars
    # create_backup
    pull_images
    deploy_application

    # Run health check but don't let it fail the deployment
    set +e  # Temporarily disable exit on error
    health_check
    health_check_result=$?
    set -e  # Re-enable exit on error

    # Run cleanup but don't let it fail the deployment
    set +e  # Temporarily disable exit on error
    cleanup_images
    cleanup_result=$?
    set -e  # Re-enable exit on error

    # Log health check result
    if [[ $health_check_result -eq 0 ]]; then
        log "Health checks completed successfully"
    else
        log "Health checks completed with warnings (exit code: $health_check_result)"
    fi

    # Log cleanup result
    if [[ $cleanup_result -eq 0 ]]; then
        log "Image cleanup completed successfully"
    else
        log "Image cleanup completed with warnings (exit code: $cleanup_result)"
    fi

    success "Deployment completed successfully!"
    log "Services are running at:"
    log "  Backend: http://localhost:8082"
    log "  Ghost: http://localhost:8888"
    log "  Directus: http://localhost:8055"
}

# Show container status
show_status() {
    log "Checking container status..."

    echo "=== Container Status ==="
    docker ps --filter 'name=ope-' --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' || warning "No containers found"
    echo ""

    echo "=== Container Logs (last 20 lines) ==="
    docker compose -f "$COMPOSE_FILE" logs --tail=20 || warning "Failed to get logs"
}

# Stop containers
stop_containers() {
    log "Stopping containers..."
    docker compose -f "$COMPOSE_FILE" down --timeout 30 || warning "No containers to stop"
    success "Containers stopped"
}

# Start containers
start_containers() {
    log "Starting containers..."
    docker compose -f "$COMPOSE_FILE" up -d || error "Failed to start containers"
    success "Containers started"
}

# Script usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  deploy    - Full deployment (default)"
    echo "  stop      - Stop all containers"
    echo "  start     - Start all containers"
    echo "  status    - Check container status"
    echo "  logs      - Show container logs"
    echo "  restart   - Restart all containers"
    echo ""
}

# Handle command line arguments
case "${1:-deploy}" in
    deploy)
        main
        ;;
    stop)
        stop_containers
        ;;
    start)
        start_containers
        ;;
    restart)
        stop_containers
        start_containers
        ;;
    status)
        show_status
        ;;
    logs)
        echo "=== Container Logs ==="
        docker compose -f "$COMPOSE_FILE" logs -f --tail=50
        ;;
    *)
        usage
        exit 1
        ;;
esac
