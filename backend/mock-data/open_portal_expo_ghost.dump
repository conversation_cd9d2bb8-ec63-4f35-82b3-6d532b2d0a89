-- MySQL dump 10.13  Distrib 8.0.27, for macos11 (arm64)
--
-- Host: ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com    Database: open_portal_expo_ghost
-- ------------------------------------------------------
-- Server version	8.0.42

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
-- SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
-- SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

-- SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `actions`
--

DROP TABLE IF EXISTS `actions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `actions` (
  `id` varchar(24) NOT NULL,
  `resource_id` varchar(24) DEFAULT NULL,
  `resource_type` varchar(50) NOT NULL,
  `actor_id` varchar(24) NOT NULL,
  `actor_type` varchar(50) NOT NULL,
  `event` varchar(50) NOT NULL,
  `context` text,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `actions`
--

LOCK TABLES `actions` WRITE;
/*!40000 ALTER TABLE `actions` DISABLE KEYS */;
INSERT INTO `actions` VALUES ('6850d035db983000014c1c72','6850cc5a9cac51000106fb73','setting','1','user','edited','{\"key\":\"title\",\"group\":\"site\"}','2025-06-17 02:17:25'),('6850d035db983000014c1c73','6850cc5a9cac51000106fb74','setting','1','user','edited','{\"key\":\"description\",\"group\":\"site\"}','2025-06-17 02:17:25'),('6850d036db983000014c1c76','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-17 02:17:26'),('6850d039db983000014c1c77','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-17 02:17:29'),('6850d04fdb983000014c1c7d','6850cc5a9cac51000106fb8b','setting','1','user','edited','{\"key\":\"active_theme\",\"group\":\"theme\"}','2025-06-17 02:17:51'),('6850d083db983000014c1c7e','6850cc5a9cac51000106fb7c','setting','1','user','edited','{\"key\":\"timezone\",\"group\":\"site\"}','2025-06-17 02:18:43'),('6850d0fcdb983000014c1c81','6850cc5a9cac51000106fbb3','setting','1','user','edited','{\"key\":\"labs\",\"group\":\"labs\"}','2025-06-17 02:20:44'),('6850d112db983000014c1c82','6850cc5a9cac51000106fb74','setting','1','user','edited','{\"key\":\"description\",\"group\":\"site\"}','2025-06-17 02:21:06'),('6850d155db983000014c1c86','6850d154db983000014c1c83','post','1','user','added','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 02:22:13'),('6850e042db983000014c1c89','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:25:54'),('6850e04adb983000014c1c8a','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:26:02'),('6850e054db983000014c1c8c','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:26:12'),('6850e066db983000014c1c90','6850e065db983000014c1c8d','post','1','user','added','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:26:30'),('6850e0b5db983000014c1c92','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:27:49'),('6850e0c1db983000014c1c93','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:28:01'),('6850e0c3db983000014c1c95','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:28:03'),('6850e0c9db983000014c1c97','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:28:09'),('6850e0e9db983000014c1c99','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:28:41'),('6850e0f0db983000014c1c9b','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:28:48'),('6850e10bdb983000014c1c9d','6850cc579cac51000106f9b4','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Coming soon\"}','2025-06-17 03:29:15'),('6850e114db983000014c1c9e','6850cc579cac51000106f9b4','post','1','user','deleted','{\"type\":\"post\",\"primary_name\":\"Coming soon\"}','2025-06-17 03:29:24'),('6850e121db983000014c1c9f','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-17 03:29:37'),('6850e64fdb983000014c1ca4','6850e64fdb983000014c1ca1','tag','1','user','added','{\"primary_name\":\"#hide\"}','2025-06-17 03:51:43'),('6850e64fdb983000014c1ca5','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:51:43'),('6850e655db983000014c1ca8','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:51:49'),('6850e65adb983000014c1cab','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:51:54'),('6850e662db983000014c1caf','6850e662db983000014c1cac','tag','1','user','added','{\"primary_name\":\"#unlisted\"}','2025-06-17 03:52:02'),('6850e662db983000014c1cb0','6850d154db983000014c1c83','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 03:52:02'),('6850e666db983000014c1cb2','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:52:06'),('6850e66adb983000014c1cb5','6850e065db983000014c1c8d','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 03:52:10'),('6850e675db983000014c1cb7','6850e64fdb983000014c1ca1','tag','1','user','deleted','{\"primary_name\":\"#hide\"}','2025-06-17 03:52:21'),('6850e726db983000014c1cbb','6850e726db983000014c1cb8','post','1','user','added','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 03:55:18'),('6850e74bdb983000014c1cbd','6850e726db983000014c1cb8','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 03:55:55'),('6850e778db983000014c1cbe','6850e726db983000014c1cb8','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 03:56:40'),('6850e880db983000014c1cbf','6850e726db983000014c1cb8','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 04:01:04'),('6850e883db983000014c1cc1','6850e726db983000014c1cb8','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 04:01:07'),('6850e886db983000014c1cc3','6850e726db983000014c1cb8','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 04:01:10'),('6850e897db983000014c1cc5','6850e726db983000014c1cb8','post','1','user','edited','{\"type\":\"post\",\"primary_name\":\"App Launching Soon\"}','2025-06-17 04:01:27'),('6850e8aedb983000014c1cc7','6850cc589cac51000106f9b6','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"About this site\"}','2025-06-17 04:01:50'),('6850e8d6db983000014c1ccb','6850e8d5db983000014c1cc8','post','1','user','added','{\"type\":\"page\",\"primary_name\":\"(Untitled)\"}','2025-06-17 04:02:30'),('6850e8dfdb983000014c1ccd','6850e8d5db983000014c1cc8','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 04:02:39'),('6850e8e2db983000014c1cce','6850e8d5db983000014c1cc8','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 04:02:42'),('6850e8e9db983000014c1cd0','6850e8d5db983000014c1cc8','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 04:02:49'),('6850e8f1db983000014c1cd1','6850e065db983000014c1c8d','post','1','user','deleted','{\"type\":\"post\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 04:02:57'),('6850e8f7db983000014c1cd2','6850d154db983000014c1c83','post','1','user','deleted','{\"type\":\"post\",\"primary_name\":\"Terms and Condition\"}','2025-06-17 04:03:03'),('6850e8ffdb983000014c1cd6','6850e8ffdb983000014c1cd3','post','1','user','added','{\"type\":\"page\",\"primary_name\":\"Terms and Conditions\"}','2025-06-17 04:03:11'),('6850e902db983000014c1cd8','6850e8ffdb983000014c1cd3','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Terms and Conditions\"}','2025-06-17 04:03:14'),('6850e90adb983000014c1cd9','6850e8ffdb983000014c1cd3','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Terms and Conditions\"}','2025-06-17 04:03:22'),('6850e90cdb983000014c1cdb','6850e8ffdb983000014c1cd3','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Terms and Conditions\"}','2025-06-17 04:03:24'),('6850e912db983000014c1cdd','6850e8d5db983000014c1cc8','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Privacy Policy\"}','2025-06-17 04:03:30'),('6850e96adb983000014c1cde','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-17 04:04:58'),('6850e98adb983000014c1cdf','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-17 04:05:30'),('6850ea01db983000014c1ce8','6850cc5a9cac51000106fb8b','setting','1','user','edited','{\"key\":\"active_theme\",\"group\":\"theme\"}','2025-06-17 04:07:29'),('6850ea6ddb983000014c1ce9','6850cc5a9cac51000106fb81','setting','1','user','edited','{\"key\":\"navigation\",\"group\":\"site\"}','2025-06-17 04:09:17'),('6850ea73db983000014c1cea','6850cc5a9cac51000106fb81','setting','1','user','edited','{\"key\":\"navigation\",\"group\":\"site\"}','2025-06-17 04:09:23'),('6850ea7fdb983000014c1cef','6850cc5a9cac51000106fb91','setting','1','user','edited','{\"key\":\"members_signup_access\",\"group\":\"members\"}','2025-06-17 04:09:35'),('68525eb6db983000014c1cf5','68525eb6db983000014c1cf2','post','1','user','added','{\"type\":\"page\",\"primary_name\":\"Rules\"}','2025-06-18 06:37:42'),('68525ec1db983000014c1cf7','68525eb6db983000014c1cf2','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Rules\"}','2025-06-18 06:37:53'),('68525ec5db983000014c1cf8','68525eb6db983000014c1cf2','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Rules\"}','2025-06-18 06:37:57'),('68525ec8db983000014c1cfa','68525eb6db983000014c1cf2','post','1','user','edited','{\"type\":\"page\",\"primary_name\":\"Rules\"}','2025-06-18 06:38:00'),('68526414db983000014c1cfb','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-18 07:00:36'),('68526460db983000014c1d06','6850cc5a9cac51000106fb8b','setting','1','user','edited','{\"key\":\"active_theme\",\"group\":\"theme\"}','2025-06-18 07:01:52'),('68529f0fdb983000014c1d11','6850cc5a9cac51000106fb8b','setting','1','user','edited','{\"key\":\"active_theme\",\"group\":\"theme\"}','2025-06-18 11:12:15'),('685cff04db983000014c1d12','1','user','1','user','edited','{\"primary_name\":\"Open Portal Expo\"}','2025-06-26 08:04:20');
/*!40000 ALTER TABLE `actions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `api_keys`
--

DROP TABLE IF EXISTS `api_keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_keys` (
  `id` varchar(24) NOT NULL,
  `type` varchar(50) NOT NULL,
  `secret` varchar(191) NOT NULL,
  `role_id` varchar(24) DEFAULT NULL,
  `integration_id` varchar(24) DEFAULT NULL,
  `user_id` varchar(24) DEFAULT NULL,
  `last_seen_at` datetime DEFAULT NULL,
  `last_seen_version` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_keys_secret_unique` (`secret`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `api_keys`
--

LOCK TABLES `api_keys` WRITE;
/*!40000 ALTER TABLE `api_keys` DISABLE KEYS */;
INSERT INTO `api_keys` VALUES ('6850cc589cac51000106f9b9','admin','8be436a704c74e4bedbba1c6c6c6b6ebe6ff944f824e1d7b93e3e1cc3bb19a70','6850cc569cac51000106f92f','6850cc589cac51000106f9b8',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc589cac51000106f9bb','admin','5ff1176dae2601a58cb652f029aed8a8b25ce67d62ff10f2dcf24b421e806bf5','6850cc569cac51000106f930','6850cc589cac51000106f9ba',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc589cac51000106f9bd','admin','ff1f1de32069644611cd5950e46b72c9fe439eea1c474b0e6e45cd3ec435e2e7','6850cc569cac51000106f931','6850cc589cac51000106f9bc',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc589cac51000106f9bf','admin','e40b22d2e2103165ff5ae3f50e2153ee2609f442c92c6ac46226bdb74ae5871d','6850cc569cac51000106f932','6850cc589cac51000106f9be',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc599cac51000106f9c1','admin','5cb003f41162309cedac8db58ed20a8856f80edde30dd7a80a01fb48d30f19c6','6850cc569cac51000106f933','6850cc589cac51000106f9c0',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc599cac51000106f9c3','content','f7bffe66974d3d5909adf8876e',NULL,'6850cc599cac51000106f9c2',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc599cac51000106f9c5','content','4d6000b52698f6c08155f33b5b',NULL,'6850cc599cac51000106f9c4',NULL,NULL,NULL,'2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850e15fdb983000014c1ca0','admin','664c6eb3f035a30f8079f2fac986198c072c45725032bed70d1ba25b488346db','6850cc569cac51000106f92f',NULL,'1',NULL,NULL,'2025-06-17 03:30:39','6850e15fdb983000014c1ca0','2025-06-17 03:30:39','6850e15fdb983000014c1ca0');
/*!40000 ALTER TABLE `api_keys` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `benefits`
--

DROP TABLE IF EXISTS `benefits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `benefits` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `benefits_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `benefits`
--

LOCK TABLES `benefits` WRITE;
/*!40000 ALTER TABLE `benefits` DISABLE KEYS */;
/*!40000 ALTER TABLE `benefits` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `brute`
--

DROP TABLE IF EXISTS `brute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `brute` (
  `key` varchar(191) NOT NULL,
  `firstRequest` bigint NOT NULL,
  `lastRequest` bigint NOT NULL,
  `lifetime` bigint NOT NULL,
  `count` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `brute`
--

LOCK TABLES `brute` WRITE;
/*!40000 ALTER TABLE `brute` DISABLE KEYS */;
INSERT INTO `brute` VALUES ('s83oIp3pFgbxPJP2DC3lQ5TGBMEhYeYqo60kAThCKvc=',1750213884084,1750213884084,1750214884107,1);
/*!40000 ALTER TABLE `brute` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `collections`
--

DROP TABLE IF EXISTS `collections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collections` (
  `id` varchar(24) NOT NULL,
  `title` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `description` varchar(2000) DEFAULT NULL,
  `type` varchar(50) NOT NULL,
  `filter` text,
  `feature_image` varchar(2000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `collections_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `collections`
--

LOCK TABLES `collections` WRITE;
/*!40000 ALTER TABLE `collections` DISABLE KEYS */;
INSERT INTO `collections` VALUES ('6850cc579cac51000106f936','Latest','latest','All posts','automatic',NULL,NULL,'2025-06-17 02:00:55','2025-06-17 02:00:55'),('6850cc579cac51000106f937','Featured','featured','Featured posts','automatic','featured:true',NULL,'2025-06-17 02:00:55','2025-06-17 02:00:55');
/*!40000 ALTER TABLE `collections` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `collections_posts`
--

DROP TABLE IF EXISTS `collections_posts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `collections_posts` (
  `id` varchar(24) NOT NULL,
  `collection_id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `collections_posts_collection_id_foreign` (`collection_id`),
  KEY `collections_posts_post_id_foreign` (`post_id`),
  CONSTRAINT `collections_posts_collection_id_foreign` FOREIGN KEY (`collection_id`) REFERENCES `collections` (`id`) ON DELETE CASCADE,
  CONSTRAINT `collections_posts_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `collections_posts`
--

LOCK TABLES `collections_posts` WRITE;
/*!40000 ALTER TABLE `collections_posts` DISABLE KEYS */;
/*!40000 ALTER TABLE `collections_posts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comment_likes`
--

DROP TABLE IF EXISTS `comment_likes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comment_likes` (
  `id` varchar(24) NOT NULL,
  `comment_id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `comment_likes_comment_id_foreign` (`comment_id`),
  KEY `comment_likes_member_id_foreign` (`member_id`),
  CONSTRAINT `comment_likes_comment_id_foreign` FOREIGN KEY (`comment_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comment_likes_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comment_likes`
--

LOCK TABLES `comment_likes` WRITE;
/*!40000 ALTER TABLE `comment_likes` DISABLE KEYS */;
/*!40000 ALTER TABLE `comment_likes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comment_reports`
--

DROP TABLE IF EXISTS `comment_reports`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comment_reports` (
  `id` varchar(24) NOT NULL,
  `comment_id` varchar(24) NOT NULL,
  `member_id` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `comment_reports_comment_id_foreign` (`comment_id`),
  KEY `comment_reports_member_id_foreign` (`member_id`),
  CONSTRAINT `comment_reports_comment_id_foreign` FOREIGN KEY (`comment_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comment_reports_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comment_reports`
--

LOCK TABLES `comment_reports` WRITE;
/*!40000 ALTER TABLE `comment_reports` DISABLE KEYS */;
/*!40000 ALTER TABLE `comment_reports` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `comments`
--

DROP TABLE IF EXISTS `comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `comments` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `member_id` varchar(24) DEFAULT NULL,
  `parent_id` varchar(24) DEFAULT NULL,
  `in_reply_to_id` varchar(24) DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'published',
  `html` longtext,
  `edited_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `comments_post_id_foreign` (`post_id`),
  KEY `comments_member_id_foreign` (`member_id`),
  KEY `comments_parent_id_foreign` (`parent_id`),
  KEY `comments_in_reply_to_id_foreign` (`in_reply_to_id`),
  CONSTRAINT `comments_in_reply_to_id_foreign` FOREIGN KEY (`in_reply_to_id`) REFERENCES `comments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `comments_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL,
  CONSTRAINT `comments_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `comments_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `comments`
--

LOCK TABLES `comments` WRITE;
/*!40000 ALTER TABLE `comments` DISABLE KEYS */;
/*!40000 ALTER TABLE `comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `custom_theme_settings`
--

DROP TABLE IF EXISTS `custom_theme_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `custom_theme_settings` (
  `id` varchar(24) NOT NULL,
  `theme` varchar(191) NOT NULL,
  `key` varchar(191) NOT NULL,
  `type` varchar(50) NOT NULL,
  `value` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `custom_theme_settings`
--

LOCK TABLES `custom_theme_settings` WRITE;
/*!40000 ALTER TABLE `custom_theme_settings` DISABLE KEYS */;
INSERT INTO `custom_theme_settings` VALUES ('6850cc5b9cac51000106fbcf','source','navigation_layout','select','Logo in the middle'),('6850cc5b9cac51000106fbd0','source','site_background_color','color','#ffffff'),('6850cc5b9cac51000106fbd1','source','header_and_footer_color','select','Background color'),('6850cc5b9cac51000106fbd2','source','title_font','select','Modern sans-serif'),('6850cc5b9cac51000106fbd3','source','body_font','select','Modern sans-serif'),('6850cc5b9cac51000106fbd4','source','signup_heading','text',NULL),('6850cc5b9cac51000106fbd5','source','signup_subheading','text',NULL),('6850cc5b9cac51000106fbd6','source','header_style','select','Landing'),('6850cc5b9cac51000106fbd7','source','header_text','text',NULL),('6850cc5b9cac51000106fbd8','source','background_image','boolean','true'),('6850cc5b9cac51000106fbd9','source','show_featured_posts','boolean','false'),('6850cc5b9cac51000106fbda','source','post_feed_style','select','List'),('6850cc5b9cac51000106fbdb','source','show_images_in_feed','boolean','true'),('6850cc5c9cac51000106fbdc','source','show_author','boolean','true'),('6850cc5c9cac51000106fbdd','source','show_publish_date','boolean','true'),('6850cc5c9cac51000106fbde','source','show_publication_info_sidebar','boolean','false'),('6850cc5c9cac51000106fbdf','source','show_post_metadata','boolean','true'),('6850cc5c9cac51000106fbe0','source','enable_drop_caps_on_posts','boolean','false'),('6850cc5c9cac51000106fbe1','source','show_related_articles','boolean','true'),('6850d04fdb983000014c1c79','dope','title_font','select','Modern sans-serif'),('6850d04fdb983000014c1c7a','dope','body_font','select','Modern sans-serif'),('6850d04fdb983000014c1c7b','dope','show_author','boolean','true'),('6850d04fdb983000014c1c7c','dope','show_related_posts','boolean','true'),('6850ea01db983000014c1ce1','wave','navigation_layout','select','Logo on the left'),('6850ea01db983000014c1ce2','wave','title_font','select','Modern sans-serif'),('6850ea01db983000014c1ce3','wave','body_font','select','Modern sans-serif'),('6850ea01db983000014c1ce4','wave','apple_podcasts_link','text','https://podcasts.apple.com/'),('6850ea01db983000014c1ce5','wave','google_podcasts_link','text','https://podcasts.google.com/'),('6850ea01db983000014c1ce6','wave','spotify_link','text','https://open.spotify.com/'),('6850ea01db983000014c1ce7','wave','rss_link','text',NULL),('68526460db983000014c1cfd','dawn','navigation_layout','select','Logo on the left'),('68526460db983000014c1cfe','dawn','title_font','select','Modern sans-serif'),('68526460db983000014c1cff','dawn','body_font','select','Modern sans-serif'),('68526460db983000014c1d00','dawn','color_scheme','select','Dark'),('68526460db983000014c1d01','dawn','white_logo_for_dark_mode','image',NULL),('68526460db983000014c1d02','dawn','show_featured_posts','boolean','true'),('68526460db983000014c1d03','dawn','featured_title','text','Featured articles'),('68526460db983000014c1d04','dawn','show_author','boolean','false'),('68526460db983000014c1d05','dawn','show_related_posts','boolean','false'),('68529f0fdb983000014c1d08','ope-dawn','navigation_layout','select','Logo on the left'),('68529f0fdb983000014c1d09','ope-dawn','title_font','select','Modern sans-serif'),('68529f0fdb983000014c1d0a','ope-dawn','body_font','select','Modern sans-serif'),('68529f0fdb983000014c1d0b','ope-dawn','color_scheme','select','Dark'),('68529f0fdb983000014c1d0c','ope-dawn','white_logo_for_dark_mode','image',NULL),('68529f0fdb983000014c1d0d','ope-dawn','show_featured_posts','boolean','true'),('68529f0fdb983000014c1d0e','ope-dawn','featured_title','text','Featured articles'),('68529f0fdb983000014c1d0f','ope-dawn','show_author','boolean','true'),('68529f0fdb983000014c1d10','ope-dawn','show_related_posts','boolean','true');
/*!40000 ALTER TABLE `custom_theme_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `donation_payment_events`
--

DROP TABLE IF EXISTS `donation_payment_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `donation_payment_events` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `email` varchar(191) NOT NULL,
  `member_id` varchar(24) DEFAULT NULL,
  `amount` int NOT NULL,
  `currency` varchar(50) NOT NULL,
  `attribution_id` varchar(24) DEFAULT NULL,
  `attribution_type` varchar(50) DEFAULT NULL,
  `attribution_url` varchar(2000) DEFAULT NULL,
  `referrer_source` varchar(191) DEFAULT NULL,
  `referrer_medium` varchar(191) DEFAULT NULL,
  `referrer_url` varchar(2000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `donation_message` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `donation_payment_events_member_id_foreign` (`member_id`),
  CONSTRAINT `donation_payment_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `donation_payment_events`
--

LOCK TABLES `donation_payment_events` WRITE;
/*!40000 ALTER TABLE `donation_payment_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `donation_payment_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_batches`
--

DROP TABLE IF EXISTS `email_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_batches` (
  `id` varchar(24) NOT NULL,
  `email_id` varchar(24) NOT NULL,
  `provider_id` varchar(255) DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `member_segment` text,
  `error_status_code` int unsigned DEFAULT NULL,
  `error_message` varchar(2000) DEFAULT NULL,
  `error_data` longtext,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `email_batches_email_id_foreign` (`email_id`),
  CONSTRAINT `email_batches_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_batches`
--

LOCK TABLES `email_batches` WRITE;
/*!40000 ALTER TABLE `email_batches` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_recipient_failures`
--

DROP TABLE IF EXISTS `email_recipient_failures`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_recipient_failures` (
  `id` varchar(24) NOT NULL,
  `email_id` varchar(24) NOT NULL,
  `member_id` varchar(24) DEFAULT NULL,
  `email_recipient_id` varchar(24) NOT NULL,
  `code` int unsigned NOT NULL,
  `enhanced_code` varchar(50) DEFAULT NULL,
  `message` varchar(2000) NOT NULL,
  `severity` varchar(50) NOT NULL DEFAULT 'permanent',
  `failed_at` datetime NOT NULL,
  `event_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_recipient_failures_email_id_foreign` (`email_id`),
  KEY `email_recipient_failures_email_recipient_id_foreign` (`email_recipient_id`),
  CONSTRAINT `email_recipient_failures_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`),
  CONSTRAINT `email_recipient_failures_email_recipient_id_foreign` FOREIGN KEY (`email_recipient_id`) REFERENCES `email_recipients` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_recipient_failures`
--

LOCK TABLES `email_recipient_failures` WRITE;
/*!40000 ALTER TABLE `email_recipient_failures` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_recipient_failures` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_recipients`
--

DROP TABLE IF EXISTS `email_recipients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_recipients` (
  `id` varchar(24) NOT NULL,
  `email_id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `batch_id` varchar(24) NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  `delivered_at` datetime DEFAULT NULL,
  `opened_at` datetime DEFAULT NULL,
  `failed_at` datetime DEFAULT NULL,
  `member_uuid` varchar(36) NOT NULL,
  `member_email` varchar(191) NOT NULL,
  `member_name` varchar(191) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `email_recipients_member_id_index` (`member_id`),
  KEY `email_recipients_batch_id_foreign` (`batch_id`),
  KEY `email_recipients_email_id_member_email_index` (`email_id`,`member_email`),
  KEY `email_recipients_email_id_delivered_at_index` (`email_id`,`delivered_at`),
  KEY `email_recipients_email_id_opened_at_index` (`email_id`,`opened_at`),
  KEY `email_recipients_email_id_failed_at_index` (`email_id`,`failed_at`),
  CONSTRAINT `email_recipients_batch_id_foreign` FOREIGN KEY (`batch_id`) REFERENCES `email_batches` (`id`),
  CONSTRAINT `email_recipients_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_recipients`
--

LOCK TABLES `email_recipients` WRITE;
/*!40000 ALTER TABLE `email_recipients` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_recipients` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_spam_complaint_events`
--

DROP TABLE IF EXISTS `email_spam_complaint_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_spam_complaint_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `email_id` varchar(24) NOT NULL,
  `email_address` varchar(191) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_spam_complaint_events_email_id_member_id_unique` (`email_id`,`member_id`),
  KEY `email_spam_complaint_events_member_id_foreign` (`member_id`),
  CONSTRAINT `email_spam_complaint_events_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`),
  CONSTRAINT `email_spam_complaint_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_spam_complaint_events`
--

LOCK TABLES `email_spam_complaint_events` WRITE;
/*!40000 ALTER TABLE `email_spam_complaint_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `email_spam_complaint_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `emails`
--

DROP TABLE IF EXISTS `emails`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `emails` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `uuid` varchar(36) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `recipient_filter` text NOT NULL,
  `error` varchar(2000) DEFAULT NULL,
  `error_data` longtext,
  `email_count` int unsigned NOT NULL DEFAULT '0',
  `delivered_count` int unsigned NOT NULL DEFAULT '0',
  `opened_count` int unsigned NOT NULL DEFAULT '0',
  `failed_count` int unsigned NOT NULL DEFAULT '0',
  `subject` varchar(300) DEFAULT NULL,
  `from` varchar(2000) DEFAULT NULL,
  `reply_to` varchar(2000) DEFAULT NULL,
  `html` longtext,
  `plaintext` longtext,
  `source` longtext,
  `source_type` varchar(50) NOT NULL DEFAULT 'html',
  `track_opens` tinyint(1) NOT NULL DEFAULT '0',
  `track_clicks` tinyint(1) NOT NULL DEFAULT '0',
  `feedback_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `submitted_at` datetime NOT NULL,
  `newsletter_id` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `emails_post_id_unique` (`post_id`),
  KEY `emails_post_id_index` (`post_id`),
  KEY `emails_newsletter_id_foreign` (`newsletter_id`),
  CONSTRAINT `emails_newsletter_id_foreign` FOREIGN KEY (`newsletter_id`) REFERENCES `newsletters` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `emails`
--

LOCK TABLES `emails` WRITE;
/*!40000 ALTER TABLE `emails` DISABLE KEYS */;
/*!40000 ALTER TABLE `emails` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `integrations`
--

DROP TABLE IF EXISTS `integrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `integrations` (
  `id` varchar(24) NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'custom',
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `icon_image` varchar(2000) DEFAULT NULL,
  `description` varchar(2000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `integrations_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `integrations`
--

LOCK TABLES `integrations` WRITE;
/*!40000 ALTER TABLE `integrations` DISABLE KEYS */;
INSERT INTO `integrations` VALUES ('6850cc589cac51000106f9b8','builtin','Zapier','zapier',NULL,'Built-in Zapier integration','2025-06-17 02:00:56','1','2025-06-17 02:00:56','1'),('6850cc589cac51000106f9ba','core','Ghost Explore','ghost-explore',NULL,'Built-in Ghost Explore integration','2025-06-17 02:00:56','1','2025-06-17 02:00:56','1'),('6850cc589cac51000106f9bc','core','Self-Serve Migration Integration','self-serve-migration',NULL,'Core Self-Serve Migration integration','2025-06-17 02:00:56','1','2025-06-17 02:00:56','1'),('6850cc589cac51000106f9be','internal','Ghost Backup','ghost-backup',NULL,'Internal DB Backup integration','2025-06-17 02:00:56','1','2025-06-17 02:00:56','1'),('6850cc589cac51000106f9c0','internal','Ghost Scheduler','ghost-scheduler',NULL,'Internal Scheduler integration','2025-06-17 02:00:56','1','2025-06-17 02:00:56','1'),('6850cc599cac51000106f9c2','internal','Ghost Internal Frontend','ghost-internal-frontend',NULL,'Internal frontend integration','2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc599cac51000106f9c4','core','Ghost Core Content API','ghost-core-content',NULL,'Internal Content API integration for Admin access','2025-06-17 02:00:57','1','2025-06-17 02:00:57','1'),('6850cc599cac51000106f9c6','internal','Ghost ActivityPub','ghost-activitypub',NULL,'Internal Integration for ActivityPub','2025-06-17 02:00:57','1','2025-06-17 02:00:57','1');
/*!40000 ALTER TABLE `integrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `invites`
--

DROP TABLE IF EXISTS `invites`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `invites` (
  `id` varchar(24) NOT NULL,
  `role_id` varchar(24) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'pending',
  `token` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `expires` bigint NOT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invites_token_unique` (`token`),
  UNIQUE KEY `invites_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `invites`
--

LOCK TABLES `invites` WRITE;
/*!40000 ALTER TABLE `invites` DISABLE KEYS */;
/*!40000 ALTER TABLE `invites` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jobs` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'queued',
  `started_at` datetime DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `metadata` varchar(2000) DEFAULT NULL,
  `queue_entry` int unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `jobs_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jobs`
--

LOCK TABLES `jobs` WRITE;
/*!40000 ALTER TABLE `jobs` DISABLE KEYS */;
INSERT INTO `jobs` VALUES ('6850cc5f9cac51000106fbe2','members-migrations','finished','2025-06-17 02:01:04','2025-06-17 02:01:04','2025-06-17 02:01:03','2025-06-17 02:01:04',NULL,NULL);
/*!40000 ALTER TABLE `jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `labels`
--

DROP TABLE IF EXISTS `labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `labels` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `labels_name_unique` (`name`),
  UNIQUE KEY `labels_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `labels`
--

LOCK TABLES `labels` WRITE;
/*!40000 ALTER TABLE `labels` DISABLE KEYS */;
/*!40000 ALTER TABLE `labels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members`
--

DROP TABLE IF EXISTS `members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members` (
  `id` varchar(24) NOT NULL,
  `uuid` varchar(36) DEFAULT NULL,
  `transient_id` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'free',
  `name` varchar(191) DEFAULT NULL,
  `expertise` varchar(191) DEFAULT NULL,
  `note` varchar(2000) DEFAULT NULL,
  `geolocation` varchar(2000) DEFAULT NULL,
  `enable_comment_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `email_count` int unsigned NOT NULL DEFAULT '0',
  `email_opened_count` int unsigned NOT NULL DEFAULT '0',
  `email_open_rate` int unsigned DEFAULT NULL,
  `email_disabled` tinyint(1) NOT NULL DEFAULT '0',
  `last_seen_at` datetime DEFAULT NULL,
  `last_commented_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `members_transient_id_unique` (`transient_id`),
  UNIQUE KEY `members_email_unique` (`email`),
  UNIQUE KEY `members_uuid_unique` (`uuid`),
  KEY `members_email_open_rate_index` (`email_open_rate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members`
--

LOCK TABLES `members` WRITE;
/*!40000 ALTER TABLE `members` DISABLE KEYS */;
/*!40000 ALTER TABLE `members` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_cancel_events`
--

DROP TABLE IF EXISTS `members_cancel_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_cancel_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `from_plan` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_cancel_events_member_id_foreign` (`member_id`),
  CONSTRAINT `members_cancel_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_cancel_events`
--

LOCK TABLES `members_cancel_events` WRITE;
/*!40000 ALTER TABLE `members_cancel_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_cancel_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_click_events`
--

DROP TABLE IF EXISTS `members_click_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_click_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `redirect_id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_click_events_member_id_foreign` (`member_id`),
  KEY `members_click_events_redirect_id_foreign` (`redirect_id`),
  CONSTRAINT `members_click_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_click_events_redirect_id_foreign` FOREIGN KEY (`redirect_id`) REFERENCES `redirects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_click_events`
--

LOCK TABLES `members_click_events` WRITE;
/*!40000 ALTER TABLE `members_click_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_click_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_created_events`
--

DROP TABLE IF EXISTS `members_created_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_created_events` (
  `id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `attribution_id` varchar(24) DEFAULT NULL,
  `attribution_type` varchar(50) DEFAULT NULL,
  `attribution_url` varchar(2000) DEFAULT NULL,
  `referrer_source` varchar(191) DEFAULT NULL,
  `referrer_medium` varchar(191) DEFAULT NULL,
  `referrer_url` varchar(2000) DEFAULT NULL,
  `source` varchar(50) NOT NULL,
  `batch_id` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `members_created_events_member_id_foreign` (`member_id`),
  KEY `members_created_events_attribution_id_index` (`attribution_id`),
  CONSTRAINT `members_created_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_created_events`
--

LOCK TABLES `members_created_events` WRITE;
/*!40000 ALTER TABLE `members_created_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_created_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_email_change_events`
--

DROP TABLE IF EXISTS `members_email_change_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_email_change_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `to_email` varchar(191) NOT NULL,
  `from_email` varchar(191) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_email_change_events_member_id_foreign` (`member_id`),
  CONSTRAINT `members_email_change_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_email_change_events`
--

LOCK TABLES `members_email_change_events` WRITE;
/*!40000 ALTER TABLE `members_email_change_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_email_change_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_feedback`
--

DROP TABLE IF EXISTS `members_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_feedback` (
  `id` varchar(24) NOT NULL,
  `score` int unsigned NOT NULL DEFAULT '0',
  `member_id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `members_feedback_member_id_foreign` (`member_id`),
  KEY `members_feedback_post_id_foreign` (`post_id`),
  CONSTRAINT `members_feedback_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_feedback_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_feedback`
--

LOCK TABLES `members_feedback` WRITE;
/*!40000 ALTER TABLE `members_feedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_feedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_labels`
--

DROP TABLE IF EXISTS `members_labels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_labels` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `label_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `members_labels_member_id_foreign` (`member_id`),
  KEY `members_labels_label_id_foreign` (`label_id`),
  CONSTRAINT `members_labels_label_id_foreign` FOREIGN KEY (`label_id`) REFERENCES `labels` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_labels_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_labels`
--

LOCK TABLES `members_labels` WRITE;
/*!40000 ALTER TABLE `members_labels` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_labels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_login_events`
--

DROP TABLE IF EXISTS `members_login_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_login_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_login_events_member_id_foreign` (`member_id`),
  CONSTRAINT `members_login_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_login_events`
--

LOCK TABLES `members_login_events` WRITE;
/*!40000 ALTER TABLE `members_login_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_login_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_newsletters`
--

DROP TABLE IF EXISTS `members_newsletters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_newsletters` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `newsletter_id` varchar(24) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_newsletters_member_id_foreign` (`member_id`),
  KEY `members_newsletters_newsletter_id_member_id_index` (`newsletter_id`,`member_id`),
  CONSTRAINT `members_newsletters_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_newsletters_newsletter_id_foreign` FOREIGN KEY (`newsletter_id`) REFERENCES `newsletters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_newsletters`
--

LOCK TABLES `members_newsletters` WRITE;
/*!40000 ALTER TABLE `members_newsletters` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_newsletters` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_paid_subscription_events`
--

DROP TABLE IF EXISTS `members_paid_subscription_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_paid_subscription_events` (
  `id` varchar(24) NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `member_id` varchar(24) NOT NULL,
  `subscription_id` varchar(24) DEFAULT NULL,
  `from_plan` varchar(255) DEFAULT NULL,
  `to_plan` varchar(255) DEFAULT NULL,
  `currency` varchar(191) NOT NULL,
  `source` varchar(50) NOT NULL,
  `mrr_delta` int NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_paid_subscription_events_member_id_foreign` (`member_id`),
  CONSTRAINT `members_paid_subscription_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_paid_subscription_events`
--

LOCK TABLES `members_paid_subscription_events` WRITE;
/*!40000 ALTER TABLE `members_paid_subscription_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_paid_subscription_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_payment_events`
--

DROP TABLE IF EXISTS `members_payment_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_payment_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `amount` int NOT NULL,
  `currency` varchar(191) NOT NULL,
  `source` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_payment_events_member_id_foreign` (`member_id`),
  CONSTRAINT `members_payment_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_payment_events`
--

LOCK TABLES `members_payment_events` WRITE;
/*!40000 ALTER TABLE `members_payment_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_payment_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_product_events`
--

DROP TABLE IF EXISTS `members_product_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_product_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `product_id` varchar(24) NOT NULL,
  `action` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_product_events_member_id_foreign` (`member_id`),
  KEY `members_product_events_product_id_foreign` (`product_id`),
  CONSTRAINT `members_product_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_product_events_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_product_events`
--

LOCK TABLES `members_product_events` WRITE;
/*!40000 ALTER TABLE `members_product_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_product_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_products`
--

DROP TABLE IF EXISTS `members_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_products` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `product_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  `expiry_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `members_products_member_id_foreign` (`member_id`),
  KEY `members_products_product_id_foreign` (`product_id`),
  CONSTRAINT `members_products_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_products`
--

LOCK TABLES `members_products` WRITE;
/*!40000 ALTER TABLE `members_products` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_status_events`
--

DROP TABLE IF EXISTS `members_status_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_status_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `from_status` varchar(50) DEFAULT NULL,
  `to_status` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `members_status_events_member_id_foreign` (`member_id`),
  CONSTRAINT `members_status_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_status_events`
--

LOCK TABLES `members_status_events` WRITE;
/*!40000 ALTER TABLE `members_status_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_status_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_stripe_customers`
--

DROP TABLE IF EXISTS `members_stripe_customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_stripe_customers` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `customer_id` varchar(255) NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `email` varchar(191) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `members_stripe_customers_customer_id_unique` (`customer_id`),
  KEY `members_stripe_customers_member_id_foreign` (`member_id`),
  CONSTRAINT `members_stripe_customers_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_stripe_customers`
--

LOCK TABLES `members_stripe_customers` WRITE;
/*!40000 ALTER TABLE `members_stripe_customers` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_stripe_customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_stripe_customers_subscriptions`
--

DROP TABLE IF EXISTS `members_stripe_customers_subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_stripe_customers_subscriptions` (
  `id` varchar(24) NOT NULL,
  `customer_id` varchar(255) NOT NULL,
  `ghost_subscription_id` varchar(24) DEFAULT NULL,
  `subscription_id` varchar(255) NOT NULL,
  `stripe_price_id` varchar(255) NOT NULL DEFAULT '',
  `status` varchar(50) NOT NULL,
  `cancel_at_period_end` tinyint(1) NOT NULL DEFAULT '0',
  `cancellation_reason` varchar(500) DEFAULT NULL,
  `current_period_end` datetime NOT NULL,
  `start_date` datetime NOT NULL,
  `default_payment_card_last4` varchar(4) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  `mrr` int unsigned NOT NULL DEFAULT '0',
  `offer_id` varchar(24) DEFAULT NULL,
  `trial_start_at` datetime DEFAULT NULL,
  `trial_end_at` datetime DEFAULT NULL,
  `plan_id` varchar(255) NOT NULL,
  `plan_nickname` varchar(50) NOT NULL,
  `plan_interval` varchar(50) NOT NULL,
  `plan_amount` int NOT NULL,
  `plan_currency` varchar(191) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `members_stripe_customers_subscriptions_subscription_id_unique` (`subscription_id`),
  KEY `members_stripe_customers_subscriptions_customer_id_foreign` (`customer_id`),
  KEY `mscs_ghost_subscription_id_foreign` (`ghost_subscription_id`),
  KEY `members_stripe_customers_subscriptions_stripe_price_id_index` (`stripe_price_id`),
  KEY `members_stripe_customers_subscriptions_offer_id_foreign` (`offer_id`),
  CONSTRAINT `members_stripe_customers_subscriptions_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `members_stripe_customers` (`customer_id`) ON DELETE CASCADE,
  CONSTRAINT `members_stripe_customers_subscriptions_offer_id_foreign` FOREIGN KEY (`offer_id`) REFERENCES `offers` (`id`),
  CONSTRAINT `mscs_ghost_subscription_id_foreign` FOREIGN KEY (`ghost_subscription_id`) REFERENCES `subscriptions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_stripe_customers_subscriptions`
--

LOCK TABLES `members_stripe_customers_subscriptions` WRITE;
/*!40000 ALTER TABLE `members_stripe_customers_subscriptions` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_stripe_customers_subscriptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_subscribe_events`
--

DROP TABLE IF EXISTS `members_subscribe_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_subscribe_events` (
  `id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `subscribed` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL,
  `source` varchar(50) DEFAULT NULL,
  `newsletter_id` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `members_subscribe_events_member_id_foreign` (`member_id`),
  KEY `members_subscribe_events_newsletter_id_foreign` (`newsletter_id`),
  CONSTRAINT `members_subscribe_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_subscribe_events_newsletter_id_foreign` FOREIGN KEY (`newsletter_id`) REFERENCES `newsletters` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_subscribe_events`
--

LOCK TABLES `members_subscribe_events` WRITE;
/*!40000 ALTER TABLE `members_subscribe_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_subscribe_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_subscription_created_events`
--

DROP TABLE IF EXISTS `members_subscription_created_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_subscription_created_events` (
  `id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `subscription_id` varchar(24) NOT NULL,
  `attribution_id` varchar(24) DEFAULT NULL,
  `attribution_type` varchar(50) DEFAULT NULL,
  `attribution_url` varchar(2000) DEFAULT NULL,
  `referrer_source` varchar(191) DEFAULT NULL,
  `referrer_medium` varchar(191) DEFAULT NULL,
  `referrer_url` varchar(2000) DEFAULT NULL,
  `batch_id` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `members_subscription_created_events_member_id_foreign` (`member_id`),
  KEY `members_subscription_created_events_subscription_id_foreign` (`subscription_id`),
  KEY `members_subscription_created_events_attribution_id_index` (`attribution_id`),
  CONSTRAINT `members_subscription_created_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `members_subscription_created_events_subscription_id_foreign` FOREIGN KEY (`subscription_id`) REFERENCES `members_stripe_customers_subscriptions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_subscription_created_events`
--

LOCK TABLES `members_subscription_created_events` WRITE;
/*!40000 ALTER TABLE `members_subscription_created_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_subscription_created_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mentions`
--

DROP TABLE IF EXISTS `mentions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mentions` (
  `id` varchar(24) NOT NULL,
  `source` varchar(2000) NOT NULL,
  `source_title` varchar(2000) DEFAULT NULL,
  `source_site_title` varchar(2000) DEFAULT NULL,
  `source_excerpt` varchar(2000) DEFAULT NULL,
  `source_author` varchar(2000) DEFAULT NULL,
  `source_featured_image` varchar(2000) DEFAULT NULL,
  `source_favicon` varchar(2000) DEFAULT NULL,
  `target` varchar(2000) NOT NULL,
  `resource_id` varchar(24) DEFAULT NULL,
  `resource_type` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `payload` text,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `verified` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mentions`
--

LOCK TABLES `mentions` WRITE;
/*!40000 ALTER TABLE `mentions` DISABLE KEYS */;
/*!40000 ALTER TABLE `mentions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(120) NOT NULL,
  `version` varchar(70) NOT NULL,
  `currentVersion` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `migrations_name_version_unique` (`name`,`version`)
) ENGINE=InnoDB AUTO_INCREMENT=355 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'1-create-tables.js','init','5.125'),(2,'2-create-fixtures.js','init','5.125'),(3,'01-final-v1.js','1.25','5.125'),(4,'02-noop.js','1.25','5.125'),(5,'01-final-v2.js','2.37','5.125'),(6,'01-final-v3.js','3.41','5.125'),(7,'01-update-mobiledoc.js','4.0','5.125'),(8,'02-add-status-column-to-members.js','4.0','5.125'),(9,'03-populate-status-column-for-members.js','4.0','5.125'),(10,'04-drop-apps-related-tables.js','4.0','5.125'),(11,'05-add-members-subscribe-events-table.js','4.0','5.125'),(12,'06-populate-members-subscribe-events-table.js','4.0','5.125'),(13,'07-alter-unique-constraint-for-posts-slug.js','4.0','5.125'),(14,'08-add-members-login-events-table.js','4.0','5.125'),(15,'09-add-members-email-change-events-table.js','4.0','5.125'),(16,'10-add-members-status-events-table.js','4.0','5.125'),(17,'11-add-members-paid-subscription-events-table.js','4.0','5.125'),(18,'12-delete-apps-related-settings-keys.js','4.0','5.125'),(19,'13-add-members-payment-events-table.js','4.0','5.125'),(20,'14-remove-orphaned-stripe-records.js','4.0','5.125'),(21,'15-add-frontmatter-column-to-meta.js','4.0','5.125'),(22,'16-refactor-slack-setting.js','4.0','5.125'),(23,'17-populate-members-status-events-table.js','4.0','5.125'),(24,'18-transform-urls-absolute-to-transform-ready.js','4.0','5.125'),(25,'19-remove-labs-members-setting.js','4.0','5.125'),(26,'20-refactor-unsplash-setting.js','4.0','5.125'),(27,'21-sanitize-email-batches-provider-id.js','4.0','5.125'),(28,'22-solve-orphaned-webhooks.js','4.0','5.125'),(29,'23-regenerate-posts-html.js','4.0','5.125'),(30,'24-add-missing-email-permissions.js','4.0','5.125'),(31,'25-populate-members-paid-subscription-events-table.js','4.0','5.125'),(32,'26-add-cascade-on-delete.js','4.0','5.125'),(33,'27-add-primary-key-brute-migrations-lock.js','4.0','5.125'),(34,'28-add-webhook-intergrations-foreign-key.js','4.0','5.125'),(35,'29-fix-foreign-key-for-members-stripe-customers-subscriptions.js','4.0','5.125'),(36,'30-set-default-accent-color.js','4.0','5.125'),(37,'01-fix-backup-content-permission-typo.js','4.1','5.125'),(38,'02-add-unique-constraint-for-member-stripe-tables.js','4.1','5.125'),(39,'01-fix-incorrect-mrr-delta-events.js','4.2','5.125'),(40,'01-add-products-table.js','4.3','5.125'),(41,'02-add-members-products-table.js','4.3','5.125'),(42,'03-add-default-product.js','4.3','5.125'),(43,'04-attach-members-to-product.js','4.3','5.125'),(44,'05-add-stripe-products-table.js','4.3','5.125'),(45,'06-add-stripe-prices-table.js','4.3','5.125'),(46,'07-add-products-permissions.js','4.3','5.125'),(47,'08-migrate-members-signup-setting.js','4.3','5.125'),(48,'09-add-price-id-column-to-subscriptions-table.js','4.3','5.125'),(49,'10-populate-stripe-price-id-in-subscriptions.js','4.3','5.125'),(50,'01-restore-free-members-signup-setting-from-backup.js','4.4','5.125'),(51,'02-migrate-members-signup-access.js','4.4','5.125'),(52,'01-add-stripe-price-description-column.js','4.5','5.125'),(53,'02-add-product-description-column.js','4.5','5.125'),(54,'03-give-label-read-permissions-to-editors.js','4.5','5.125'),(55,'04-remove-unique-constraint-from-product-name.js','4.5','5.125'),(56,'05-rename-default-product-to-site-title.js','4.5','5.125'),(57,'01-remove-comped-status.js','4.6','5.125'),(58,'01-add-monthly-price-column-to-products.js','4.7','5.125'),(59,'02-add-yearly-price-column-to-products.js','4.7','5.125'),(60,'03-add-labs-setting.js','4.7','5.125'),(61,'01-add-feature-image-alt-column-to-posts-meta.js','4.8','5.125'),(62,'02-add-feature-image-caption-column-to-posts-meta.js','4.8','5.125'),(63,'03-add-default-product-portal-products.js','4.8','5.125'),(64,'04-migrate-show-newsletter-header-setting.js','4.8','5.125'),(65,'01-add-reset-all-passwords-permission.js','4.9','5.125'),(66,'02-add-benefits-table.js','4.9','5.125'),(67,'03-add-products-benefits-table.js','4.9','5.125'),(68,'04-add-member-segment-to-email-batches.js','4.9','5.125'),(69,'05-fix-missed-mobiledoc-url-transforms.js','4.9','5.125'),(70,'06-add-comped-status.js','4.9','5.125'),(71,'07-update-comped-members-status-events.js','4.9','5.125'),(72,'01-add-oauth-user-data.js','4.11','5.125'),(73,'02-add-email-verification-required-setting.js','4.11','5.125'),(74,'01-add-email-only-column-to-posts-meta-table.js','4.12','5.125'),(75,'02-fix-member-statuses.js','4.12','5.125'),(76,'01-add-members-stripe-connect-auth-permission-to-administrators.js','4.13','5.125'),(77,'02-add-members-products-events-table.js','4.13','5.125'),(78,'01-fix-comped-member-statuses.js','4.14','5.125'),(79,'02-fix-free-members-status-events.js','4.14','5.125'),(80,'01-add-temp-members-analytic-events-table.js','4.15','5.125'),(81,'01-add-custom-theme-settings-table.js','4.16','5.125'),(82,'01-add-custom-theme-settings-permissions.js','4.17','5.125'),(83,'02-add-offers-table.js','4.17','5.125'),(84,'03-add-offers-permissions.js','4.17','5.125'),(85,'01-add-active-column-to-offers.js','4.19','5.125'),(86,'02-add-offer-redemptions-table.js','4.19','5.125'),(87,'01-remove-offer-redemptions-table.js','4.20','5.125'),(88,'02-remove-offers-table.js','4.20','5.125'),(89,'03-add-offers-table.js','4.20','5.125'),(90,'04-add-offer-redemptions-table.js','4.20','5.125'),(91,'05-remove-not-null-constraint-from-portal-title.js','4.20','5.125'),(92,'01-add-is-launch-complete-setting.js','4.22','5.125'),(93,'02-update-launch-complete-setting-from-user-data.js','4.22','5.125'),(94,'01-truncate-offer-names.js','4.23','5.125'),(95,'2022-01-14-11-50-add-type-column-to-products.js','4.33','5.125'),(96,'2022-01-14-11-51-add-default-free-tier.js','4.33','5.125'),(97,'2022-01-18-09-07-remove-duplicate-offer-redemptions.js','4.33','5.125'),(98,'2022-01-19-10-43-add-active-column-to-products-table.js','4.33','5.125'),(99,'2022-01-25-13-53-add-welcome-page-url-column-to-products.js','4.34','5.125'),(100,'2022-01-20-05-55-add-post-products-table.js','4.35','5.125'),(101,'2022-01-30-15-17-set-welcome-page-url-from-settings.js','4.35','5.125'),(102,'2022-02-01-11-48-update-email-recipient-filter-column-type.js','4.35','5.125'),(103,'2022-02-01-12-03-update-recipient-filter-column-type.js','4.35','5.125'),(104,'2022-02-02-10-38-add-default-content-visibility-tiers-setting.js','4.35','5.125'),(105,'2022-02-02-13-10-transform-specific-tiers-default-content-visibility.js','4.35','5.125'),(106,'2022-02-04-04-34-populate-empty-portal-products.js','4.35','5.125'),(107,'2022-02-07-14-34-add-last-seen-at-column-to-members.js','4.36','5.125'),(108,'2022-02-21-09-53-backfill-members-last-seen-at-column.js','4.37','5.125'),(109,'2022-03-01-08-46-add-visibility-to-tiers.js','4.38','5.125'),(110,'2022-03-03-16-12-add-visibility-to-tiers.js','4.38','5.125'),(111,'2022-03-03-16-17-drop-tiers-visible-column.js','4.38','5.125'),(112,'2022-03-07-10-57-update-free-products-visibility-column.js','4.39','5.125'),(113,'2022-03-07-10-57-update-products-visibility-column.js','4.39','5.125'),(114,'2022-03-07-14-37-add-members-cancel-events-table.js','4.40','5.125'),(115,'2022-03-15-06-40-add-offers-admin-integration-permission-roles.js','4.40','5.125'),(116,'2022-03-15-06-40-add-tiers-admin-integration-permission-roles.js','4.40','5.125'),(117,'2022-03-21-17-17-add.js','4.42','5.125'),(118,'2022-03-30-15-44-add-newsletter-permissions.js','4.42','5.125'),(119,'2022-03-28-19-26-recreate-newsletter-table.js','4.43','5.125'),(120,'2022-03-29-14-45-add-members-newsletters-table.js','4.43','5.125'),(121,'2022-04-01-10-13-add-post-newsletter-relation.js','4.43','5.125'),(122,'2022-04-06-09-47-add-type-column-to-paid-subscription-events.js','4.43','5.125'),(123,'2022-04-06-14-56-add-email-newsletter-relation.js','4.43','5.125'),(124,'2022-04-08-10-45-add-subscription-id-to-mrr-events.js','4.43','5.125'),(125,'2022-04-06-15-22-populate-type-column-for-paid-subscription-events.js','4.44','5.125'),(126,'2022-04-08-11-54-add-cancelled-events.js','4.44','5.125'),(127,'2022-04-11-08-24-add-newsletter-permissions.js','4.44','5.125'),(128,'2022-04-11-10-54-add-mrr-to-subscriptions.js','4.44','5.125'),(129,'2022-04-12-07-33-fill-mrr.js','4.44','5.125'),(130,'2022-04-13-12-00-remove-newsletter-sender-name-not-null-constraint.js','4.44','5.125'),(131,'2022-04-15-07-53-add-offer-id-to-subscriptions.js','4.44','5.125'),(132,'2022-04-19-12-23-backfill-subscriptions-offers.js','4.45','5.125'),(133,'2022-04-20-11-25-add-newsletter-read-permission.js','4.45','5.125'),(134,'2022-04-21-02-55-add-notifications-key-entry-to-settings-table.js','4.45','5.125'),(135,'2022-04-13-12-00-add-created-at-newsletters.js','4.46','5.125'),(136,'2022-04-13-12-01-add-updated-at-newsletters.js','4.46','5.125'),(137,'2022-04-13-12-02-fill-created-at-newsletters.js','4.46','5.125'),(138,'2022-04-13-12-03-drop-nullable-created-at-newsletters.js','4.46','5.125'),(139,'2022-04-13-12-08-newsletters-show-header-name.js','4.46','5.125'),(140,'2022-04-13-12-57-add-uuid-column-to-newsletters.js','4.46','5.125'),(141,'2022-04-13-12-58-fill-uuid-for-newsletters.js','4.46','5.125'),(142,'2022-04-13-12-59-drop-nullable-uuid-newsletters.js','4.46','5.125'),(143,'2022-04-13-13-00-add-default-newsletter.js','4.46','5.125'),(144,'2022-04-20-08-39-map-subscribers-to-default-newsletter.js','4.46','5.125'),(145,'2022-04-22-07-43-add-newsletter-id-to-subscribe-events.js','4.46','5.125'),(146,'2022-04-27-07-59-set-newsletter-id-subscribe-events.js','4.46','5.125'),(147,'2022-05-03-15-30-update-newsletter-sending-options.js','4.47','5.125'),(148,'2022-05-04-10-03-transform-newsletter-header-image.js','4.47','5.125'),(149,'2022-03-14-12-33-delete-duplicate-offer-redemptions.js','5.0','5.125'),(150,'2022-03-28-15-25-backfill-mrr-adjustments-for-offers.js','5.0','5.125'),(151,'2022-04-25-10-32-backfill-mrr-for-discounted-subscriptions.js','5.0','5.125'),(152,'2022-04-26-15-44-backfill-mrr-events-for-canceled-subscriptions.js','5.0','5.125'),(153,'2022-04-27-11-26-backfill-mrr-for-canceled-subscriptions.js','5.0','5.125'),(154,'2022-04-28-03-26-remove-author-id-column-from-posts-table.js','5.0','5.125'),(155,'2022-05-03-09-39-drop-nullable-subscribe-event-newsletter-id.js','5.0','5.125'),(156,'2022-05-04-15-24-map-existing-emails-to-default-newsletter.js','5.0','5.125'),(157,'2022-05-05-13-13-migrate-legacy-recipient-filters.js','5.0','5.125'),(158,'2022-05-05-13-29-add-newsletters-admin-integration-permission-roles.js','5.0','5.125'),(159,'2022-05-05-15-17-drop-oauth-table.js','5.0','5.125'),(160,'2022-05-06-08-16-cleanup-client-subscriber-permissions.js','5.0','5.125'),(161,'2022-05-06-13-22-add-frontend-integration.js','5.0','5.125'),(162,'2022-05-09-10-00-drop-members-subscribed-column.js','5.0','5.125'),(163,'2022-05-09-14-17-cleanup-invalid-users-status.js','5.0','5.125'),(164,'2022-05-10-08-33-drop-members-analytics-table.js','5.0','5.125'),(165,'2022-05-10-14-57-cleanup-invalid-posts-status.js','5.0','5.125'),(166,'2022-05-11-12-08-drop-webhooks-status-column.js','5.0','5.125'),(167,'2022-05-11-13-12-rename-settings.js','5.0','5.125'),(168,'2022-05-11-16-36-remove-unused-settings.js','5.0','5.125'),(169,'2022-05-12-10-29-add-newsletter-permissions-for-editors-and-authors.js','5.0','5.125'),(170,'2022-05-12-13-51-add-label-permissions-for-authors.js','5.0','5.125'),(171,'2022-05-13-11-38-drop-none-email-recipient-filter.js','5.0','5.125'),(172,'2022-05-21-00-00-regenerate-posts-html.js','5.0','5.125'),(173,'2022-07-04-13-49-add-comments-table.js','5.3','5.125'),(174,'2022-07-05-09-36-add-comments-likes-table.js','5.3','5.125'),(175,'2022-07-05-09-47-add-comments-reports-table.js','5.3','5.125'),(176,'2022-07-05-10-00-add-comment-related-fields-to-members.js','5.3','5.125'),(177,'2022-07-05-12-55-add-comments-crud-permissions.js','5.3','5.125'),(178,'2022-07-05-15-35-add-comment-notifications-field-to-users-table.js','5.3','5.125'),(179,'2022-07-06-07-26-add-comments-enabled-setting.js','5.3','5.125'),(180,'2022-07-06-07-58-add-ghost-explore-integration-role.js','5.3','5.125'),(181,'2022-07-06-09-13-add-ghost-explore-integration-role-permissions.js','5.3','5.125'),(182,'2022-07-06-09-17-add-ghost-explore-integration.js','5.3','5.125'),(183,'2022-07-06-09-26-add-ghost-explore-integration-api-key.js','5.3','5.125'),(184,'2022-07-18-14-29-add-comment-reporting-permissions.js','5.5','5.125'),(185,'2022-07-18-14-31-drop-reports-reason.js','5.5','5.125'),(186,'2022-07-18-14-32-drop-nullable-member-id-from-likes.js','5.5','5.125'),(187,'2022-07-18-14-33-fix-comments-on-delete-foreign-keys.js','5.5','5.125'),(188,'2022-07-21-08-56-add-jobs-table.js','5.5','5.125'),(189,'2022-07-27-13-40-change-explore-type.js','5.6','5.125'),(190,'2022-08-02-06-09-add-trial-period-days-column-to-tiers.js','5.8','5.125'),(191,'2022-08-03-15-28-add-trial-start-column-to-stripe-subscriptions.js','5.8','5.125'),(192,'2022-08-03-15-29-add-trial-end-column-to-stripe-subscriptions.js','5.8','5.125'),(193,'2022-08-09-08-32-added-new-integration-type.js','5.9','5.125'),(194,'2022-08-15-05-34-add-expiry-at-column-to-members-products.js','5.10','5.125'),(195,'2022-08-16-14-25-add-member-created-events-table.js','5.10','5.125'),(196,'2022-08-16-14-25-add-subscription-created-events-table.js','5.10','5.125'),(197,'2022-08-19-14-15-fix-comments-deletion-strategy.js','5.10','5.125'),(198,'2022-08-22-11-03-add-member-alert-settings-columns-to-users.js','5.11','5.125'),(199,'2022-08-23-13-41-backfill-members-created-events.js','5.11','5.125'),(200,'2022-08-23-13-59-fix-page-resource-type.js','5.11','5.125'),(201,'2022-09-02-12-55-rename-members-bio-to-expertise.js','5.14','5.125'),(202,'2022-09-12-16-10-add-posts-lexical-column.js','5.15','5.125'),(203,'2022-09-14-12-46-add-email-track-clicks-setting.js','5.15','5.125'),(204,'2022-09-16-08-22-add-post-revisions-table.js','5.15','5.125'),(205,'2022-09-19-09-04-add-link-redirects-table.js','5.16','5.125'),(206,'2022-09-19-09-05-add-members-link-click-events-table.js','5.16','5.125'),(207,'2022-09-19-17-44-add-referrer-columns-to-member-events-table.js','5.16','5.125'),(208,'2022-09-19-17-44-add-referrer-columns-to-subscription-events-table.js','5.16','5.125'),(209,'2022-09-27-13-53-remove-click-tracking-tables.js','5.17','5.125'),(210,'2022-09-27-13-55-add-redirects-table.js','5.17','5.125'),(211,'2022-09-27-13-56-add-members-click-events-table.js','5.17','5.125'),(212,'2022-09-27-16-49-set-track-clicks-based-on-opens.js','5.17','5.125'),(213,'2022-09-29-12-39-add-track-clicks-column-to-emails.js','5.17','5.125'),(214,'2022-09-02-20-25-add-columns-to-products-table.js','5.19','5.125'),(215,'2022-09-02-20-52-backfill-new-product-columns.js','5.19','5.125'),(216,'2022-10-10-06-58-add-subscriptions-table.js','5.19','5.125'),(217,'2022-10-10-10-05-add-members-feedback-table.js','5.19','5.125'),(218,'2022-10-11-10-38-add-feedback-enabled-column-to-newsletters.js','5.19','5.125'),(219,'2022-10-18-05-39-drop-nullable-tier-id.js','5.20','5.125'),(220,'2022-10-18-10-13-add-ghost-subscription-id-column-to-mscs.js','5.20','5.125'),(221,'2022-10-19-11-17-add-link-browse-permissions.js','5.20','5.125'),(222,'2022-10-20-02-52-add-link-edit-permissions.js','5.20','5.125'),(223,'2022-10-24-07-23-disable-feedback-enabled.js','5.21','5.125'),(224,'2022-10-25-12-05-backfill-missed-products-columns.js','5.21','5.125'),(225,'2022-10-26-04-49-add-batch-id-members-created-events.js','5.21','5.125'),(226,'2022-10-26-04-49-add-batch-id-subscription-created-events.js','5.21','5.125'),(227,'2022-10-26-04-50-member-subscription-created-batch-id.js','5.21','5.125'),(228,'2022-10-26-09-32-add-feedback-enabled-column-to-emails.js','5.21','5.125'),(229,'2022-10-27-09-50-add-member-track-source-setting.js','5.21','5.125'),(230,'2022-10-31-12-03-backfill-new-product-columns.js','5.22','5.125'),(231,'2022-11-21-09-32-add-source-columns-to-emails-table.js','5.24','5.125'),(232,'2022-11-21-15-03-populate-source-column-with-html-for-emails.js','5.24','5.125'),(233,'2022-11-21-15-57-add-error-columns-for-email-batches.js','5.24','5.125'),(234,'2022-11-24-10-36-add-suppressions-table.js','5.25','5.125'),(235,'2022-11-24-10-37-add-email-spam-complaint-events-table.js','5.25','5.125'),(236,'2022-11-29-08-30-add-error-recipient-failures-table.js','5.25','5.125'),(237,'2022-12-13-16-15-add-usage-colums-to-tokens.js','5.27','5.125'),(238,'2023-01-04-04-12-drop-suppressions-table.js','5.27','5.125'),(239,'2023-01-04-04-13-add-suppressions-table.js','5.27','5.125'),(240,'2023-01-05-15-13-add-active-theme-permissions.js','5.28','5.125'),(241,'2023-01-11-02-45-truncate-suppressions.js','5.29','5.125'),(242,'2023-01-13-04-25-unsubscribe-suppressed-emails.js','5.30','5.125'),(243,'2022-12-05-09-56-update-newsletter-subscriptions.js','5.31','5.125'),(244,'2023-01-17-14-59-add-outbound-link-tagging-setting.js','5.31','5.125'),(245,'2023-01-19-07-46-add-mentions-table.js','5.31','5.125'),(246,'2023-01-24-08-00-fix-invalid-tier-expiry-for-paid-members.js','5.32','5.125'),(247,'2023-01-24-08-09-restore-incorrect-expired-tiers-for-members.js','5.32','5.125'),(248,'2023-01-30-07-27-add-mentions-permission.js','5.34','5.125'),(249,'2023-02-08-03-08-add-mentions-notifications-column.js','5.34','5.125'),(250,'2023-02-08-22-32-add-mentions-delete-column.js','5.34','5.125'),(251,'2023-02-13-06-24-add-mentions-verified-column.js','5.35','5.125'),(252,'2023-02-20-12-22-add-milestones-table.js','5.36','5.125'),(253,'2023-02-21-12-29-add-milestone-notifications-column.js','5.36','5.125'),(254,'2023-02-23-10-40-set-outbound-link-tagging-based-on-source-tracking.js','5.36','5.125'),(255,'2023-03-13-09-29-add-newsletter-show-post-title-section.js','5.39','5.125'),(256,'2023-03-13-13-11-add-newsletter-show-comment-cta.js','5.39','5.125'),(257,'2023-03-13-14-30-add-newsletter-show-subscription-details.js','5.39','5.125'),(258,'2023-03-14-12-26-add-last-mentions-email-report-timestamp-setting.js','5.39','5.125'),(259,'2023-03-13-14-05-add-newsletter-show-latest-posts.js','5.40','5.125'),(260,'2023-03-21-18-42-add-self-serve-integration-role.js','5.40','5.125'),(261,'2023-03-21-18-43-add-self-serve-migration-and-permissions.js','5.40','5.125'),(262,'2023-03-21-18-52-add-self-serve-integration.js','5.40','5.125'),(263,'2023-03-21-19-02-add-self-serve-integration-api-key.js','5.40','5.125'),(264,'2023-03-27-15-00-add-newsletter-colors.js','5.41','5.125'),(265,'2023-03-27-17-51-fix-self-serve-integration-api-key-type.js','5.41','5.125'),(266,'2023-04-04-07-03-add-portal-terms-settings.js','5.42','5.125'),(267,'2023-04-14-04-17-add-snippets-lexical-column.js','5.44','5.125'),(268,'2023-04-17-11-05-add-post-revision-author.js','5.45','5.125'),(269,'2023-04-18-12-56-add-announcement-settings.js','5.45','5.125'),(270,'2023-04-19-13-45-add-pintura-settings.js','5.45','5.125'),(271,'2023-04-20-14-19-add-announcement-visibility-setting.js','5.45','5.125'),(272,'2023-04-21-08-54-add-post-revision-status.js','5.45','5.125'),(273,'2023-04-21-10-30-add-feature-image-to-revisions.js','5.45','5.125'),(274,'2023-04-21-13-01-add-feature-image-meta-to-post-revisions.js','5.45','5.125'),(275,'2023-05-30-19-03-update-pintura-setting.js','5.51','5.125'),(276,'2023-06-07-10-17-add-collections-crud-persmissions.js','5.51','5.125'),(277,'2023-06-13-12-24-add-temp-mail-events-table.js','5.53','5.125'),(278,'2023-06-20-10-18-add-collections-table.js','5.53','5.125'),(279,'2023-06-20-10-19-add-collections-posts-table.js','5.53','5.125'),(280,'2023-07-07-11-57-add-show-title-and-feature-image-column-to-posts.js','5.54','5.125'),(281,'2023-07-10-05-15-55-add-built-in-collections.js','5.55','5.125'),(282,'2023-07-10-05-16-55-add-built-in-collection-posts.js','5.55','5.125'),(283,'2023-07-14-10-11-12-add-email-disabled-field-to-members.js','5.56','5.125'),(284,'2023-07-15-10-11-12-update-members-email-disabled-field.js','5.56','5.125'),(285,'2023-07-26-12-44-stripe-products-nullable-product.js','5.57','5.125'),(286,'2023-07-27-11-47-49-create-donation-events.js','5.57','5.125'),(287,'2023-08-02-09-42-add-donation-settings.js','5.58','5.125'),(288,'2023-08-07-10-42-add-donation-notifications-column.js','5.59','5.125'),(289,'2023-08-07-11-17-05-add-posts-published-at-index.js','5.59','5.125'),(290,'2023-08-29-10-17-add-recommendations-crud-permissions.js','5.61','5.125'),(291,'2023-08-29-11-39-10-add-recommendations-table.js','5.61','5.125'),(292,'2023-08-30-07-37-04-add-recommendations-enabled-settings.js','5.61','5.125'),(293,'2023-09-12-11-22-10-add-recommendation-click-events-table.js','5.63','5.125'),(294,'2023-09-12-11-22-11-add-recommendation-subscribe-events-table.js','5.63','5.125'),(295,'2023-09-13-13-03-10-add-ghost-core-content-integration.js','5.63','5.125'),(296,'2023-09-13-13-34-11-add-ghost-core-content-integration-key.js','5.63','5.125'),(297,'2023-09-19-04-25-40-truncate-stale-built-in-collections-posts.js','5.64','5.125'),(298,'2023-09-19-04-34-10-repopulate-built-in-collection-posts.js','5.64','5.125'),(299,'2023-09-22-06-42-15-truncate-stale-built-in-collections-posts.js','5.65','5.125'),(300,'2023-09-22-06-42-55-repopulate-built-in-featured-collection-posts.js','5.65','5.125'),(301,'2023-09-22-14-15-add-recommendation-notifications-column.js','5.66','5.125'),(302,'2023-10-03-00-32-32-rollback-source-theme.js','5.67','5.125'),(303,'2023-10-06-15-06-00-rename-recommendations-reason-to-description.js','5.69','5.125'),(304,'2023-10-31-11-06-00-members-created-attribution-id-index.js','5.72','5.125'),(305,'2023-10-31-11-06-01-members-subscription-created-attribution-id-index.js','5.72','5.125'),(306,'2023-11-14-11-15-00-add-transient-id-column-nullable.js','5.74','5.125'),(307,'2023-11-14-11-16-00-fill-transient-id-column.js','5.74','5.125'),(308,'2023-11-14-11-17-00-drop-nullable-transient-id-column.js','5.74','5.125'),(309,'2023-11-27-15-55-add-members-newsletters-index.js','5.75','5.125'),(310,'2023-12-05-11-00-add-portal-default-plan-setting.js','5.76','5.125'),(311,'2024-01-30-19-36-44-fix-discrepancy-in-free-tier-visibility.js','5.79','5.125'),(312,'2024-03-18-16-20-add-missing-post-permissions.js','5.81','5.125'),(313,'2024-03-25-16-46-10-add-email-recipients-email-id-indexes.js','5.82','5.125'),(314,'2024-03-25-16-51-29-drop-email-recipients-non-email-id-indexes.js','5.82','5.125'),(315,'2024-05-28-02-20-55-add-show-subhead-column-newsletters.js','5.83','5.125'),(316,'2024-06-04-09-13-33-rename-newsletters-show-subhead.js','5.84','5.125'),(317,'2024-06-04-11-10-37-add-custom-excerpt-to-post-revisions.js','5.84','5.125'),(318,'2024-06-05-08-42-34-populate-post-revisions-custom-excerpt.js','5.84','5.125'),(319,'2024-06-05-13-48-35-rename-newsletters-show-subtitle.js','5.84','5.125'),(320,'2024-06-10-14-53-31-add-posts-updated-at-index.js','5.85','5.125'),(321,'2024-06-25-12-08-20-add-posts-tags-post-tag-index.js','5.87','5.125'),(322,'2024-06-25-12-08-45-add-posts-type-status-updated-at-index.js','5.87','5.125'),(323,'2024-07-30-19-51-06-backfill-offer-redemptions.js','5.89','5.125'),(324,'2024-08-20-09-40-24-update-default-donations-suggested-amount.js','5.90','5.125'),(325,'2024-08-28-05-28-22-add-donation-message-column-to-donation-payment-events.js','5.91','5.125'),(326,'2024-09-03-18-51-01-update-stripe-prices-nickname-length.js','5.93','5.125'),(327,'2024-09-03-20-09-40-null-analytics-jobs-timings.js','5.94','5.125'),(328,'2024-10-08-14-25-27-added-body-font-settings.js','5.97','5.125'),(329,'2024-10-08-14-36-58-added-heading-font-setting.js','5.97','5.125'),(330,'2024-10-09-14-04-10-add-session-verification-field.js','5.97','5.125'),(331,'2024-10-10-01-02-03-add-signin-urls-permissions.js','5.97','5.125'),(332,'2024-10-31-15-27-42-add-jobs-queue-columns.js','5.100','5.125'),(333,'2024-11-05-14-48-08-add-comments-in-reply-to-id.js','5.100','5.125'),(334,'2024-11-06-04-45-15-add-activitypub-integration.js','5.100','5.125'),(335,'2024-12-02-17-32-40-alter-length-redirects-from.js','5.102','5.125'),(336,'2024-12-02-17-48-40-add-index-redirects-from.js','5.102','5.125'),(337,'2025-01-23-02-51-10-add-blocked-email-domains-setting.js','5.108','5.125'),(338,'2025-03-05-16-36-39-add-captcha-setting.js','5.111','5.125'),(339,'2025-03-10-10-01-01-add-require-mfa-setting.js','5.112','5.125'),(340,'2025-03-07-12-24-00-add-super-editor.js','5.113','5.125'),(341,'2025-03-07-12-25-00-add-member-perms-to-super-editor.js','5.113','5.125'),(342,'2025-03-19-03-13-04-add-index-to-posts-uuid.js','5.114','5.125'),(343,'2025-03-24-07-19-27-add-identity-read-permission-to-administrators.js','5.115','5.125'),(344,'2025-04-14-02-36-30-add-additional-social-accounts-columns-to-user-table.js','5.117','5.125'),(345,'2025-04-30-13-01-28-remove-captcha-setting.js','5.119','5.125'),(346,'2025-05-07-14-57-38-add-newsletters-button-corners-column.js','5.120','5.125'),(347,'2025-05-13-17-36-56-add-newsletters-button-style-column.js','5.120','5.125'),(348,'2025-05-14-20-00-15-add-newsletters-setting-columns.js','5.120','5.125'),(349,'2025-05-26-08-59-26-drop-newsletters-border-color-column.js','5.121','5.125'),(350,'2025-05-26-09-10-30-rename-newsletters-title-color-column.js','5.121','5.125'),(351,'2025-05-26-12-03-24-add-newsletters-color-columns.js','5.121','5.125'),(352,'2025-05-29-08-41-04-add-member-export-permissions-to-backup-integration.js','5.121','5.125'),(353,'2025-06-03-19-32-57-change-default-for-newsletters-button-color.js','5.122','5.125'),(354,'2025-06-06-23-12-11-create-site-uuid-setting.js','5.124','5.125');
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations_lock`
--

DROP TABLE IF EXISTS `migrations_lock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations_lock` (
  `lock_key` varchar(191) NOT NULL,
  `locked` tinyint(1) DEFAULT '0',
  `acquired_at` datetime DEFAULT NULL,
  `released_at` datetime DEFAULT NULL,
  PRIMARY KEY (`lock_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations_lock`
--

LOCK TABLES `migrations_lock` WRITE;
/*!40000 ALTER TABLE `migrations_lock` DISABLE KEYS */;
INSERT INTO `migrations_lock` VALUES ('km01',0,'2025-06-17 02:00:42','2025-06-17 02:00:58');
/*!40000 ALTER TABLE `migrations_lock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `milestones`
--

DROP TABLE IF EXISTS `milestones`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `milestones` (
  `id` varchar(24) NOT NULL,
  `type` varchar(24) NOT NULL,
  `value` int NOT NULL,
  `currency` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `email_sent_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `milestones`
--

LOCK TABLES `milestones` WRITE;
/*!40000 ALTER TABLE `milestones` DISABLE KEYS */;
INSERT INTO `milestones` VALUES ('6850cc659cac51000106fbe3','members',0,NULL,'2025-06-17 02:01:09',NULL);
/*!40000 ALTER TABLE `milestones` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mobiledoc_revisions`
--

DROP TABLE IF EXISTS `mobiledoc_revisions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mobiledoc_revisions` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `mobiledoc` longtext,
  `created_at_ts` bigint NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `mobiledoc_revisions_post_id_index` (`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mobiledoc_revisions`
--

LOCK TABLES `mobiledoc_revisions` WRITE;
/*!40000 ALTER TABLE `mobiledoc_revisions` DISABLE KEYS */;
INSERT INTO `mobiledoc_revisions` VALUES ('6850d035db983000014c1c6e','6850cc589cac51000106f9b6','{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[[\"hr\",{}]],\"markups\":[[\"a\",[\"href\",\"https://ghost.org\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"This is an independent publication. If you subscribe today, you\'ll get full access to the website as well as email newsletters about new content when it\'s available. Your subscription makes this site possible. Thank you!\"]]],[1,\"h3\",[[0,[],0,\"Access all areas\"]]],[1,\"p\",[[0,[],0,\"By signing up, you\'ll get access to the full archive of everything that\'s been published before and everything that\'s still to come. Your very own private library.\"]]],[1,\"h3\",[[0,[],0,\"Fresh content, delivered\"]]],[1,\"p\",[[0,[],0,\"Stay up to date with new content sent straight to your inbox! No more worrying about whether you missed something because of a pesky algorithm or news feed.\"]]],[1,\"h3\",[[0,[],0,\"Meet people like you\"]]],[1,\"p\",[[0,[],0,\"Join a community of other subscribers who share the same interests.\"]]],[10,0],[1,\"h3\",[[0,[],0,\"Start your own thing\"]]],[1,\"p\",[[0,[],0,\"Enjoying the experience? Get started for free and set up your very own subscription business using \"],[0,[0],1,\"Ghost\"],[0,[],0,\", the same platform that powers this website.\"]]]],\"ghostVersion\":\"4.0\"}',1750126645334,'2025-06-17 02:17:25'),('6850d035db983000014c1c6f','6850cc589cac51000106f9b6','{\"version\":\"0.3.1\",\"atoms\":[],\"cards\":[[\"hr\",{}]],\"markups\":[[\"a\",[\"href\",\"https://ghost.org\"]]],\"sections\":[[1,\"p\",[[0,[],0,\"Open Portal Expo Media is an independent publication launched in June 2025 by Open Portal Expo. If you subscribe today, you\'ll get full access to the website as well as email newsletters about new content when it\'s available. Your subscription makes this site possible, and allows Open Portal Expo Media to continue to exist. Thank you!\"]]],[1,\"h3\",[[0,[],0,\"Access all areas\"]]],[1,\"p\",[[0,[],0,\"By signing up, you\'ll get access to the full archive of everything that\'s been published before and everything that\'s still to come. Your very own private library.\"]]],[1,\"h3\",[[0,[],0,\"Fresh content, delivered\"]]],[1,\"p\",[[0,[],0,\"Stay up to date with new content sent straight to your inbox! No more worrying about whether you missed something because of a pesky algorithm or news feed.\"]]],[1,\"h3\",[[0,[],0,\"Meet people like you\"]]],[1,\"p\",[[0,[],0,\"Join a community of other subscribers who share the same interests.\"]]],[10,0],[1,\"h3\",[[0,[],0,\"Start your own thing\"]]],[1,\"p\",[[0,[],0,\"Enjoying the experience? Get started for free and set up your very own subscription business using \"],[0,[0],1,\"Ghost\"],[0,[],0,\", the same platform that powers this website.\"]]]],\"ghostVersion\":\"4.0\"}',1750126645335,'2025-06-17 02:17:25');
/*!40000 ALTER TABLE `mobiledoc_revisions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `newsletters`
--

DROP TABLE IF EXISTS `newsletters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `newsletters` (
  `id` varchar(24) NOT NULL,
  `uuid` varchar(36) NOT NULL,
  `name` varchar(191) NOT NULL,
  `description` varchar(2000) DEFAULT NULL,
  `feedback_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `slug` varchar(191) NOT NULL,
  `sender_name` varchar(191) DEFAULT NULL,
  `sender_email` varchar(191) DEFAULT NULL,
  `sender_reply_to` varchar(191) NOT NULL DEFAULT 'newsletter',
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `visibility` varchar(50) NOT NULL DEFAULT 'members',
  `subscribe_on_signup` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  `header_image` varchar(2000) DEFAULT NULL,
  `show_header_icon` tinyint(1) NOT NULL DEFAULT '1',
  `show_header_title` tinyint(1) NOT NULL DEFAULT '1',
  `show_excerpt` tinyint(1) NOT NULL DEFAULT '0',
  `title_font_category` varchar(191) NOT NULL DEFAULT 'sans_serif',
  `title_alignment` varchar(191) NOT NULL DEFAULT 'center',
  `show_feature_image` tinyint(1) NOT NULL DEFAULT '1',
  `body_font_category` varchar(191) NOT NULL DEFAULT 'sans_serif',
  `footer_content` text,
  `show_badge` tinyint(1) NOT NULL DEFAULT '1',
  `show_header_name` tinyint(1) NOT NULL DEFAULT '1',
  `show_post_title_section` tinyint(1) NOT NULL DEFAULT '1',
  `show_comment_cta` tinyint(1) NOT NULL DEFAULT '1',
  `show_subscription_details` tinyint(1) NOT NULL DEFAULT '0',
  `show_latest_posts` tinyint(1) NOT NULL DEFAULT '0',
  `background_color` varchar(50) NOT NULL DEFAULT 'light',
  `post_title_color` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `button_corners` varchar(50) NOT NULL DEFAULT 'rounded',
  `button_style` varchar(50) NOT NULL DEFAULT 'fill',
  `title_font_weight` varchar(50) NOT NULL DEFAULT 'bold',
  `link_style` varchar(50) NOT NULL DEFAULT 'underline',
  `image_corners` varchar(50) NOT NULL DEFAULT 'square',
  `header_background_color` varchar(50) NOT NULL DEFAULT 'transparent',
  `section_title_color` varchar(50) DEFAULT NULL,
  `divider_color` varchar(50) DEFAULT NULL,
  `button_color` varchar(50) DEFAULT 'accent',
  `link_color` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `newsletters_uuid_unique` (`uuid`),
  UNIQUE KEY `newsletters_name_unique` (`name`),
  UNIQUE KEY `newsletters_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `newsletters`
--

LOCK TABLES `newsletters` WRITE;
/*!40000 ALTER TABLE `newsletters` DISABLE KEYS */;
INSERT INTO `newsletters` VALUES ('6850cc579cac51000106f93a','05077a64-9725-4a2e-b60b-ef305d923589','Open Portal Expo Media',NULL,0,'default-newsletter',NULL,NULL,'newsletter','active','members',1,0,NULL,1,1,0,'sans_serif','center',1,'sans_serif',NULL,1,0,1,1,0,0,'light',NULL,'2025-06-17 02:00:55','2025-06-17 02:17:25','rounded','fill','bold','underline','square','transparent',NULL,NULL,'accent',NULL);
/*!40000 ALTER TABLE `newsletters` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `offer_redemptions`
--

DROP TABLE IF EXISTS `offer_redemptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `offer_redemptions` (
  `id` varchar(24) NOT NULL,
  `offer_id` varchar(24) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `subscription_id` varchar(24) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `offer_redemptions_offer_id_foreign` (`offer_id`),
  KEY `offer_redemptions_member_id_foreign` (`member_id`),
  KEY `offer_redemptions_subscription_id_foreign` (`subscription_id`),
  CONSTRAINT `offer_redemptions_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `offer_redemptions_offer_id_foreign` FOREIGN KEY (`offer_id`) REFERENCES `offers` (`id`) ON DELETE CASCADE,
  CONSTRAINT `offer_redemptions_subscription_id_foreign` FOREIGN KEY (`subscription_id`) REFERENCES `members_stripe_customers_subscriptions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `offer_redemptions`
--

LOCK TABLES `offer_redemptions` WRITE;
/*!40000 ALTER TABLE `offer_redemptions` DISABLE KEYS */;
/*!40000 ALTER TABLE `offer_redemptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `offers`
--

DROP TABLE IF EXISTS `offers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `offers` (
  `id` varchar(24) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `name` varchar(191) NOT NULL,
  `code` varchar(191) NOT NULL,
  `product_id` varchar(24) NOT NULL,
  `stripe_coupon_id` varchar(255) DEFAULT NULL,
  `interval` varchar(50) NOT NULL,
  `currency` varchar(50) DEFAULT NULL,
  `discount_type` varchar(50) NOT NULL,
  `discount_amount` int NOT NULL,
  `duration` varchar(50) NOT NULL,
  `duration_in_months` int DEFAULT NULL,
  `portal_title` varchar(191) DEFAULT NULL,
  `portal_description` varchar(2000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `offers_name_unique` (`name`),
  UNIQUE KEY `offers_code_unique` (`code`),
  UNIQUE KEY `offers_stripe_coupon_id_unique` (`stripe_coupon_id`),
  KEY `offers_product_id_foreign` (`product_id`),
  CONSTRAINT `offers_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `offers`
--

LOCK TABLES `offers` WRITE;
/*!40000 ALTER TABLE `offers` DISABLE KEYS */;
/*!40000 ALTER TABLE `offers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` varchar(24) NOT NULL,
  `name` varchar(50) NOT NULL,
  `object_type` varchar(50) NOT NULL,
  `action_type` varchar(50) NOT NULL,
  `object_id` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permissions_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES ('6850cc579cac51000106f93c','Read explore data','explore','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f93d','Export database','db','exportContent',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f93e','Import database','db','importContent',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f93f','Delete all content','db','deleteAllContent',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f940','Send mail','mail','send',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f941','Browse notifications','notification','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f942','Add notifications','notification','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f943','Delete notifications','notification','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f944','Browse posts','post','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f945','Read posts','post','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f946','Edit posts','post','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f947','Add posts','post','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f948','Delete posts','post','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f949','Browse settings','setting','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f94a','Read settings','setting','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f94b','Edit settings','setting','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f94c','Generate slugs','slug','generate',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f94d','Browse tags','tag','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f94e','Read tags','tag','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f94f','Edit tags','tag','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f950','Add tags','tag','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f951','Delete tags','tag','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f952','Browse themes','theme','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f953','Edit themes','theme','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f954','Activate themes','theme','activate',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f955','View active theme details','theme','readActive',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f956','Upload themes','theme','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f957','Download themes','theme','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f958','Delete themes','theme','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f959','Browse users','user','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f95a','Read users','user','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f95b','Edit users','user','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f95c','Add users','user','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f95d','Delete users','user','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f95e','Assign a role','role','assign',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f95f','Browse roles','role','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f960','Browse invites','invite','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f961','Read invites','invite','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f962','Edit invites','invite','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f963','Add invites','invite','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f964','Delete invites','invite','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f965','Download redirects','redirect','download',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f966','Upload redirects','redirect','upload',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f967','Add webhooks','webhook','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f968','Edit webhooks','webhook','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f969','Delete webhooks','webhook','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f96a','Browse integrations','integration','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f96b','Read integrations','integration','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f96c','Edit integrations','integration','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f96d','Add integrations','integration','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f96e','Delete integrations','integration','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f96f','Browse API keys','api_key','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f970','Read API keys','api_key','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f971','Edit API keys','api_key','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f972','Add API keys','api_key','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f973','Delete API keys','api_key','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f974','Browse Actions','action','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f975','Browse Members','member','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f976','Read Members','member','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f977','Edit Members','member','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f978','Add Members','member','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f979','Delete Members','member','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f97a','Browse Products','product','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f97b','Read Products','product','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f97c','Edit Products','product','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f97d','Add Products','product','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f97e','Delete Products','product','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f97f','Publish posts','post','publish',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f980','Backup database','db','backupContent',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f981','Email preview','email_preview','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f982','Send test email','email_preview','sendTestEmail',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f983','Browse emails','email','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f984','Read emails','email','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f985','Retry emails','email','retry',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f986','Browse labels','label','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f987','Read labels','label','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f988','Edit labels','label','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f989','Add labels','label','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f98a','Delete labels','label','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f98b','Read member signin urls','member_signin_url','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f98c','Read identities','identity','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f98d','Auth Stripe Connect for Members','members_stripe_connect','auth',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f98e','Browse snippets','snippet','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f98f','Read snippets','snippet','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f990','Edit snippets','snippet','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f991','Add snippets','snippet','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f992','Delete snippets','snippet','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f993','Browse offers','offer','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f994','Read offers','offer','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f995','Edit offers','offer','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f996','Add offers','offer','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f997','Reset all passwords','authentication','resetAllPasswords',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f998','Browse custom theme settings','custom_theme_setting','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f999','Edit custom theme settings','custom_theme_setting','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f99a','Browse newsletters','newsletter','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f99b','Read newsletters','newsletter','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f99c','Add newsletters','newsletter','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f99d','Edit newsletters','newsletter','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f99e','Browse comments','comment','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f99f','Read comments','comment','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a0','Edit comments','comment','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a1','Add comments','comment','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a2','Delete comments','comment','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a3','Moderate comments','comment','moderate',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a4','Like comments','comment','like',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a5','Unlike comments','comment','unlike',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a6','Report comments','comment','report',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a7','Browse links','link','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a8','Edit links','link','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9a9','Browse mentions','mention','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9aa','Browse collections','collection','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9ab','Read collections','collection','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9ac','Edit collections','collection','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9ad','Add collections','collection','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9ae','Delete collections','collection','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9af','Browse recommendations','recommendation','browse',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9b0','Read recommendations','recommendation','read',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9b1','Edit recommendations','recommendation','edit',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9b2','Add recommendations','recommendation','add',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850cc579cac51000106f9b3','Delete recommendations','recommendation','destroy',NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions_roles`
--

DROP TABLE IF EXISTS `permissions_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions_roles` (
  `id` varchar(24) NOT NULL,
  `role_id` varchar(24) NOT NULL,
  `permission_id` varchar(24) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions_roles`
--

LOCK TABLES `permissions_roles` WRITE;
/*!40000 ALTER TABLE `permissions_roles` DISABLE KEYS */;
INSERT INTO `permissions_roles` VALUES ('6850cc599cac51000106f9c7','6850cc569cac51000106f92a','6850cc579cac51000106f93d'),('6850cc599cac51000106f9c8','6850cc569cac51000106f92a','6850cc579cac51000106f93e'),('6850cc599cac51000106f9c9','6850cc569cac51000106f92a','6850cc579cac51000106f93f'),('6850cc599cac51000106f9ca','6850cc569cac51000106f92a','6850cc579cac51000106f980'),('6850cc599cac51000106f9cb','6850cc569cac51000106f92a','6850cc579cac51000106f940'),('6850cc599cac51000106f9cc','6850cc569cac51000106f92a','6850cc579cac51000106f941'),('6850cc599cac51000106f9cd','6850cc569cac51000106f92a','6850cc579cac51000106f942'),('6850cc599cac51000106f9ce','6850cc569cac51000106f92a','6850cc579cac51000106f943'),('6850cc599cac51000106f9cf','6850cc569cac51000106f92a','6850cc579cac51000106f944'),('6850cc599cac51000106f9d0','6850cc569cac51000106f92a','6850cc579cac51000106f945'),('6850cc599cac51000106f9d1','6850cc569cac51000106f92a','6850cc579cac51000106f946'),('6850cc599cac51000106f9d2','6850cc569cac51000106f92a','6850cc579cac51000106f947'),('6850cc599cac51000106f9d3','6850cc569cac51000106f92a','6850cc579cac51000106f948'),('6850cc599cac51000106f9d4','6850cc569cac51000106f92a','6850cc579cac51000106f97f'),('6850cc599cac51000106f9d5','6850cc569cac51000106f92a','6850cc579cac51000106f949'),('6850cc599cac51000106f9d6','6850cc569cac51000106f92a','6850cc579cac51000106f94a'),('6850cc599cac51000106f9d7','6850cc569cac51000106f92a','6850cc579cac51000106f94b'),('6850cc599cac51000106f9d8','6850cc569cac51000106f92a','6850cc579cac51000106f94c'),('6850cc599cac51000106f9d9','6850cc569cac51000106f92a','6850cc579cac51000106f94d'),('6850cc599cac51000106f9da','6850cc569cac51000106f92a','6850cc579cac51000106f94e'),('6850cc599cac51000106f9db','6850cc569cac51000106f92a','6850cc579cac51000106f94f'),('6850cc599cac51000106f9dc','6850cc569cac51000106f92a','6850cc579cac51000106f950'),('6850cc599cac51000106f9dd','6850cc569cac51000106f92a','6850cc579cac51000106f951'),('6850cc599cac51000106f9de','6850cc569cac51000106f92a','6850cc579cac51000106f952'),('6850cc599cac51000106f9df','6850cc569cac51000106f92a','6850cc579cac51000106f953'),('6850cc599cac51000106f9e0','6850cc569cac51000106f92a','6850cc579cac51000106f954'),('6850cc599cac51000106f9e1','6850cc569cac51000106f92a','6850cc579cac51000106f955'),('6850cc599cac51000106f9e2','6850cc569cac51000106f92a','6850cc579cac51000106f956'),('6850cc599cac51000106f9e3','6850cc569cac51000106f92a','6850cc579cac51000106f957'),('6850cc599cac51000106f9e4','6850cc569cac51000106f92a','6850cc579cac51000106f958'),('6850cc599cac51000106f9e5','6850cc569cac51000106f92a','6850cc579cac51000106f959'),('6850cc599cac51000106f9e6','6850cc569cac51000106f92a','6850cc579cac51000106f95a'),('6850cc599cac51000106f9e7','6850cc569cac51000106f92a','6850cc579cac51000106f95b'),('6850cc599cac51000106f9e8','6850cc569cac51000106f92a','6850cc579cac51000106f95c'),('6850cc599cac51000106f9e9','6850cc569cac51000106f92a','6850cc579cac51000106f95d'),('6850cc599cac51000106f9ea','6850cc569cac51000106f92a','6850cc579cac51000106f95e'),('6850cc599cac51000106f9eb','6850cc569cac51000106f92a','6850cc579cac51000106f95f'),('6850cc599cac51000106f9ec','6850cc569cac51000106f92a','6850cc579cac51000106f960'),('6850cc599cac51000106f9ed','6850cc569cac51000106f92a','6850cc579cac51000106f961'),('6850cc599cac51000106f9ee','6850cc569cac51000106f92a','6850cc579cac51000106f962'),('6850cc599cac51000106f9ef','6850cc569cac51000106f92a','6850cc579cac51000106f963'),('6850cc599cac51000106f9f0','6850cc569cac51000106f92a','6850cc579cac51000106f964'),('6850cc599cac51000106f9f1','6850cc569cac51000106f92a','6850cc579cac51000106f965'),('6850cc599cac51000106f9f2','6850cc569cac51000106f92a','6850cc579cac51000106f966'),('6850cc599cac51000106f9f3','6850cc569cac51000106f92a','6850cc579cac51000106f967'),('6850cc599cac51000106f9f4','6850cc569cac51000106f92a','6850cc579cac51000106f968'),('6850cc599cac51000106f9f5','6850cc569cac51000106f92a','6850cc579cac51000106f969'),('6850cc599cac51000106f9f6','6850cc569cac51000106f92a','6850cc579cac51000106f96a'),('6850cc599cac51000106f9f7','6850cc569cac51000106f92a','6850cc579cac51000106f96b'),('6850cc599cac51000106f9f8','6850cc569cac51000106f92a','6850cc579cac51000106f96c'),('6850cc599cac51000106f9f9','6850cc569cac51000106f92a','6850cc579cac51000106f96d'),('6850cc599cac51000106f9fa','6850cc569cac51000106f92a','6850cc579cac51000106f96e'),('6850cc599cac51000106f9fb','6850cc569cac51000106f92a','6850cc579cac51000106f96f'),('6850cc599cac51000106f9fc','6850cc569cac51000106f92a','6850cc579cac51000106f970'),('6850cc599cac51000106f9fd','6850cc569cac51000106f92a','6850cc579cac51000106f971'),('6850cc599cac51000106f9fe','6850cc569cac51000106f92a','6850cc579cac51000106f972'),('6850cc599cac51000106f9ff','6850cc569cac51000106f92a','6850cc579cac51000106f973'),('6850cc599cac51000106fa00','6850cc569cac51000106f92a','6850cc579cac51000106f974'),('6850cc599cac51000106fa01','6850cc569cac51000106f92a','6850cc579cac51000106f975'),('6850cc599cac51000106fa02','6850cc569cac51000106f92a','6850cc579cac51000106f976'),('6850cc599cac51000106fa03','6850cc569cac51000106f92a','6850cc579cac51000106f977'),('6850cc599cac51000106fa04','6850cc569cac51000106f92a','6850cc579cac51000106f978'),('6850cc599cac51000106fa05','6850cc569cac51000106f92a','6850cc579cac51000106f979'),('6850cc599cac51000106fa06','6850cc569cac51000106f92a','6850cc579cac51000106f97a'),('6850cc599cac51000106fa07','6850cc569cac51000106f92a','6850cc579cac51000106f97b'),('6850cc599cac51000106fa08','6850cc569cac51000106f92a','6850cc579cac51000106f97c'),('6850cc599cac51000106fa09','6850cc569cac51000106f92a','6850cc579cac51000106f97d'),('6850cc599cac51000106fa0a','6850cc569cac51000106f92a','6850cc579cac51000106f97e'),('6850cc599cac51000106fa0b','6850cc569cac51000106f92a','6850cc579cac51000106f986'),('6850cc599cac51000106fa0c','6850cc569cac51000106f92a','6850cc579cac51000106f987'),('6850cc599cac51000106fa0d','6850cc569cac51000106f92a','6850cc579cac51000106f988'),('6850cc599cac51000106fa0e','6850cc569cac51000106f92a','6850cc579cac51000106f989'),('6850cc599cac51000106fa0f','6850cc569cac51000106f92a','6850cc579cac51000106f98a'),('6850cc599cac51000106fa10','6850cc569cac51000106f92a','6850cc579cac51000106f981'),('6850cc599cac51000106fa11','6850cc569cac51000106f92a','6850cc579cac51000106f982'),('6850cc599cac51000106fa12','6850cc569cac51000106f92a','6850cc579cac51000106f983'),('6850cc599cac51000106fa13','6850cc569cac51000106f92a','6850cc579cac51000106f984'),('6850cc599cac51000106fa14','6850cc569cac51000106f92a','6850cc579cac51000106f985'),('6850cc599cac51000106fa15','6850cc569cac51000106f92a','6850cc579cac51000106f98b'),('6850cc599cac51000106fa16','6850cc569cac51000106f92a','6850cc579cac51000106f98e'),('6850cc599cac51000106fa17','6850cc569cac51000106f92a','6850cc579cac51000106f98f'),('6850cc599cac51000106fa18','6850cc569cac51000106f92a','6850cc579cac51000106f990'),('6850cc599cac51000106fa19','6850cc569cac51000106f92a','6850cc579cac51000106f991'),('6850cc599cac51000106fa1a','6850cc569cac51000106f92a','6850cc579cac51000106f992'),('6850cc599cac51000106fa1b','6850cc569cac51000106f92a','6850cc579cac51000106f998'),('6850cc599cac51000106fa1c','6850cc569cac51000106f92a','6850cc579cac51000106f999'),('6850cc599cac51000106fa1d','6850cc569cac51000106f92a','6850cc579cac51000106f993'),('6850cc599cac51000106fa1e','6850cc569cac51000106f92a','6850cc579cac51000106f994'),('6850cc599cac51000106fa1f','6850cc569cac51000106f92a','6850cc579cac51000106f995'),('6850cc599cac51000106fa20','6850cc569cac51000106f92a','6850cc579cac51000106f996'),('6850cc599cac51000106fa21','6850cc569cac51000106f92a','6850cc579cac51000106f997'),('6850cc599cac51000106fa22','6850cc569cac51000106f92a','6850cc579cac51000106f98d'),('6850cc599cac51000106fa23','6850cc569cac51000106f92a','6850cc579cac51000106f99a'),('6850cc599cac51000106fa24','6850cc569cac51000106f92a','6850cc579cac51000106f99b'),('6850cc599cac51000106fa25','6850cc569cac51000106f92a','6850cc579cac51000106f99c'),('6850cc599cac51000106fa26','6850cc569cac51000106f92a','6850cc579cac51000106f99d'),('6850cc599cac51000106fa27','6850cc569cac51000106f92a','6850cc579cac51000106f93c'),('6850cc599cac51000106fa28','6850cc569cac51000106f92a','6850cc579cac51000106f99e'),('6850cc599cac51000106fa29','6850cc569cac51000106f92a','6850cc579cac51000106f99f'),('6850cc599cac51000106fa2a','6850cc569cac51000106f92a','6850cc579cac51000106f9a0'),('6850cc599cac51000106fa2b','6850cc569cac51000106f92a','6850cc579cac51000106f9a1'),('6850cc599cac51000106fa2c','6850cc569cac51000106f92a','6850cc579cac51000106f9a2'),('6850cc599cac51000106fa2d','6850cc569cac51000106f92a','6850cc579cac51000106f9a3'),('6850cc599cac51000106fa2e','6850cc569cac51000106f92a','6850cc579cac51000106f9a4'),('6850cc599cac51000106fa2f','6850cc569cac51000106f92a','6850cc579cac51000106f9a5'),('6850cc599cac51000106fa30','6850cc569cac51000106f92a','6850cc579cac51000106f9a6'),('6850cc599cac51000106fa31','6850cc569cac51000106f92a','6850cc579cac51000106f9a7'),('6850cc599cac51000106fa32','6850cc569cac51000106f92a','6850cc579cac51000106f9a8'),('6850cc599cac51000106fa33','6850cc569cac51000106f92a','6850cc579cac51000106f9a9'),('6850cc599cac51000106fa34','6850cc569cac51000106f92a','6850cc579cac51000106f9aa'),('6850cc599cac51000106fa35','6850cc569cac51000106f92a','6850cc579cac51000106f9ab'),('6850cc599cac51000106fa36','6850cc569cac51000106f92a','6850cc579cac51000106f9ac'),('6850cc599cac51000106fa37','6850cc569cac51000106f92a','6850cc579cac51000106f9ad'),('6850cc599cac51000106fa38','6850cc569cac51000106f92a','6850cc579cac51000106f9ae'),('6850cc599cac51000106fa39','6850cc569cac51000106f92a','6850cc579cac51000106f9af'),('6850cc599cac51000106fa3a','6850cc569cac51000106f92a','6850cc579cac51000106f9b0'),('6850cc599cac51000106fa3b','6850cc569cac51000106f92a','6850cc579cac51000106f9b1'),('6850cc599cac51000106fa3c','6850cc569cac51000106f92a','6850cc579cac51000106f9b2'),('6850cc599cac51000106fa3d','6850cc569cac51000106f92a','6850cc579cac51000106f9b3'),('6850cc599cac51000106fa3e','6850cc569cac51000106f92a','6850cc579cac51000106f98c'),('6850cc599cac51000106fa3f','6850cc569cac51000106f932','6850cc579cac51000106f93d'),('6850cc599cac51000106fa40','6850cc569cac51000106f932','6850cc579cac51000106f93e'),('6850cc599cac51000106fa41','6850cc569cac51000106f932','6850cc579cac51000106f93f'),('6850cc599cac51000106fa42','6850cc569cac51000106f932','6850cc579cac51000106f980'),('6850cc599cac51000106fa43','6850cc569cac51000106f932','6850cc579cac51000106f975'),('6850cc599cac51000106fa44','6850cc569cac51000106f933','6850cc579cac51000106f97f'),('6850cc599cac51000106fa45','6850cc569cac51000106f930','6850cc579cac51000106f93c'),('6850cc599cac51000106fa46','6850cc569cac51000106f931','6850cc579cac51000106f93e'),('6850cc599cac51000106fa47','6850cc569cac51000106f931','6850cc579cac51000106f978'),('6850cc599cac51000106fa48','6850cc569cac51000106f931','6850cc579cac51000106f94e'),('6850cc599cac51000106fa49','6850cc569cac51000106f92f','6850cc579cac51000106f940'),('6850cc599cac51000106fa4a','6850cc569cac51000106f92f','6850cc579cac51000106f941'),('6850cc599cac51000106fa4b','6850cc569cac51000106f92f','6850cc579cac51000106f942'),('6850cc599cac51000106fa4c','6850cc569cac51000106f92f','6850cc579cac51000106f943'),('6850cc599cac51000106fa4d','6850cc569cac51000106f92f','6850cc579cac51000106f944'),('6850cc599cac51000106fa4e','6850cc569cac51000106f92f','6850cc579cac51000106f945'),('6850cc599cac51000106fa4f','6850cc569cac51000106f92f','6850cc579cac51000106f946'),('6850cc599cac51000106fa50','6850cc569cac51000106f92f','6850cc579cac51000106f947'),('6850cc599cac51000106fa51','6850cc569cac51000106f92f','6850cc579cac51000106f948'),('6850cc599cac51000106fa52','6850cc569cac51000106f92f','6850cc579cac51000106f97f'),('6850cc599cac51000106fa53','6850cc569cac51000106f92f','6850cc579cac51000106f949'),('6850cc599cac51000106fa54','6850cc569cac51000106f92f','6850cc579cac51000106f94a'),('6850cc599cac51000106fa55','6850cc569cac51000106f92f','6850cc579cac51000106f94b'),('6850cc599cac51000106fa56','6850cc569cac51000106f92f','6850cc579cac51000106f94c'),('6850cc599cac51000106fa57','6850cc569cac51000106f92f','6850cc579cac51000106f94d'),('6850cc599cac51000106fa58','6850cc569cac51000106f92f','6850cc579cac51000106f94e'),('6850cc599cac51000106fa59','6850cc569cac51000106f92f','6850cc579cac51000106f94f'),('6850cc599cac51000106fa5a','6850cc569cac51000106f92f','6850cc579cac51000106f950'),('6850cc599cac51000106fa5b','6850cc569cac51000106f92f','6850cc579cac51000106f951'),('6850cc599cac51000106fa5c','6850cc569cac51000106f92f','6850cc579cac51000106f952'),('6850cc599cac51000106fa5d','6850cc569cac51000106f92f','6850cc579cac51000106f953'),('6850cc599cac51000106fa5e','6850cc569cac51000106f92f','6850cc579cac51000106f954'),('6850cc599cac51000106fa5f','6850cc569cac51000106f92f','6850cc579cac51000106f955'),('6850cc599cac51000106fa60','6850cc569cac51000106f92f','6850cc579cac51000106f956'),('6850cc599cac51000106fa61','6850cc569cac51000106f92f','6850cc579cac51000106f957'),('6850cc599cac51000106fa62','6850cc569cac51000106f92f','6850cc579cac51000106f958'),('6850cc599cac51000106fa63','6850cc569cac51000106f92f','6850cc579cac51000106f959'),('6850cc599cac51000106fa64','6850cc569cac51000106f92f','6850cc579cac51000106f95a'),('6850cc599cac51000106fa65','6850cc569cac51000106f92f','6850cc579cac51000106f95b'),('6850cc599cac51000106fa66','6850cc569cac51000106f92f','6850cc579cac51000106f95c'),('6850cc599cac51000106fa67','6850cc569cac51000106f92f','6850cc579cac51000106f95d'),('6850cc599cac51000106fa68','6850cc569cac51000106f92f','6850cc579cac51000106f95e'),('6850cc599cac51000106fa69','6850cc569cac51000106f92f','6850cc579cac51000106f95f'),('6850cc599cac51000106fa6a','6850cc569cac51000106f92f','6850cc579cac51000106f960'),('6850cc599cac51000106fa6b','6850cc569cac51000106f92f','6850cc579cac51000106f961'),('6850cc599cac51000106fa6c','6850cc569cac51000106f92f','6850cc579cac51000106f962'),('6850cc599cac51000106fa6d','6850cc569cac51000106f92f','6850cc579cac51000106f963'),('6850cc599cac51000106fa6e','6850cc569cac51000106f92f','6850cc579cac51000106f964'),('6850cc599cac51000106fa6f','6850cc569cac51000106f92f','6850cc579cac51000106f965'),('6850cc599cac51000106fa70','6850cc569cac51000106f92f','6850cc579cac51000106f966'),('6850cc599cac51000106fa71','6850cc569cac51000106f92f','6850cc579cac51000106f967'),('6850cc599cac51000106fa72','6850cc569cac51000106f92f','6850cc579cac51000106f968'),('6850cc599cac51000106fa73','6850cc569cac51000106f92f','6850cc579cac51000106f969'),('6850cc599cac51000106fa74','6850cc569cac51000106f92f','6850cc579cac51000106f974'),('6850cc599cac51000106fa75','6850cc569cac51000106f92f','6850cc579cac51000106f975'),('6850cc599cac51000106fa76','6850cc569cac51000106f92f','6850cc579cac51000106f976'),('6850cc599cac51000106fa77','6850cc569cac51000106f92f','6850cc579cac51000106f977'),('6850cc599cac51000106fa78','6850cc569cac51000106f92f','6850cc579cac51000106f978'),('6850cc599cac51000106fa79','6850cc569cac51000106f92f','6850cc579cac51000106f979'),('6850cc599cac51000106fa7a','6850cc569cac51000106f92f','6850cc579cac51000106f986'),('6850cc599cac51000106fa7b','6850cc569cac51000106f92f','6850cc579cac51000106f987'),('6850cc599cac51000106fa7c','6850cc569cac51000106f92f','6850cc579cac51000106f988'),('6850cc599cac51000106fa7d','6850cc569cac51000106f92f','6850cc579cac51000106f989'),('6850cc599cac51000106fa7e','6850cc569cac51000106f92f','6850cc579cac51000106f98a'),('6850cc599cac51000106fa7f','6850cc569cac51000106f92f','6850cc579cac51000106f981'),('6850cc599cac51000106fa80','6850cc569cac51000106f92f','6850cc579cac51000106f982'),('6850cc599cac51000106fa81','6850cc569cac51000106f92f','6850cc579cac51000106f983'),('6850cc599cac51000106fa82','6850cc569cac51000106f92f','6850cc579cac51000106f984'),('6850cc599cac51000106fa83','6850cc569cac51000106f92f','6850cc579cac51000106f985'),('6850cc599cac51000106fa84','6850cc569cac51000106f92f','6850cc579cac51000106f98e'),('6850cc599cac51000106fa85','6850cc569cac51000106f92f','6850cc579cac51000106f98f'),('6850cc599cac51000106fa86','6850cc569cac51000106f92f','6850cc579cac51000106f990'),('6850cc599cac51000106fa87','6850cc569cac51000106f92f','6850cc579cac51000106f991'),('6850cc599cac51000106fa88','6850cc569cac51000106f92f','6850cc579cac51000106f992'),('6850cc599cac51000106fa89','6850cc569cac51000106f92f','6850cc579cac51000106f97a'),('6850cc599cac51000106fa8a','6850cc569cac51000106f92f','6850cc579cac51000106f97b'),('6850cc599cac51000106fa8b','6850cc569cac51000106f92f','6850cc579cac51000106f97c'),('6850cc599cac51000106fa8c','6850cc569cac51000106f92f','6850cc579cac51000106f97d'),('6850cc599cac51000106fa8d','6850cc569cac51000106f92f','6850cc579cac51000106f993'),('6850cc599cac51000106fa8e','6850cc569cac51000106f92f','6850cc579cac51000106f994'),('6850cc599cac51000106fa8f','6850cc569cac51000106f92f','6850cc579cac51000106f995'),('6850cc599cac51000106fa90','6850cc569cac51000106f92f','6850cc579cac51000106f996'),('6850cc599cac51000106fa91','6850cc569cac51000106f92f','6850cc579cac51000106f99a'),('6850cc599cac51000106fa92','6850cc569cac51000106f92f','6850cc579cac51000106f99b'),('6850cc599cac51000106fa93','6850cc569cac51000106f92f','6850cc579cac51000106f99c'),('6850cc599cac51000106fa94','6850cc569cac51000106f92f','6850cc579cac51000106f99d'),('6850cc599cac51000106fa95','6850cc569cac51000106f92f','6850cc579cac51000106f93c'),('6850cc599cac51000106fa96','6850cc569cac51000106f92f','6850cc579cac51000106f99e'),('6850cc599cac51000106fa97','6850cc569cac51000106f92f','6850cc579cac51000106f99f'),('6850cc599cac51000106fa98','6850cc569cac51000106f92f','6850cc579cac51000106f9a0'),('6850cc599cac51000106fa99','6850cc569cac51000106f92f','6850cc579cac51000106f9a1'),('6850cc599cac51000106fa9a','6850cc569cac51000106f92f','6850cc579cac51000106f9a2'),('6850cc599cac51000106fa9b','6850cc569cac51000106f92f','6850cc579cac51000106f9a3'),('6850cc599cac51000106fa9c','6850cc569cac51000106f92f','6850cc579cac51000106f9a4'),('6850cc599cac51000106fa9d','6850cc569cac51000106f92f','6850cc579cac51000106f9a5'),('6850cc599cac51000106fa9e','6850cc569cac51000106f92f','6850cc579cac51000106f9a6'),('6850cc599cac51000106fa9f','6850cc569cac51000106f92f','6850cc579cac51000106f9a7'),('6850cc599cac51000106faa0','6850cc569cac51000106f92f','6850cc579cac51000106f9a8'),('6850cc599cac51000106faa1','6850cc569cac51000106f92f','6850cc579cac51000106f9a9'),('6850cc599cac51000106faa2','6850cc569cac51000106f92f','6850cc579cac51000106f9aa'),('6850cc599cac51000106faa3','6850cc569cac51000106f92f','6850cc579cac51000106f9ab'),('6850cc599cac51000106faa4','6850cc569cac51000106f92f','6850cc579cac51000106f9ac'),('6850cc599cac51000106faa5','6850cc569cac51000106f92f','6850cc579cac51000106f9ad'),('6850cc599cac51000106faa6','6850cc569cac51000106f92f','6850cc579cac51000106f9ae'),('6850cc599cac51000106faa7','6850cc569cac51000106f92f','6850cc579cac51000106f9af'),('6850cc599cac51000106faa8','6850cc569cac51000106f92f','6850cc579cac51000106f9b0'),('6850cc599cac51000106faa9','6850cc569cac51000106f92f','6850cc579cac51000106f9b1'),('6850cc599cac51000106faaa','6850cc569cac51000106f92f','6850cc579cac51000106f9b2'),('6850cc599cac51000106faab','6850cc569cac51000106f92f','6850cc579cac51000106f9b3'),('6850cc599cac51000106faac','6850cc569cac51000106f92f','6850cc579cac51000106f98b'),('6850cc599cac51000106faad','6850cc569cac51000106f934','6850cc579cac51000106f941'),('6850cc599cac51000106faae','6850cc569cac51000106f934','6850cc579cac51000106f942'),('6850cc599cac51000106faaf','6850cc569cac51000106f934','6850cc579cac51000106f943'),('6850cc599cac51000106fab0','6850cc569cac51000106f934','6850cc579cac51000106f944'),('6850cc599cac51000106fab1','6850cc569cac51000106f934','6850cc579cac51000106f945'),('6850cc599cac51000106fab2','6850cc569cac51000106f934','6850cc579cac51000106f946'),('6850cc599cac51000106fab3','6850cc569cac51000106f934','6850cc579cac51000106f947'),('6850cc599cac51000106fab4','6850cc569cac51000106f934','6850cc579cac51000106f948'),('6850cc599cac51000106fab5','6850cc569cac51000106f934','6850cc579cac51000106f97f'),('6850cc599cac51000106fab6','6850cc569cac51000106f934','6850cc579cac51000106f949'),('6850cc599cac51000106fab7','6850cc569cac51000106f934','6850cc579cac51000106f94a'),('6850cc599cac51000106fab8','6850cc569cac51000106f934','6850cc579cac51000106f94c'),('6850cc599cac51000106fab9','6850cc569cac51000106f934','6850cc579cac51000106f94d'),('6850cc599cac51000106faba','6850cc569cac51000106f934','6850cc579cac51000106f94e'),('6850cc599cac51000106fabb','6850cc569cac51000106f934','6850cc579cac51000106f94f'),('6850cc599cac51000106fabc','6850cc569cac51000106f934','6850cc579cac51000106f950'),('6850cc599cac51000106fabd','6850cc569cac51000106f934','6850cc579cac51000106f951'),('6850cc599cac51000106fabe','6850cc569cac51000106f934','6850cc579cac51000106f959'),('6850cc599cac51000106fabf','6850cc569cac51000106f934','6850cc579cac51000106f95a'),('6850cc599cac51000106fac0','6850cc569cac51000106f934','6850cc579cac51000106f95b'),('6850cc599cac51000106fac1','6850cc569cac51000106f934','6850cc579cac51000106f95c'),('6850cc599cac51000106fac2','6850cc569cac51000106f934','6850cc579cac51000106f95d'),('6850cc599cac51000106fac3','6850cc569cac51000106f934','6850cc579cac51000106f95e'),('6850cc599cac51000106fac4','6850cc569cac51000106f934','6850cc579cac51000106f95f'),('6850cc599cac51000106fac5','6850cc569cac51000106f934','6850cc579cac51000106f960'),('6850cc599cac51000106fac6','6850cc569cac51000106f934','6850cc579cac51000106f961'),('6850cc599cac51000106fac7','6850cc569cac51000106f934','6850cc579cac51000106f962'),('6850cc599cac51000106fac8','6850cc569cac51000106f934','6850cc579cac51000106f963'),('6850cc599cac51000106fac9','6850cc569cac51000106f934','6850cc579cac51000106f964'),('6850cc599cac51000106faca','6850cc569cac51000106f934','6850cc579cac51000106f952'),('6850cc599cac51000106facb','6850cc569cac51000106f934','6850cc579cac51000106f955'),('6850cc599cac51000106facc','6850cc569cac51000106f934','6850cc579cac51000106f981'),('6850cc599cac51000106facd','6850cc569cac51000106f934','6850cc579cac51000106f982'),('6850cc599cac51000106face','6850cc569cac51000106f934','6850cc579cac51000106f983'),('6850cc599cac51000106facf','6850cc569cac51000106f934','6850cc579cac51000106f984'),('6850cc599cac51000106fad0','6850cc569cac51000106f934','6850cc579cac51000106f985'),('6850cc599cac51000106fad1','6850cc569cac51000106f934','6850cc579cac51000106f98e'),('6850cc599cac51000106fad2','6850cc569cac51000106f934','6850cc579cac51000106f98f'),('6850cc599cac51000106fad3','6850cc569cac51000106f934','6850cc579cac51000106f990'),('6850cc599cac51000106fad4','6850cc569cac51000106f934','6850cc579cac51000106f991'),('6850cc599cac51000106fad5','6850cc569cac51000106f934','6850cc579cac51000106f992'),('6850cc599cac51000106fad6','6850cc569cac51000106f934','6850cc579cac51000106f986'),('6850cc599cac51000106fad7','6850cc569cac51000106f934','6850cc579cac51000106f987'),('6850cc599cac51000106fad8','6850cc569cac51000106f934','6850cc579cac51000106f988'),('6850cc599cac51000106fad9','6850cc569cac51000106f934','6850cc579cac51000106f989'),('6850cc599cac51000106fada','6850cc569cac51000106f934','6850cc579cac51000106f98a'),('6850cc599cac51000106fadb','6850cc569cac51000106f934','6850cc579cac51000106f97a'),('6850cc599cac51000106fadc','6850cc569cac51000106f934','6850cc579cac51000106f97b'),('6850cc599cac51000106fadd','6850cc569cac51000106f934','6850cc579cac51000106f99a'),('6850cc599cac51000106fade','6850cc569cac51000106f934','6850cc579cac51000106f99b'),('6850cc599cac51000106fadf','6850cc569cac51000106f934','6850cc579cac51000106f9aa'),('6850cc599cac51000106fae0','6850cc569cac51000106f934','6850cc579cac51000106f9ab'),('6850cc599cac51000106fae1','6850cc569cac51000106f934','6850cc579cac51000106f9ac'),('6850cc599cac51000106fae2','6850cc569cac51000106f934','6850cc579cac51000106f9ad'),('6850cc599cac51000106fae3','6850cc569cac51000106f934','6850cc579cac51000106f9ae'),('6850cc599cac51000106fae4','6850cc569cac51000106f934','6850cc579cac51000106f9af'),('6850cc599cac51000106fae5','6850cc569cac51000106f934','6850cc579cac51000106f9b0'),('6850cc599cac51000106fae6','6850cc569cac51000106f934','6850cc579cac51000106f975'),('6850cc599cac51000106fae7','6850cc569cac51000106f934','6850cc579cac51000106f976'),('6850cc599cac51000106fae8','6850cc569cac51000106f934','6850cc579cac51000106f977'),('6850cc599cac51000106fae9','6850cc569cac51000106f934','6850cc579cac51000106f978'),('6850cc599cac51000106faea','6850cc569cac51000106f934','6850cc579cac51000106f979'),('6850cc599cac51000106faeb','6850cc569cac51000106f934','6850cc579cac51000106f98b'),('6850cc599cac51000106faec','6850cc569cac51000106f934','6850cc579cac51000106f993'),('6850cc599cac51000106faed','6850cc569cac51000106f934','6850cc579cac51000106f994'),('6850cc599cac51000106faee','6850cc569cac51000106f934','6850cc579cac51000106f99e'),('6850cc599cac51000106faef','6850cc569cac51000106f934','6850cc579cac51000106f99f'),('6850cc599cac51000106faf0','6850cc569cac51000106f934','6850cc579cac51000106f9a0'),('6850cc599cac51000106faf1','6850cc569cac51000106f934','6850cc579cac51000106f9a1'),('6850cc599cac51000106faf2','6850cc569cac51000106f934','6850cc579cac51000106f9a2'),('6850cc599cac51000106faf3','6850cc569cac51000106f934','6850cc579cac51000106f9a3'),('6850cc599cac51000106faf4','6850cc569cac51000106f934','6850cc579cac51000106f9a4'),('6850cc599cac51000106faf5','6850cc569cac51000106f934','6850cc579cac51000106f9a5'),('6850cc599cac51000106faf6','6850cc569cac51000106f934','6850cc579cac51000106f9a6'),('6850cc599cac51000106faf7','6850cc569cac51000106f92b','6850cc579cac51000106f941'),('6850cc599cac51000106faf8','6850cc569cac51000106f92b','6850cc579cac51000106f942'),('6850cc599cac51000106faf9','6850cc569cac51000106f92b','6850cc579cac51000106f943'),('6850cc599cac51000106fafa','6850cc569cac51000106f92b','6850cc579cac51000106f944'),('6850cc599cac51000106fafb','6850cc569cac51000106f92b','6850cc579cac51000106f945'),('6850cc599cac51000106fafc','6850cc569cac51000106f92b','6850cc579cac51000106f946'),('6850cc599cac51000106fafd','6850cc569cac51000106f92b','6850cc579cac51000106f947'),('6850cc599cac51000106fafe','6850cc569cac51000106f92b','6850cc579cac51000106f948'),('6850cc599cac51000106faff','6850cc569cac51000106f92b','6850cc579cac51000106f97f'),('6850cc599cac51000106fb00','6850cc569cac51000106f92b','6850cc579cac51000106f949'),('6850cc599cac51000106fb01','6850cc569cac51000106f92b','6850cc579cac51000106f94a'),('6850cc599cac51000106fb02','6850cc569cac51000106f92b','6850cc579cac51000106f94c'),('6850cc599cac51000106fb03','6850cc569cac51000106f92b','6850cc579cac51000106f94d'),('6850cc599cac51000106fb04','6850cc569cac51000106f92b','6850cc579cac51000106f94e'),('6850cc599cac51000106fb05','6850cc569cac51000106f92b','6850cc579cac51000106f94f'),('6850cc599cac51000106fb06','6850cc569cac51000106f92b','6850cc579cac51000106f950'),('6850cc599cac51000106fb07','6850cc569cac51000106f92b','6850cc579cac51000106f951'),('6850cc599cac51000106fb08','6850cc569cac51000106f92b','6850cc579cac51000106f959'),('6850cc599cac51000106fb09','6850cc569cac51000106f92b','6850cc579cac51000106f95a'),('6850cc599cac51000106fb0a','6850cc569cac51000106f92b','6850cc579cac51000106f95b'),('6850cc599cac51000106fb0b','6850cc569cac51000106f92b','6850cc579cac51000106f95c'),('6850cc599cac51000106fb0c','6850cc569cac51000106f92b','6850cc579cac51000106f95d'),('6850cc599cac51000106fb0d','6850cc569cac51000106f92b','6850cc579cac51000106f95e'),('6850cc599cac51000106fb0e','6850cc569cac51000106f92b','6850cc579cac51000106f95f'),('6850cc599cac51000106fb0f','6850cc569cac51000106f92b','6850cc579cac51000106f960'),('6850cc599cac51000106fb10','6850cc569cac51000106f92b','6850cc579cac51000106f961'),('6850cc599cac51000106fb11','6850cc569cac51000106f92b','6850cc579cac51000106f962'),('6850cc599cac51000106fb12','6850cc569cac51000106f92b','6850cc579cac51000106f963'),('6850cc599cac51000106fb13','6850cc569cac51000106f92b','6850cc579cac51000106f964'),('6850cc599cac51000106fb14','6850cc569cac51000106f92b','6850cc579cac51000106f952'),('6850cc599cac51000106fb15','6850cc569cac51000106f92b','6850cc579cac51000106f955'),('6850cc599cac51000106fb16','6850cc569cac51000106f92b','6850cc579cac51000106f981'),('6850cc599cac51000106fb17','6850cc569cac51000106f92b','6850cc579cac51000106f982'),('6850cc599cac51000106fb18','6850cc569cac51000106f92b','6850cc579cac51000106f983'),('6850cc599cac51000106fb19','6850cc569cac51000106f92b','6850cc579cac51000106f984'),('6850cc599cac51000106fb1a','6850cc569cac51000106f92b','6850cc579cac51000106f985'),('6850cc599cac51000106fb1b','6850cc569cac51000106f92b','6850cc579cac51000106f98e'),('6850cc599cac51000106fb1c','6850cc569cac51000106f92b','6850cc579cac51000106f98f'),('6850cc599cac51000106fb1d','6850cc569cac51000106f92b','6850cc579cac51000106f990'),('6850cc599cac51000106fb1e','6850cc569cac51000106f92b','6850cc579cac51000106f991'),('6850cc599cac51000106fb1f','6850cc569cac51000106f92b','6850cc579cac51000106f992'),('6850cc599cac51000106fb20','6850cc569cac51000106f92b','6850cc579cac51000106f986'),('6850cc599cac51000106fb21','6850cc569cac51000106f92b','6850cc579cac51000106f987'),('6850cc599cac51000106fb22','6850cc569cac51000106f92b','6850cc579cac51000106f97a'),('6850cc599cac51000106fb23','6850cc569cac51000106f92b','6850cc579cac51000106f97b'),('6850cc599cac51000106fb24','6850cc569cac51000106f92b','6850cc579cac51000106f99a'),('6850cc599cac51000106fb25','6850cc569cac51000106f92b','6850cc579cac51000106f99b'),('6850cc599cac51000106fb26','6850cc569cac51000106f92b','6850cc579cac51000106f9aa'),('6850cc599cac51000106fb27','6850cc569cac51000106f92b','6850cc579cac51000106f9ab'),('6850cc599cac51000106fb28','6850cc569cac51000106f92b','6850cc579cac51000106f9ac'),('6850cc599cac51000106fb29','6850cc569cac51000106f92b','6850cc579cac51000106f9ad'),('6850cc599cac51000106fb2a','6850cc569cac51000106f92b','6850cc579cac51000106f9ae'),('6850cc599cac51000106fb2b','6850cc569cac51000106f92b','6850cc579cac51000106f9af'),('6850cc599cac51000106fb2c','6850cc569cac51000106f92b','6850cc579cac51000106f9b0'),('6850cc599cac51000106fb2d','6850cc569cac51000106f92c','6850cc579cac51000106f944'),('6850cc599cac51000106fb2e','6850cc569cac51000106f92c','6850cc579cac51000106f945'),('6850cc599cac51000106fb2f','6850cc569cac51000106f92c','6850cc579cac51000106f946'),('6850cc599cac51000106fb30','6850cc569cac51000106f92c','6850cc579cac51000106f947'),('6850cc599cac51000106fb31','6850cc569cac51000106f92c','6850cc579cac51000106f948'),('6850cc599cac51000106fb32','6850cc569cac51000106f92c','6850cc579cac51000106f949'),('6850cc599cac51000106fb33','6850cc569cac51000106f92c','6850cc579cac51000106f94a'),('6850cc599cac51000106fb34','6850cc569cac51000106f92c','6850cc579cac51000106f94c'),('6850cc599cac51000106fb35','6850cc569cac51000106f92c','6850cc579cac51000106f94d'),('6850cc599cac51000106fb36','6850cc569cac51000106f92c','6850cc579cac51000106f94e'),('6850cc599cac51000106fb37','6850cc569cac51000106f92c','6850cc579cac51000106f950'),('6850cc599cac51000106fb38','6850cc569cac51000106f92c','6850cc579cac51000106f959'),('6850cc599cac51000106fb39','6850cc569cac51000106f92c','6850cc579cac51000106f95a'),('6850cc599cac51000106fb3a','6850cc569cac51000106f92c','6850cc579cac51000106f95f'),('6850cc599cac51000106fb3b','6850cc569cac51000106f92c','6850cc579cac51000106f952'),('6850cc599cac51000106fb3c','6850cc569cac51000106f92c','6850cc579cac51000106f955'),('6850cc599cac51000106fb3d','6850cc569cac51000106f92c','6850cc579cac51000106f981'),('6850cc599cac51000106fb3e','6850cc569cac51000106f92c','6850cc579cac51000106f984'),('6850cc599cac51000106fb3f','6850cc569cac51000106f92c','6850cc579cac51000106f98e'),('6850cc599cac51000106fb40','6850cc569cac51000106f92c','6850cc579cac51000106f98f'),('6850cc599cac51000106fb41','6850cc569cac51000106f92c','6850cc579cac51000106f986'),('6850cc599cac51000106fb42','6850cc569cac51000106f92c','6850cc579cac51000106f987'),('6850cc599cac51000106fb43','6850cc569cac51000106f92c','6850cc579cac51000106f97a'),('6850cc599cac51000106fb44','6850cc569cac51000106f92c','6850cc579cac51000106f97b'),('6850cc599cac51000106fb45','6850cc569cac51000106f92c','6850cc579cac51000106f99a'),('6850cc599cac51000106fb46','6850cc569cac51000106f92c','6850cc579cac51000106f99b'),('6850cc599cac51000106fb47','6850cc569cac51000106f92c','6850cc579cac51000106f9aa'),('6850cc599cac51000106fb48','6850cc569cac51000106f92c','6850cc579cac51000106f9ab'),('6850cc599cac51000106fb49','6850cc569cac51000106f92c','6850cc579cac51000106f9ad'),('6850cc599cac51000106fb4a','6850cc569cac51000106f92c','6850cc579cac51000106f9af'),('6850cc599cac51000106fb4b','6850cc569cac51000106f92c','6850cc579cac51000106f9b0'),('6850cc599cac51000106fb4c','6850cc569cac51000106f92d','6850cc579cac51000106f944'),('6850cc599cac51000106fb4d','6850cc569cac51000106f92d','6850cc579cac51000106f945'),('6850cc599cac51000106fb4e','6850cc569cac51000106f92d','6850cc579cac51000106f946'),('6850cc599cac51000106fb4f','6850cc569cac51000106f92d','6850cc579cac51000106f947'),('6850cc599cac51000106fb50','6850cc569cac51000106f92d','6850cc579cac51000106f948'),('6850cc599cac51000106fb51','6850cc569cac51000106f92d','6850cc579cac51000106f949'),('6850cc599cac51000106fb52','6850cc569cac51000106f92d','6850cc579cac51000106f94a'),('6850cc599cac51000106fb53','6850cc569cac51000106f92d','6850cc579cac51000106f94c'),('6850cc599cac51000106fb54','6850cc569cac51000106f92d','6850cc579cac51000106f94d'),('6850cc599cac51000106fb55','6850cc569cac51000106f92d','6850cc579cac51000106f94e'),('6850cc599cac51000106fb56','6850cc569cac51000106f92d','6850cc579cac51000106f959'),('6850cc599cac51000106fb57','6850cc569cac51000106f92d','6850cc579cac51000106f95a'),('6850cc599cac51000106fb58','6850cc569cac51000106f92d','6850cc579cac51000106f95f'),('6850cc599cac51000106fb59','6850cc569cac51000106f92d','6850cc579cac51000106f952'),('6850cc599cac51000106fb5a','6850cc569cac51000106f92d','6850cc579cac51000106f981'),('6850cc599cac51000106fb5b','6850cc569cac51000106f92d','6850cc579cac51000106f984'),('6850cc599cac51000106fb5c','6850cc569cac51000106f92d','6850cc579cac51000106f98e'),('6850cc599cac51000106fb5d','6850cc569cac51000106f92d','6850cc579cac51000106f98f'),('6850cc599cac51000106fb5e','6850cc569cac51000106f92d','6850cc579cac51000106f9aa'),('6850cc599cac51000106fb5f','6850cc569cac51000106f92d','6850cc579cac51000106f9ab'),('6850cc599cac51000106fb60','6850cc569cac51000106f92d','6850cc579cac51000106f9af'),('6850cc599cac51000106fb61','6850cc569cac51000106f92d','6850cc579cac51000106f9b0');
/*!40000 ALTER TABLE `permissions_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions_users`
--

DROP TABLE IF EXISTS `permissions_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions_users` (
  `id` varchar(24) NOT NULL,
  `user_id` varchar(24) NOT NULL,
  `permission_id` varchar(24) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions_users`
--

LOCK TABLES `permissions_users` WRITE;
/*!40000 ALTER TABLE `permissions_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `permissions_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `post_revisions`
--

DROP TABLE IF EXISTS `post_revisions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `post_revisions` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `lexical` longtext,
  `created_at_ts` bigint NOT NULL,
  `created_at` datetime NOT NULL,
  `author_id` varchar(24) DEFAULT NULL,
  `title` varchar(2000) DEFAULT NULL,
  `post_status` varchar(50) DEFAULT NULL,
  `reason` varchar(50) DEFAULT NULL,
  `feature_image` varchar(2000) DEFAULT NULL,
  `feature_image_alt` varchar(191) DEFAULT NULL,
  `feature_image_caption` text,
  `custom_excerpt` varchar(2000) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `post_revisions_post_id_index` (`post_id`),
  KEY `post_revs_author_id_foreign` (`author_id`),
  CONSTRAINT `post_revs_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `post_revisions`
--

LOCK TABLES `post_revisions` WRITE;
/*!40000 ALTER TABLE `post_revisions` DISABLE KEYS */;
INSERT INTO `post_revisions` VALUES ('6850e726db983000014c1cba','6850e726db983000014c1cb8','{\"root\":{\"children\":[{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132518346,'2025-06-17 03:55:18','1','App Launching Soon','draft','initial_revision',NULL,NULL,NULL,NULL),('6850e883db983000014c1cc0','6850e726db983000014c1cb8','{\"root\":{\"children\":[{\"type\":\"image\",\"version\":1,\"src\":\"https://ope-ghost-stg.zafar.dev/content/images/2025/06/EN_01_iOS_1242_2208.png\",\"width\":1242,\"height\":2208,\"title\":\"\",\"alt\":\"\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132867063,'2025-06-17 04:01:07','1','App Launching Soon','draft','explicit_save',NULL,NULL,NULL,NULL),('6850e886db983000014c1cc2','6850e726db983000014c1cb8','{\"root\":{\"children\":[{\"type\":\"image\",\"version\":1,\"src\":\"https://ope-ghost-stg.zafar.dev/content/images/2025/06/EN_01_iOS_1242_2208.png\",\"width\":1242,\"height\":2208,\"title\":\"\",\"alt\":\"\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132870146,'2025-06-17 04:01:10','1','App Launching Soon','published','published',NULL,NULL,NULL,NULL),('6850e897db983000014c1cc4','6850e726db983000014c1cb8','{\"root\":{\"children\":[{\"type\":\"image\",\"version\":1,\"src\":\"https://ope-ghost-stg.zafar.dev/content/images/2025/06/EN_01_iOS_1242_2208.png\",\"width\":1242,\"height\":2208,\"title\":\"\",\"alt\":\"\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132887704,'2025-06-17 04:01:27','1','App Launching Soon','published','published',NULL,NULL,NULL,NULL),('6850e8d6db983000014c1cca','6850e8d5db983000014c1cc8','{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your Company Name (\\\"we,\\\" \\\"our,\\\" or \\\"us\\\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use Your App Name (the \\\"App\\\"). Please read this policy carefully. If you do not agree with the terms of this Privacy Policy, please do not use the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"1. Information We Collect\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect the following types of information when you use the App:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"a. Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Name\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Name\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone Number\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Other Identifiers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Device ID, IP address, or other unique identifiers.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"b. Non-Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Usage Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Information about how you interact with the App, such as features used, time spent, and navigation patterns.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Device Information\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Details about your device, including operating system, device type, and app version.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Location Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Approximate location based on IP address or precise location (if you grant permission).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"c. Information from Third Parties\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect information from third-party services (e.g., social media platforms or analytics providers) if you choose to connect them to the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"2. How We Use Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may use the information we collect for the following purposes:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To provide, maintain, and improve the App’s functionality.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To personalize your experience within the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To communicate with you, including sending updates, notifications, or promotional content (if you opt in).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To analyze usage trends and optimize App performance.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To comply with legal obligations or protect our rights.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3. How We Share Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We do not sell your personal information. We may share your information in the following cases:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Service Providers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": With third-party vendors who perform services on our behalf, such as hosting, analytics, or customer support.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Legal Requirements\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": If required by law, regulation, or legal process.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Business Transfers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": In connection with a merger, acquisition, or sale of assets.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"With Your Consent\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": When you explicitly agree to share your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"4. Data Security\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We implement reasonable security measures to protect your information from unauthorized access, use, or disclosure. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5. Your Choices and Rights\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You may have the following rights regarding your information, subject to applicable laws:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Access\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request access to the personal information we hold about you.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Correction\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request corrections to inaccurate or incomplete information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Deletion\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request deletion of your personal information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Opt-Out\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Opt out of receiving promotional communications.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Data Portability\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request a copy of your data in a structured, commonly used format.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To exercise these rights, please contact us at Your Contact Email.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"6. Third-Party Links and Services\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App may contain links to third-party websites or services. We are not responsible for the privacy practices or content of these third parties. We encourage you to review their privacy policies.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"7. Children’s Privacy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we learn that we have collected such information, we will take steps to delete it.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"8. International Data Transfers\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your information may be transferred to and processed in countries other than your own, where data protection laws may differ. We ensure appropriate safeguards are in place to protect your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"9. Changes to This Privacy Policy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may update this Privacy Policy from time to time. We will notify you of any changes by posting the updated policy in the App or by other means. Your continued use of the App after such changes constitutes your acceptance of the new terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10. Contact Us\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"If you have any questions or concerns about this Privacy Policy, please contact us at:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Company Address\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By using the App, you acknowledge that you have read and understood this Privacy Policy.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132949970,'2025-06-17 04:02:30','1','(Untitled)','draft','initial_revision',NULL,NULL,NULL,NULL),('6850e8e9db983000014c1ccf','6850e8d5db983000014c1cc8','{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your Company Name (\\\"we,\\\" \\\"our,\\\" or \\\"us\\\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use Your App Name (the \\\"App\\\"). Please read this policy carefully. If you do not agree with the terms of this Privacy Policy, please do not use the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"1. Information We Collect\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect the following types of information when you use the App:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"a. Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Name\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Name\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone Number\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Other Identifiers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Device ID, IP address, or other unique identifiers.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"b. Non-Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Usage Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Information about how you interact with the App, such as features used, time spent, and navigation patterns.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Device Information\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Details about your device, including operating system, device type, and app version.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Location Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Approximate location based on IP address or precise location (if you grant permission).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"c. Information from Third Parties\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect information from third-party services (e.g., social media platforms or analytics providers) if you choose to connect them to the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"2. How We Use Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may use the information we collect for the following purposes:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To provide, maintain, and improve the App’s functionality.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To personalize your experience within the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To communicate with you, including sending updates, notifications, or promotional content (if you opt in).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To analyze usage trends and optimize App performance.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To comply with legal obligations or protect our rights.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3. How We Share Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We do not sell your personal information. We may share your information in the following cases:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Service Providers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": With third-party vendors who perform services on our behalf, such as hosting, analytics, or customer support.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Legal Requirements\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": If required by law, regulation, or legal process.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Business Transfers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": In connection with a merger, acquisition, or sale of assets.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"With Your Consent\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": When you explicitly agree to share your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"4. Data Security\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We implement reasonable security measures to protect your information from unauthorized access, use, or disclosure. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5. Your Choices and Rights\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You may have the following rights regarding your information, subject to applicable laws:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Access\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request access to the personal information we hold about you.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Correction\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request corrections to inaccurate or incomplete information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Deletion\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request deletion of your personal information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Opt-Out\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Opt out of receiving promotional communications.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Data Portability\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request a copy of your data in a structured, commonly used format.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To exercise these rights, please contact us at Your Contact Email.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"6. Third-Party Links and Services\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App may contain links to third-party websites or services. We are not responsible for the privacy practices or content of these third parties. We encourage you to review their privacy policies.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"7. Children’s Privacy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we learn that we have collected such information, we will take steps to delete it.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"8. International Data Transfers\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your information may be transferred to and processed in countries other than your own, where data protection laws may differ. We ensure appropriate safeguards are in place to protect your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"9. Changes to This Privacy Policy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may update this Privacy Policy from time to time. We will notify you of any changes by posting the updated policy in the App or by other means. Your continued use of the App after such changes constitutes your acceptance of the new terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10. Contact Us\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"If you have any questions or concerns about this Privacy Policy, please contact us at:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Company Address\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By using the App, you acknowledge that you have read and understood this Privacy Policy.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132969347,'2025-06-17 04:02:49','1','Privacy Policy','published','published',NULL,NULL,NULL,NULL),('6850e8ffdb983000014c1cd5','6850e8ffdb983000014c1cd3','{\"root\":{\"children\":[{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750132991563,'2025-06-17 04:03:11','1','Terms and Conditions','draft','initial_revision',NULL,NULL,NULL,NULL),('6850e90bdb983000014c1cda','6850e8ffdb983000014c1cd3','{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Welcome to [App Name]! These Terms and Conditions (\\\"Terms\\\") govern your use of the [App Name] mobile application (the \\\"App\\\") provided by [Company Name], a company registered in [State/Country] with its principal office at [Company Address] (\\\"we,\\\" \\\"us,\\\" or \\\"our\\\"). By downloading, installing, or using the App, you agree to be bound by these Terms. If you do not agree, please do not use the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"1. Acceptance of Terms\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By accessing or using the App, you confirm that you are at least [Minimum Age, e.g., 13] years old and have the legal capacity to enter into these Terms. You agree to comply with these Terms and any applicable laws.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"2. Changes to Terms\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may update these Terms from time to time. We will notify you of changes by posting the updated Terms in the App or via [Notification Method, e.g., email or in-app notification]. Your continued use of the App after such changes constitutes your acceptance of the new Terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3. Use of the App\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3.1 License\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We grant you a non-exclusive, non-transferable, revocable license to use the App for personal, non-commercial purposes, subject to these Terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3.2 Restrictions\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You agree not to:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Modify, reverse-engineer, or decompile the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Use the App for illegal or unauthorized purposes.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Share your account credentials with others.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Use the App to harm or interfere with its functionality or other users.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"4. User Accounts\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To access certain features, you may need to create an account. You are responsible for:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Providing accurate and complete information during registration.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Maintaining the confidentiality of your account credentials.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Notifying us immediately of any unauthorized use of your account at [Contact Email].\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5. Content\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5.1 User Content\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You may be able to submit content (e.g., text, images) through the App (\\\"User Content\\\"). You retain ownership of your User Content but grant us a worldwide, non-exclusive, royalty-free license to use, store, and display it as necessary to provide the App’s services.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5.2 Prohibited Content\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You agree not to submit content that is unlawful, defamatory, obscene, or infringes on intellectual property rights.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"6. Intellectual Property\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"All content, trademarks, and materials in the App, excluding User Content, are owned by or licensed to [Company Name]. You may not use these materials without our prior written consent.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"7. Privacy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your use of the App is subject to our Privacy Policy, available at [Privacy Policy URL]. Please review it to understand how we collect, use, and protect your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"8. Payments and Subscriptions\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Some features of the App may require payment or subscriptions. All payments are processed through [Payment Processor, e.g., Apple App Store, Google Play]. You agree to pay all applicable fees and taxes. Subscriptions may auto-renew unless canceled as described in [Cancellation Policy URL].\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"9. Termination\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may suspend or terminate your access to the App if you violate these Terms or for any other reason at our discretion. Upon termination, your right to use the App will cease immediately.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10. Disclaimers and Limitation of Liability\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10.1 Disclaimers\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App is provided \\\"as is\\\" without warranties of any kind, express or implied. We do not guarantee that the App will be error-free or uninterrupted.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10.2 Limitation of Liability\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To the fullest extent permitted by law, [Company Name] shall not be liable for any indirect, incidental, or consequential damages arising from your use of the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"11. Indemnification\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You agree to indemnify and hold [Company Name], its affiliates, and employees harmless from any claims, losses, or damages arising from your use of the App or violation of these Terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"12. Governing Law\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"These Terms are governed by the laws of [Governing Jurisdiction].\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750133003964,'2025-06-17 04:03:23','1','Terms and Conditions','published','published',NULL,NULL,NULL,NULL),('6850e912db983000014c1cdc','6850e8d5db983000014c1cc8','{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your Company Name (\\\"we,\\\" \\\"our,\\\" or \\\"us\\\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use Your App Name (the \\\"App\\\"). Please read this policy carefully. If you do not agree with the terms of this Privacy Policy, please do not use the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"1. Information We Collect\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect the following types of information when you use the App:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"a. Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Name\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Name\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone Number\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Other Identifiers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Device ID, IP address, or other unique identifiers.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"b. Non-Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Usage Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Information about how you interact with the App, such as features used, time spent, and navigation patterns.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Device Information\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Details about your device, including operating system, device type, and app version.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Location Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Approximate location based on IP address or precise location (if you grant permission).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"c. Information from Third Parties\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect information from third-party services (e.g., social media platforms or analytics providers) if you choose to connect them to the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"2. How We Use Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may use the information we collect for the following purposes:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To provide, maintain, and improve the App’s functionality.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To personalize your experience within the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To communicate with you, including sending updates, notifications, or promotional content (if you opt in).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To analyze usage trends and optimize App performance.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To comply with legal obligations or protect our rights.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3. How We Share Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We do not sell your personal information. We may share your information in the following cases:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Service Providers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": With third-party vendors who perform services on our behalf, such as hosting, analytics, or customer support.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Legal Requirements\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": If required by law, regulation, or legal process.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Business Transfers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": In connection with a merger, acquisition, or sale of assets.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"With Your Consent\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": When you explicitly agree to share your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"4. Data Security\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We implement reasonable security measures to protect your information from unauthorized access, use, or disclosure. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5. Your Choices and Rights\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You may have the following rights regarding your information, subject to applicable laws:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Access\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request access to the personal information we hold about you.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Correction\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request corrections to inaccurate or incomplete information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Deletion\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request deletion of your personal information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Opt-Out\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Opt out of receiving promotional communications.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Data Portability\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request a copy of your data in a structured, commonly used format.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To exercise these rights, please contact us at Your Contact Email.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"6. Third-Party Links and Services\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App may contain links to third-party websites or services. We are not responsible for the privacy practices or content of these third parties. We encourage you to review their privacy policies.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"7. Children’s Privacy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we learn that we have collected such information, we will take steps to delete it.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"8. International Data Transfers\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your information may be transferred to and processed in countries other than your own, where data protection laws may differ. We ensure appropriate safeguards are in place to protect your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"9. Changes to This Privacy Policy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may update this Privacy Policy from time to time. We will notify you of any changes by posting the updated policy in the App or by other means. Your continued use of the App after such changes constitutes your acceptance of the new terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10. Contact Us\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"If you have any questions or concerns about this Privacy Policy, please contact us at:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Company Address\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By using the App, you acknowledge that you have read and understood this Privacy Policy.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750133010181,'2025-06-17 04:03:30','1','Privacy Policy','published','published',NULL,NULL,NULL,NULL),('68525eb6db983000014c1cf4','68525eb6db983000014c1cf2','{\"root\":{\"children\":[{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750228662289,'2025-06-18 06:37:42','1','Rules','draft','initial_revision',NULL,NULL,NULL,NULL),('68525ec8db983000014c1cf9','68525eb6db983000014c1cf2','{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"TODO: Application Rules English\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}',1750228680350,'2025-06-18 06:38:00','1','Rules','published','published',NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `post_revisions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `posts`
--

DROP TABLE IF EXISTS `posts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `posts` (
  `id` varchar(24) NOT NULL,
  `uuid` varchar(36) NOT NULL,
  `title` varchar(2000) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `mobiledoc` longtext,
  `lexical` longtext,
  `html` longtext,
  `comment_id` varchar(50) DEFAULT NULL,
  `plaintext` longtext,
  `feature_image` varchar(2000) DEFAULT NULL,
  `featured` tinyint(1) NOT NULL DEFAULT '0',
  `type` varchar(50) NOT NULL DEFAULT 'post',
  `status` varchar(50) NOT NULL DEFAULT 'draft',
  `locale` varchar(6) DEFAULT NULL,
  `visibility` varchar(50) NOT NULL DEFAULT 'public',
  `email_recipient_filter` text NOT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  `published_at` datetime DEFAULT NULL,
  `published_by` varchar(24) DEFAULT NULL,
  `custom_excerpt` varchar(2000) DEFAULT NULL,
  `codeinjection_head` text,
  `codeinjection_foot` text,
  `custom_template` varchar(100) DEFAULT NULL,
  `canonical_url` text,
  `newsletter_id` varchar(24) DEFAULT NULL,
  `show_title_and_feature_image` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `posts_slug_type_unique` (`slug`,`type`),
  KEY `posts_uuid_index` (`uuid`),
  KEY `posts_updated_at_index` (`updated_at`),
  KEY `posts_published_at_index` (`published_at`),
  KEY `posts_newsletter_id_foreign` (`newsletter_id`),
  KEY `posts_type_status_updated_at_index` (`type`,`status`,`updated_at`),
  CONSTRAINT `posts_newsletter_id_foreign` FOREIGN KEY (`newsletter_id`) REFERENCES `newsletters` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `posts`
--

LOCK TABLES `posts` WRITE;
/*!40000 ALTER TABLE `posts` DISABLE KEYS */;
INSERT INTO `posts` VALUES ('6850cc589cac51000106f9b6','ad3f66e3-435a-4d87-8e42-042f1e6fadc9','About this site','about',NULL,'{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Open Portal Expo Media is an independent publication launched in June 2025 by Open Portal Expo. If you subscribe today, you\'ll get full access to the website as well as email newsletters about new content when it\'s available. Your subscription makes this site possible, and allows Open Portal Expo Media to continue to exist. Thank you!\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Access all areas\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h3\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By signing up, you\'ll get access to the full archive of everything that\'s been published before and everything that\'s still to come. Your very own private library.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Fresh content, delivered\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h3\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Stay up to date with new content sent straight to your inbox! No more worrying about whether you missed something because of a pesky algorithm or news feed.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Meet people like you\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h3\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Join a community of other subscribers who share the same interests.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"type\":\"horizontalrule\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Start your own thing\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"heading\",\"tag\":\"h3\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Enjoying the experience? Get started for free and set up your very own subscription business using \",\"type\":\"text\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Ghost\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"link\",\"rel\":null,\"target\":null,\"title\":null,\"url\":\"https://ghost.org\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\", the same platform that powers this website.\",\"type\":\"text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}','<p>Open Portal Expo Media is an independent publication launched in June 2025 by Open Portal Expo. If you subscribe today, you\'ll get full access to the website as well as email newsletters about new content when it\'s available. Your subscription makes this site possible, and allows Open Portal Expo Media to continue to exist. Thank you!</p><h3 id=\"access-all-areas\">Access all areas</h3><p>By signing up, you\'ll get access to the full archive of everything that\'s been published before and everything that\'s still to come. Your very own private library.</p><h3 id=\"fresh-content-delivered\">Fresh content, delivered</h3><p>Stay up to date with new content sent straight to your inbox! No more worrying about whether you missed something because of a pesky algorithm or news feed.</p><h3 id=\"meet-people-like-you\">Meet people like you</h3><p>Join a community of other subscribers who share the same interests.</p><hr><h3 id=\"start-your-own-thing\">Start your own thing</h3><p>Enjoying the experience? Get started for free and set up your very own subscription business using <a href=\"https://ghost.org\">Ghost</a>, the same platform that powers this website.</p>','6850cc589cac51000106f9b6','Open Portal Expo Media is an independent publication launched in June 2025 by Open Portal Expo. If you subscribe today, you\'ll get full access to the website as well as email newsletters about new content when it\'s available. Your subscription makes this site possible, and allows Open Portal Expo Media to continue to exist. Thank you!\n\n\nAccess all areas\n\nBy signing up, you\'ll get access to the full archive of everything that\'s been published before and everything that\'s still to come. Your very own private library.\n\n\nFresh content, delivered\n\nStay up to date with new content sent straight to your inbox! No more worrying about whether you missed something because of a pesky algorithm or news feed.\n\n\nMeet people like you\n\nJoin a community of other subscribers who share the same interests.\n\n\nStart your own thing\n\nEnjoying the experience? Get started for free and set up your very own subscription business using Ghost, the same platform that powers this website.',NULL,0,'page','published',NULL,'public','all','2025-06-17 02:00:56','1','2025-06-17 04:01:50','1','2025-06-17 02:00:56','1',NULL,NULL,NULL,NULL,NULL,NULL,1),('6850e726db983000014c1cb8','183e90d8-fcdb-40f7-8050-e0f2da6a4d55','App Launching Soon','app-launching-soon',NULL,'{\"root\":{\"children\":[{\"type\":\"image\",\"version\":1,\"src\":\"__GHOST_URL__/content/images/2025/06/EN_01_iOS_1242_2208.png\",\"width\":1242,\"height\":2208,\"title\":\"\",\"alt\":\"\",\"caption\":\"\",\"cardWidth\":\"regular\",\"href\":\"\"},{\"children\":[],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":null,\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}','<figure class=\"kg-card kg-image-card\"><img src=\"__GHOST_URL__/content/images/2025/06/EN_01_iOS_1242_2208.png\" class=\"kg-image\" alt=\"\" loading=\"lazy\" width=\"1242\" height=\"2208\" srcset=\"__GHOST_URL__/content/images/size/w600/2025/06/EN_01_iOS_1242_2208.png 600w, __GHOST_URL__/content/images/size/w1000/2025/06/EN_01_iOS_1242_2208.png 1000w, __GHOST_URL__/content/images/2025/06/EN_01_iOS_1242_2208.png 1242w\" sizes=\"(min-width: 720px) 720px\"></figure>','6850e726db983000014c1cb8',NULL,NULL,1,'post','published',NULL,'public','all','2025-06-17 03:55:18','1','2025-06-17 04:01:27','1','2025-06-17 04:01:10','1',NULL,NULL,NULL,NULL,NULL,NULL,1),('6850e8d5db983000014c1cc8','734bd73c-33c7-4b3f-a915-eb4906c764bb','Privacy Policy','privacy-policy-en',NULL,'{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your Company Name (\\\"we,\\\" \\\"our,\\\" or \\\"us\\\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use Your App Name (the \\\"App\\\"). Please read this policy carefully. If you do not agree with the terms of this Privacy Policy, please do not use the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"1. Information We Collect\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect the following types of information when you use the App:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"a. Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Name\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Name\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone Number\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Other Identifiers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Device ID, IP address, or other unique identifiers.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"b. Non-Personal Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Usage Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Information about how you interact with the App, such as features used, time spent, and navigation patterns.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Device Information\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Details about your device, including operating system, device type, and app version.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Location Data\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Approximate location based on IP address or precise location (if you grant permission).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"c. Information from Third Parties\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may collect information from third-party services (e.g., social media platforms or analytics providers) if you choose to connect them to the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"2. How We Use Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may use the information we collect for the following purposes:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To provide, maintain, and improve the App’s functionality.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To personalize your experience within the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To communicate with you, including sending updates, notifications, or promotional content (if you opt in).\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To analyze usage trends and optimize App performance.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To comply with legal obligations or protect our rights.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3. How We Share Your Information\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We do not sell your personal information. We may share your information in the following cases:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Service Providers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": With third-party vendors who perform services on our behalf, such as hosting, analytics, or customer support.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Legal Requirements\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": If required by law, regulation, or legal process.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Business Transfers\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": In connection with a merger, acquisition, or sale of assets.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"With Your Consent\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": When you explicitly agree to share your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"4. Data Security\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We implement reasonable security measures to protect your information from unauthorized access, use, or disclosure. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5. Your Choices and Rights\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You may have the following rights regarding your information, subject to applicable laws:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Access\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request access to the personal information we hold about you.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Correction\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request corrections to inaccurate or incomplete information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Deletion\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request deletion of your personal information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Opt-Out\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Opt out of receiving promotional communications.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Data Portability\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Request a copy of your data in a structured, commonly used format.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":5}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To exercise these rights, please contact us at Your Contact Email.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"6. Third-Party Links and Services\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App may contain links to third-party websites or services. We are not responsible for the privacy practices or content of these third parties. We encourage you to review their privacy policies.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"7. Children’s Privacy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we learn that we have collected such information, we will take steps to delete it.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"8. International Data Transfers\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your information may be transferred to and processed in countries other than your own, where data protection laws may differ. We ensure appropriate safeguards are in place to protect your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"9. Changes to This Privacy Policy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may update this Privacy Policy from time to time. We will notify you of any changes by posting the updated policy in the App or by other means. Your continued use of the App after such changes constitutes your acceptance of the new terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10. Contact Us\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"If you have any questions or concerns about this Privacy Policy, please contact us at:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Email\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Email\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Address\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Company Address\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":1,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Phone\",\"type\":\"extended-text\",\"version\":1},{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\": Your Contact Phone Number\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By using the App, you acknowledge that you have read and understood this Privacy Policy.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}','<p>Your Company Name (\"we,\" \"our,\" or \"us\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use Your App Name (the \"App\"). Please read this policy carefully. If you do not agree with the terms of this Privacy Policy, please do not use the App.</p><h2 id=\"1-information-we-collect\">1. Information We Collect</h2><p>We may collect the following types of information when you use the App:</p><h3 id=\"a-personal-information\">a. Personal Information</h3><ul><li><strong>Name</strong>: Your Name</li><li><strong>Email Address</strong>: Your Email</li><li><strong>Phone Number</strong>: Your Phone Number</li><li><strong>Other Identifiers</strong>: Device ID, IP address, or other unique identifiers.</li></ul><h3 id=\"b-non-personal-information\">b. Non-Personal Information</h3><ul><li><strong>Usage Data</strong>: Information about how you interact with the App, such as features used, time spent, and navigation patterns.</li><li><strong>Device Information</strong>: Details about your device, including operating system, device type, and app version.</li><li><strong>Location Data</strong>: Approximate location based on IP address or precise location (if you grant permission).</li></ul><h3 id=\"c-information-from-third-parties\">c. Information from Third Parties</h3><p>We may collect information from third-party services (e.g., social media platforms or analytics providers) if you choose to connect them to the App.</p><h2 id=\"2-how-we-use-your-information\">2. How We Use Your Information</h2><p>We may use the information we collect for the following purposes:</p><ul><li>To provide, maintain, and improve the App’s functionality.</li><li>To personalize your experience within the App.</li><li>To communicate with you, including sending updates, notifications, or promotional content (if you opt in).</li><li>To analyze usage trends and optimize App performance.</li><li>To comply with legal obligations or protect our rights.</li></ul><h2 id=\"3-how-we-share-your-information\">3. How We Share Your Information</h2><p>We do not sell your personal information. We may share your information in the following cases:</p><ul><li><strong>Service Providers</strong>: With third-party vendors who perform services on our behalf, such as hosting, analytics, or customer support.</li><li><strong>Legal Requirements</strong>: If required by law, regulation, or legal process.</li><li><strong>Business Transfers</strong>: In connection with a merger, acquisition, or sale of assets.</li><li><strong>With Your Consent</strong>: When you explicitly agree to share your information.</li></ul><h2 id=\"4-data-security\">4. Data Security</h2><p>We implement reasonable security measures to protect your information from unauthorized access, use, or disclosure. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.</p><h2 id=\"5-your-choices-and-rights\">5. Your Choices and Rights</h2><p>You may have the following rights regarding your information, subject to applicable laws:</p><ul><li><strong>Access</strong>: Request access to the personal information we hold about you.</li><li><strong>Correction</strong>: Request corrections to inaccurate or incomplete information.</li><li><strong>Deletion</strong>: Request deletion of your personal information.</li><li><strong>Opt-Out</strong>: Opt out of receiving promotional communications.</li><li><strong>Data Portability</strong>: Request a copy of your data in a structured, commonly used format.</li></ul><p>To exercise these rights, please contact us at Your Contact Email.</p><h2 id=\"6-third-party-links-and-services\">6. Third-Party Links and Services</h2><p>The App may contain links to third-party websites or services. We are not responsible for the privacy practices or content of these third parties. We encourage you to review their privacy policies.</p><h2 id=\"7-children%E2%80%99s-privacy\">7. Children’s Privacy</h2><p>The App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we learn that we have collected such information, we will take steps to delete it.</p><h2 id=\"8-international-data-transfers\">8. International Data Transfers</h2><p>Your information may be transferred to and processed in countries other than your own, where data protection laws may differ. We ensure appropriate safeguards are in place to protect your information.</p><h2 id=\"9-changes-to-this-privacy-policy\">9. Changes to This Privacy Policy</h2><p>We may update this Privacy Policy from time to time. We will notify you of any changes by posting the updated policy in the App or by other means. Your continued use of the App after such changes constitutes your acceptance of the new terms.</p><h2 id=\"10-contact-us\">10. Contact Us</h2><p>If you have any questions or concerns about this Privacy Policy, please contact us at:</p><ul><li><strong>Email</strong>: Your Contact Email</li><li><strong>Address</strong>: Your Company Address</li><li><strong>Phone</strong>: Your Contact Phone Number</li></ul><p>By using the App, you acknowledge that you have read and understood this Privacy Policy.</p>','6850e8d5db983000014c1cc8','Your Company Name (\"we,\" \"our,\" or \"us\") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use Your App Name (the \"App\"). Please read this policy carefully. If you do not agree with the terms of this Privacy Policy, please do not use the App.\n\n\n1. Information We Collect\n\nWe may collect the following types of information when you use the App:\n\n\na. Personal Information\n\n * Name: Your Name\n * Email Address: Your Email\n * Phone Number: Your Phone Number\n * Other Identifiers: Device ID, IP address, or other unique identifiers.\n\n\nb. Non-Personal Information\n\n * Usage Data: Information about how you interact with the App, such as features used, time spent, and navigation patterns.\n * Device Information: Details about your device, including operating system, device type, and app version.\n * Location Data: Approximate location based on IP address or precise location (if you grant permission).\n\n\nc. Information from Third Parties\n\nWe may collect information from third-party services (e.g., social media platforms or analytics providers) if you choose to connect them to the App.\n\n\n2. How We Use Your Information\n\nWe may use the information we collect for the following purposes:\n\n * To provide, maintain, and improve the App’s functionality.\n * To personalize your experience within the App.\n * To communicate with you, including sending updates, notifications, or promotional content (if you opt in).\n * To analyze usage trends and optimize App performance.\n * To comply with legal obligations or protect our rights.\n\n\n3. How We Share Your Information\n\nWe do not sell your personal information. We may share your information in the following cases:\n\n * Service Providers: With third-party vendors who perform services on our behalf, such as hosting, analytics, or customer support.\n * Legal Requirements: If required by law, regulation, or legal process.\n * Business Transfers: In connection with a merger, acquisition, or sale of assets.\n * With Your Consent: When you explicitly agree to share your information.\n\n\n4. Data Security\n\nWe implement reasonable security measures to protect your information from unauthorized access, use, or disclosure. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.\n\n\n5. Your Choices and Rights\n\nYou may have the following rights regarding your information, subject to applicable laws:\n\n * Access: Request access to the personal information we hold about you.\n * Correction: Request corrections to inaccurate or incomplete information.\n * Deletion: Request deletion of your personal information.\n * Opt-Out: Opt out of receiving promotional communications.\n * Data Portability: Request a copy of your data in a structured, commonly used format.\n\nTo exercise these rights, please contact us at Your Contact Email.\n\n\n6. Third-Party Links and Services\n\nThe App may contain links to third-party websites or services. We are not responsible for the privacy practices or content of these third parties. We encourage you to review their privacy policies.\n\n\n7. Children’s Privacy\n\nThe App is not intended for children under the age of 13. We do not knowingly collect personal information from children under 13. If we learn that we have collected such information, we will take steps to delete it.\n\n\n8. International Data Transfers\n\nYour information may be transferred to and processed in countries other than your own, where data protection laws may differ. We ensure appropriate safeguards are in place to protect your information.\n\n\n9. Changes to This Privacy Policy\n\nWe may update this Privacy Policy from time to time. We will notify you of any changes by posting the updated policy in the App or by other means. Your continued use of the App after such changes constitutes your acceptance of the new terms.\n\n\n10. Contact Us\n\nIf you have any questions or concerns about this Privacy Policy, please contact us at:\n\n * Email: Your Contact Email\n * Address: Your Company Address\n * Phone: Your Contact Phone Number\n\nBy using the App, you acknowledge that you have read and understood this Privacy Policy.',NULL,0,'page','published',NULL,'public','all','2025-06-17 04:02:29','1','2025-06-17 04:03:30','1','2025-06-17 04:02:49','1',NULL,NULL,NULL,NULL,NULL,NULL,1),('6850e8ffdb983000014c1cd3','05eb098b-f7eb-4db6-a32c-e999fb66c962','Terms and Conditions','terms-and-conditions-en',NULL,'{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Welcome to [App Name]! These Terms and Conditions (\\\"Terms\\\") govern your use of the [App Name] mobile application (the \\\"App\\\") provided by [Company Name], a company registered in [State/Country] with its principal office at [Company Address] (\\\"we,\\\" \\\"us,\\\" or \\\"our\\\"). By downloading, installing, or using the App, you agree to be bound by these Terms. If you do not agree, please do not use the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"1. Acceptance of Terms\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"By accessing or using the App, you confirm that you are at least [Minimum Age, e.g., 13] years old and have the legal capacity to enter into these Terms. You agree to comply with these Terms and any applicable laws.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"2. Changes to Terms\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may update these Terms from time to time. We will notify you of changes by posting the updated Terms in the App or via [Notification Method, e.g., email or in-app notification]. Your continued use of the App after such changes constitutes your acceptance of the new Terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3. Use of the App\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3.1 License\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We grant you a non-exclusive, non-transferable, revocable license to use the App for personal, non-commercial purposes, subject to these Terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"3.2 Restrictions\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You agree not to:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Modify, reverse-engineer, or decompile the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Use the App for illegal or unauthorized purposes.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Share your account credentials with others.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Use the App to harm or interfere with its functionality or other users.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":4}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"4. User Accounts\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To access certain features, you may need to create an account. You are responsible for:\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Providing accurate and complete information during registration.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Maintaining the confidentiality of your account credentials.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":2},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Notifying us immediately of any unauthorized use of your account at [Contact Email].\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"listitem\",\"version\":1,\"value\":3}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"list\",\"version\":1,\"listType\":\"bullet\",\"start\":1,\"tag\":\"ul\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5. Content\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5.1 User Content\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You may be able to submit content (e.g., text, images) through the App (\\\"User Content\\\"). You retain ownership of your User Content but grant us a worldwide, non-exclusive, royalty-free license to use, store, and display it as necessary to provide the App’s services.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"5.2 Prohibited Content\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You agree not to submit content that is unlawful, defamatory, obscene, or infringes on intellectual property rights.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"6. Intellectual Property\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"All content, trademarks, and materials in the App, excluding User Content, are owned by or licensed to [Company Name]. You may not use these materials without our prior written consent.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"7. Privacy\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Your use of the App is subject to our Privacy Policy, available at [Privacy Policy URL]. Please review it to understand how we collect, use, and protect your information.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"8. Payments and Subscriptions\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"Some features of the App may require payment or subscriptions. All payments are processed through [Payment Processor, e.g., Apple App Store, Google Play]. You agree to pay all applicable fees and taxes. Subscriptions may auto-renew unless canceled as described in [Cancellation Policy URL].\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"9. Termination\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"We may suspend or terminate your access to the App if you violate these Terms or for any other reason at our discretion. Upon termination, your right to use the App will cease immediately.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10. Disclaimers and Limitation of Liability\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10.1 Disclaimers\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"The App is provided \\\"as is\\\" without warranties of any kind, express or implied. We do not guarantee that the App will be error-free or uninterrupted.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"10.2 Limitation of Liability\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h3\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"To the fullest extent permitted by law, [Company Name] shall not be liable for any indirect, incidental, or consequential damages arising from your use of the App.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"11. Indemnification\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"You agree to indemnify and hold [Company Name], its affiliates, and employees harmless from any claims, losses, or damages arising from your use of the App or violation of these Terms.\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"12. Governing Law\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"extended-heading\",\"version\":1,\"tag\":\"h2\"},{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"These Terms are governed by the laws of [Governing Jurisdiction].\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}','<p>Welcome to [App Name]! These Terms and Conditions (\"Terms\") govern your use of the [App Name] mobile application (the \"App\") provided by [Company Name], a company registered in [State/Country] with its principal office at [Company Address] (\"we,\" \"us,\" or \"our\"). By downloading, installing, or using the App, you agree to be bound by these Terms. If you do not agree, please do not use the App.</p><h2 id=\"1-acceptance-of-terms\">1. Acceptance of Terms</h2><p>By accessing or using the App, you confirm that you are at least [Minimum Age, e.g., 13] years old and have the legal capacity to enter into these Terms. You agree to comply with these Terms and any applicable laws.</p><h2 id=\"2-changes-to-terms\">2. Changes to Terms</h2><p>We may update these Terms from time to time. We will notify you of changes by posting the updated Terms in the App or via [Notification Method, e.g., email or in-app notification]. Your continued use of the App after such changes constitutes your acceptance of the new Terms.</p><h2 id=\"3-use-of-the-app\">3. Use of the App</h2><h3 id=\"31-license\">3.1 License</h3><p>We grant you a non-exclusive, non-transferable, revocable license to use the App for personal, non-commercial purposes, subject to these Terms.</p><h3 id=\"32-restrictions\">3.2 Restrictions</h3><p>You agree not to:</p><ul><li>Modify, reverse-engineer, or decompile the App.</li><li>Use the App for illegal or unauthorized purposes.</li><li>Share your account credentials with others.</li><li>Use the App to harm or interfere with its functionality or other users.</li></ul><h2 id=\"4-user-accounts\">4. User Accounts</h2><p>To access certain features, you may need to create an account. You are responsible for:</p><ul><li>Providing accurate and complete information during registration.</li><li>Maintaining the confidentiality of your account credentials.</li><li>Notifying us immediately of any unauthorized use of your account at [Contact Email].</li></ul><h2 id=\"5-content\">5. Content</h2><h3 id=\"51-user-content\">5.1 User Content</h3><p>You may be able to submit content (e.g., text, images) through the App (\"User Content\"). You retain ownership of your User Content but grant us a worldwide, non-exclusive, royalty-free license to use, store, and display it as necessary to provide the App’s services.</p><h3 id=\"52-prohibited-content\">5.2 Prohibited Content</h3><p>You agree not to submit content that is unlawful, defamatory, obscene, or infringes on intellectual property rights.</p><h2 id=\"6-intellectual-property\">6. Intellectual Property</h2><p>All content, trademarks, and materials in the App, excluding User Content, are owned by or licensed to [Company Name]. You may not use these materials without our prior written consent.</p><h2 id=\"7-privacy\">7. Privacy</h2><p>Your use of the App is subject to our Privacy Policy, available at [Privacy Policy URL]. Please review it to understand how we collect, use, and protect your information.</p><h2 id=\"8-payments-and-subscriptions\">8. Payments and Subscriptions</h2><p>Some features of the App may require payment or subscriptions. All payments are processed through [Payment Processor, e.g., Apple App Store, Google Play]. You agree to pay all applicable fees and taxes. Subscriptions may auto-renew unless canceled as described in [Cancellation Policy URL].</p><h2 id=\"9-termination\">9. Termination</h2><p>We may suspend or terminate your access to the App if you violate these Terms or for any other reason at our discretion. Upon termination, your right to use the App will cease immediately.</p><h2 id=\"10-disclaimers-and-limitation-of-liability\">10. Disclaimers and Limitation of Liability</h2><h3 id=\"101-disclaimers\">10.1 Disclaimers</h3><p>The App is provided \"as is\" without warranties of any kind, express or implied. We do not guarantee that the App will be error-free or uninterrupted.</p><h3 id=\"102-limitation-of-liability\">10.2 Limitation of Liability</h3><p>To the fullest extent permitted by law, [Company Name] shall not be liable for any indirect, incidental, or consequential damages arising from your use of the App.</p><h2 id=\"11-indemnification\">11. Indemnification</h2><p>You agree to indemnify and hold [Company Name], its affiliates, and employees harmless from any claims, losses, or damages arising from your use of the App or violation of these Terms.</p><h2 id=\"12-governing-law\">12. Governing Law</h2><p>These Terms are governed by the laws of [Governing Jurisdiction].</p>','6850e8ffdb983000014c1cd3','Welcome to [App Name]! These Terms and Conditions (\"Terms\") govern your use of the [App Name] mobile application (the \"App\") provided by [Company Name], a company registered in [State/Country] with its principal office at [Company Address] (\"we,\" \"us,\" or \"our\"). By downloading, installing, or using the App, you agree to be bound by these Terms. If you do not agree, please do not use the App.\n\n\n1. Acceptance of Terms\n\nBy accessing or using the App, you confirm that you are at least [Minimum Age, e.g., 13] years old and have the legal capacity to enter into these Terms. You agree to comply with these Terms and any applicable laws.\n\n\n2. Changes to Terms\n\nWe may update these Terms from time to time. We will notify you of changes by posting the updated Terms in the App or via [Notification Method, e.g., email or in-app notification]. Your continued use of the App after such changes constitutes your acceptance of the new Terms.\n\n\n3. Use of the App\n\n\n3.1 License\n\nWe grant you a non-exclusive, non-transferable, revocable license to use the App for personal, non-commercial purposes, subject to these Terms.\n\n\n3.2 Restrictions\n\nYou agree not to:\n\n * Modify, reverse-engineer, or decompile the App.\n * Use the App for illegal or unauthorized purposes.\n * Share your account credentials with others.\n * Use the App to harm or interfere with its functionality or other users.\n\n\n4. User Accounts\n\nTo access certain features, you may need to create an account. You are responsible for:\n\n * Providing accurate and complete information during registration.\n * Maintaining the confidentiality of your account credentials.\n * Notifying us immediately of any unauthorized use of your account at [Contact Email].\n\n\n5. Content\n\n\n5.1 User Content\n\nYou may be able to submit content (e.g., text, images) through the App (\"User Content\"). You retain ownership of your User Content but grant us a worldwide, non-exclusive, royalty-free license to use, store, and display it as necessary to provide the App’s services.\n\n\n5.2 Prohibited Content\n\nYou agree not to submit content that is unlawful, defamatory, obscene, or infringes on intellectual property rights.\n\n\n6. Intellectual Property\n\nAll content, trademarks, and materials in the App, excluding User Content, are owned by or licensed to [Company Name]. You may not use these materials without our prior written consent.\n\n\n7. Privacy\n\nYour use of the App is subject to our Privacy Policy, available at [Privacy Policy URL]. Please review it to understand how we collect, use, and protect your information.\n\n\n8. Payments and Subscriptions\n\nSome features of the App may require payment or subscriptions. All payments are processed through [Payment Processor, e.g., Apple App Store, Google Play]. You agree to pay all applicable fees and taxes. Subscriptions may auto-renew unless canceled as described in [Cancellation Policy URL].\n\n\n9. Termination\n\nWe may suspend or terminate your access to the App if you violate these Terms or for any other reason at our discretion. Upon termination, your right to use the App will cease immediately.\n\n\n10. Disclaimers and Limitation of Liability\n\n\n10.1 Disclaimers\n\nThe App is provided \"as is\" without warranties of any kind, express or implied. We do not guarantee that the App will be error-free or uninterrupted.\n\n\n10.2 Limitation of Liability\n\nTo the fullest extent permitted by law, [Company Name] shall not be liable for any indirect, incidental, or consequential damages arising from your use of the App.\n\n\n11. Indemnification\n\nYou agree to indemnify and hold [Company Name], its affiliates, and employees harmless from any claims, losses, or damages arising from your use of the App or violation of these Terms.\n\n\n12. Governing Law\n\nThese Terms are governed by the laws of [Governing Jurisdiction].',NULL,0,'page','published',NULL,'public','all','2025-06-17 04:03:11','1','2025-06-17 04:03:23','1','2025-06-17 04:03:23','1',NULL,NULL,NULL,NULL,NULL,NULL,1),('68525eb6db983000014c1cf2','e52370fa-9567-4824-896a-7a682d38f838','Rules','rules-en',NULL,'{\"root\":{\"children\":[{\"children\":[{\"detail\":0,\"format\":0,\"mode\":\"normal\",\"style\":\"\",\"text\":\"TODO: Application Rules English\",\"type\":\"extended-text\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"paragraph\",\"version\":1}],\"direction\":\"ltr\",\"format\":\"\",\"indent\":0,\"type\":\"root\",\"version\":1}}','<p>TODO: Application Rules English</p>','68525eb6db983000014c1cf2','TODO: Application Rules English',NULL,0,'page','published',NULL,'public','all','2025-06-18 06:37:42','1','2025-06-18 06:38:00','1','2025-06-18 06:38:00','1',NULL,NULL,NULL,NULL,NULL,NULL,1);
/*!40000 ALTER TABLE `posts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `posts_authors`
--

DROP TABLE IF EXISTS `posts_authors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `posts_authors` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `author_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `posts_authors_post_id_foreign` (`post_id`),
  KEY `posts_authors_author_id_foreign` (`author_id`),
  CONSTRAINT `posts_authors_author_id_foreign` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`),
  CONSTRAINT `posts_authors_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `posts_authors`
--

LOCK TABLES `posts_authors` WRITE;
/*!40000 ALTER TABLE `posts_authors` DISABLE KEYS */;
INSERT INTO `posts_authors` VALUES ('6850cc589cac51000106f9b7','6850cc589cac51000106f9b6','1',0),('6850e726db983000014c1cb9','6850e726db983000014c1cb8','1',0),('6850e8d5db983000014c1cc9','6850e8d5db983000014c1cc8','1',0),('6850e8ffdb983000014c1cd4','6850e8ffdb983000014c1cd3','1',0),('68525eb6db983000014c1cf3','68525eb6db983000014c1cf2','1',0);
/*!40000 ALTER TABLE `posts_authors` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `posts_meta`
--

DROP TABLE IF EXISTS `posts_meta`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `posts_meta` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `og_image` varchar(2000) DEFAULT NULL,
  `og_title` varchar(300) DEFAULT NULL,
  `og_description` varchar(500) DEFAULT NULL,
  `twitter_image` varchar(2000) DEFAULT NULL,
  `twitter_title` varchar(300) DEFAULT NULL,
  `twitter_description` varchar(500) DEFAULT NULL,
  `meta_title` varchar(2000) DEFAULT NULL,
  `meta_description` varchar(2000) DEFAULT NULL,
  `email_subject` varchar(300) DEFAULT NULL,
  `frontmatter` text,
  `feature_image_alt` varchar(191) DEFAULT NULL,
  `feature_image_caption` text,
  `email_only` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `posts_meta_post_id_unique` (`post_id`),
  CONSTRAINT `posts_meta_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `posts_meta`
--

LOCK TABLES `posts_meta` WRITE;
/*!40000 ALTER TABLE `posts_meta` DISABLE KEYS */;
/*!40000 ALTER TABLE `posts_meta` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `posts_products`
--

DROP TABLE IF EXISTS `posts_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `posts_products` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `product_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `posts_products_post_id_foreign` (`post_id`),
  KEY `posts_products_product_id_foreign` (`product_id`),
  CONSTRAINT `posts_products_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `posts_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `posts_products`
--

LOCK TABLES `posts_products` WRITE;
/*!40000 ALTER TABLE `posts_products` DISABLE KEYS */;
INSERT INTO `posts_products` VALUES ('6850e74bdb983000014c1cbc','6850e726db983000014c1cb8','6850cc579cac51000106f939',0),('6850e8aedb983000014c1cc6','6850cc589cac51000106f9b6','6850cc579cac51000106f939',0),('6850e8dfdb983000014c1ccc','6850e8d5db983000014c1cc8','6850cc579cac51000106f939',0),('6850e902db983000014c1cd7','6850e8ffdb983000014c1cd3','6850cc579cac51000106f939',0),('68525ec1db983000014c1cf6','68525eb6db983000014c1cf2','6850cc579cac51000106f939',0);
/*!40000 ALTER TABLE `posts_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `posts_tags`
--

DROP TABLE IF EXISTS `posts_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `posts_tags` (
  `id` varchar(24) NOT NULL,
  `post_id` varchar(24) NOT NULL,
  `tag_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `posts_tags_tag_id_foreign` (`tag_id`),
  KEY `posts_tags_post_id_tag_id_index` (`post_id`,`tag_id`),
  CONSTRAINT `posts_tags_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`),
  CONSTRAINT `posts_tags_tag_id_foreign` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `posts_tags`
--

LOCK TABLES `posts_tags` WRITE;
/*!40000 ALTER TABLE `posts_tags` DISABLE KEYS */;
/*!40000 ALTER TABLE `posts_tags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `welcome_page_url` varchar(2000) DEFAULT NULL,
  `visibility` varchar(50) NOT NULL DEFAULT 'none',
  `trial_days` int unsigned NOT NULL DEFAULT '0',
  `description` varchar(191) DEFAULT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'paid',
  `currency` varchar(50) DEFAULT NULL,
  `monthly_price` int unsigned DEFAULT NULL,
  `yearly_price` int unsigned DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `monthly_price_id` varchar(24) DEFAULT NULL,
  `yearly_price_id` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `products_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES ('6850cc579cac51000106f938','Free','free',1,NULL,'public',0,NULL,'free',NULL,NULL,NULL,'2025-06-17 02:00:55','2025-06-17 02:00:55',NULL,NULL),('6850cc579cac51000106f939','Open Portal Expo Media','default-product',1,NULL,'public',0,NULL,'paid','USD',500,5000,'2025-06-17 02:00:55','2025-06-17 02:17:24',NULL,NULL);
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products_benefits`
--

DROP TABLE IF EXISTS `products_benefits`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `products_benefits` (
  `id` varchar(24) NOT NULL,
  `product_id` varchar(24) NOT NULL,
  `benefit_id` varchar(24) NOT NULL,
  `sort_order` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `products_benefits_product_id_foreign` (`product_id`),
  KEY `products_benefits_benefit_id_foreign` (`benefit_id`),
  CONSTRAINT `products_benefits_benefit_id_foreign` FOREIGN KEY (`benefit_id`) REFERENCES `benefits` (`id`) ON DELETE CASCADE,
  CONSTRAINT `products_benefits_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products_benefits`
--

LOCK TABLES `products_benefits` WRITE;
/*!40000 ALTER TABLE `products_benefits` DISABLE KEYS */;
/*!40000 ALTER TABLE `products_benefits` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `recommendation_click_events`
--

DROP TABLE IF EXISTS `recommendation_click_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recommendation_click_events` (
  `id` varchar(24) NOT NULL,
  `recommendation_id` varchar(24) NOT NULL,
  `member_id` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `recommendation_click_events_recommendation_id_foreign` (`recommendation_id`),
  KEY `recommendation_click_events_member_id_foreign` (`member_id`),
  CONSTRAINT `recommendation_click_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL,
  CONSTRAINT `recommendation_click_events_recommendation_id_foreign` FOREIGN KEY (`recommendation_id`) REFERENCES `recommendations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `recommendation_click_events`
--

LOCK TABLES `recommendation_click_events` WRITE;
/*!40000 ALTER TABLE `recommendation_click_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `recommendation_click_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `recommendation_subscribe_events`
--

DROP TABLE IF EXISTS `recommendation_subscribe_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recommendation_subscribe_events` (
  `id` varchar(24) NOT NULL,
  `recommendation_id` varchar(24) NOT NULL,
  `member_id` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `recommendation_subscribe_events_recommendation_id_foreign` (`recommendation_id`),
  KEY `recommendation_subscribe_events_member_id_foreign` (`member_id`),
  CONSTRAINT `recommendation_subscribe_events_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE SET NULL,
  CONSTRAINT `recommendation_subscribe_events_recommendation_id_foreign` FOREIGN KEY (`recommendation_id`) REFERENCES `recommendations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `recommendation_subscribe_events`
--

LOCK TABLES `recommendation_subscribe_events` WRITE;
/*!40000 ALTER TABLE `recommendation_subscribe_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `recommendation_subscribe_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `recommendations`
--

DROP TABLE IF EXISTS `recommendations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `recommendations` (
  `id` varchar(24) NOT NULL,
  `url` varchar(2000) NOT NULL,
  `title` varchar(2000) NOT NULL,
  `excerpt` varchar(2000) DEFAULT NULL,
  `featured_image` varchar(2000) DEFAULT NULL,
  `favicon` varchar(2000) DEFAULT NULL,
  `description` varchar(2000) DEFAULT NULL,
  `one_click_subscribe` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `recommendations`
--

LOCK TABLES `recommendations` WRITE;
/*!40000 ALTER TABLE `recommendations` DISABLE KEYS */;
/*!40000 ALTER TABLE `recommendations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `redirects`
--

DROP TABLE IF EXISTS `redirects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `redirects` (
  `id` varchar(24) NOT NULL,
  `from` varchar(191) NOT NULL,
  `to` varchar(2000) NOT NULL,
  `post_id` varchar(24) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `redirects_from_index` (`from`),
  KEY `redirects_post_id_foreign` (`post_id`),
  CONSTRAINT `redirects_post_id_foreign` FOREIGN KEY (`post_id`) REFERENCES `posts` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `redirects`
--

LOCK TABLES `redirects` WRITE;
/*!40000 ALTER TABLE `redirects` DISABLE KEYS */;
/*!40000 ALTER TABLE `redirects` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` varchar(24) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` varchar(2000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES ('6850cc569cac51000106f92a','Administrator','Administrators','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f92b','Editor','Editors','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f92c','Author','Authors','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f92d','Contributor','Contributors','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f92e','Owner','Blog Owner','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f92f','Admin Integration','External Apps','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f930','Ghost Explore Integration','Internal Integration for the Ghost Explore directory','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f931','Self-Serve Migration Integration','Internal Integration for the Self-Serve migration tool','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f932','DB Backup Integration','Internal DB Backup Client','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f933','Scheduler Integration','Internal Scheduler Client','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1'),('6850cc569cac51000106f934','Super Editor','Super Editors','2025-06-17 02:00:54','1','2025-06-17 02:00:54','1');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles_users`
--

DROP TABLE IF EXISTS `roles_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles_users` (
  `id` varchar(24) NOT NULL,
  `role_id` varchar(24) NOT NULL,
  `user_id` varchar(24) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles_users`
--

LOCK TABLES `roles_users` WRITE;
/*!40000 ALTER TABLE `roles_users` DISABLE KEYS */;
INSERT INTO `roles_users` VALUES ('6850cc579cac51000106f935','6850cc569cac51000106f92e','1');
/*!40000 ALTER TABLE `roles_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(24) NOT NULL,
  `session_id` varchar(32) NOT NULL,
  `user_id` varchar(24) NOT NULL,
  `session_data` varchar(2000) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sessions_session_id_unique` (`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES ('6850d036db983000014c1c75','IkgqDNihCL-C4jl_xIJXuktlTDSQhdmf','1','{\"cookie\":{\"originalMaxAge\":15552000000,\"expires\":\"2025-12-14T04:50:58.023Z\",\"secure\":true,\"httpOnly\":true,\"path\":\"/ghost\",\"sameSite\":\"none\"},\"origin\":\"https://ope-ghost-stg.zafar.dev\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"ip\":\"2405:6580:22e0:3300:a1b7:b79d:669a:5ac1\",\"verified\":true,\"user_id\":\"1\"}','2025-06-17 02:17:26','2025-06-17 04:50:58'),('6850f472db983000014c1cf0','B_-0N8izPtjpKKejxRS2MAOzGjjCj1-X','1','{\"cookie\":{\"originalMaxAge\":15552000000,\"expires\":\"2025-12-14T04:52:02.462Z\",\"secure\":true,\"httpOnly\":true,\"path\":\"/ghost\",\"sameSite\":\"none\"},\"user_id\":\"1\",\"origin\":\"https://ope-ghost-stg.zafar.dev\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"ip\":\"2400:2410:3261:de00:6b:9392:4f37:2cec\"}','2025-06-17 04:52:02','2025-06-17 04:52:02'),('685109d8db983000014c1cf1','dm7xeKL59hiL78HJUSWZu4lp5D4tQ4gc','1','{\"cookie\":{\"originalMaxAge\":15552000000,\"expires\":\"2025-12-14T06:23:20.805Z\",\"secure\":true,\"httpOnly\":true,\"path\":\"/ghost\",\"sameSite\":\"none\"},\"user_id\":\"1\",\"origin\":\"https://ope-ghost-stg.zafar.dev\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\",\"ip\":\"***************\"}','2025-06-17 06:23:21','2025-06-17 06:23:21');
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `id` varchar(24) NOT NULL,
  `group` varchar(50) NOT NULL DEFAULT 'core',
  `key` varchar(50) NOT NULL,
  `value` text,
  `type` varchar(50) NOT NULL,
  `flags` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `settings_key_unique` (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `snippets`
--

DROP TABLE IF EXISTS `snippets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `snippets` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `mobiledoc` longtext NOT NULL,
  `lexical` longtext,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `snippets_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `snippets`
--

LOCK TABLES `snippets` WRITE;
/*!40000 ALTER TABLE `snippets` DISABLE KEYS */;
/*!40000 ALTER TABLE `snippets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stripe_prices`
--

DROP TABLE IF EXISTS `stripe_prices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stripe_prices` (
  `id` varchar(24) NOT NULL,
  `stripe_price_id` varchar(255) NOT NULL,
  `stripe_product_id` varchar(255) NOT NULL,
  `active` tinyint(1) NOT NULL,
  `nickname` varchar(255) DEFAULT NULL,
  `currency` varchar(191) NOT NULL,
  `amount` int NOT NULL,
  `type` varchar(50) NOT NULL DEFAULT 'recurring',
  `interval` varchar(50) DEFAULT NULL,
  `description` varchar(191) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stripe_prices_stripe_price_id_unique` (`stripe_price_id`),
  KEY `stripe_prices_stripe_product_id_foreign` (`stripe_product_id`),
  CONSTRAINT `stripe_prices_stripe_product_id_foreign` FOREIGN KEY (`stripe_product_id`) REFERENCES `stripe_products` (`stripe_product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stripe_prices`
--

LOCK TABLES `stripe_prices` WRITE;
/*!40000 ALTER TABLE `stripe_prices` DISABLE KEYS */;
/*!40000 ALTER TABLE `stripe_prices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stripe_products`
--

DROP TABLE IF EXISTS `stripe_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `stripe_products` (
  `id` varchar(24) NOT NULL,
  `product_id` varchar(24) DEFAULT NULL,
  `stripe_product_id` varchar(255) NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stripe_products_stripe_product_id_unique` (`stripe_product_id`),
  KEY `stripe_products_product_id_foreign` (`product_id`),
  CONSTRAINT `stripe_products_product_id_foreign` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stripe_products`
--

LOCK TABLES `stripe_products` WRITE;
/*!40000 ALTER TABLE `stripe_products` DISABLE KEYS */;
/*!40000 ALTER TABLE `stripe_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `subscriptions`
--

DROP TABLE IF EXISTS `subscriptions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `subscriptions` (
  `id` varchar(24) NOT NULL,
  `type` varchar(50) NOT NULL,
  `status` varchar(50) NOT NULL,
  `member_id` varchar(24) NOT NULL,
  `tier_id` varchar(24) NOT NULL,
  `cadence` varchar(50) DEFAULT NULL,
  `currency` varchar(50) DEFAULT NULL,
  `amount` int DEFAULT NULL,
  `payment_provider` varchar(50) DEFAULT NULL,
  `payment_subscription_url` varchar(2000) DEFAULT NULL,
  `payment_user_url` varchar(2000) DEFAULT NULL,
  `offer_id` varchar(24) DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `subscriptions_member_id_foreign` (`member_id`),
  KEY `subscriptions_tier_id_foreign` (`tier_id`),
  KEY `subscriptions_offer_id_foreign` (`offer_id`),
  CONSTRAINT `subscriptions_member_id_foreign` FOREIGN KEY (`member_id`) REFERENCES `members` (`id`) ON DELETE CASCADE,
  CONSTRAINT `subscriptions_offer_id_foreign` FOREIGN KEY (`offer_id`) REFERENCES `offers` (`id`),
  CONSTRAINT `subscriptions_tier_id_foreign` FOREIGN KEY (`tier_id`) REFERENCES `products` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `subscriptions`
--

LOCK TABLES `subscriptions` WRITE;
/*!40000 ALTER TABLE `subscriptions` DISABLE KEYS */;
/*!40000 ALTER TABLE `subscriptions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `suppressions`
--

DROP TABLE IF EXISTS `suppressions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `suppressions` (
  `id` varchar(24) NOT NULL,
  `email` varchar(191) NOT NULL,
  `email_id` varchar(24) DEFAULT NULL,
  `reason` varchar(50) NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `suppressions_email_unique` (`email`),
  KEY `suppressions_email_id_foreign` (`email_id`),
  CONSTRAINT `suppressions_email_id_foreign` FOREIGN KEY (`email_id`) REFERENCES `emails` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `suppressions`
--

LOCK TABLES `suppressions` WRITE;
/*!40000 ALTER TABLE `suppressions` DISABLE KEYS */;
/*!40000 ALTER TABLE `suppressions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tags`
--

DROP TABLE IF EXISTS `tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tags` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `description` text,
  `feature_image` varchar(2000) DEFAULT NULL,
  `parent_id` varchar(191) DEFAULT NULL,
  `visibility` varchar(50) NOT NULL DEFAULT 'public',
  `og_image` varchar(2000) DEFAULT NULL,
  `og_title` varchar(300) DEFAULT NULL,
  `og_description` varchar(500) DEFAULT NULL,
  `twitter_image` varchar(2000) DEFAULT NULL,
  `twitter_title` varchar(300) DEFAULT NULL,
  `twitter_description` varchar(500) DEFAULT NULL,
  `meta_title` varchar(2000) DEFAULT NULL,
  `meta_description` varchar(2000) DEFAULT NULL,
  `codeinjection_head` text,
  `codeinjection_foot` text,
  `canonical_url` varchar(2000) DEFAULT NULL,
  `accent_color` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tags_slug_unique` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tags`
--

LOCK TABLES `tags` WRITE;
/*!40000 ALTER TABLE `tags` DISABLE KEYS */;
INSERT INTO `tags` VALUES ('6850cc579cac51000106f93b','News','news',NULL,NULL,NULL,'public',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-17 02:00:55','1','2025-06-17 02:00:55','1'),('6850e662db983000014c1cac','#unlisted','hash-unlisted',NULL,NULL,NULL,'internal',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-06-17 03:52:02','1','2025-06-17 03:52:02','1');
/*!40000 ALTER TABLE `tags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `temp_mail_events`
--

DROP TABLE IF EXISTS `temp_mail_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `temp_mail_events` (
  `id` varchar(100) NOT NULL,
  `type` varchar(50) NOT NULL,
  `message_id` varchar(150) NOT NULL,
  `recipient` varchar(191) NOT NULL,
  `occurred_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `temp_mail_events`
--

LOCK TABLES `temp_mail_events` WRITE;
/*!40000 ALTER TABLE `temp_mail_events` DISABLE KEYS */;
/*!40000 ALTER TABLE `temp_mail_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tokens`
--

DROP TABLE IF EXISTS `tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tokens` (
  `id` varchar(24) NOT NULL,
  `token` varchar(32) NOT NULL,
  `data` varchar(2000) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `first_used_at` datetime DEFAULT NULL,
  `used_count` int unsigned NOT NULL DEFAULT '0',
  `created_by` varchar(24) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `tokens_token_index` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tokens`
--

LOCK TABLES `tokens` WRITE;
/*!40000 ALTER TABLE `tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` varchar(24) NOT NULL,
  `name` varchar(191) NOT NULL,
  `slug` varchar(191) NOT NULL,
  `password` varchar(60) NOT NULL,
  `email` varchar(191) NOT NULL,
  `profile_image` varchar(2000) DEFAULT NULL,
  `cover_image` varchar(2000) DEFAULT NULL,
  `bio` text,
  `website` varchar(2000) DEFAULT NULL,
  `location` text,
  `facebook` varchar(2000) DEFAULT NULL,
  `twitter` varchar(2000) DEFAULT NULL,
  `threads` varchar(191) DEFAULT NULL,
  `bluesky` varchar(191) DEFAULT NULL,
  `mastodon` varchar(191) DEFAULT NULL,
  `tiktok` varchar(191) DEFAULT NULL,
  `youtube` varchar(191) DEFAULT NULL,
  `instagram` varchar(191) DEFAULT NULL,
  `linkedin` varchar(191) DEFAULT NULL,
  `accessibility` text,
  `status` varchar(50) NOT NULL DEFAULT 'active',
  `locale` varchar(6) DEFAULT NULL,
  `visibility` varchar(50) NOT NULL DEFAULT 'public',
  `meta_title` varchar(2000) DEFAULT NULL,
  `meta_description` varchar(2000) DEFAULT NULL,
  `tour` text,
  `last_seen` datetime DEFAULT NULL,
  `comment_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `free_member_signup_notification` tinyint(1) NOT NULL DEFAULT '1',
  `paid_subscription_started_notification` tinyint(1) NOT NULL DEFAULT '1',
  `paid_subscription_canceled_notification` tinyint(1) NOT NULL DEFAULT '0',
  `mention_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `recommendation_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `milestone_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `donation_notifications` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_slug_unique` (`slug`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('1','Open Portal Expo','open','$2a$10$jLoruWLXeQK7Dh9q/mOxIefOsyByTc/vrgF9U3bGUc8/f76JUvTDO','<EMAIL>','__GHOST_URL__/content/images/2025/06/Not---------------------Default.png',NULL,NULL,'https://openportalexpo.com',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'{\"onboarding\":{\"completedSteps\":[\"customize-design\"],\"checklistState\":\"started\"},\"whatsNew\":{\"lastSeenDate\":\"2025-06-19T13:19:00.000+00:00\"},\"nightShift\":true}','active',NULL,'public',NULL,NULL,NULL,'2025-06-26 08:04:09',1,1,1,0,1,1,1,1,'2025-06-17 02:00:54','1','2025-06-26 08:04:20','1');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `webhooks`
--

DROP TABLE IF EXISTS `webhooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `webhooks` (
  `id` varchar(24) NOT NULL,
  `event` varchar(50) NOT NULL,
  `target_url` varchar(2000) NOT NULL,
  `name` varchar(191) DEFAULT NULL,
  `secret` varchar(191) DEFAULT NULL,
  `api_version` varchar(50) NOT NULL DEFAULT 'v2',
  `integration_id` varchar(24) NOT NULL,
  `last_triggered_at` datetime DEFAULT NULL,
  `last_triggered_status` varchar(50) DEFAULT NULL,
  `last_triggered_error` varchar(50) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `created_by` varchar(24) NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(24) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `webhooks_integration_id_foreign` (`integration_id`),
  CONSTRAINT `webhooks_integration_id_foreign` FOREIGN KEY (`integration_id`) REFERENCES `integrations` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `webhooks`
--

LOCK TABLES `webhooks` WRITE;
/*!40000 ALTER TABLE `webhooks` DISABLE KEYS */;
/*!40000 ALTER TABLE `webhooks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'open_portal_expo_ghost'
--
-- SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-30  9:53:11
