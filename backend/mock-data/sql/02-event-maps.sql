-- Event Maps Configuration
-- This script adds multiple map configurations for different events

USE open_portal_expo;

-- Insert maps for Open Portal Expo 2024 (Event ID 1)
INSERT INTO t_event_map (name, description, event_id, map_url, map_width, map_height, user_id, create_at, update_at) VALUES
('Main Exhibition Hall', 'Primary exhibition space featuring main booths, keynote stage, and central networking area', 1, 'https://picsum.photos/1920/1080?random=1', 1920, 1080, 1, NOW(), NOW());

-- Add additional maps for the main event (Event ID 1)
INSERT INTO t_event_map (name, description, event_id, map_url, map_width, map_height, user_id, create_at, update_at) VALUES
('Conference Center', 'Multi-purpose conference rooms and presentation spaces for workshops and seminars', 1, 'https://picsum.photos/1920/1080?random=2', 1920, 1080, 1, NOW(), NOW()),
('Food Court & Networking', 'Dining area with virtual food vendors and casual networking spaces', 1, 'https://picsum.photos/1920/1080?random=3', 1920, 1080, 1, NOW(), NOW()),
('Demo Zone', 'Interactive demonstration area for hands-on product experiences', 1, 'https://picsum.photos/1920/1080?random=4', 1920, 1080, 1, NOW(), NOW());

-- Add maps for other events
INSERT INTO t_event_map (name, description, event_id, map_url, map_width, map_height, user_id, create_at, update_at) VALUES
-- Tech Innovation Summit (Event ID 2)
('Innovation Hub', 'Central hub for tech demonstrations and innovation showcases', 2, 'https://picsum.photos/1920/1080?random=5', 1920, 1080, 1, NOW(), NOW()),
('Workshop Rooms', 'Dedicated spaces for hands-on technical workshops', 2, 'https://picsum.photos/1920/1080?random=6', 1920, 1080, 1, NOW(), NOW()),

-- Startup Showcase (Event ID 3)
('Pitch Arena', 'Main stage for startup pitches and investor presentations', 3, 'https://picsum.photos/1920/1080?random=7', 1920, 1080, 1, NOW(), NOW()),
('Startup Alley', 'Exhibition space for early-stage startups and entrepreneurs', 3, 'https://picsum.photos/1920/1080?random=8', 1920, 1080, 1, NOW(), NOW()),

-- Digital Marketing Expo (Event ID 4)
('Marketing Central', 'Main exhibition area for marketing tools and platforms', 4, 'https://picsum.photos/1920/1080?random=9', 1920, 1080, 1, NOW(), NOW()),
('Content Studio', 'Interactive content creation and social media spaces', 4, 'https://picsum.photos/1920/1080?random=10', 1920, 1080, 1, NOW(), NOW()),

-- Future of Work Conference (Event ID 5)
('Virtual Office', 'Demonstration of remote work tools and environments', 5, 'https://picsum.photos/1920/1080?random=11', 1920, 1080, 1, NOW(), NOW()),
('Collaboration Hub', 'Interactive spaces for team collaboration and networking', 5, 'https://picsum.photos/1920/1080?random=12', 1920, 1080, 1, NOW(), NOW());

-- Verify the data
SELECT em.id, em.name, em.description, e.name as event_name, em.map_width, em.map_height 
FROM t_event_map em 
JOIN t_event e ON em.event_id = e.id 
ORDER BY em.event_id, em.id;
