-- Upload File Information (File Management System)
-- This script creates upload file records for various media types used throughout the expo

USE open_portal_expo;

-- Upload files for various expo content
-- These represent files that have been uploaded to the system for use in events, exhibits, etc.

INSERT INTO t_upload_file (file_name, file_size, content_type, file_key, signed_url, media_type, upload_state, user_id, create_at, update_at) VALUES
-- Event promotional images (media_type: 2 = image)
('expo_2024_banner.jpg', 2048576, 'image/jpeg', 'expo2024_banner_001', 'https://cdn.example.com/expo2024_banner_001.jpg', 2, 1, 1, NOW(), NOW()),
('keynote_speakers_collage.png', 1536000, 'image/png', 'speakers_collage_002', 'https://cdn.example.com/speakers_collage_002.png', 2, 1, 1, NOW(), NOW()),
('expo_floor_plan.pdf', 5242880, 'application/pdf', 'floor_plan_003', 'https://cdn.example.com/floor_plan_003.pdf', 3, 1, 1, NOW(), NOW()),
('welcome_video_intro.mp4', 52428800, 'video/mp4', 'welcome_intro_004', 'https://cdn.example.com/welcome_intro_004.mp4', 1, 1, 1, NOW(), NOW()),

-- Exhibitor content files
('microsoft_booth_tour.mp4', 41943040, 'video/mp4', 'ms_booth_tour_005', 'https://cdn.example.com/ms_booth_tour_005.mp4', 1, 1, 1, NOW(), NOW()),
('google_cloud_demo.mp4', 38654976, 'video/mp4', 'gc_demo_006', 'https://cdn.example.com/gc_demo_006.mp4', 1, 1, 1, NOW(), NOW()),
('aws_architecture_diagram.png', 1024000, 'image/png', 'aws_arch_007', 'https://cdn.example.com/aws_arch_007.png', 2, 1, 1, NOW(), NOW()),
('meta_vr_experience.mp4', 67108864, 'video/mp4', 'meta_vr_008', 'https://cdn.example.com/meta_vr_008.mp4', 1, 1, 1, NOW(), NOW()),
('nvidia_ai_showcase.jpg', 1843200, 'image/jpeg', 'nvidia_ai_009', 'https://cdn.example.com/nvidia_ai_009.jpg', 2, 1, 1, NOW(), NOW()),

-- Workshop and presentation materials
('ai_fundamentals_slides.pdf', 8388608, 'application/pdf', 'ai_slides_010', 'https://cdn.example.com/ai_slides_010.pdf', 3, 1, 1, NOW(), NOW()),
('blockchain_workshop_code.zip', 2097152, 'application/zip', 'blockchain_code_011', 'https://cdn.example.com/blockchain_code_011.zip', 3, 1, 1, NOW(), NOW()),
('iot_demo_video.mp4', 45097156, 'video/mp4', 'iot_demo_012', 'https://cdn.example.com/iot_demo_012.mp4', 1, 1, 1, NOW(), NOW()),
('startup_pitch_template.pptx', 3145728, 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'pitch_template_013', 'https://cdn.example.com/pitch_template_013.pptx', 3, 1, 1, NOW(), NOW()),

-- Product and reward images
('expo_tshirt_mockup.jpg', 1024000, 'image/jpeg', 'tshirt_mockup_014', 'https://cdn.example.com/tshirt_mockup_014.jpg', 2, 1, 1, NOW(), NOW()),
('tech_mug_product_shot.png', 768000, 'image/png', 'mug_product_015', 'https://cdn.example.com/mug_product_015.png', 2, 1, 1, NOW(), NOW()),
('wireless_charger_360.jpg', 1536000, 'image/jpeg', 'charger_360_016', 'https://cdn.example.com/charger_360_016.jpg', 2, 1, 1, NOW(), NOW()),
('vip_pass_design.png', 512000, 'image/png', 'vip_pass_017', 'https://cdn.example.com/vip_pass_017.png', 2, 1, 1, NOW(), NOW()),

-- Event documentation and reports
('day1_highlights_video.mp4', 73400320, 'video/mp4', 'day1_highlights_018', 'https://cdn.example.com/day1_highlights_018.mp4', 1, 1, 1, NOW(), NOW()),
('networking_photos_gallery.zip', 15728640, 'application/zip', 'networking_gallery_019', 'https://cdn.example.com/networking_gallery_019.zip', 3, 1, 1, NOW(), NOW()),
('keynote_recording_full.mp4', 157286400, 'video/mp4', 'keynote_full_020', 'https://cdn.example.com/keynote_full_020.mp4', 1, 1, 1, NOW(), NOW()),
('expo_statistics_report.pdf', 4194304, 'application/pdf', 'stats_report_021', 'https://cdn.example.com/stats_report_021.pdf', 3, 1, 1, NOW(), NOW()),

-- User-generated content examples
('attendee_selfie_001.jpg', 2048000, 'image/jpeg', 'selfie_001_022', 'https://cdn.example.com/selfie_001_022.jpg', 2, 1, 1, NOW(), NOW()),
('booth_visit_video.mp4', 20971520, 'video/mp4', 'booth_visit_023', 'https://cdn.example.com/booth_visit_023.mp4', 1, 1, 1, NOW(), NOW()),
('workshop_notes.pdf', 1048576, 'application/pdf', 'workshop_notes_024', 'https://cdn.example.com/workshop_notes_024.pdf', 3, 1, 1, NOW(), NOW()),
('networking_card_scan.jpg', 512000, 'image/jpeg', 'card_scan_025', 'https://cdn.example.com/card_scan_025.jpg', 2, 1, 1, NOW(), NOW()),

-- Marketing and promotional content
('social_media_kit.zip', 10485760, 'application/zip', 'social_kit_026', 'https://cdn.example.com/social_kit_026.zip', 3, 1, 1, NOW(), NOW()),
('influencer_content_pack.zip', 26214400, 'application/zip', 'influencer_pack_027', 'https://cdn.example.com/influencer_pack_027.zip', 3, 1, 1, NOW(), NOW()),
('press_release_images.zip', 8388608, 'application/zip', 'press_images_028', 'https://cdn.example.com/press_images_028.zip', 3, 1, 1, NOW(), NOW()),
('expo_logo_variations.ai', 2097152, 'application/postscript', 'logo_variations_029', 'https://cdn.example.com/logo_variations_029.ai', 3, 1, 1, NOW(), NOW()),

-- Technical documentation
('api_documentation.pdf', 6291456, 'application/pdf', 'api_docs_030', 'https://cdn.example.com/api_docs_030.pdf', 3, 1, 1, NOW(), NOW()),
('mobile_app_screenshots.zip', 12582912, 'application/zip', 'app_screenshots_031', 'https://cdn.example.com/app_screenshots_031.zip', 3, 1, 1, NOW(), NOW()),
('system_architecture.png', 2048000, 'image/png', 'sys_arch_032', 'https://cdn.example.com/sys_arch_032.png', 2, 1, 1, NOW(), NOW()),
('database_schema.pdf', 3145728, 'application/pdf', 'db_schema_033', 'https://cdn.example.com/db_schema_033.pdf', 3, 1, 1, NOW(), NOW()),

-- Audio content
('keynote_audio_only.mp3', 31457280, 'audio/mpeg', 'keynote_audio_034', 'https://cdn.example.com/keynote_audio_034.mp3', 3, 1, 1, NOW(), NOW()),
('panel_discussion_audio.wav', 52428800, 'audio/wav', 'panel_audio_035', 'https://cdn.example.com/panel_audio_035.wav', 3, 1, 1, NOW(), NOW()),
('expo_background_music.mp3', 15728640, 'audio/mpeg', 'bg_music_036', 'https://cdn.example.com/bg_music_036.mp3', 3, 1, 1, NOW(), NOW()),

-- Failed upload examples (upload_state: 98 = failed)
('large_video_failed.mp4', 524288000, 'video/mp4', 'failed_video_037', 'https://cdn.example.com/failed_video_037.mp4', 1, 98, 1, NOW(), NOW()),
('corrupted_image.jpg', 0, 'image/jpeg', 'corrupted_img_038', 'https://cdn.example.com/corrupted_img_038.jpg', 2, 98, 1, NOW(), NOW()),

-- Pending uploads (upload_state: 0 = initial)
('pending_presentation.pptx', 5242880, 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'pending_pres_039', 'https://cdn.example.com/pending_pres_039.pptx', 3, 0, 1, NOW(), NOW()),
('uploading_video.mp4', 41943040, 'video/mp4', 'uploading_vid_040', 'https://cdn.example.com/uploading_vid_040.mp4', 1, 0, 1, NOW(), NOW());

-- Verify the upload file data
SELECT 
    uf.id, 
    uf.file_name, 
    uf.file_size, 
    uf.content_type, 
    uf.media_type,
    CASE 
        WHEN uf.upload_state = 0 THEN 'Initial'
        WHEN uf.upload_state = 1 THEN 'Finished'
        WHEN uf.upload_state = 98 THEN 'Failed'
        WHEN uf.upload_state = 99 THEN 'Timeout'
        ELSE 'Unknown'
    END as status,
    CASE 
        WHEN uf.media_type = 1 THEN 'Video'
        WHEN uf.media_type = 2 THEN 'Image'
        WHEN uf.media_type = 3 THEN 'Other'
        ELSE 'Unknown'
    END as media_type_name
FROM t_upload_file uf 
ORDER BY uf.upload_state, uf.media_type, uf.id 
LIMIT 15;
