-- MySQL dump 10.13  Distrib 8.0.27, for macos11 (arm64)
--
-- Host: 127.0.0.1    Database: open_portal_expo
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `sys_login_log`
--

DROP TABLE IF EXISTS `sys_login_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_login_log` (
  `user_type` int DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `login_ip` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `login_result` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统登录日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_login_log`
--

LOCK TABLES `sys_login_log` WRITE;
/*!40000 ALTER TABLE `sys_login_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_login_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_perm`
--

DROP TABLE IF EXISTS `sys_perm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_perm` (
  `pval` varchar(255) DEFAULT NULL,
  `parent_id` int DEFAULT '0',
  `pname` varchar(255) DEFAULT NULL,
  `ranking` int DEFAULT NULL,
  `ptype` int DEFAULT NULL COMMENT '权限类型：1.菜单; 2.api; 3.按钮; 4.数据',
  `leaf` bit(1) DEFAULT b'1',
  `icon` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_pval` (`pval`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_perm`
--

LOCK TABLES `sys_perm` WRITE;
/*!40000 ALTER TABLE `sys_perm` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_perm` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `rname` varchar(255) DEFAULT NULL,
  `rdesc` varchar(255) DEFAULT NULL,
  `rval` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_rval` (`rval`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='权限角色';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_perm`
--

DROP TABLE IF EXISTS `sys_role_perm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_perm` (
  `role_id` int DEFAULT NULL,
  `perm_id` int DEFAULT NULL,
  `perm_type` int DEFAULT NULL COMMENT '权限类型：1.菜单；2.api 3.数据',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_perm`
--

LOCK TABLES `sys_role_perm` WRITE;
/*!40000 ALTER TABLE `sys_role_perm` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_perm` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `uname` varchar(255) DEFAULT NULL,
  `nick` varchar(255) DEFAULT NULL,
  `pwd` varchar(255) DEFAULT NULL,
  `salt` varchar(255) DEFAULT NULL,
  `is_lock` bit(1) DEFAULT b'0',
  `tel` varchar(255) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统用户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_role` (
  `role_id` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户角色';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_area`
--

DROP TABLE IF EXISTS `t_area`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_area` (
  `event_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `width` int DEFAULT NULL,
  `height` int DEFAULT NULL,
  `x_axis` int DEFAULT NULL,
  `y_axis` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_area';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_area`
--

LOCK TABLES `t_area` WRITE;
/*!40000 ALTER TABLE `t_area` DISABLE KEYS */;
INSERT INTO `t_area` VALUES (1,'Main Entrance',300,200,50,50,1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Tech Pavilion',400,300,400,100,2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Startup Corner',350,250,900,150,3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Keynote Stage',500,200,300,500,4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Networking Lounge',300,300,1200,400,5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Information Desk',150,100,100,300,6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Conference Room A',250,200,200,200,7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Conference Room B',250,200,500,200,8,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Conference Room C',250,200,800,200,9,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Workshop Space 1',300,250,200,450,10,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Workshop Space 2',300,250,550,450,11,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Food Court Central',400,300,400,300,12,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Coffee Corner',200,150,150,400,13,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Casual Seating',300,200,900,350,14,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VIP Lounge',250,200,1300,300,15,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Interactive Demos',350,300,300,250,16,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Product Showcase',400,250,750,300,17,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Gaming Zone',300,200,1200,250,18,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'AI & Machine Learning',400,300,300,200,19,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Blockchain Hub',350,250,800,200,20,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'IoT Showcase',300,300,1200,150,21,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Innovation Lab',450,200,400,500,22,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Tech Talks Stage',500,250,200,750,23,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Early Stage Startups',400,300,200,200,24,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Growth Stage',350,300,650,200,25,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Investor Lounge',300,200,1100,250,26,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Pitch Competition',500,300,400,550,27,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Mentorship Corner',250,200,1000,600,28,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Social Media Zone',350,300,300,200,29,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Content Marketing',400,250,750,250,30,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Analytics Corner',300,200,1200,300,31,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Influencer Hub',350,250,400,550,32,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Marketing Tools',400,300,800,500,33,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Work Tools',400,300,250,200,34,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Collaboration Space',350,250,700,200,35,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Digital Wellness',300,200,1100,250,36,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Future Office Demo',450,300,400,500,37,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'HR Tech Zone',350,250,900,550,38,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_area` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_bulletin`
--

DROP TABLE IF EXISTS `t_bulletin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_bulletin` (
  `event_id` bigint DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `brief` varchar(255) DEFAULT NULL,
  `page_url` varchar(255) DEFAULT NULL,
  `publish_at` datetime DEFAULT NULL,
  `published` bit(1) DEFAULT b'0',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_bulletin';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_bulletin`
--

LOCK TABLES `t_bulletin` WRITE;
/*!40000 ALTER TABLE `t_bulletin` DISABLE KEYS */;
INSERT INTO `t_bulletin` VALUES (1,'Welcome to Open Portal Expo 2024!','Join us for three days of cutting-edge technology, innovation, and networking opportunities.','https://expo.example.com/welcome','2024-06-10 09:00:00',_binary '',1,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Keynote Speaker Lineup Announced','Featuring CEOs from Microsoft, Google, AWS, and Meta discussing the future of technology.','https://expo.example.com/speakers','2024-06-12 14:00:00',_binary '',2,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Mobile App Now Available','Download the official Open Portal Expo app for interactive maps, schedules, and networking features.','https://expo.example.com/app','2024-06-13 10:00:00',_binary '',3,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Live Stream: AI Revolution Panel','Join the live discussion on AI ethics and implementation strategies. Streaming now on main stage.','https://expo.example.com/live-ai','2024-06-15 11:00:00',_binary '',4,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Startup Pitch Competition Results','Congratulations to our top 10 finalists! Final presentations begin at 2 PM in the main auditorium.','https://expo.example.com/pitch-results','2024-06-15 13:30:00',_binary '',5,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Networking Reception Tonight','Join us for cocktails and networking at the VIP Lounge from 6-8 PM. All attendees welcome!','https://expo.example.com/reception','2024-06-15 16:00:00',_binary '',6,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Workshop Materials Available','Download presentation slides and code samples from today\'s workshops in your attendee portal.','https://expo.example.com/materials','2024-06-16 09:00:00',_binary '',7,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Final Day Highlights','Don\'t miss the closing ceremony and awards presentation at 4 PM. Thank you for an amazing expo!','https://expo.example.com/closing','2024-06-17 12:00:00',_binary '',8,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(1,'Thank You for Attending!','Event recordings, presentation materials, and networking contacts are now available in your portal.','https://expo.example.com/thank-you','2024-06-18 10:00:00',_binary '',9,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(2,'AI Innovation Showcase Opens','Explore the latest breakthroughs in artificial intelligence and machine learning technologies.','https://summit.example.com/ai-showcase','2024-07-20 08:00:00',_binary '',10,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(2,'Blockchain Workshop Full','The hands-on blockchain development workshop has reached capacity. Waitlist available.','https://summit.example.com/blockchain-full','2024-07-20 10:30:00',_binary '',11,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(2,'IoT Demo Zone Now Open','Experience smart city technologies and industrial IoT solutions in our interactive demo area.','https://summit.example.com/iot-demo','2024-07-20 11:00:00',_binary '',12,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(2,'Innovation Awards Ceremony','Join us tonight for the recognition of outstanding technological innovations and breakthrough research.','https://summit.example.com/awards','2024-07-21 18:00:00',_binary '',13,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(3,'Investor Meetups Begin','One-on-one investor meetings are now underway. Check your schedule for confirmed appointments.','https://startup.example.com/investor-meetups','2024-09-10 09:00:00',_binary '',14,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(3,'Pitch Competition Guidelines','Final reminders for pitch presentations: 5 minutes + 3 minutes Q&A. Slides due by noon.','https://startup.example.com/pitch-guidelines','2024-09-10 10:00:00',_binary '',15,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(3,'Mentorship Sessions Available','Book your free 30-minute session with industry mentors. Limited slots remaining.','https://startup.example.com/mentorship','2024-09-11 14:00:00',_binary '',16,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(3,'Demo Day Success Stories','Celebrating our alumni: 15 startups have raised over $50M since participating in our showcase.','https://startup.example.com/success-stories','2024-09-12 16:00:00',_binary '',17,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(4,'Content Creator Masterclass','Learn from top influencers and content creators. Interactive workshop starts in Conference Room A.','https://marketing.example.com/creator-masterclass','2024-10-05 10:00:00',_binary '',18,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(4,'Social Media Trends Report','Download our exclusive 2024 social media trends report, featuring insights from 1000+ marketers.','https://marketing.example.com/trends-report','2024-10-05 13:00:00',_binary '',19,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(4,'Analytics Workshop Extended','Due to popular demand, we\'ve added an additional analytics deep-dive session tomorrow at 2 PM.','https://marketing.example.com/analytics-extended','2024-10-06 11:00:00',_binary '',20,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(4,'Networking Happy Hour','Join fellow marketers for drinks and discussion at the Content Studio from 5-7 PM today.','https://marketing.example.com/happy-hour','2024-10-06 15:00:00',_binary '',21,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(5,'Remote Work Survey Results','New research reveals 85% of workers prefer hybrid models. Full report available in the resource center.','https://future-work.example.com/survey-results','2024-11-15 09:00:00',_binary '',22,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(5,'Virtual Office Tour','Experience the future of workspaces with our VR office tour. Available at the collaboration hub.','https://future-work.example.com/vr-tour','2024-11-15 11:00:00',_binary '',23,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(5,'Productivity Tools Showcase','Discover the latest tools for remote team collaboration and productivity optimization.','https://future-work.example.com/tools-showcase','2024-11-15 14:00:00',_binary '',24,'2025-06-09 11:26:19','2025-06-09 11:26:19',1),(5,'Digital Wellness Workshop','Learn strategies for maintaining work-life balance in digital environments. Workshop starts at 3 PM.','https://future-work.example.com/wellness-workshop','2024-11-15 15:00:00',_binary '',25,'2025-06-09 11:26:19','2025-06-09 11:26:19',1);
/*!40000 ALTER TABLE `t_bulletin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_event`
--

DROP TABLE IF EXISTS `t_event`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_event` (
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_event';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_event`
--

LOCK TABLES `t_event` WRITE;
/*!40000 ALTER TABLE `t_event` DISABLE KEYS */;
INSERT INTO `t_event` VALUES ('Open Portal Expo 2024','The premier virtual expo experience featuring cutting-edge technology, innovative startups, and industry leaders. Join us for three days of networking, learning, and discovery in the digital realm.','2024-06-15 09:00:00','2024-06-17 18:00:00',1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Tech Innovation Summit 2024','A focused summit on emerging technologies including AI, blockchain, and IoT. Features keynote speakers from major tech companies and hands-on workshops.','2024-07-20 08:00:00','2024-07-22 20:00:00',2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Startup Showcase Fall 2024','Discover the next generation of startups and entrepreneurs. Pitch competitions, investor meetings, and networking opportunities for the startup ecosystem.','2024-09-10 10:00:00','2024-09-12 17:00:00',3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Digital Marketing Expo 2024','The ultimate destination for digital marketing professionals. Learn about the latest trends in social media, content marketing, and customer engagement.','2024-10-05 09:30:00','2024-10-07 16:30:00',4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Future of Work Conference','Exploring remote work, digital collaboration, and the changing landscape of professional environments. Interactive sessions and virtual networking.','2024-11-15 08:30:00','2024-11-16 19:00:00',5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_event` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_event_map`
--

DROP TABLE IF EXISTS `t_event_map`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_event_map` (
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `event_id` bigint DEFAULT NULL,
  `map_url` varchar(255) DEFAULT NULL,
  `map_width` int DEFAULT NULL,
  `map_height` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_event_map';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_event_map`
--

LOCK TABLES `t_event_map` WRITE;
/*!40000 ALTER TABLE `t_event_map` DISABLE KEYS */;
INSERT INTO `t_event_map` VALUES ('Main Exhibition Hall','Primary exhibition space featuring main booths, keynote stage, and central networking area',1,'https://picsum.photos/1920/1080?random=1',1920,1080,1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Conference Center','Multi-purpose conference rooms and presentation spaces for workshops and seminars',1,'https://picsum.photos/1920/1080?random=2',1920,1080,2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Food Court & Networking','Dining area with virtual food vendors and casual networking spaces',1,'https://picsum.photos/1920/1080?random=3',1920,1080,3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Demo Zone','Interactive demonstration area for hands-on product experiences',1,'https://picsum.photos/1920/1080?random=4',1920,1080,4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Innovation Hub','Central hub for tech demonstrations and innovation showcases',2,'https://picsum.photos/1920/1080?random=5',1920,1080,5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Workshop Rooms','Dedicated spaces for hands-on technical workshops',2,'https://picsum.photos/1920/1080?random=6',1920,1080,6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Pitch Arena','Main stage for startup pitches and investor presentations',3,'https://picsum.photos/1920/1080?random=7',1920,1080,7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Startup Alley','Exhibition space for early-stage startups and entrepreneurs',3,'https://picsum.photos/1920/1080?random=8',1920,1080,8,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Marketing Central','Main exhibition area for marketing tools and platforms',4,'https://picsum.photos/1920/1080?random=9',1920,1080,9,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Content Studio','Interactive content creation and social media spaces',4,'https://picsum.photos/1920/1080?random=10',1920,1080,10,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Virtual Office','Demonstration of remote work tools and environments',5,'https://picsum.photos/1920/1080?random=11',1920,1080,11,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),('Collaboration Hub','Interactive spaces for team collaboration and networking',5,'https://picsum.photos/1920/1080?random=12',1920,1080,12,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_event_map` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_exhibitor`
--

DROP TABLE IF EXISTS `t_exhibitor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_exhibitor` (
  `mark_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_mark_id` (`mark_id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_exhibitor';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_exhibitor`
--

LOCK TABLES `t_exhibitor` WRITE;
/*!40000 ALTER TABLE `t_exhibitor` DISABLE KEYS */;
INSERT INTO `t_exhibitor` VALUES (4,'Microsoft Corporation','https://picsum.photos/300/200?random=101','Leading technology company specializing in cloud computing, productivity software, and AI solutions. Discover the latest innovations in Azure, Microsoft 365, and cutting-edge AI technologies.',1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Google Cloud','https://picsum.photos/300/200?random=102','Google\'s comprehensive cloud computing platform offering scalable infrastructure, machine learning tools, and data analytics solutions for businesses of all sizes.',2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(6,'Amazon Web Services','https://picsum.photos/300/200?random=103','The world\'s most comprehensive and broadly adopted cloud platform, offering over 200 fully featured services from data centers globally.',3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(7,'Meta Reality Labs','https://picsum.photos/300/200?random=104','Pioneering the future of virtual and augmented reality experiences. Explore immersive technologies that will transform how we connect, work, and play.',4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(8,'NVIDIA Corporation','https://picsum.photos/300/200?random=105','Leading AI computing company powering breakthroughs in gaming, data centers, professional visualization, and autonomous vehicles.',5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(9,'TechStart Alpha','https://picsum.photos/300/200?random=106','Innovative startup developing next-generation productivity tools powered by artificial intelligence. Our mission is to revolutionize workplace efficiency.',6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(10,'InnovateLab','https://picsum.photos/300/200?random=107','Research and development company focused on breakthrough technologies in quantum computing and advanced materials science.',7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(11,'NextGen Solutions','https://picsum.photos/300/200?random=108','Sustainable technology company creating eco-friendly solutions for smart cities and renewable energy management systems.',8,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(12,'AI Pioneers','https://picsum.photos/300/200?random=109','Cutting-edge artificial intelligence company specializing in natural language processing and computer vision applications for healthcare.',9,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(13,'BlockChain Ventures','https://picsum.photos/300/200?random=110','Blockchain technology company building decentralized applications and smart contract solutions for financial services and supply chain management.',10,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(19,'Virtual Cafe Central','https://picsum.photos/300/200?random=111','Premium virtual dining experience offering gourmet coffee, artisanal pastries, and networking opportunities in a digital environment.',11,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(20,'International Cuisine Hub','https://picsum.photos/300/200?random=112','Diverse culinary experience featuring authentic dishes from around the world, bringing global flavors to the virtual expo space.',12,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(21,'Espresso Bar Digital','https://picsum.photos/300/200?random=113','Specialty coffee experience with expert baristas, premium beans, and the perfect environment for casual business conversations.',13,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(29,'OpenAI Technologies','https://picsum.photos/300/200?random=114','Leading artificial intelligence research company developing safe and beneficial AI systems. Experience the latest in large language models and AI safety.',14,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(30,'TensorFlow Community','https://picsum.photos/300/200?random=115','Open-source machine learning platform enabling developers and researchers to build and deploy ML applications at scale across various industries.',15,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(32,'Ethereum Foundation','https://picsum.photos/300/200?random=116','Non-profit organization supporting Ethereum ecosystem development, promoting decentralized applications and blockchain innovation worldwide.',16,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(33,'DeFi Innovations Inc','https://picsum.photos/300/200?random=117','Pioneering decentralized finance solutions that democratize access to financial services through blockchain technology and smart contracts.',17,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(34,'NFT Gallery Collective','https://picsum.photos/300/200?random=118','Digital art marketplace and community platform showcasing unique NFT collections from emerging and established digital artists globally.',18,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(36,'Industrial IoT Solutions','https://picsum.photos/300/200?random=119','Enterprise IoT platform provider specializing in industrial automation, predictive maintenance, and smart manufacturing solutions.',19,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(37,'Connected Devices Corp','https://picsum.photos/300/200?random=120','Consumer IoT company creating smart home devices that seamlessly integrate with existing ecosystems for enhanced living experiences.',20,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(38,'HealthTech Innovations','https://picsum.photos/300/200?random=121','Revolutionary healthcare startup developing AI-powered diagnostic tools and telemedicine platforms to improve patient outcomes and accessibility.',21,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(39,'EdTech Revolution','https://picsum.photos/300/200?random=122','Educational technology company creating personalized learning experiences through adaptive AI and immersive virtual reality environments.',22,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(40,'FinTech Pioneer Solutions','https://picsum.photos/300/200?random=123','Next-generation financial technology startup offering digital banking solutions, cryptocurrency integration, and AI-driven investment advice.',23,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(41,'ScaleUp Success Stories','https://picsum.photos/300/200?random=124','Growth-stage company providing business intelligence and analytics platforms that help enterprises make data-driven decisions at scale.',24,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(42,'Series B Spotlight','https://picsum.photos/300/200?random=125','Rapidly growing startup in the logistics sector, revolutionizing supply chain management through autonomous vehicles and AI optimization.',25,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(44,'Instagram Creator Studio','https://picsum.photos/300/200?random=126','Official Instagram business solutions helping creators and brands build authentic connections through innovative content creation and analytics tools.',26,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(45,'TikTok for Business','https://picsum.photos/300/200?random=127','TikTok\'s comprehensive business platform offering advertising solutions, creator partnerships, and trend analytics for brand growth.',27,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(46,'LinkedIn Marketing Solutions','https://picsum.photos/300/200?random=128','Professional networking platform\'s marketing division providing B2B advertising, lead generation, and professional audience targeting capabilities.',28,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(48,'Content Strategy Hub','https://picsum.photos/300/200?random=129','Full-service content marketing agency specializing in strategic content planning, creation, and distribution across multiple digital channels.',29,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(50,'Google Analytics Pro','https://picsum.photos/300/200?random=130','Advanced web analytics platform providing deep insights into user behavior, conversion tracking, and ROI measurement for digital marketing campaigns.',30,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(52,'Zoom Workplace Solutions','https://picsum.photos/300/200?random=131','Leading video communications platform offering comprehensive remote work solutions including meetings, webinars, and collaborative workspaces.',31,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(53,'Slack Technologies','https://picsum.photos/300/200?random=132','Business communication platform transforming how teams collaborate through channels, integrations, and workflow automation tools.',32,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(54,'Microsoft Teams Enterprise','https://picsum.photos/300/200?random=133','Integrated collaboration platform combining chat, video meetings, file storage, and application integration for seamless teamwork.',33,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(56,'Project Management Pro','https://picsum.photos/300/200?random=134','Advanced project management software providing agile workflows, resource planning, and team collaboration tools for distributed teams.',34,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(58,'Smart Office Technologies','https://picsum.photos/300/200?random=135','IoT-enabled office solutions creating intelligent workspaces that adapt to employee needs and optimize productivity through data-driven insights.',35,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(27,'Virtual Cafe Central','https://picsum.photos/300/200?random=201','Premium virtual dining experience offering gourmet coffee, artisanal pastries, and networking opportunities in a digital environment.',36,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(28,'International Cuisine Hub','https://picsum.photos/300/200?random=202','Diverse culinary experience featuring authentic dishes from around the world, bringing global flavors to the virtual expo space.',37,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(31,'Tea Station Premium','https://picsum.photos/300/200?random=203','Specialty tea experience featuring premium loose-leaf teas, traditional brewing methods, and mindful networking spaces.',38,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(35,'Gaming Arena Pro','https://picsum.photos/300/200?random=204','Professional esports and gaming company providing competitive gaming experiences, tournaments, and interactive entertainment solutions.',39,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(43,'Industrial IoT Solutions','https://picsum.photos/300/200?random=205','Enterprise IoT platform provider specializing in industrial automation, predictive maintenance, and smart manufacturing solutions.',40,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(47,'FinTech Pioneer Solutions','https://picsum.photos/300/200?random=206','Next-generation financial technology startup offering digital banking solutions, cryptocurrency integration, and AI-driven investment advice.',41,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(49,'Series B Growth Partners','https://picsum.photos/300/200?random=207','Venture capital and growth equity firm specializing in Series B investments, providing strategic guidance and scaling expertise.',42,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(55,'Content Strategy Experts','https://picsum.photos/300/200?random=208','Full-service content marketing agency specializing in strategic content planning, creation, and distribution across multiple digital channels.',43,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(57,'Analytics Pro Consulting','https://picsum.photos/300/200?random=209','Advanced analytics consulting firm providing deep insights into user behavior, conversion optimization, and ROI measurement strategies.',44,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(59,'Zoom Enterprise Solutions','https://picsum.photos/300/200?random=210','Leading video communications platform offering comprehensive remote work solutions including meetings, webinars, and collaborative workspaces.',45,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(60,'Slack Business Solutions','https://picsum.photos/300/200?random=211','Business communication platform transforming how teams collaborate through channels, integrations, and workflow automation tools.',46,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(61,'Microsoft Teams Pro','https://picsum.photos/300/200?random=212','Integrated collaboration platform combining chat, video meetings, file storage, and application integration for seamless teamwork.',47,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(62,'Virtual Whiteboard Co','https://picsum.photos/300/200?random=213','Digital collaboration tools company creating innovative whiteboard solutions for remote teams, brainstorming, and visual project management.',48,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(63,'Project Management Solutions','https://picsum.photos/300/200?random=214','Advanced project management software providing agile workflows, resource planning, and team collaboration tools for distributed teams.',49,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(64,'Hybrid Office Innovations','https://picsum.photos/300/200?random=215','Workplace design and technology company creating flexible office solutions that seamlessly blend remote and in-person work experiences.',50,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(65,'Smart Office Technologies','https://picsum.photos/300/200?random=216','IoT-enabled office solutions creating intelligent workspaces that adapt to employee needs and optimize productivity through data-driven insights.',51,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_exhibitor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_game`
--

DROP TABLE IF EXISTS `t_game`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_game` (
  `event_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `game_url` varchar(255) DEFAULT NULL,
  `game_level` int DEFAULT NULL,
  `mark_id` bigint DEFAULT NULL,
  `area_id` bigint DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_mark_id` (`mark_id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_game';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_game`
--

LOCK TABLES `t_game` WRITE;
/*!40000 ALTER TABLE `t_game` DISABLE KEYS */;
INSERT INTO `t_game` VALUES (1,'Welcome Check-in','Check in at the welcome desk to start your expo journey and earn your first points!','https://game.example.com/checkin',1,1,1,1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Expo Explorer','Visit 5 different booths in the Tech Pavilion to complete this exploration mission.','https://game.example.com/explorer',1,4,2,2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Coffee Break Challenge','Find and visit the coffee station for a quick networking break.','https://game.example.com/coffee',1,21,13,3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Information Hunter','Collect information from 3 different information points around the expo.','https://game.example.com/info',1,3,1,4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Tech Trivia Challenge','Answer technology-related questions at various booths to test your knowledge.','https://game.example.com/trivia',2,5,2,5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Startup Pitch Bingo','Listen to startup pitches and mark off common buzzwords on your bingo card.','https://game.example.com/bingo',2,10,3,6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Networking Master','Connect with 10 different attendees and collect their virtual business cards.','https://game.example.com/networking',2,14,5,7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Workshop Warrior','Attend 3 different workshops and complete the associated quizzes.','https://game.example.com/workshop',2,18,10,8,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Innovation Scavenger Hunt','Find hidden QR codes throughout the expo and solve technology puzzles.','https://game.example.com/scavenger',3,8,2,9,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Demo Master Challenge','Successfully complete hands-on demos at 5 different technology stations.','https://game.example.com/demo',3,25,15,10,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Keynote Knowledge Test','Watch the keynote presentation and answer detailed questions about the content.','https://game.example.com/keynote',3,12,4,11,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VR Experience Champion','Complete the full VR experience and achieve the highest score in the virtual challenge.','https://game.example.com/vr',3,26,16,12,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'AI Ethics Quiz','Test your knowledge of AI ethics and responsible machine learning practices.','https://game.example.com/ai-ethics',2,29,18,13,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Machine Learning Basics','Complete an interactive tutorial on machine learning fundamentals.','https://game.example.com/ml-basics',1,30,18,14,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Neural Network Builder','Build and train a simple neural network in this interactive challenge.','https://game.example.com/neural',3,31,18,15,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Crypto Wallet Setup','Learn how to set up and secure a cryptocurrency wallet safely.','https://game.example.com/wallet',1,32,19,16,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Smart Contract Basics','Understand the fundamentals of smart contracts and their applications.','https://game.example.com/smart-contract',2,33,19,17,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'DeFi Explorer','Navigate the world of decentralized finance and complete trading simulations.','https://game.example.com/defi',3,34,19,18,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Smart Home Setup','Configure a virtual smart home system with various IoT devices.','https://game.example.com/smart-home',2,35,20,19,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'IoT Security Challenge','Identify and fix security vulnerabilities in IoT device networks.','https://game.example.com/iot-security',3,36,20,20,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Pitch Perfect','Rate startup pitches and predict which ones will succeed.','https://game.example.com/pitch-perfect',2,43,26,21,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Startup Valuation Game','Learn to evaluate startup valuations and investment potential.','https://game.example.com/valuation',3,38,23,22,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Entrepreneur Quiz','Test your entrepreneurial knowledge and business acumen.','https://game.example.com/entrepreneur',1,39,23,23,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Investment Simulator','Make virtual investments in startups and track your portfolio performance.','https://game.example.com/investment',3,41,24,24,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Social Media Strategy','Create a comprehensive social media strategy for a fictional brand.','https://game.example.com/social-strategy',2,44,28,25,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Content Creation Challenge','Produce engaging content for different social media platforms.','https://game.example.com/content',2,48,29,26,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Analytics Interpretation','Analyze marketing data and draw actionable insights from the results.','https://game.example.com/analytics',3,50,30,27,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Influencer Matching','Match brands with appropriate influencers based on audience demographics.','https://game.example.com/influencer',2,45,28,28,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Team Building','Complete virtual team building exercises and collaboration challenges.','https://game.example.com/team-building',1,52,33,29,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Productivity Optimizer','Learn and implement productivity techniques for remote work environments.','https://game.example.com/productivity',2,55,34,30,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Digital Wellness Check','Assess and improve your digital wellness and work-life balance.','https://game.example.com/wellness',1,56,35,31,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Future Office Designer','Design the ideal hybrid office space using virtual planning tools.','https://game.example.com/office-design',3,57,36,32,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_game` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_game_record`
--

DROP TABLE IF EXISTS `t_game_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_game_record` (
  `game_id` bigint DEFAULT NULL,
  `game_name` varchar(255) DEFAULT NULL,
  `game_level` int DEFAULT NULL,
  `finished_at` datetime DEFAULT NULL,
  `is_public` int DEFAULT NULL,
  `score` int DEFAULT NULL,
  `mark_id` bigint DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_game_record';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_game_record`
--

LOCK TABLES `t_game_record` WRITE;
/*!40000 ALTER TABLE `t_game_record` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_game_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_hotel`
--

DROP TABLE IF EXISTS `t_hotel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_hotel` (
  `mark_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_mark_id` (`mark_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_hotel';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_hotel`
--

LOCK TABLES `t_hotel` WRITE;
/*!40000 ALTER TABLE `t_hotel` DISABLE KEYS */;
INSERT INTO `t_hotel` VALUES (1,'Welcome Plaza Hotel','https://picsum.photos/300/200?random=301','Luxury hotel located at the main entrance of the expo district. Features executive suites, conference facilities, and premium amenities for business travelers and VIP guests.',1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Registration District Inn','https://picsum.photos/300/200?random=302','Convenient hotel near registration areas. Modern rooms, business center, and shuttle service to all expo venues.',2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Information Hub Hotel','https://picsum.photos/300/200?random=303','Boutique hotel with concierge services and information desk. Perfect for first-time expo visitors and international guests.',3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(16,'Tech Support Suites','https://picsum.photos/300/200?random=304','Extended-stay hotel perfect for technical professionals. Each suite includes workspace, high-speed internet, and tech support services.',4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(22,'Workshop Residence','https://picsum.photos/300/200?random=305','Educational-focused hotel designed for workshop attendees and trainers. Study rooms, collaboration spaces, and learning-friendly environment.',5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(25,'Hands-on Learning Lodge','https://picsum.photos/300/200?random=306','Practical learning hotel with maker spaces and development labs. Perfect for developers and hands-on learners.',6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(26,'Coding Bootcamp Hotel','https://picsum.photos/300/200?random=307','Developer-friendly hotel with 24/7 coding spaces, high-speed internet, and collaborative work areas for programming teams.',7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_hotel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_mark`
--

DROP TABLE IF EXISTS `t_mark`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_mark` (
  `event_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `area_id` bigint DEFAULT NULL,
  `mark_type` int DEFAULT NULL,
  `x_axis` int DEFAULT NULL,
  `y_axis` int DEFAULT NULL,
  `mark_score` int DEFAULT '0',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_mark';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_mark`
--

LOCK TABLES `t_mark` WRITE;
/*!40000 ALTER TABLE `t_mark` DISABLE KEYS */;
INSERT INTO `t_mark` VALUES (1,'Welcome Desk',1,2,150,150,0,1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Registration',1,2,200,120,0,2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Information Kiosk',1,2,250,180,0,3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Microsoft Booth',2,2,450,200,0,4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Google Cloud',2,2,550,180,0,5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Amazon Web Services',2,2,650,220,0,6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Meta Reality Labs',2,2,500,300,0,7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'NVIDIA AI Corner',2,2,600,350,0,8,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'TechStart Alpha',3,2,950,200,0,9,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'InnovateLab',3,2,1050,180,0,10,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'NextGen Solutions',3,2,1150,220,0,11,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'AI Pioneers',3,2,1000,300,0,12,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'BlockChain Ventures',3,2,1100,350,0,13,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Main Stage',4,3,550,600,0,14,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Speaker Green Room',4,2,400,550,0,15,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Audio/Visual Control',4,2,700,550,0,16,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Business Lounge',5,2,1300,500,0,17,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Coffee Station',5,2,1250,450,0,18,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Charging Station',5,2,1350,550,0,19,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Help Desk',6,2,175,350,0,20,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Lost & Found',6,2,125,380,0,21,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Workshop: AI Fundamentals',7,3,325,300,0,22,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Panel: Future of Tech',8,3,625,300,0,23,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Seminar: Digital Transformation',9,3,925,300,0,24,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Hands-on: Cloud Computing',10,1,350,575,0,25,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Coding Bootcamp',11,1,700,575,0,26,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Virtual Cafe Central',12,2,600,450,0,27,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'International Cuisine',12,2,500,500,0,28,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Healthy Options',12,2,700,500,0,29,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Espresso Bar',13,2,200,475,0,30,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Tea Station',13,2,250,500,0,31,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VR Experience',15,1,450,350,0,32,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'AR Showcase',15,1,550,400,0,33,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Product Demo Theater',16,3,950,400,0,34,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Gaming Tournament',17,1,1350,350,0,35,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'OpenAI Showcase',18,2,450,300,0,36,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'TensorFlow Demo',18,2,550,350,0,37,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'ML Workshop Station',18,1,600,400,0,38,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Ethereum Foundation',19,2,900,300,0,39,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'DeFi Innovations',19,2,1000,250,0,40,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'NFT Gallery',19,2,1100,350,0,41,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Smart Home Demo',20,1,1350,250,0,42,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Industrial IoT',20,2,1400,300,0,43,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Connected Devices',20,2,1450,350,0,44,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'HealthTech Startup',23,2,350,300,0,45,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'EdTech Innovation',23,2,450,350,0,46,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'FinTech Pioneer',23,2,550,300,0,47,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'ScaleUp Success',24,2,800,300,0,48,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Series B Spotlight',24,2,900,350,0,49,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Pitch Stage',26,3,650,700,0,50,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Judge Panel',26,2,550,650,0,51,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Instagram Creator Studio',28,2,450,300,0,52,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'TikTok Business',28,2,550,350,0,53,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'LinkedIn Marketing',28,2,500,400,0,54,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Content Strategy Hub',29,2,950,350,0,55,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Video Production',29,1,1050,400,0,56,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Google Analytics',30,2,1350,400,0,57,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Data Visualization',30,1,1400,350,0,58,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Zoom Workspace',33,2,400,300,0,59,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Slack Integration',33,2,500,350,0,60,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Microsoft Teams',33,2,600,300,0,61,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Virtual Whiteboard',34,1,850,300,0,62,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Project Management',34,2,950,350,0,63,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Hybrid Office Model',36,1,625,650,0,64,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Smart Office Tech',36,2,725,700,0,65,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_mark` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_mark_status`
--

DROP TABLE IF EXISTS `t_mark_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_mark_status` (
  `mark_id` bigint DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `got_score` bit(1) DEFAULT b'0',
  `collected` bit(1) DEFAULT b'0',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_markId_userId` (`mark_id`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_mark_status';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_mark_status`
--

LOCK TABLES `t_mark_status` WRITE;
/*!40000 ALTER TABLE `t_mark_status` DISABLE KEYS */;
INSERT INTO `t_mark_status` VALUES (1,2,_binary '\0',_binary '\0',1,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(2,2,_binary '\0',_binary '\0',2,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(3,2,_binary '\0',_binary '\0',3,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(4,2,_binary '\0',_binary '\0',4,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(5,2,_binary '\0',_binary '\0',5,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(6,2,_binary '\0',_binary '\0',6,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(7,2,_binary '\0',_binary '\0',7,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(8,2,_binary '\0',_binary '\0',8,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(9,2,_binary '\0',_binary '\0',9,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(10,2,_binary '\0',_binary '\0',10,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(11,2,_binary '\0',_binary '\0',11,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(12,2,_binary '\0',_binary '\0',12,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(13,2,_binary '\0',_binary '\0',13,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(14,2,_binary '\0',_binary '\0',14,'2025-06-09 10:56:41','2025-06-08 19:29:26'),(15,2,_binary '\0',_binary '\0',15,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(16,2,_binary '\0',_binary '\0',16,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(17,2,_binary '\0',_binary '\0',17,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(18,2,_binary '\0',_binary '\0',18,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(19,2,_binary '\0',_binary '\0',19,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(20,2,_binary '\0',_binary '\0',20,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(21,2,_binary '\0',_binary '\0',21,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(22,2,_binary '\0',_binary '\0',22,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(23,2,_binary '\0',_binary '\0',23,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(24,2,_binary '\0',_binary '\0',24,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(25,2,_binary '\0',_binary '\0',25,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(26,2,_binary '\0',_binary '\0',26,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(27,2,_binary '\0',_binary '\0',27,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(28,2,_binary '\0',_binary '\0',28,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(29,2,_binary '\0',_binary '\0',29,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(30,2,_binary '\0',_binary '\0',30,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(31,2,_binary '\0',_binary '\0',31,'2025-06-08 19:53:25','2025-06-08 19:29:26'),(32,2,_binary '\0',_binary '\0',32,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(33,2,_binary '\0',_binary '\0',33,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(34,2,_binary '\0',_binary '\0',34,'2025-06-08 19:53:16','2025-06-08 19:29:26'),(35,2,_binary '\0',_binary '\0',35,'2025-06-08 19:53:16','2025-06-08 19:29:26');
/*!40000 ALTER TABLE `t_mark_status` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_personalinfo`
--

DROP TABLE IF EXISTS `t_personalinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_personalinfo` (
  `name` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `province` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `area` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `identify_status` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户个人信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_personalinfo`
--

LOCK TABLES `t_personalinfo` WRITE;
/*!40000 ALTER TABLE `t_personalinfo` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_personalinfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_restaurant`
--

DROP TABLE IF EXISTS `t_restaurant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_restaurant` (
  `mark_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_mark_id` (`mark_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_restaurant';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_restaurant`
--

LOCK TABLES `t_restaurant` WRITE;
/*!40000 ALTER TABLE `t_restaurant` DISABLE KEYS */;
INSERT INTO `t_restaurant` VALUES (27,'Virtual Cafe Central','https://picsum.photos/300/200?random=401','Premium virtual dining experience offering gourmet coffee, artisanal pastries, and light meals. Perfect for networking breaks and casual business meetings.',1,'2025-06-09 11:08:05','2025-06-09 11:08:05',1),(31,'Tea Station Premium','https://picsum.photos/300/200?random=403','Specialty tea house featuring premium loose-leaf teas from around the world. Traditional brewing methods and peaceful atmosphere for mindful networking.',2,'2025-06-09 11:08:05','2025-06-09 11:08:05',1),(14,'Main Stage Catering','https://picsum.photos/300/200?random=404','Premium catering service for main stage events. Gourmet meals and refreshments for speakers, VIPs, and special event attendees.',3,'2025-06-09 11:08:05','2025-06-09 11:08:05',1),(23,'Panel Discussion Cafe','https://picsum.photos/300/200?random=405','Casual cafe serving light meals and beverages during panel discussions. Perfect for networking breaks between sessions.',4,'2025-06-09 11:08:05','2025-06-09 11:08:05',1),(24,'Seminar Snack Bar','https://picsum.photos/300/200?random=406','Quick service snack bar offering healthy options for seminar attendees. Energy bars, fresh fruit, and specialty drinks.',5,'2025-06-09 11:08:05','2025-06-09 11:08:05',1);
/*!40000 ALTER TABLE `t_restaurant` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_reward`
--

DROP TABLE IF EXISTS `t_reward`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_reward` (
  `event_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `detail_description` varchar(255) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `point_price` int DEFAULT NULL,
  `inventory` int DEFAULT '0',
  `bought` int DEFAULT '0',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_reward';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_reward`
--

LOCK TABLES `t_reward` WRITE;
/*!40000 ALTER TABLE `t_reward` DISABLE KEYS */;
INSERT INTO `t_reward` VALUES (1,'Expo 2024 T-Shirt','Official Open Portal Expo 2024 commemorative t-shirt','High-quality cotton t-shirt featuring the official expo logo and design. Available in multiple sizes and colors. Perfect souvenir to remember your expo experience.','https://picsum.photos/300/200?random=701',50,100,15,1,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(1,'Tech Innovation Mug','Premium ceramic mug with tech-themed design','Durable ceramic mug featuring innovative technology graphics and expo branding. Microwave and dishwasher safe. Great for your morning coffee while coding.','https://picsum.photos/300/200?random=702',30,150,8,2,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(1,'VIP Networking Pass','Exclusive access to VIP networking events','Upgrade your expo experience with access to exclusive VIP networking sessions, premium lounges, and special meet-and-greet opportunities with industry leaders.','https://picsum.photos/300/200?random=703',200,25,12,3,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(1,'Wireless Charging Pad','Expo-branded wireless charging station','Sleek wireless charging pad with expo branding. Compatible with all Qi-enabled devices. Perfect for keeping your devices powered during long expo days.','https://picsum.photos/300/200?random=704',75,80,22,4,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(1,'Digital Swag Bag','Collection of digital tools and resources','Comprehensive digital package including software licenses, e-books, online course access, and exclusive digital content from expo partners.','https://picsum.photos/300/200?random=705',100,200,45,5,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(1,'Expo Hoodie','Premium hoodie with expo branding','Comfortable and stylish hoodie featuring the expo logo. Made from high-quality materials, perfect for casual wear and showing your tech enthusiasm.','https://picsum.photos/300/200?random=706',120,60,18,6,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(2,'AI Innovation Badge','Digital badge recognizing AI expertise','Official digital credential recognizing your participation in AI innovation sessions. Shareable on LinkedIn and other professional platforms.','https://picsum.photos/300/200?random=707',40,500,67,7,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(2,'Blockchain Starter Kit','Educational blockchain development kit','Complete starter kit for blockchain development including documentation, sample code, and access to development tools and platforms.','https://picsum.photos/300/200?random=708',150,50,8,8,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(2,'IoT Sensor Pack','Collection of IoT sensors for prototyping','Assorted IoT sensors and development boards perfect for building smart device prototypes. Includes temperature, humidity, motion, and light sensors.','https://picsum.photos/300/200?random=709',180,30,5,9,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(2,'Tech Summit Water Bottle','Insulated water bottle with smart features','Smart water bottle with temperature display and hydration tracking. Features summit branding and connects to mobile app for health monitoring.','https://picsum.photos/300/200?random=710',60,100,25,10,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(3,'Entrepreneur Toolkit','Essential tools for startup founders','Comprehensive toolkit including business plan templates, pitch deck guides, legal document templates, and access to startup resources.','https://picsum.photos/300/200?random=711',90,75,12,11,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(3,'Investor Network Access','One-month access to investor network','Premium access to exclusive investor network platform. Connect with VCs, angel investors, and potential partners for your startup.','https://picsum.photos/300/200?random=712',250,20,3,12,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(3,'Startup Showcase Backpack','Professional backpack for entrepreneurs','High-quality backpack designed for busy entrepreneurs. Multiple compartments for laptop, documents, and business essentials.','https://picsum.photos/300/200?random=713',80,90,28,13,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(3,'Mentorship Session','One-on-one session with industry mentor','Exclusive one-hour mentorship session with successful entrepreneurs and industry experts. Personalized advice for your startup journey.','https://picsum.photos/300/200?random=714',300,15,7,14,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(4,'Marketing Analytics Course','Online course on marketing analytics','Comprehensive online course covering advanced marketing analytics, data interpretation, and campaign optimization strategies.','https://picsum.photos/300/200?random=715',120,100,35,15,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(4,'Social Media Toolkit','Complete social media management toolkit','Professional toolkit including content templates, scheduling tools, analytics dashboards, and social media strategy guides.','https://picsum.photos/300/200?random=716',85,80,19,16,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(4,'Influencer Collaboration Guide','Guide to successful influencer partnerships','Detailed guide covering influencer identification, outreach strategies, campaign management, and ROI measurement for influencer marketing.','https://picsum.photos/300/200?random=717',65,120,42,17,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(4,'Content Creator Camera Kit','Professional camera equipment for content','Portable camera kit including ring light, tripod, and smartphone accessories. Perfect for creating high-quality marketing content.','https://picsum.photos/300/200?random=718',200,40,11,18,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(5,'Remote Work Setup Guide','Complete guide to remote work optimization','Comprehensive guide covering home office setup, productivity tools, time management, and work-life balance strategies for remote workers.','https://picsum.photos/300/200?random=719',45,150,38,19,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(5,'Productivity Software Bundle','Collection of productivity applications','Premium software bundle including project management tools, time tracking apps, collaboration platforms, and productivity enhancement software.','https://picsum.photos/300/200?random=720',160,60,14,20,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(5,'Ergonomic Desk Accessories','Ergonomic accessories for home office','Set of ergonomic accessories including laptop stand, wireless keyboard, ergonomic mouse, and posture support items for healthy remote work.','https://picsum.photos/300/200?random=721',110,70,21,21,'2025-06-09 11:09:49','2025-06-09 11:09:49',1),(5,'Digital Wellness Course','Course on maintaining digital wellness','Online course focusing on digital wellness, screen time management, stress reduction, and maintaining mental health in digital work environments.','https://picsum.photos/300/200?random=722',75,100,29,22,'2025-06-09 11:09:49','2025-06-09 11:09:49',1);
/*!40000 ALTER TABLE `t_reward` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_reward_image`
--

DROP TABLE IF EXISTS `t_reward_image`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_reward_image` (
  `reward_id` bigint DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_reward_image';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_reward_image`
--

LOCK TABLES `t_reward_image` WRITE;
/*!40000 ALTER TABLE `t_reward_image` DISABLE KEYS */;
INSERT INTO `t_reward_image` VALUES (1,'https://picsum.photos/300/200?random=801',1,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(1,'https://picsum.photos/300/200?random=802',2,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(1,'https://picsum.photos/300/200?random=803',3,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(2,'https://picsum.photos/300/200?random=804',4,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(2,'https://picsum.photos/300/200?random=805',5,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(3,'https://picsum.photos/300/200?random=806',6,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(3,'https://picsum.photos/300/200?random=807',7,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(4,'https://picsum.photos/300/200?random=808',8,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(4,'https://picsum.photos/300/200?random=809',9,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(4,'https://picsum.photos/300/200?random=810',10,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(5,'https://picsum.photos/300/200?random=811',11,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(5,'https://picsum.photos/300/200?random=812',12,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(6,'https://picsum.photos/300/200?random=813',13,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(6,'https://picsum.photos/300/200?random=814',14,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(6,'https://picsum.photos/300/200?random=815',15,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(7,'https://picsum.photos/300/200?random=816',16,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(7,'https://picsum.photos/300/200?random=817',17,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(8,'https://picsum.photos/300/200?random=818',18,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(8,'https://picsum.photos/300/200?random=819',19,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(8,'https://picsum.photos/300/200?random=820',20,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(9,'https://picsum.photos/300/200?random=821',21,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(9,'https://picsum.photos/300/200?random=822',22,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(9,'https://picsum.photos/300/200?random=823',23,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(10,'https://picsum.photos/300/200?random=824',24,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(10,'https://picsum.photos/300/200?random=825',25,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(11,'https://picsum.photos/300/200?random=826',26,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(11,'https://picsum.photos/300/200?random=827',27,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(12,'https://picsum.photos/300/200?random=828',28,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(12,'https://picsum.photos/300/200?random=829',29,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(13,'https://picsum.photos/300/200?random=830',30,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(13,'https://picsum.photos/300/200?random=831',31,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(13,'https://picsum.photos/300/200?random=832',32,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(14,'https://picsum.photos/300/200?random=833',33,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(14,'https://picsum.photos/300/200?random=834',34,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(15,'https://picsum.photos/300/200?random=835',35,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(15,'https://picsum.photos/300/200?random=836',36,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(16,'https://picsum.photos/300/200?random=837',37,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(16,'https://picsum.photos/300/200?random=838',38,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(16,'https://picsum.photos/300/200?random=839',39,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(17,'https://picsum.photos/300/200?random=840',40,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(17,'https://picsum.photos/300/200?random=841',41,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(18,'https://picsum.photos/300/200?random=842',42,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(18,'https://picsum.photos/300/200?random=843',43,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(18,'https://picsum.photos/300/200?random=844',44,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(19,'https://picsum.photos/300/200?random=845',45,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(19,'https://picsum.photos/300/200?random=846',46,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(20,'https://picsum.photos/300/200?random=847',47,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(20,'https://picsum.photos/300/200?random=848',48,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(20,'https://picsum.photos/300/200?random=849',49,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(21,'https://picsum.photos/300/200?random=850',50,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(21,'https://picsum.photos/300/200?random=851',51,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(21,'https://picsum.photos/300/200?random=852',52,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(22,'https://picsum.photos/300/200?random=853',53,'2025-06-09 11:09:55','2025-06-09 11:09:55',1),(22,'https://picsum.photos/300/200?random=854',54,'2025-06-09 11:09:55','2025-06-09 11:09:55',1);
/*!40000 ALTER TABLE `t_reward_image` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_score_record`
--

DROP TABLE IF EXISTS `t_score_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_score_record` (
  `score` varchar(255) DEFAULT NULL,
  `source_id` bigint DEFAULT NULL,
  `score_source` int DEFAULT NULL,
  `add_or_subtract` int DEFAULT NULL,
  `source_name` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='积分记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_score_record`
--

LOCK TABLES `t_score_record` WRITE;
/*!40000 ALTER TABLE `t_score_record` DISABLE KEYS */;
INSERT INTO `t_score_record` VALUES ('+500',NULL,5,1,'Registration reward',1,'2025-06-08 16:07:13','2025-06-08 16:07:13',NULL),('+500',NULL,5,1,'Registration reward',2,'2025-06-08 19:08:14','2025-06-08 19:08:14',NULL);
/*!40000 ALTER TABLE `t_score_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_send_info`
--

DROP TABLE IF EXISTS `t_send_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_send_info` (
  `count` int DEFAULT NULL,
  `fee` double(10,0) DEFAULT NULL,
  `sid` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='手机发送信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_send_info`
--

LOCK TABLES `t_send_info` WRITE;
/*!40000 ALTER TABLE `t_send_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_send_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_send_verify_code_record`
--

DROP TABLE IF EXISTS `t_send_verify_code_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_send_verify_code_record` (
  `phone_num` varchar(255) DEFAULT NULL,
  `send_info` varchar(255) DEFAULT NULL,
  `send_type` int DEFAULT NULL,
  `error_info` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='手机发送记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_send_verify_code_record`
--

LOCK TABLES `t_send_verify_code_record` WRITE;
/*!40000 ALTER TABLE `t_send_verify_code_record` DISABLE KEYS */;
INSERT INTO `t_send_verify_code_record` VALUES ('+817014601880','Your verification code is: 720007. Valid for 5 minutes.',1,'',6,'2025-06-08 18:57:58','2025-06-08 18:57:58',NULL),('+817014601880','Your verification code is: 286175. Valid for 5 minutes.',1,'',7,'2025-06-08 19:13:37','2025-06-08 19:13:37',NULL);
/*!40000 ALTER TABLE `t_send_verify_code_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_shop`
--

DROP TABLE IF EXISTS `t_shop`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_shop` (
  `mark_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_mark_id` (`mark_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_shop';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_shop`
--

LOCK TABLES `t_shop` WRITE;
/*!40000 ALTER TABLE `t_shop` DISABLE KEYS */;
INSERT INTO `t_shop` VALUES (4,'Microsoft Store','https://picsum.photos/300/200?random=501','Official Microsoft merchandise and technology store. Latest Surface devices, Xbox accessories, and exclusive expo merchandise available.',1,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(5,'Google Gear Shop','https://picsum.photos/300/200?random=502','Google branded merchandise and accessories. Pixel devices, Nest products, and limited edition expo collectibles.',2,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(6,'AWS Swag Store','https://picsum.photos/300/200?random=503','Amazon Web Services merchandise and cloud computing accessories. Developer tools, branded apparel, and tech gadgets.',3,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(7,'Meta Reality Store','https://picsum.photos/300/200?random=504','Virtual and augmented reality equipment store. VR headsets, AR glasses, and immersive technology accessories.',4,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(8,'NVIDIA Tech Shop','https://picsum.photos/300/200?random=505','High-performance computing and AI hardware store. Graphics cards, development kits, and professional workstation accessories.',5,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(9,'TechStart Merchandise','https://picsum.photos/300/200?random=506','Startup-themed merchandise and innovation accessories. Entrepreneur apparel, productivity tools, and motivational items.',6,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(10,'InnovateLab Store','https://picsum.photos/300/200?random=507','Research and development tools store. Laboratory equipment, prototyping materials, and scientific instruments.',7,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(11,'NextGen Eco Shop','https://picsum.photos/300/200?random=508','Sustainable technology and eco-friendly products. Solar gadgets, recycled materials, and green technology accessories.',8,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(12,'AI Pioneer Shop','https://picsum.photos/300/200?random=509','Artificial intelligence and machine learning tools. Development boards, sensors, and AI-powered gadgets.',9,'2025-06-09 11:09:39','2025-06-09 11:09:39',1),(13,'Blockchain Boutique','https://picsum.photos/300/200?random=510','Cryptocurrency and blockchain merchandise. Hardware wallets, mining equipment, and crypto-themed collectibles.',10,'2025-06-09 11:09:39','2025-06-09 11:09:39',1);
/*!40000 ALTER TABLE `t_shop` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_show`
--

DROP TABLE IF EXISTS `t_show`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_show` (
  `mark_id` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `begin_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_show';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_show`
--

LOCK TABLES `t_show` WRITE;
/*!40000 ALTER TABLE `t_show` DISABLE KEYS */;
INSERT INTO `t_show` VALUES ('14','Opening Keynote: Future of Technology','https://picsum.photos/300/200?random=601','Join industry leaders as they unveil the latest technological breakthroughs and discuss the future of innovation. Featuring CEOs from major tech companies.','2024-06-15 09:00:00','2024-06-15 10:30:00',1,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('14','AI Revolution Panel Discussion','https://picsum.photos/300/200?random=602','Expert panel discussing the impact of artificial intelligence on various industries. Learn about AI ethics, implementation strategies, and future developments.','2024-06-15 11:00:00','2024-06-15 12:00:00',2,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('14','Startup Pitch Competition Finals','https://picsum.photos/300/200?random=603','Watch the most promising startups compete for funding and recognition. Innovative solutions across various tech sectors will be presented.','2024-06-15 14:00:00','2024-06-15 16:00:00',3,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('14','Closing Ceremony & Awards','https://picsum.photos/300/200?random=604','Celebration of expo achievements, award presentations, and networking reception. Recognition of outstanding exhibitors and innovations.','2024-06-17 16:00:00','2024-06-17 18:00:00',4,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('22','AI Fundamentals Workshop','https://picsum.photos/300/200?random=605','Hands-on workshop covering the basics of artificial intelligence and machine learning. Perfect for beginners and intermediate developers.','2024-06-15 10:00:00','2024-06-15 12:00:00',5,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('23','Future of Tech Panel','https://picsum.photos/300/200?random=606','Industry experts discuss emerging technologies and their potential impact on society. Interactive Q&A session with audience participation.','2024-06-15 13:00:00','2024-06-15 14:30:00',6,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('24','Digital Transformation Seminar','https://picsum.photos/300/200?random=607','Learn how organizations are successfully implementing digital transformation strategies. Case studies and best practices from industry leaders.','2024-06-16 09:00:00','2024-06-16 11:00:00',7,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('25','Cloud Computing Hands-on Lab','https://picsum.photos/300/200?random=608','Interactive laboratory session where participants build and deploy cloud applications. Bring your laptop and learn by doing.','2024-06-15 14:00:00','2024-06-15 17:00:00',8,'2025-06-09 11:09:43','2025-06-09 11:09:43',1),('26','Coding Bootcamp Intensive','https://picsum.photos/300/200?random=609','Intensive coding session covering modern development frameworks and best practices. Suitable for developers of all skill levels.','2024-06-16 10:00:00','2024-06-16 15:00:00',9,'2025-06-09 11:09:43','2025-06-09 11:09:43',1);
/*!40000 ALTER TABLE `t_show` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_system_settings`
--

DROP TABLE IF EXISTS `t_system_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_system_settings` (
  `push_notification_accept` int DEFAULT '1',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户系统设置信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_system_settings`
--

LOCK TABLES `t_system_settings` WRITE;
/*!40000 ALTER TABLE `t_system_settings` DISABLE KEYS */;
INSERT INTO `t_system_settings` VALUES (1,1,'2025-06-08 16:07:13','2025-06-08 16:07:13',NULL),(1,2,'2025-06-08 19:08:14','2025-06-08 19:08:14',NULL);
/*!40000 ALTER TABLE `t_system_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_ticket`
--

DROP TABLE IF EXISTS `t_ticket`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_ticket` (
  `event_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  `bound_at` datetime DEFAULT NULL,
  `checked_at` datetime DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_eventId_code` (`event_id`,`code`)
) ENGINE=InnoDB AUTO_INCREMENT=81 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_ticket';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_ticket`
--

LOCK TABLES `t_ticket` WRITE;
/*!40000 ALTER TABLE `t_ticket` DISABLE KEYS */;
INSERT INTO `t_ticket` VALUES (1,'VIP All-Access Pass','VIP2024-001',NULL,NULL,1,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VIP All-Access Pass','VIP2024-002',NULL,NULL,2,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VIP All-Access Pass','VIP2024-003',NULL,NULL,3,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VIP All-Access Pass','VIP2024-004',NULL,NULL,4,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'VIP All-Access Pass','VIP2024-005',NULL,NULL,5,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-001',NULL,NULL,6,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-002',NULL,NULL,7,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-003',NULL,NULL,8,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-004',NULL,NULL,9,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-005',NULL,NULL,10,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-006',NULL,NULL,11,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-007',NULL,NULL,12,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-008',NULL,NULL,13,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-009',NULL,NULL,14,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'General Admission','GA2024-010',NULL,NULL,15,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Speaker Pass','SPK2024-001',NULL,NULL,16,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Speaker Pass','SPK2024-002',NULL,NULL,17,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Speaker Pass','SPK2024-003',NULL,NULL,18,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Speaker Pass','SPK2024-004',NULL,NULL,19,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Staff Pass','STF2024-001',NULL,NULL,20,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Staff Pass','STF2024-002',NULL,NULL,21,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Staff Pass','STF2024-003',NULL,NULL,22,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Press Pass','PRS2024-001',NULL,NULL,23,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Press Pass','PRS2024-002',NULL,NULL,24,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Student Ticket','STU2024-001',NULL,NULL,25,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Student Ticket','STU2024-002',NULL,NULL,26,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Student Ticket','STU2024-003',NULL,NULL,27,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Student Ticket','STU2024-004',NULL,NULL,28,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(1,'Student Ticket','STU2024-005',NULL,NULL,29,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Premium Access','PREM-TIS-001',NULL,NULL,30,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Premium Access','PREM-TIS-002',NULL,NULL,31,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Premium Access','PREM-TIS-003',NULL,NULL,32,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Standard Access','STD-TIS-001',NULL,NULL,33,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Standard Access','STD-TIS-002',NULL,NULL,34,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Standard Access','STD-TIS-003',NULL,NULL,35,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Standard Access','STD-TIS-004',NULL,NULL,36,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Standard Access','STD-TIS-005',NULL,NULL,37,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Developer Pass','DEV-TIS-001',NULL,NULL,38,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Developer Pass','DEV-TIS-002',NULL,NULL,39,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(2,'Developer Pass','DEV-TIS-003',NULL,NULL,40,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Investor Pass','INV-SS-001',NULL,NULL,41,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Investor Pass','INV-SS-002',NULL,NULL,42,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Investor Pass','INV-SS-003',NULL,NULL,43,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Entrepreneur Ticket','ENT-SS-001',NULL,NULL,44,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Entrepreneur Ticket','ENT-SS-002',NULL,NULL,45,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Entrepreneur Ticket','ENT-SS-003',NULL,NULL,46,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Entrepreneur Ticket','ENT-SS-004',NULL,NULL,47,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Entrepreneur Ticket','ENT-SS-005',NULL,NULL,48,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Mentor Pass','MEN-SS-001',NULL,NULL,49,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'Mentor Pass','MEN-SS-002',NULL,NULL,50,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'General Attendee','GEN-SS-001',NULL,NULL,51,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'General Attendee','GEN-SS-002',NULL,NULL,52,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(3,'General Attendee','GEN-SS-003',NULL,NULL,53,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Marketing Professional','MKT-DME-001',NULL,NULL,54,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Marketing Professional','MKT-DME-002',NULL,NULL,55,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Marketing Professional','MKT-DME-003',NULL,NULL,56,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Marketing Professional','MKT-DME-004',NULL,NULL,57,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Agency Pass','AGY-DME-001',NULL,NULL,58,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Agency Pass','AGY-DME-002',NULL,NULL,59,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Agency Pass','AGY-DME-003',NULL,NULL,60,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Influencer Pass','INF-DME-001',NULL,NULL,61,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Influencer Pass','INF-DME-002',NULL,NULL,62,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Brand Manager','BRD-DME-001',NULL,NULL,63,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Brand Manager','BRD-DME-002',NULL,NULL,64,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(4,'Brand Manager','BRD-DME-003',NULL,NULL,65,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Executive Pass','EXE-FOW-001',NULL,NULL,66,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Executive Pass','EXE-FOW-002',NULL,NULL,67,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Executive Pass','EXE-FOW-003',NULL,NULL,68,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'HR Professional','HRP-FOW-001',NULL,NULL,69,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'HR Professional','HRP-FOW-002',NULL,NULL,70,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'HR Professional','HRP-FOW-003',NULL,NULL,71,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'HR Professional','HRP-FOW-004',NULL,NULL,72,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Worker','RMT-FOW-001',NULL,NULL,73,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Worker','RMT-FOW-002',NULL,NULL,74,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Worker','RMT-FOW-003',NULL,NULL,75,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Worker','RMT-FOW-004',NULL,NULL,76,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Remote Worker','RMT-FOW-005',NULL,NULL,77,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Team Lead','TLD-FOW-001',NULL,NULL,78,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Team Lead','TLD-FOW-002',NULL,NULL,79,'2025-06-09 11:07:39','2025-06-09 11:07:39',1),(5,'Team Lead','TLD-FOW-003',NULL,NULL,80,'2025-06-09 11:07:39','2025-06-09 11:07:39',1);
/*!40000 ALTER TABLE `t_ticket` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_upload_file`
--

DROP TABLE IF EXISTS `t_upload_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_upload_file` (
  `file_name` varchar(255) DEFAULT NULL,
  `file_size` bigint DEFAULT NULL,
  `content_type` varchar(255) DEFAULT NULL,
  `file_key` varchar(255) DEFAULT NULL,
  `signed_url` varchar(255) DEFAULT NULL,
  `media_type` tinyint DEFAULT NULL COMMENT '1 video, 2 image, 3 other',
  `upload_state` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_file_key` (`file_key`),
  UNIQUE KEY `idx_uni_signed_url` (`signed_url`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='上传文件信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_upload_file`
--

LOCK TABLES `t_upload_file` WRITE;
/*!40000 ALTER TABLE `t_upload_file` DISABLE KEYS */;
INSERT INTO `t_upload_file` VALUES ('expo_2024_banner.jpg',2048576,'image/jpeg','expo2024_banner_001','https://cdn.example.com/expo2024_banner_001.jpg',2,1,1,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('keynote_speakers_collage.png',1536000,'image/png','speakers_collage_002','https://cdn.example.com/speakers_collage_002.png',2,1,2,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('expo_floor_plan.pdf',5242880,'application/pdf','floor_plan_003','https://cdn.example.com/floor_plan_003.pdf',3,1,3,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('welcome_video_intro.mp4',52428800,'video/mp4','welcome_intro_004','https://cdn.example.com/welcome_intro_004.mp4',1,1,4,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('microsoft_booth_tour.mp4',41943040,'video/mp4','ms_booth_tour_005','https://cdn.example.com/ms_booth_tour_005.mp4',1,1,5,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('google_cloud_demo.mp4',38654976,'video/mp4','gc_demo_006','https://cdn.example.com/gc_demo_006.mp4',1,1,6,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('aws_architecture_diagram.png',1024000,'image/png','aws_arch_007','https://cdn.example.com/aws_arch_007.png',2,1,7,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('meta_vr_experience.mp4',67108864,'video/mp4','meta_vr_008','https://cdn.example.com/meta_vr_008.mp4',1,1,8,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('nvidia_ai_showcase.jpg',1843200,'image/jpeg','nvidia_ai_009','https://cdn.example.com/nvidia_ai_009.jpg',2,1,9,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('ai_fundamentals_slides.pdf',8388608,'application/pdf','ai_slides_010','https://cdn.example.com/ai_slides_010.pdf',3,1,10,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('blockchain_workshop_code.zip',2097152,'application/zip','blockchain_code_011','https://cdn.example.com/blockchain_code_011.zip',3,1,11,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('iot_demo_video.mp4',45097156,'video/mp4','iot_demo_012','https://cdn.example.com/iot_demo_012.mp4',1,1,12,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('startup_pitch_template.pptx',3145728,'application/vnd.openxmlformats-officedocument.presentationml.presentation','pitch_template_013','https://cdn.example.com/pitch_template_013.pptx',3,1,13,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('expo_tshirt_mockup.jpg',1024000,'image/jpeg','tshirt_mockup_014','https://cdn.example.com/tshirt_mockup_014.jpg',2,1,14,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('tech_mug_product_shot.png',768000,'image/png','mug_product_015','https://cdn.example.com/mug_product_015.png',2,1,15,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('wireless_charger_360.jpg',1536000,'image/jpeg','charger_360_016','https://cdn.example.com/charger_360_016.jpg',2,1,16,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('vip_pass_design.png',512000,'image/png','vip_pass_017','https://cdn.example.com/vip_pass_017.png',2,1,17,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('day1_highlights_video.mp4',73400320,'video/mp4','day1_highlights_018','https://cdn.example.com/day1_highlights_018.mp4',1,1,18,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('networking_photos_gallery.zip',15728640,'application/zip','networking_gallery_019','https://cdn.example.com/networking_gallery_019.zip',3,1,19,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('keynote_recording_full.mp4',157286400,'video/mp4','keynote_full_020','https://cdn.example.com/keynote_full_020.mp4',1,1,20,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('expo_statistics_report.pdf',4194304,'application/pdf','stats_report_021','https://cdn.example.com/stats_report_021.pdf',3,1,21,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('attendee_selfie_001.jpg',2048000,'image/jpeg','selfie_001_022','https://cdn.example.com/selfie_001_022.jpg',2,1,22,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('booth_visit_video.mp4',20971520,'video/mp4','booth_visit_023','https://cdn.example.com/booth_visit_023.mp4',1,1,23,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('workshop_notes.pdf',1048576,'application/pdf','workshop_notes_024','https://cdn.example.com/workshop_notes_024.pdf',3,1,24,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('networking_card_scan.jpg',512000,'image/jpeg','card_scan_025','https://cdn.example.com/card_scan_025.jpg',2,1,25,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('social_media_kit.zip',10485760,'application/zip','social_kit_026','https://cdn.example.com/social_kit_026.zip',3,1,26,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('influencer_content_pack.zip',26214400,'application/zip','influencer_pack_027','https://cdn.example.com/influencer_pack_027.zip',3,1,27,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('press_release_images.zip',8388608,'application/zip','press_images_028','https://cdn.example.com/press_images_028.zip',3,1,28,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('expo_logo_variations.ai',2097152,'application/postscript','logo_variations_029','https://cdn.example.com/logo_variations_029.ai',3,1,29,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('api_documentation.pdf',6291456,'application/pdf','api_docs_030','https://cdn.example.com/api_docs_030.pdf',3,1,30,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('mobile_app_screenshots.zip',12582912,'application/zip','app_screenshots_031','https://cdn.example.com/app_screenshots_031.zip',3,1,31,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('system_architecture.png',2048000,'image/png','sys_arch_032','https://cdn.example.com/sys_arch_032.png',2,1,32,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('database_schema.pdf',3145728,'application/pdf','db_schema_033','https://cdn.example.com/db_schema_033.pdf',3,1,33,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('keynote_audio_only.mp3',31457280,'audio/mpeg','keynote_audio_034','https://cdn.example.com/keynote_audio_034.mp3',3,1,34,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('panel_discussion_audio.wav',52428800,'audio/wav','panel_audio_035','https://cdn.example.com/panel_audio_035.wav',3,1,35,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('expo_background_music.mp3',15728640,'audio/mpeg','bg_music_036','https://cdn.example.com/bg_music_036.mp3',3,1,36,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('large_video_failed.mp4',524288000,'video/mp4','failed_video_037','https://cdn.example.com/failed_video_037.mp4',1,98,37,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('corrupted_image.jpg',0,'image/jpeg','corrupted_img_038','https://cdn.example.com/corrupted_img_038.jpg',2,98,38,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('pending_presentation.pptx',5242880,'application/vnd.openxmlformats-officedocument.presentationml.presentation','pending_pres_039','https://cdn.example.com/pending_pres_039.pptx',3,0,39,'2025-06-09 11:26:34','2025-06-09 11:26:34',1),('uploading_video.mp4',41943040,'video/mp4','uploading_vid_040','https://cdn.example.com/uploading_vid_040.mp4',1,0,40,'2025-06-09 11:26:34','2025-06-09 11:26:34',1);
/*!40000 ALTER TABLE `t_upload_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user`
--

DROP TABLE IF EXISTS `t_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `nickname` varchar(255) DEFAULT NULL,
  `gender` int DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `signature` varchar(255) DEFAULT NULL,
  `point` int DEFAULT NULL,
  `coupon_collector_id` int DEFAULT NULL,
  `permission_id` int DEFAULT NULL,
  `shop_id` int DEFAULT NULL,
  `personal_info_id` int DEFAULT NULL,
  `system_setting_id` int DEFAULT NULL,
  `interest_list_id` int DEFAULT NULL,
  `address_list_id` int DEFAULT NULL,
  `blacklist_id` int DEFAULT NULL,
  `admin_level_id` int DEFAULT NULL,
  `status` int DEFAULT NULL,
  `auth_token` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user`
--

LOCK TABLES `t_user` WRITE;
/*!40000 ALTER TABLE `t_user` DISABLE KEYS */;
INSERT INTO `t_user` VALUES (1,'2025-06-08 16:07:13','2025-06-08 16:07:13',NULL,1,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'27783b0e-6d21-4d49-ad86-d2ab035e3bc9'),(2,'2025-06-08 19:13:54','2025-06-08 19:08:14','Ben',0,'2010-06-08','Japan',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'12bbadf6-c0cc-4d14-9962-6bf2e59c5f44');
/*!40000 ALTER TABLE `t_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user_device`
--

DROP TABLE IF EXISTS `t_user_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user_device` (
  `device_id` varchar(255) DEFAULT NULL,
  `device_type` int DEFAULT NULL COMMENT 'device type, 1 iOS 2 android 3 other',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user_device`
--

LOCK TABLES `t_user_device` WRITE;
/*!40000 ALTER TABLE `t_user_device` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_user_device` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user_login`
--

DROP TABLE IF EXISTS `t_user_login`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user_login` (
  `user_id` bigint DEFAULT NULL,
  `nickname` varchar(255) DEFAULT NULL,
  `is_third_party` int DEFAULT NULL,
  `third_party_type` int DEFAULT NULL,
  `third_party_id` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_third_party_id` (`third_party_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='登录信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user_login`
--

LOCK TABLES `t_user_login` WRITE;
/*!40000 ALTER TABLE `t_user_login` DISABLE KEYS */;
INSERT INTO `t_user_login` VALUES (1,NULL,1,3,'bfa925e2-1c71-45bf-a466-fb01a648dd0d','','1234567890',1,'2025-06-08 16:07:13','2025-06-08 16:07:13'),(2,NULL,1,3,'89c4571f-f9e4-497f-81c6-c641712e7e30','','+817014601880',2,'2025-06-08 19:08:14','2025-06-08 19:08:14');
/*!40000 ALTER TABLE `t_user_login` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user_profile_img`
--

DROP TABLE IF EXISTS `t_user_profile_img`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user_profile_img` (
  `profile_img` varchar(255) DEFAULT NULL,
  `file_name` varchar(255) DEFAULT NULL,
  `status` int DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户头像列表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user_profile_img`
--

LOCK TABLES `t_user_profile_img` WRITE;
/*!40000 ALTER TABLE `t_user_profile_img` DISABLE KEYS */;
INSERT INTO `t_user_profile_img` VALUES ('',NULL,1,1,'2025-06-08 16:07:13','2025-06-08 16:07:13',NULL),('',NULL,1,2,'2025-06-08 19:08:14','2025-06-08 19:08:14',NULL);
/*!40000 ALTER TABLE `t_user_profile_img` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user_reward`
--

DROP TABLE IF EXISTS `t_user_reward`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user_reward` (
  `reward_id` bigint DEFAULT NULL,
  `exchanged_at` datetime DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_user_reward';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user_reward`
--

LOCK TABLES `t_user_reward` WRITE;
/*!40000 ALTER TABLE `t_user_reward` DISABLE KEYS */;
/*!40000 ALTER TABLE `t_user_reward` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_user_score`
--

DROP TABLE IF EXISTS `t_user_score`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_user_score` (
  `total_score` int NOT NULL DEFAULT '0',
  `ranking_score` int NOT NULL DEFAULT '0',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户积分表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_user_score`
--

LOCK TABLES `t_user_score` WRITE;
/*!40000 ALTER TABLE `t_user_score` DISABLE KEYS */;
INSERT INTO `t_user_score` VALUES (500,500,1,'2025-06-08 16:07:13','2025-06-08 16:07:13',1),(500,500,2,'2025-06-08 19:08:14','2025-06-08 19:08:14',2);
/*!40000 ALTER TABLE `t_user_score` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `t_other`
--

DROP TABLE IF EXISTS `t_other`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `t_other` (
  `mark_id` bigint DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `id` bigint NOT NULL AUTO_INCREMENT,
  `update_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `create_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_uni_mark_id` (`mark_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='t_other';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `t_other`
--

LOCK TABLES `t_other` WRITE;
/*!40000 ALTER TABLE `t_other` DISABLE KEYS */;
INSERT INTO `t_other` VALUES (14,'Main Stage Panorama','https://picsum.photos/300/200?random=901','Capture the grandeur of our main stage with the full expo backdrop. Perfect for keynote moments and crowd shots during major presentations.',1,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(4,'Microsoft Innovation Wall','https://picsum.photos/300/200?random=902','Interactive technology wall showcasing Microsoft\'s latest innovations. Great for tech enthusiasts and professional networking photos.',2,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(5,'Google Cloud Architecture','https://picsum.photos/300/200?random=903','Stunning visualization of cloud infrastructure and data flow. Perfect backdrop for technology professionals and cloud computing enthusiasts.',3,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(6,'AWS Solutions Gallery','https://picsum.photos/300/200?random=904','Impressive display of AWS service architecture and global infrastructure. Ideal for developers and cloud architects.',4,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(7,'Meta VR Experience Zone','https://picsum.photos/300/200?random=905','Immersive virtual reality demonstration area with futuristic design elements. Perfect for capturing the future of digital interaction.',5,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(8,'NVIDIA AI Visualization','https://picsum.photos/300/200?random=906','Real-time AI processing visualization with stunning graphics displays. Great for AI researchers and technology enthusiasts.',6,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(9,'Innovation Startup Hub','https://picsum.photos/300/200?random=907','Dynamic startup showcase area with creative displays and entrepreneur networking space. Perfect for capturing the startup energy.',7,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(10,'Research Lab Display','https://picsum.photos/300/200?random=908','Cutting-edge research and development showcase with interactive prototypes. Ideal for innovation and technology documentation.',8,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(11,'Sustainable Tech Corner','https://picsum.photos/300/200?random=909','Eco-friendly technology demonstrations with green innovation displays. Perfect for environmental technology enthusiasts.',9,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(12,'AI Pioneer Showcase','https://picsum.photos/300/200?random=910','Advanced artificial intelligence demonstrations with interactive AI models. Great for AI professionals and researchers.',10,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(13,'Blockchain Innovation Center','https://picsum.photos/300/200?random=911','Cryptocurrency and blockchain technology displays with live transaction visualizations. Perfect for fintech professionals.',11,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(17,'VIP Networking Lounge','https://picsum.photos/300/200?random=912','Elegant networking space with panoramic expo views. Perfect for professional networking photos and business meetings.',12,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(18,'Coffee Culture Corner','https://picsum.photos/300/200?random=913','Artisan coffee setup with expo branding and comfortable seating. Great for casual networking and coffee culture photos.',13,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(22,'AI Learning Lab','https://picsum.photos/300/200?random=914','Hands-on AI workshop space with interactive learning stations. Perfect for educational content and skill development photos.',14,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(23,'Tech Talk Theater','https://picsum.photos/300/200?random=915','Professional presentation space with expert panel setup. Ideal for capturing thought leadership and industry discussions.',15,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(24,'Digital Transformation Hub','https://picsum.photos/300/200?random=916','Modern workspace showcasing digital transformation tools and methodologies. Great for business transformation content.',16,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(25,'Cloud Computing Lab','https://picsum.photos/300/200?random=917','Interactive cloud development environment with multiple workstations. Perfect for developer community and learning photos.',17,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(26,'Coding Bootcamp Studio','https://picsum.photos/300/200?random=918','Intensive coding environment with collaborative workspaces. Great for developer community and education content.',18,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(43,'IoT Innovation Gallery','https://picsum.photos/300/200?random=919','Comprehensive display of Internet of Things devices and smart city solutions. Perfect for technology innovation documentation.',19,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(47,'FinTech Revolution Center','https://picsum.photos/300/200?random=920','Modern financial technology showcase with cryptocurrency and digital payment displays. Ideal for fintech professionals.',20,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(51,'Investor Pitch Arena','https://picsum.photos/300/200?random=921','Professional pitch presentation space with investor panel setup. Perfect for capturing entrepreneurial moments and funding discussions.',21,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(55,'Content Creator Studio','https://picsum.photos/300/200?random=922','Professional content creation space with lighting and recording equipment. Perfect for influencer content and marketing materials.',22,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(57,'Analytics Command Center','https://picsum.photos/300/200?random=923','Data visualization and analytics dashboard display. Great for marketing professionals and data analysts.',23,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(62,'Virtual Collaboration Space','https://picsum.photos/300/200?random=924','Modern remote work demonstration area with virtual meeting setups. Perfect for future of work content and remote team photos.',24,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(64,'Hybrid Office Model','https://picsum.photos/300/200?random=925','Innovative office design showcasing flexible work environments. Ideal for workplace design and productivity content.',25,'2025-06-09 11:26:28','2025-06-09 11:26:28',1),(65,'Smart Office Technology','https://picsum.photos/300/200?random=926','IoT-enabled office environment with intelligent workplace solutions. Great for smart office and productivity technology photos.',26,'2025-06-09 11:26:28','2025-06-09 11:26:28',1);
/*!40000 ALTER TABLE `t_other` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'open_portal_expo'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-09 11:36:14
