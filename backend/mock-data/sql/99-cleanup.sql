-- Cleanup Script for Mock Data
-- This script removes all mock data and resets tables to clean state

USE open_portal_expo;

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear all mock data in reverse dependency order
DELETE FROM t_upload_file WHERE id > 0;
DELETE FROM t_other WHERE id > 0;
DELETE FROM t_bulletin WHERE id > 0;
DELETE FROM t_reward_image WHERE id > 0;
DELETE FROM t_reward WHERE id > 0;
DELETE FROM t_show WHERE id > 0;
DELETE FROM t_shop WHERE id > 0;
DELETE FROM t_restaurant WHERE id > 0;
DELETE FROM t_hotel WHERE id > 0;
DELETE FROM t_exhibitor WHERE id > 0;
DELETE FROM t_ticket WHERE id > 0;
DELETE FROM t_game WHERE id > 0;
DELETE FROM t_mark WHERE id > 0;
DELETE FROM t_area WHERE id > 0;
DELETE FROM t_event_map WHERE id > 0;
DELETE FROM t_event WHERE id > 0;

-- Reset auto-increment counters
ALTER TABLE t_upload_file AUTO_INCREMENT = 1;
ALTER TABLE t_other AUTO_INCREMENT = 1;
ALTER TABLE t_bulletin AUTO_INCREMENT = 1;
ALTER TABLE t_reward_image AUTO_INCREMENT = 1;
ALTER TABLE t_reward AUTO_INCREMENT = 1;
ALTER TABLE t_show AUTO_INCREMENT = 1;
ALTER TABLE t_shop AUTO_INCREMENT = 1;
ALTER TABLE t_restaurant AUTO_INCREMENT = 1;
ALTER TABLE t_hotel AUTO_INCREMENT = 1;
ALTER TABLE t_exhibitor AUTO_INCREMENT = 1;
ALTER TABLE t_ticket AUTO_INCREMENT = 1;
ALTER TABLE t_game AUTO_INCREMENT = 1;
ALTER TABLE t_mark AUTO_INCREMENT = 1;
ALTER TABLE t_area AUTO_INCREMENT = 1;
ALTER TABLE t_event_map AUTO_INCREMENT = 1;
ALTER TABLE t_event AUTO_INCREMENT = 1;

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Verify cleanup
SELECT
    'Events' as table_name, COUNT(*) as record_count FROM t_event
UNION ALL
SELECT
    'Event Maps' as table_name, COUNT(*) as record_count FROM t_event_map
UNION ALL
SELECT
    'Areas' as table_name, COUNT(*) as record_count FROM t_area
UNION ALL
SELECT
    'Marks' as table_name, COUNT(*) as record_count FROM t_mark
UNION ALL
SELECT
    'Games' as table_name, COUNT(*) as record_count FROM t_game
UNION ALL
SELECT
    'Tickets' as table_name, COUNT(*) as record_count FROM t_ticket
UNION ALL
SELECT
    'Exhibitors' as table_name, COUNT(*) as record_count FROM t_exhibitor
UNION ALL
SELECT
    'Hotels' as table_name, COUNT(*) as record_count FROM t_hotel
UNION ALL
SELECT
    'Restaurants' as table_name, COUNT(*) as record_count FROM t_restaurant
UNION ALL
SELECT
    'Shops' as table_name, COUNT(*) as record_count FROM t_shop
UNION ALL
SELECT
    'Shows' as table_name, COUNT(*) as record_count FROM t_show
UNION ALL
SELECT
    'Rewards' as table_name, COUNT(*) as record_count FROM t_reward
UNION ALL
SELECT
    'Reward Images' as table_name, COUNT(*) as record_count FROM t_reward_image
UNION ALL
SELECT
    'Bulletins' as table_name, COUNT(*) as record_count FROM t_bulletin
UNION ALL
SELECT
    'Others' as table_name, COUNT(*) as record_count FROM t_other
UNION ALL
SELECT
    'Upload Files' as table_name, COUNT(*) as record_count FROM t_upload_file;

SELECT 'Cleanup completed successfully!' as status;
