-- Add tags and website_url columns to non-expo marker type tables
-- This script adds tags and website_url support to restaurant, shop, hotel, and other marker types
-- to match the functionality available for exhibitors

USE open_portal_expo;

-- Add tags and website_url columns to t_restaurant table
ALTER TABLE t_restaurant 
ADD COLUMN tags TEXT COMMENT 'Comma-separated tags (e.g., "Japanese,Fine Dining,Vegetarian")',
ADD COLUMN website_url VARCHAR(500) COMMENT 'Website URL for the restaurant';

-- Add tags and website_url columns to t_shop table  
ALTER TABLE t_shop 
ADD COLUMN tags TEXT COMMENT 'Comma-separated tags (e.g., "Electronics,Gadgets,Tech")',
ADD COLUMN website_url VARCHAR(500) COMMENT 'Website URL for the shop';

-- Add tags and website_url columns to t_hotel table
ALTER TABLE t_hotel 
ADD COLUMN tags TEXT COMMENT 'Comma-separated tags (e.g., "Luxury,Business,Conference")',
ADD COLUMN website_url VARCHAR(500) COMMENT 'Website URL for the hotel';

-- Add tags and website_url columns to t_other table
ALTER TABLE t_other 
ADD COLUMN tags TEXT COMMENT 'Comma-separated tags (e.g., "Information,Services,Facilities")',
ADD COLUMN website_url VARCHAR(500) COMMENT 'Website URL for the facility';

-- Populate sample tag data for restaurants
UPDATE t_restaurant SET tags = 'Japanese,Fine Dining,Sushi' WHERE id IN (1, 2);
UPDATE t_restaurant SET tags = 'Italian,Pizza,Casual' WHERE id IN (3, 4);
UPDATE t_restaurant SET tags = 'American,Burgers,Fast Food' WHERE id IN (5, 6);
UPDATE t_restaurant SET tags = 'Chinese,Asian,Authentic' WHERE id IN (7, 8);
UPDATE t_restaurant SET tags = 'Vegetarian,Healthy,Organic' WHERE id IN (9, 10);

-- Populate sample tag data for shops
UPDATE t_shop SET tags = 'Electronics,Gadgets,Tech' WHERE id IN (1, 2);
UPDATE t_shop SET tags = 'Fashion,Clothing,Accessories' WHERE id IN (3, 4);
UPDATE t_shop SET tags = 'Books,Stationery,Educational' WHERE id IN (5, 6);
UPDATE t_shop SET tags = 'Souvenirs,Gifts,Memorabilia' WHERE id IN (7, 8);
UPDATE t_shop SET tags = 'Sports,Outdoor,Equipment' WHERE id IN (9, 10);

-- Populate sample tag data for hotels
UPDATE t_hotel SET tags = 'Luxury,Business,Conference' WHERE id IN (1, 2);
UPDATE t_hotel SET tags = 'Budget,Economy,Basic' WHERE id IN (3, 4);
UPDATE t_hotel SET tags = 'Boutique,Design,Modern' WHERE id IN (5, 6);
UPDATE t_hotel SET tags = 'Family,Kids,Recreation' WHERE id IN (7, 8);
UPDATE t_hotel SET tags = 'Executive,Premium,VIP' WHERE id IN (9, 10);

-- Populate sample tag data for other facilities
UPDATE t_other SET tags = 'Information,Help,Services' WHERE id IN (1, 2);
UPDATE t_other SET tags = 'Restrooms,Facilities,Public' WHERE id IN (3, 4);
UPDATE t_other SET tags = 'ATM,Banking,Financial' WHERE id IN (5, 6);
UPDATE t_other SET tags = 'First Aid,Medical,Emergency' WHERE id IN (7, 8);
UPDATE t_other SET tags = 'Security,Safety,Assistance' WHERE id IN (9, 10);

-- Populate sample website URLs
UPDATE t_restaurant SET website_url = 'https://example-restaurant.com' WHERE id <= 5;
UPDATE t_shop SET website_url = 'https://example-shop.com' WHERE id <= 5;
UPDATE t_hotel SET website_url = 'https://example-hotel.com' WHERE id <= 5;
UPDATE t_other SET website_url = 'https://example-facility.com' WHERE id <= 5;

-- Verify the updates
SELECT 'Restaurant tags verification:' as info;
SELECT id, name, tags, website_url FROM t_restaurant WHERE tags IS NOT NULL LIMIT 5;

SELECT 'Shop tags verification:' as info;
SELECT id, name, tags, website_url FROM t_shop WHERE tags IS NOT NULL LIMIT 5;

SELECT 'Hotel tags verification:' as info;
SELECT id, name, tags, website_url FROM t_hotel WHERE tags IS NOT NULL LIMIT 5;

SELECT 'Other facility tags verification:' as info;
SELECT id, name, tags, website_url FROM t_other WHERE tags IS NOT NULL LIMIT 5;

-- Show tag distribution for each marker type
SELECT 'Restaurant tag distribution:' as info;
SELECT tags, COUNT(*) as count FROM t_restaurant WHERE tags IS NOT NULL GROUP BY tags ORDER BY count DESC;

SELECT 'Shop tag distribution:' as info;
SELECT tags, COUNT(*) as count FROM t_shop WHERE tags IS NOT NULL GROUP BY tags ORDER BY count DESC;

SELECT 'Hotel tag distribution:' as info;
SELECT tags, COUNT(*) as count FROM t_hotel WHERE tags IS NOT NULL GROUP BY tags ORDER BY count DESC;

SELECT 'Other facility tag distribution:' as info;
SELECT tags, COUNT(*) as count FROM t_other WHERE tags IS NOT NULL GROUP BY tags ORDER BY count DESC;
