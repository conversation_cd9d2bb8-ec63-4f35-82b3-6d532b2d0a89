-- Available Games (Minigames Catalog)
-- This script creates the catalog of available minigames that can be assigned to game markers

USE open_portal_expo;

-- Insert available minigames into t_game_available
INSERT INTO t_game_available (name, description, game_url, user_id, create_at, update_at) VALUES
-- Puzzle and Brain Games
('Match Game', 'A fast-paced matching game where players find pairs of characters within a time limit. Test your memory and speed!', 'http://localhost:3000', 1, NOW(), NOW()),
('Memory Challenge', 'Classic memory card game with expo-themed images. Flip cards to find matching pairs and improve your memory skills.', 'https://minigame.example.com/memory', 1, NOW(), NOW()),
('Word Puzzle', 'Solve word puzzles related to technology and innovation. Find hidden words in the grid to earn points.', 'https://minigame.example.com/wordpuzzle', 1, NOW(), NOW()),
('Number Sequence', 'Complete mathematical sequences and number patterns. Perfect for testing logical thinking skills.', 'https://minigame.example.com/numbers', 1, NOW(), NOW()),

-- Trivia and Knowledge Games
('Tech Trivia', 'Answer questions about technology, startups, and innovation. Test your knowledge of the latest tech trends.', 'https://minigame.example.com/trivia', 1, NOW(), NOW()),
('Startup Quiz', 'Learn about entrepreneurship and startup culture through interactive quizzes and challenges.', 'https://minigame.example.com/startup-quiz', 1, NOW(), NOW()),
('Innovation Facts', 'Discover interesting facts about technological innovations and their impact on society.', 'https://minigame.example.com/innovation', 1, NOW(), NOW()),

-- Action and Skill Games
('Reaction Time Test', 'Test your reflexes and reaction speed with this fast-paced clicking game.', 'https://minigame.example.com/reaction', 1, NOW(), NOW()),
('Typing Speed Challenge', 'Improve your typing skills while learning about expo exhibitors and their products.', 'https://minigame.example.com/typing', 1, NOW(), NOW()),
('Pattern Recognition', 'Identify and complete visual patterns to advance through increasingly difficult levels.', 'https://minigame.example.com/patterns', 1, NOW(), NOW()),

-- Social and Interactive Games
('Virtual Networking', 'Practice networking skills in a virtual environment. Connect with AI characters and learn conversation techniques.', 'https://minigame.example.com/networking', 1, NOW(), NOW()),
('Presentation Simulator', 'Practice your presentation skills with this interactive public speaking simulator.', 'https://minigame.example.com/presentation', 1, NOW(), NOW()),

-- Educational Games
('Coding Basics', 'Learn fundamental programming concepts through interactive coding challenges and puzzles.', 'https://minigame.example.com/coding', 1, NOW(), NOW()),
('Digital Marketing Game', 'Create and manage virtual marketing campaigns. Learn about SEO, social media, and analytics.', 'https://minigame.example.com/marketing', 1, NOW(), NOW()),
('Business Strategy', 'Make strategic business decisions in this simulation game. Manage resources and grow your virtual company.', 'https://minigame.example.com/business', 1, NOW(), NOW());

-- Verify the data
SELECT id, name, LEFT(description, 50) as short_description, game_url
FROM t_game_available 
ORDER BY id;
