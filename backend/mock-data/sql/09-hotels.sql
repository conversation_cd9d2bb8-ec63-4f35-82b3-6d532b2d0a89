-- Hotel Information
-- This script creates hotel data linked to appropriate map markers

USE open_portal_expo;

-- Hotels should be linked to marks that represent accommodation or lodging areas
-- Let's identify suitable marks and create hotel entries

INSERT INTO t_hotel (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
-- Use available service marks for hotel partnerships
(1, 'Welcome Plaza Hotel', 'https://picsum.photos/300/200?random=301', 'Luxury hotel located at the main entrance of the expo district. Features executive suites, conference facilities, and premium amenities for business travelers and VIP guests.', 1, NOW(), NOW()),

(2, 'Registration District Inn', 'https://picsum.photos/300/200?random=302', 'Convenient hotel near registration areas. Modern rooms, business center, and shuttle service to all expo venues.', 1, NOW(), NOW()),

(3, 'Information Hub Hotel', 'https://picsum.photos/300/200?random=303', 'Boutique hotel with concierge services and information desk. Perfect for first-time expo visitors and international guests.', 1, NOW(), NOW()),

(16, 'Tech Support Suites', 'https://picsum.photos/300/200?random=304', 'Extended-stay hotel perfect for technical professionals. Each suite includes workspace, high-speed internet, and tech support services.', 1, NOW(), NOW()),

(22, 'Workshop Residence', 'https://picsum.photos/300/200?random=305', 'Educational-focused hotel designed for workshop attendees and trainers. Study rooms, collaboration spaces, and learning-friendly environment.', 1, NOW(), NOW()),

(25, 'Hands-on Learning Lodge', 'https://picsum.photos/300/200?random=306', 'Practical learning hotel with maker spaces and development labs. Perfect for developers and hands-on learners.', 1, NOW(), NOW()),

(26, 'Coding Bootcamp Hotel', 'https://picsum.photos/300/200?random=307', 'Developer-friendly hotel with 24/7 coding spaces, high-speed internet, and collaborative work areas for programming teams.', 1, NOW(), NOW());

-- Verify the hotel data
SELECT h.id, h.name, m.name as mark_location, ev.name as event_name
FROM t_hotel h
JOIN t_mark m ON h.mark_id = m.id
JOIN t_event ev ON m.event_id = ev.id
ORDER BY ev.id, h.id;
