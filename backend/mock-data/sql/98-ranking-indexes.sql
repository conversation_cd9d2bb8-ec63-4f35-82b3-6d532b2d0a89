-- Ranking System Performance Indexes
-- This script creates indexes to optimize MySQL-based ranking queries

USE open_portal_expo;

-- Index on ranking_score for fast sorting (most important for rankings)
-- This index supports ORDER BY ranking_score DESC queries
CREATE INDEX IF NOT EXISTS idx_user_score_ranking_score ON t_user_score(ranking_score DESC);

-- Index on user_id for fast user rank lookups
-- This index supports WHERE user_id = ? queries
CREATE INDEX IF NOT EXISTS idx_user_score_user_id ON t_user_score(user_id);

-- Composite index for complex ranking queries
-- This index supports both sorting and filtering efficiently
CREATE INDEX IF NOT EXISTS idx_user_score_ranking_composite ON t_user_score(ranking_score DESC, user_id);

-- Index to filter out users with zero scores efficiently
-- This index supports WHERE ranking_score > 0 queries
CREATE INDEX IF NOT EXISTS idx_user_score_positive_scores ON t_user_score(ranking_score) WHERE ranking_score > 0;

-- Show the indexes created
SHOW INDEX FROM t_user_score WHERE Key_name LIKE 'idx_user_score%';

-- Analyze the table to update statistics for the query optimizer
ANALYZE TABLE t_user_score;

SELECT 'Ranking system indexes created successfully!' as status;
