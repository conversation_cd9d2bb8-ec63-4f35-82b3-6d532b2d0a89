-- Map Markers for Events
-- This script creates interactive markers on event maps

USE open_portal_expo;

-- Get area IDs for reference (we'll use these in our INSERT statements)
-- Marks for Open Portal Expo 2024 (Event ID 1)

-- Main Exhibition Hall markers
INSERT INTO t_mark (event_id, area_id, name, mark_type, x_axis, y_axis, user_id, create_at, update_at) VALUES
-- Main Entrance area markers
(1, 1, 'Welcome Desk', 2, 150, 150, 1, NOW(), NOW()),
(1, 1, 'Registration', 2, 200, 120, 1, NOW(), NOW()),
(1, 1, 'Information Kiosk', 2, 250, 180, 1, NOW(), NOW()),

-- Tech Pavilion markers
(1, 2, 'Microsoft Booth', 2, 450, 200, 1, NOW(), NOW()),
(1, 2, 'Google Cloud', 2, 550, 180, 1, NOW(), NOW()),
(1, 2, 'Amazon Web Services', 2, 650, 220, 1, NOW(), NOW()),
(1, 2, 'Meta Reality Labs', 2, 500, 300, 1, NOW(), NOW()),
(1, 2, 'NVIDIA AI Corner', 2, 600, 350, 1, NOW(), NOW()),

-- Startup Corner markers
(1, 3, 'TechStart Alpha', 2, 950, 200, 1, NOW(), NOW()),
(1, 3, 'InnovateLab', 2, 1050, 180, 1, NOW(), NOW()),
(1, 3, 'NextGen Solutions', 2, 1150, 220, 1, NOW(), NOW()),
(1, 3, 'AI Pioneers', 2, 1000, 300, 1, NOW(), NOW()),
(1, 3, 'BlockChain Ventures', 2, 1100, 350, 1, NOW(), NOW()),

-- Keynote Stage markers
(1, 4, 'Main Stage', 3, 550, 600, 1, NOW(), NOW()),
(1, 4, 'Speaker Green Room', 2, 400, 550, 1, NOW(), NOW()),
(1, 4, 'Audio/Visual Control', 2, 700, 550, 1, NOW(), NOW()),

-- Networking Lounge markers
(1, 5, 'Business Lounge', 2, 1300, 500, 1, NOW(), NOW()),
(1, 5, 'Coffee Station', 2, 1250, 450, 1, NOW(), NOW()),
(1, 5, 'Charging Station', 2, 1350, 550, 1, NOW(), NOW()),

-- Information Desk markers
(1, 6, 'Help Desk', 2, 175, 350, 1, NOW(), NOW()),
(1, 6, 'Lost & Found', 2, 125, 380, 1, NOW(), NOW()),

-- Conference rooms markers
(1, 7, 'Workshop: AI Fundamentals', 3, 325, 300, 1, NOW(), NOW()),
(1, 8, 'Panel: Future of Tech', 3, 625, 300, 1, NOW(), NOW()),
(1, 9, 'Seminar: Digital Transformation', 3, 925, 300, 1, NOW(), NOW()),

-- Workshop spaces markers
(1, 10, 'Hands-on: Cloud Computing', 1, 350, 575, 1, NOW(), NOW()),
(1, 11, 'Coding Bootcamp', 1, 700, 575, 1, NOW(), NOW()),

-- Food court markers
(1, 12, 'Virtual Cafe Central', 2, 600, 450, 1, NOW(), NOW()),
(1, 12, 'International Cuisine', 2, 500, 500, 1, NOW(), NOW()),
(1, 12, 'Healthy Options', 2, 700, 500, 1, NOW(), NOW()),

-- Coffee corner markers
(1, 13, 'Espresso Bar', 2, 200, 475, 1, NOW(), NOW()),
(1, 13, 'Tea Station', 2, 250, 500, 1, NOW(), NOW()),

-- Demo zone markers
(1, 15, 'VR Experience', 1, 450, 350, 1, NOW(), NOW()),
(1, 15, 'AR Showcase', 1, 550, 400, 1, NOW(), NOW()),
(1, 16, 'Product Demo Theater', 3, 950, 400, 1, NOW(), NOW()),
(1, 17, 'Gaming Tournament', 1, 1350, 350, 1, NOW(), NOW());

-- Marks for Tech Innovation Summit (Event ID 2)
INSERT INTO t_mark (event_id, area_id, name, mark_type, x_axis, y_axis, user_id, create_at, update_at) VALUES
-- AI & Machine Learning area
(2, 18, 'OpenAI Showcase', 2, 450, 300, 1, NOW(), NOW()),
(2, 18, 'TensorFlow Demo', 2, 550, 350, 1, NOW(), NOW()),
(2, 18, 'ML Workshop Station', 1, 600, 400, 1, NOW(), NOW()),

-- Blockchain Hub
(2, 19, 'Ethereum Foundation', 2, 900, 300, 1, NOW(), NOW()),
(2, 19, 'DeFi Innovations', 2, 1000, 250, 1, NOW(), NOW()),
(2, 19, 'NFT Gallery', 2, 1100, 350, 1, NOW(), NOW()),

-- IoT Showcase
(2, 20, 'Smart Home Demo', 1, 1350, 250, 1, NOW(), NOW()),
(2, 20, 'Industrial IoT', 2, 1400, 300, 1, NOW(), NOW()),
(2, 20, 'Connected Devices', 2, 1450, 350, 1, NOW(), NOW());

-- Marks for Startup Showcase (Event ID 3)
INSERT INTO t_mark (event_id, area_id, name, mark_type, x_axis, y_axis, user_id, create_at, update_at) VALUES
-- Early Stage Startups
(3, 23, 'HealthTech Startup', 2, 350, 300, 1, NOW(), NOW()),
(3, 23, 'EdTech Innovation', 2, 450, 350, 1, NOW(), NOW()),
(3, 23, 'FinTech Pioneer', 2, 550, 300, 1, NOW(), NOW()),

-- Growth Stage
(3, 24, 'ScaleUp Success', 2, 800, 300, 1, NOW(), NOW()),
(3, 24, 'Series B Spotlight', 2, 900, 350, 1, NOW(), NOW()),

-- Pitch Competition
(3, 26, 'Pitch Stage', 3, 650, 700, 1, NOW(), NOW()),
(3, 26, 'Judge Panel', 2, 550, 650, 1, NOW(), NOW());

-- Marks for Digital Marketing Expo (Event ID 4)
INSERT INTO t_mark (event_id, area_id, name, mark_type, x_axis, y_axis, user_id, create_at, update_at) VALUES
-- Social Media Zone
(4, 28, 'Instagram Creator Studio', 2, 450, 300, 1, NOW(), NOW()),
(4, 28, 'TikTok Business', 2, 550, 350, 1, NOW(), NOW()),
(4, 28, 'LinkedIn Marketing', 2, 500, 400, 1, NOW(), NOW()),

-- Content Marketing
(4, 29, 'Content Strategy Hub', 2, 950, 350, 1, NOW(), NOW()),
(4, 29, 'Video Production', 1, 1050, 400, 1, NOW(), NOW()),

-- Analytics Corner
(4, 30, 'Google Analytics', 2, 1350, 400, 1, NOW(), NOW()),
(4, 30, 'Data Visualization', 1, 1400, 350, 1, NOW(), NOW());

-- Marks for Future of Work Conference (Event ID 5)
INSERT INTO t_mark (event_id, area_id, name, mark_type, x_axis, y_axis, user_id, create_at, update_at) VALUES
-- Remote Work Tools
(5, 33, 'Zoom Workspace', 2, 400, 300, 1, NOW(), NOW()),
(5, 33, 'Slack Integration', 2, 500, 350, 1, NOW(), NOW()),
(5, 33, 'Microsoft Teams', 2, 600, 300, 1, NOW(), NOW()),

-- Collaboration Space
(5, 34, 'Virtual Whiteboard', 1, 850, 300, 1, NOW(), NOW()),
(5, 34, 'Project Management', 2, 950, 350, 1, NOW(), NOW()),

-- Future Office Demo
(5, 36, 'Hybrid Office Model', 1, 625, 650, 1, NOW(), NOW()),
(5, 36, 'Smart Office Tech', 2, 725, 700, 1, NOW(), NOW());

-- Verify the data
SELECT m.id, m.name, m.mark_type, e.name as event_name, a.name as area_name, m.x_axis, m.y_axis 
FROM t_mark m 
JOIN t_event e ON m.event_id = e.id 
JOIN t_area a ON m.area_id = a.id 
ORDER BY m.event_id, m.area_id, m.id 
LIMIT 20;
