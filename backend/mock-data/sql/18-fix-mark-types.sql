-- Fix Mark Types to Align with Backend MarkTypeEnum
-- This script corrects mark_type values to match the backend enum:
-- 1=GAME, 2=BOOTH, 3=STAGE, 4=RESTAURANT, 5=SHOP, 6=HOTEL, 7=OTHER

USE open_portal_expo;

-- First, let's see what we have currently
SELECT 'Current mark types distribution:' as info;
SELECT mark_type, COUNT(*) as count, 
       CASE mark_type 
         WHEN 1 THEN 'GAME'
         WHEN 2 THEN 'BOOTH' 
         WHEN 3 THEN 'STAGE'
         WHEN 4 THEN 'RESTAURANT'
         WHEN 5 THEN 'SHOP'
         WHEN 6 THEN 'HOTEL'
         WHEN 7 THEN 'OTHER'
         ELSE 'UNKNOWN'
       END as type_name
FROM t_mark 
GROUP BY mark_type 
ORDER BY mark_type;

-- Update marks that should be RESTAURANT (4) based on their linked restaurants
UPDATE t_mark m
INNER JOIN t_restaurant r ON m.id = r.mark_id
SET m.mark_type = 4;

-- Update marks that should be HOTEL (6) based on their linked hotels
UPDATE t_mark m
INNER JOIN t_hotel h ON m.id = h.mark_id
SET m.mark_type = 6;

-- Update marks that should be SHOP (5) based on their linked shops
UPDATE t_mark m
INNER JOIN t_shop s ON m.id = s.mark_id
SET m.mark_type = 5;

-- Update marks that should be OTHER (7) based on their linked other facilities
UPDATE t_mark m
INNER JOIN t_other v ON m.id = v.mark_id
SET m.mark_type = 7;

-- Update specific marks that should be GAME (1) based on their names
UPDATE t_mark SET mark_type = 1 
WHERE name LIKE '%Workshop%' 
   OR name LIKE '%Hands-on%' 
   OR name LIKE '%Coding%'
   OR name LIKE '%VR Experience%'
   OR name LIKE '%AR Showcase%'
   OR name LIKE '%Gaming Tournament%'
   OR name LIKE '%Demo%'
   OR name LIKE '%Interactive%'
   OR name LIKE '%Virtual Whiteboard%'
   OR name LIKE '%Smart Home Demo%'
   OR name LIKE '%ML Workshop%'
   OR name LIKE '%Video Production%'
   OR name LIKE '%Data Visualization%'
   OR name LIKE '%Hybrid Office Model%';

-- Update marks that should be STAGE (3) based on their names
UPDATE t_mark SET mark_type = 3
WHERE name LIKE '%Stage%'
   OR name LIKE '%Theater%'
   OR name LIKE '%Panel%'
   OR name LIKE '%Seminar%'
   OR name LIKE '%Pitch%';

-- Update marks that should be STAGE (3) based on their linked shows
UPDATE t_mark m
INNER JOIN t_show s ON m.id = s.mark_id
SET m.mark_type = 3;

-- All remaining marks with exhibitors should be BOOTH (2)
UPDATE t_mark m
INNER JOIN t_exhibitor e ON m.id = e.mark_id
SET m.mark_type = 2
WHERE m.mark_type NOT IN (4, 5, 6, 7); -- Don't override restaurant, shop, hotel, other

-- Service/infrastructure marks should remain as BOOTH (2) if they don't fit other categories
UPDATE t_mark SET mark_type = 2 
WHERE mark_type NOT IN (1, 3, 4, 5, 6, 7)
  AND (name LIKE '%Desk%' 
       OR name LIKE '%Registration%' 
       OR name LIKE '%Information%'
       OR name LIKE '%Kiosk%'
       OR name LIKE '%Lounge%'
       OR name LIKE '%Station%'
       OR name LIKE '%Control%'
       OR name LIKE '%Green Room%'
       OR name LIKE '%Lost & Found%'
       OR name LIKE '%Help%');

-- Show the updated distribution
SELECT 'Updated mark types distribution:' as info;
SELECT mark_type, COUNT(*) as count, 
       CASE mark_type 
         WHEN 1 THEN 'GAME'
         WHEN 2 THEN 'BOOTH' 
         WHEN 3 THEN 'STAGE'
         WHEN 4 THEN 'RESTAURANT'
         WHEN 5 THEN 'SHOP'
         WHEN 6 THEN 'HOTEL'
         WHEN 7 THEN 'OTHER'
         ELSE 'UNKNOWN'
       END as type_name
FROM t_mark 
GROUP BY mark_type 
ORDER BY mark_type;

-- Verify relationships are correct
SELECT 'Verification - Marks with linked services:' as info;
SELECT 
  m.mark_type,
  CASE m.mark_type 
    WHEN 1 THEN 'GAME'
    WHEN 2 THEN 'BOOTH' 
    WHEN 3 THEN 'STAGE'
    WHEN 4 THEN 'RESTAURANT'
    WHEN 5 THEN 'SHOP'
    WHEN 6 THEN 'HOTEL'
    WHEN 7 THEN 'OTHER'
    ELSE 'UNKNOWN'
  END as type_name,
  COUNT(DISTINCT CASE WHEN e.mark_id IS NOT NULL THEN m.id END) as with_exhibitor,
  COUNT(DISTINCT CASE WHEN r.mark_id IS NOT NULL THEN m.id END) as with_restaurant,
  COUNT(DISTINCT CASE WHEN h.mark_id IS NOT NULL THEN m.id END) as with_hotel,
  COUNT(DISTINCT CASE WHEN s.mark_id IS NOT NULL THEN m.id END) as with_shop,
  COUNT(DISTINCT CASE WHEN sh.mark_id IS NOT NULL THEN m.id END) as with_show,
  COUNT(DISTINCT CASE WHEN v.mark_id IS NOT NULL THEN m.id END) as with_other,
  COUNT(DISTINCT m.id) as total_marks
FROM t_mark m
LEFT JOIN t_exhibitor e ON m.id = e.mark_id
LEFT JOIN t_restaurant r ON m.id = r.mark_id  
LEFT JOIN t_hotel h ON m.id = h.mark_id
LEFT JOIN t_shop s ON m.id = s.mark_id
LEFT JOIN t_show sh ON m.id = sh.mark_id
LEFT JOIN t_other v ON m.id = v.mark_id
GROUP BY m.mark_type
ORDER BY m.mark_type;

-- Show any potential issues (marks with wrong type for their linked services)
SELECT 'Potential issues - marks with mismatched types:' as info;
SELECT m.id, m.name, m.mark_type,
       CASE 
         WHEN r.mark_id IS NOT NULL AND m.mark_type != 4 THEN 'Should be RESTAURANT(4)'
         WHEN h.mark_id IS NOT NULL AND m.mark_type != 6 THEN 'Should be HOTEL(6)'
         WHEN s.mark_id IS NOT NULL AND m.mark_type != 5 THEN 'Should be SHOP(5)'
         WHEN v.mark_id IS NOT NULL AND m.mark_type != 7 THEN 'Should be OTHER(7)'
         WHEN sh.mark_id IS NOT NULL AND m.mark_type != 3 THEN 'Should be STAGE(3)'
         ELSE 'OK'
       END as issue
FROM t_mark m
LEFT JOIN t_restaurant r ON m.id = r.mark_id  
LEFT JOIN t_hotel h ON m.id = h.mark_id
LEFT JOIN t_shop s ON m.id = s.mark_id
LEFT JOIN t_show sh ON m.id = sh.mark_id
LEFT JOIN t_other v ON m.id = v.mark_id
HAVING issue != 'OK'
ORDER BY m.id;
