-- Enhanced Event Data for Open Portal Expo
-- This script updates the existing event with more realistic information
-- and adds additional events for testing

USE open_portal_expo;

-- Insert the main event first
INSERT INTO t_event (name, description, start_date, end_date, user_id, create_at, update_at) VALUES
('Open Portal Expo 2024', 'The premier virtual expo experience featuring cutting-edge technology, innovative startups, and industry leaders. Join us for three days of networking, learning, and discovery in the digital realm.', '2024-06-15 09:00:00', '2024-06-17 18:00:00', 1, NOW(), NOW());

-- Add additional events for testing different scenarios
INSERT INTO t_event (name, description, start_date, end_date, user_id, create_at, update_at) VALUES
('Tech Innovation Summit 2024', 'A focused summit on emerging technologies including AI, blockchain, and IoT. Features keynote speakers from major tech companies and hands-on workshops.', '2024-07-20 08:00:00', '2024-07-22 20:00:00', 1, NOW(), NOW()),
('Startup Showcase Fall 2024', 'Discover the next generation of startups and entrepreneurs. Pitch competitions, investor meetings, and networking opportunities for the startup ecosystem.', '2024-09-10 10:00:00', '2024-09-12 17:00:00', 1, NOW(), NOW()),
('Digital Marketing Expo 2024', 'The ultimate destination for digital marketing professionals. Learn about the latest trends in social media, content marketing, and customer engagement.', '2024-10-05 09:30:00', '2024-10-07 16:30:00', 1, NOW(), NOW()),
('Future of Work Conference', 'Exploring remote work, digital collaboration, and the changing landscape of professional environments. Interactive sessions and virtual networking.', '2024-11-15 08:30:00', '2024-11-16 19:00:00', 1, NOW(), NOW());

-- Verify the updates
SELECT id, name, description, start_date, end_date FROM t_event ORDER BY id;
