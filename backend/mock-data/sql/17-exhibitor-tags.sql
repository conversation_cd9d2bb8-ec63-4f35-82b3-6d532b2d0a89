-- Add tags column to t_exhibitor table and populate sample data
-- This script adds simple comma-separated tags support to exhibitors

USE open_portal_expo;

-- Add tags column to existing t_exhibitor table
ALTER TABLE t_exhibitor ADD COLUMN tags TEXT COMMENT 'Comma-separated tags (e.g., "Copyrighted material,Figure,Original")';

-- Populate sample tag data for existing exhibitors
-- Using the original hardcoded tags from mobile app: 'Copyrighted material', 'Figure', 'Other', 'Original'

-- Tech and Innovation exhibitors
UPDATE t_exhibitor SET tags = 'Original,Figure' WHERE id IN (1, 2, 3);
UPDATE t_exhibitor SET tags = 'Copyrighted material,Original' WHERE id IN (4, 5, 6);
UPDATE t_exhibitor SET tags = 'Figure,Other' WHERE id IN (7, 8, 9);

-- Gaming and Entertainment exhibitors  
UPDATE t_exhibitor SET tags = 'Figure,Copyrighted material' WHERE id IN (10, 11, 12);
UPDATE t_exhibitor SET tags = 'Original,Other' WHERE id IN (13, 14, 15);
UPDATE t_exhibitor SET tags = 'Figure' WHERE id IN (16, 17, 18);

-- Art and Design exhibitors
UPDATE t_exhibitor SET tags = 'Original' WHERE id IN (19, 20, 21);
UPDATE t_exhibitor SET tags = 'Copyrighted material,Figure,Original' WHERE id IN (22, 23, 24);
UPDATE t_exhibitor SET tags = 'Other,Original' WHERE id IN (25, 26, 27);

-- Business and Services exhibitors
UPDATE t_exhibitor SET tags = 'Other' WHERE id IN (28, 29, 30);
UPDATE t_exhibitor SET tags = 'Copyrighted material' WHERE id IN (31, 32, 33);
UPDATE t_exhibitor SET tags = 'Figure,Other,Original' WHERE id IN (34, 35, 36);

-- Food and Hospitality exhibitors
UPDATE t_exhibitor SET tags = 'Other,Original' WHERE id IN (37, 38, 39);
UPDATE t_exhibitor SET tags = 'Copyrighted material,Other' WHERE id IN (40, 41, 42);

-- Education and Training exhibitors
UPDATE t_exhibitor SET tags = 'Original,Copyrighted material' WHERE id IN (43, 44, 45);
UPDATE t_exhibitor SET tags = 'Figure,Original' WHERE id IN (46, 47, 48);

-- Health and Wellness exhibitors
UPDATE t_exhibitor SET tags = 'Other' WHERE id IN (49, 50, 51);

-- Verify the update
SELECT id, name, tags FROM t_exhibitor WHERE tags IS NOT NULL LIMIT 10;

-- Show tag distribution
SELECT 
    tags,
    COUNT(*) as count
FROM t_exhibitor 
WHERE tags IS NOT NULL 
GROUP BY tags 
ORDER BY count DESC;
