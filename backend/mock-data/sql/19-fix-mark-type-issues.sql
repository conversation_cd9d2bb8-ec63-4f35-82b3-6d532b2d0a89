-- Fix specific mark type issues identified in the verification
-- This script corrects the remaining mismatched mark types

USE open_portal_expo;

-- Fix marks that have multiple services - prioritize the primary service type
-- Workshop: AI Fundamentals (ID 22) - should be GAME, not HOTEL
UPDATE t_mark SET mark_type = 1 WHERE id = 22 AND name = 'Workshop: AI Fundamentals';

-- Seminar: Digital Transformation (ID 24) - should be GAME, not RESTAURANT  
UPDATE t_mark SET mark_type = 1 WHERE id = 24 AND name = 'Seminar: Digital Transformation';

-- Judge Panel (ID 51) - should be BOOTH, not OTHER
UPDATE t_mark SET mark_type = 2 WHERE id = 51 AND name = 'Judge Panel';

-- Virtual Whiteboard (ID 62) - should be GAME, not OTHER
UPDATE t_mark SET mark_type = 1 WHERE id = 62 AND name = 'Virtual Whiteboard';

-- Hybrid Office Model (ID 64) - should be GAME, not OTHER
UPDATE t_mark SET mark_type = 1 WHERE id = 64 AND name = 'Hybrid Office Model';

-- Show the final distribution
SELECT 'Final mark types distribution:' as info;
SELECT mark_type, COUNT(*) as count, 
       CASE mark_type 
         WHEN 1 THEN 'GAME'
         WHEN 2 THEN 'BOOTH' 
         WHEN 3 THEN 'STAGE'
         WHEN 4 THEN 'RESTAURANT'
         WHEN 5 THEN 'SHOP'
         WHEN 6 THEN 'HOTEL'
         WHEN 7 THEN 'OTHER'
         ELSE 'UNKNOWN'
       END as type_name
FROM t_mark 
GROUP BY mark_type 
ORDER BY mark_type;

-- Final verification - should show no issues
SELECT 'Final verification - any remaining issues:' as info;
SELECT m.id, m.name, m.mark_type,
       CASE 
         WHEN r.mark_id IS NOT NULL AND m.mark_type != 4 THEN 'Should be RESTAURANT(4)'
         WHEN h.mark_id IS NOT NULL AND m.mark_type != 6 THEN 'Should be HOTEL(6)'
         WHEN s.mark_id IS NOT NULL AND m.mark_type != 5 THEN 'Should be SHOP(5)'
         WHEN v.mark_id IS NOT NULL AND m.mark_type != 7 THEN 'Should be OTHER(7)'
         WHEN sh.mark_id IS NOT NULL AND m.mark_type != 3 THEN 'Should be STAGE(3)'
         ELSE 'OK'
       END as issue
FROM t_mark m
LEFT JOIN t_restaurant r ON m.id = r.mark_id  
LEFT JOIN t_hotel h ON m.id = h.mark_id
LEFT JOIN t_shop s ON m.id = s.mark_id
LEFT JOIN t_show sh ON m.id = sh.mark_id
LEFT JOIN t_other v ON m.id = v.mark_id
HAVING issue != 'OK'
ORDER BY m.id;

-- Show summary of mark types and their associated services
SELECT 'Summary - Mark types and associated services:' as info;
SELECT 
  m.mark_type,
  CASE m.mark_type 
    WHEN 1 THEN 'GAME'
    WHEN 2 THEN 'BOOTH' 
    WHEN 3 THEN 'STAGE'
    WHEN 4 THEN 'RESTAURANT'
    WHEN 5 THEN 'SHOP'
    WHEN 6 THEN 'HOTEL'
    WHEN 7 THEN 'OTHER'
    ELSE 'UNKNOWN'
  END as type_name,
  COUNT(DISTINCT CASE WHEN e.mark_id IS NOT NULL THEN m.id END) as with_exhibitor,
  COUNT(DISTINCT CASE WHEN r.mark_id IS NOT NULL THEN m.id END) as with_restaurant,
  COUNT(DISTINCT CASE WHEN h.mark_id IS NOT NULL THEN m.id END) as with_hotel,
  COUNT(DISTINCT CASE WHEN s.mark_id IS NOT NULL THEN m.id END) as with_shop,
  COUNT(DISTINCT CASE WHEN sh.mark_id IS NOT NULL THEN m.id END) as with_show,
  COUNT(DISTINCT CASE WHEN v.mark_id IS NOT NULL THEN m.id END) as with_other,
  COUNT(DISTINCT m.id) as total_marks
FROM t_mark m
LEFT JOIN t_exhibitor e ON m.id = e.mark_id
LEFT JOIN t_restaurant r ON m.id = r.mark_id  
LEFT JOIN t_hotel h ON m.id = h.mark_id
LEFT JOIN t_shop s ON m.id = s.mark_id
LEFT JOIN t_show sh ON m.id = sh.mark_id
LEFT JOIN t_other v ON m.id = v.mark_id
GROUP BY m.mark_type
ORDER BY m.mark_type;
