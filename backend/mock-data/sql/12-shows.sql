-- Show Information
-- This script creates show/event data linked to presentation and stage markers

USE open_portal_expo;

-- Shows should be linked to stage and presentation marks
-- Note: Show.markId is String type, so we need to convert mark IDs to strings
-- Using only marks that exist and are appropriate for shows

INSERT INTO t_show (mark_id, name, thumbnail_url, description, begin_time, end_time, user_id, create_at, update_at) VALUES
-- Main stage shows for Open Portal Expo 2024
('14', 'Opening Keynote: Future of Technology', 'https://picsum.photos/300/200?random=601', 'Join industry leaders as they unveil the latest technological breakthroughs and discuss the future of innovation. Featuring CEOs from major tech companies.', '2024-06-15 09:00:00', '2024-06-15 10:30:00', 1, NOW(), NOW()),

('14', 'AI Revolution Panel Discussion', 'https://picsum.photos/300/200?random=602', 'Expert panel discussing the impact of artificial intelligence on various industries. Learn about AI ethics, implementation strategies, and future developments.', '2024-06-15 11:00:00', '2024-06-15 12:00:00', 1, NOW(), NOW()),

('14', 'Startup Pitch Competition Finals', 'https://picsum.photos/300/200?random=603', 'Watch the most promising startups compete for funding and recognition. Innovative solutions across various tech sectors will be presented.', '2024-06-15 14:00:00', '2024-06-15 16:00:00', 1, NOW(), NOW()),

('14', 'Closing Ceremony & Awards', 'https://picsum.photos/300/200?random=604', 'Celebration of expo achievements, award presentations, and networking reception. Recognition of outstanding exhibitors and innovations.', '2024-06-17 16:00:00', '2024-06-17 18:00:00', 1, NOW(), NOW()),

-- Workshop and conference room shows
('22', 'AI Fundamentals Workshop', 'https://picsum.photos/300/200?random=605', 'Hands-on workshop covering the basics of artificial intelligence and machine learning. Perfect for beginners and intermediate developers.', '2024-06-15 10:00:00', '2024-06-15 12:00:00', 1, NOW(), NOW()),

('23', 'Future of Tech Panel', 'https://picsum.photos/300/200?random=606', 'Industry experts discuss emerging technologies and their potential impact on society. Interactive Q&A session with audience participation.', '2024-06-15 13:00:00', '2024-06-15 14:30:00', 1, NOW(), NOW()),

('24', 'Digital Transformation Seminar', 'https://picsum.photos/300/200?random=607', 'Learn how organizations are successfully implementing digital transformation strategies. Case studies and best practices from industry leaders.', '2024-06-16 09:00:00', '2024-06-16 11:00:00', 1, NOW(), NOW()),

-- Demo and interactive shows
('25', 'Cloud Computing Hands-on Lab', 'https://picsum.photos/300/200?random=608', 'Interactive laboratory session where participants build and deploy cloud applications. Bring your laptop and learn by doing.', '2024-06-15 14:00:00', '2024-06-15 17:00:00', 1, NOW(), NOW()),

('26', 'Coding Bootcamp Intensive', 'https://picsum.photos/300/200?random=609', 'Intensive coding session covering modern development frameworks and best practices. Suitable for developers of all skill levels.', '2024-06-16 10:00:00', '2024-06-16 15:00:00', 1, NOW(), NOW());

-- Verify the show data
SELECT s.id, s.name, s.begin_time, s.end_time, m.name as mark_location, ev.name as event_name
FROM t_show s
JOIN t_mark m ON s.mark_id = CAST(m.id AS CHAR)
JOIN t_event ev ON m.event_id = ev.id
ORDER BY s.begin_time, s.id;
