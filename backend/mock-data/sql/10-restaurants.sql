-- Restaurant Information
-- This script creates restaurant data linked to food service map markers

USE open_portal_expo;

-- Restaurants should be linked to food service marks
-- Note: Since most marks are already assigned to exhibitors, we'll create restaurants
-- for marks that represent food/service areas or use some exhibitor marks that could also have restaurants

INSERT INTO t_restaurant (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
-- Use some exhibitor marks that could also have restaurant services
(27, 'Virtual Cafe Central', 'https://picsum.photos/300/200?random=401', 'Premium virtual dining experience offering gourmet coffee, artisanal pastries, and light meals. Perfect for networking breaks and casual business meetings.', 1, NOW(), NOW()),

(31, 'Tea Station Premium', 'https://picsum.photos/300/200?random=403', 'Specialty tea house featuring premium loose-leaf teas from around the world. Traditional brewing methods and peaceful atmosphere for mindful networking.', 1, NOW(), NOW()),

-- Use some available service marks for food services
(14, 'Main Stage Catering', 'https://picsum.photos/300/200?random=404', 'Premium catering service for main stage events. Gourmet meals and refreshments for speakers, VIPs, and special event attendees.', 1, NOW(), NOW()),

(23, 'Panel Discussion Cafe', 'https://picsum.photos/300/200?random=405', 'Casual cafe serving light meals and beverages during panel discussions. Perfect for networking breaks between sessions.', 1, NOW(), NOW()),

(24, 'Seminar Snack Bar', 'https://picsum.photos/300/200?random=406', 'Quick service snack bar offering healthy options for seminar attendees. Energy bars, fresh fruit, and specialty drinks.', 1, NOW(), NOW());

-- Verify the restaurant data
SELECT r.id, r.name, m.name as mark_location, ev.name as event_name
FROM t_restaurant r
JOIN t_mark m ON r.mark_id = m.id
JOIN t_event ev ON m.event_id = ev.id
ORDER BY ev.id, r.id;
