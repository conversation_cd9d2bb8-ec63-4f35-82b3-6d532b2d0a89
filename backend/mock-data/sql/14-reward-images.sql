-- Reward Images
-- This script creates additional images for rewards (detail images)

USE open_portal_expo;

-- Add multiple images for each reward to show different angles, colors, etc.
INSERT INTO t_reward_image (reward_id, image_url, user_id, create_at, update_at) VALUES
-- Open Portal Expo 2024 Reward Images
-- T-Shirt (Reward ID 1) - Multiple colors/angles
(1, 'https://picsum.photos/300/200?random=801', 1, NOW(), NOW()),
(1, 'https://picsum.photos/300/200?random=802', 1, NOW(), NOW()),
(1, 'https://picsum.photos/300/200?random=803', 1, NOW(), NOW()),

-- Tech Innovation Mug (Reward ID 2) - Different angles
(2, 'https://picsum.photos/300/200?random=804', 1, NOW(), NOW()),
(2, 'https://picsum.photos/300/200?random=805', 1, NOW(), NOW()),

-- VIP Networking Pass (Reward ID 3) - Digital pass examples
(3, 'https://picsum.photos/300/200?random=806', 1, NOW(), NOW()),
(3, 'https://picsum.photos/300/200?random=807', 1, NOW(), NOW()),

-- Wireless Charging Pad (Reward ID 4) - Product shots
(4, 'https://picsum.photos/300/200?random=808', 1, NOW(), NOW()),
(4, 'https://picsum.photos/300/200?random=809', 1, NOW(), NOW()),
(4, 'https://picsum.photos/300/200?random=810', 1, NOW(), NOW()),

-- Digital Swag Bag (Reward ID 5) - Content preview
(5, 'https://picsum.photos/300/200?random=811', 1, NOW(), NOW()),
(5, 'https://picsum.photos/300/200?random=812', 1, NOW(), NOW()),

-- Expo Hoodie (Reward ID 6) - Different colors/sizes
(6, 'https://picsum.photos/300/200?random=813', 1, NOW(), NOW()),
(6, 'https://picsum.photos/300/200?random=814', 1, NOW(), NOW()),
(6, 'https://picsum.photos/300/200?random=815', 1, NOW(), NOW()),

-- Tech Innovation Summit Reward Images
-- AI Innovation Badge (Reward ID 7) - Digital badge examples
(7, 'https://picsum.photos/300/200?random=816', 1, NOW(), NOW()),
(7, 'https://picsum.photos/300/200?random=817', 1, NOW(), NOW()),

-- Blockchain Starter Kit (Reward ID 8) - Kit contents
(8, 'https://picsum.photos/300/200?random=818', 1, NOW(), NOW()),
(8, 'https://picsum.photos/300/200?random=819', 1, NOW(), NOW()),
(8, 'https://picsum.photos/300/200?random=820', 1, NOW(), NOW()),

-- IoT Sensor Pack (Reward ID 9) - Individual sensors
(9, 'https://picsum.photos/300/200?random=821', 1, NOW(), NOW()),
(9, 'https://picsum.photos/300/200?random=822', 1, NOW(), NOW()),
(9, 'https://picsum.photos/300/200?random=823', 1, NOW(), NOW()),

-- Tech Summit Water Bottle (Reward ID 10) - Features
(10, 'https://picsum.photos/300/200?random=824', 1, NOW(), NOW()),
(10, 'https://picsum.photos/300/200?random=825', 1, NOW(), NOW()),

-- Startup Showcase Reward Images
-- Entrepreneur Toolkit (Reward ID 11) - Toolkit contents
(11, 'https://picsum.photos/300/200?random=826', 1, NOW(), NOW()),
(11, 'https://picsum.photos/300/200?random=827', 1, NOW(), NOW()),

-- Investor Network Access (Reward ID 12) - Platform screenshots
(12, 'https://picsum.photos/300/200?random=828', 1, NOW(), NOW()),
(12, 'https://picsum.photos/300/200?random=829', 1, NOW(), NOW()),

-- Startup Showcase Backpack (Reward ID 13) - Different views
(13, 'https://picsum.photos/300/200?random=830', 1, NOW(), NOW()),
(13, 'https://picsum.photos/300/200?random=831', 1, NOW(), NOW()),
(13, 'https://picsum.photos/300/200?random=832', 1, NOW(), NOW()),

-- Mentorship Session (Reward ID 14) - Mentor profiles
(14, 'https://picsum.photos/300/200?random=833', 1, NOW(), NOW()),
(14, 'https://picsum.photos/300/200?random=834', 1, NOW(), NOW()),

-- Digital Marketing Expo Reward Images
-- Marketing Analytics Course (Reward ID 15) - Course preview
(15, 'https://picsum.photos/300/200?random=835', 1, NOW(), NOW()),
(15, 'https://picsum.photos/300/200?random=836', 1, NOW(), NOW()),

-- Social Media Toolkit (Reward ID 16) - Tool examples
(16, 'https://picsum.photos/300/200?random=837', 1, NOW(), NOW()),
(16, 'https://picsum.photos/300/200?random=838', 1, NOW(), NOW()),
(16, 'https://picsum.photos/300/200?random=839', 1, NOW(), NOW()),

-- Influencer Collaboration Guide (Reward ID 17) - Guide preview
(17, 'https://picsum.photos/300/200?random=840', 1, NOW(), NOW()),
(17, 'https://picsum.photos/300/200?random=841', 1, NOW(), NOW()),

-- Content Creator Camera Kit (Reward ID 18) - Kit components
(18, 'https://picsum.photos/300/200?random=842', 1, NOW(), NOW()),
(18, 'https://picsum.photos/300/200?random=843', 1, NOW(), NOW()),
(18, 'https://picsum.photos/300/200?random=844', 1, NOW(), NOW()),

-- Future of Work Conference Reward Images
-- Remote Work Setup Guide (Reward ID 19) - Setup examples
(19, 'https://picsum.photos/300/200?random=845', 1, NOW(), NOW()),
(19, 'https://picsum.photos/300/200?random=846', 1, NOW(), NOW()),

-- Productivity Software Bundle (Reward ID 20) - Software screenshots
(20, 'https://picsum.photos/300/200?random=847', 1, NOW(), NOW()),
(20, 'https://picsum.photos/300/200?random=848', 1, NOW(), NOW()),
(20, 'https://picsum.photos/300/200?random=849', 1, NOW(), NOW()),

-- Ergonomic Desk Accessories (Reward ID 21) - Individual accessories
(21, 'https://picsum.photos/300/200?random=850', 1, NOW(), NOW()),
(21, 'https://picsum.photos/300/200?random=851', 1, NOW(), NOW()),
(21, 'https://picsum.photos/300/200?random=852', 1, NOW(), NOW()),

-- Digital Wellness Course (Reward ID 22) - Course content
(22, 'https://picsum.photos/300/200?random=853', 1, NOW(), NOW()),
(22, 'https://picsum.photos/300/200?random=854', 1, NOW(), NOW());

-- Verify the reward image data
SELECT ri.id, r.name as reward_name, ri.image_url, ev.name as event_name
FROM t_reward_image ri
JOIN t_reward r ON ri.reward_id = r.id
JOIN t_event ev ON r.event_id = ev.id
ORDER BY r.event_id, ri.reward_id, ri.id
LIMIT 20;
