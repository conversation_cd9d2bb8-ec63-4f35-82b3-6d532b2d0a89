-- Additional Exhibitors for Missing Map Markers
-- This script adds exhibitors for marks that should have business/service providers

USE open_portal_expo;

-- Food and Service Providers
INSERT INTO t_exhibitor (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
-- Food service marks
(27, 'Virtual Cafe Central', 'https://picsum.photos/300/200?random=201', 'Premium virtual dining experience offering gourmet coffee, artisanal pastries, and networking opportunities in a digital environment.', 1, NOW(), NOW()),
(28, 'International Cuisine Hub', 'https://picsum.photos/300/200?random=202', 'Diverse culinary experience featuring authentic dishes from around the world, bringing global flavors to the virtual expo space.', 1, NOW(), NOW()),
(31, 'Tea Station Premium', 'https://picsum.photos/300/200?random=203', 'Specialty tea experience featuring premium loose-leaf teas, traditional brewing methods, and mindful networking spaces.', 1, NOW(), NOW()),

-- Technology and Demo Providers
(35, 'Gaming Arena Pro', 'https://picsum.photos/300/200?random=204', 'Professional esports and gaming company providing competitive gaming experiences, tournaments, and interactive entertainment solutions.', 1, NOW(), NOW()),
(43, 'Industrial IoT Solutions', 'https://picsum.photos/300/200?random=205', 'Enterprise IoT platform provider specializing in industrial automation, predictive maintenance, and smart manufacturing solutions.', 1, NOW(), NOW()),
(47, 'FinTech Pioneer Solutions', 'https://picsum.photos/300/200?random=206', 'Next-generation financial technology startup offering digital banking solutions, cryptocurrency integration, and AI-driven investment advice.', 1, NOW(), NOW()),
(49, 'Series B Growth Partners', 'https://picsum.photos/300/200?random=207', 'Venture capital and growth equity firm specializing in Series B investments, providing strategic guidance and scaling expertise.', 1, NOW(), NOW()),

-- Professional Services
(55, 'Content Strategy Experts', 'https://picsum.photos/300/200?random=208', 'Full-service content marketing agency specializing in strategic content planning, creation, and distribution across multiple digital channels.', 1, NOW(), NOW()),
(57, 'Analytics Pro Consulting', 'https://picsum.photos/300/200?random=209', 'Advanced analytics consulting firm providing deep insights into user behavior, conversion optimization, and ROI measurement strategies.', 1, NOW(), NOW()),

-- Workspace Technology Providers
(59, 'Zoom Enterprise Solutions', 'https://picsum.photos/300/200?random=210', 'Leading video communications platform offering comprehensive remote work solutions including meetings, webinars, and collaborative workspaces.', 1, NOW(), NOW()),
(60, 'Slack Business Solutions', 'https://picsum.photos/300/200?random=211', 'Business communication platform transforming how teams collaborate through channels, integrations, and workflow automation tools.', 1, NOW(), NOW()),
(61, 'Microsoft Teams Pro', 'https://picsum.photos/300/200?random=212', 'Integrated collaboration platform combining chat, video meetings, file storage, and application integration for seamless teamwork.', 1, NOW(), NOW()),
(62, 'Virtual Whiteboard Co', 'https://picsum.photos/300/200?random=213', 'Digital collaboration tools company creating innovative whiteboard solutions for remote teams, brainstorming, and visual project management.', 1, NOW(), NOW()),
(63, 'Project Management Solutions', 'https://picsum.photos/300/200?random=214', 'Advanced project management software providing agile workflows, resource planning, and team collaboration tools for distributed teams.', 1, NOW(), NOW()),
(64, 'Hybrid Office Innovations', 'https://picsum.photos/300/200?random=215', 'Workplace design and technology company creating flexible office solutions that seamlessly blend remote and in-person work experiences.', 1, NOW(), NOW()),
(65, 'Smart Office Technologies', 'https://picsum.photos/300/200?random=216', 'IoT-enabled office solutions creating intelligent workspaces that adapt to employee needs and optimize productivity through data-driven insights.', 1, NOW(), NOW());

-- Verify the additions
SELECT 
    COUNT(*) as total_marks,
    COUNT(e.mark_id) as marks_with_exhibitors,
    COUNT(*) - COUNT(e.mark_id) as marks_without_exhibitors
FROM t_mark m 
LEFT JOIN t_exhibitor e ON m.id = e.mark_id;

-- Show marks that still don't have exhibitors (should be service/infrastructure only)
SELECT m.id, m.name as mark_name, m.mark_type, 'No exhibitor (Service/Infrastructure)' as reason
FROM t_mark m 
LEFT JOIN t_exhibitor e ON m.id = e.mark_id 
WHERE e.mark_id IS NULL 
ORDER BY m.id;
