-- Add booth_number column to t_mark table
-- This script adds booth number support for exhibition markers
-- Booth numbers are typically used for exhibition/booth markers (type 2)
-- but can be used for any marker type where a booth/location identifier is needed

USE open_portal_expo;

-- Add booth_number column to t_mark table
ALTER TABLE t_mark 
ADD COLUMN booth_number VARCHAR(50) NULL 
COMMENT 'Booth number for markers (e.g., A-101, Hall-B-25, Booth-42)';

-- Create index for booth number searches (within event scope)
CREATE INDEX idx_mark_booth_number ON t_mark(event_id, booth_number);

-- Verify the column was added
DESCRIBE t_mark;

-- Show current mark structure with the new column
SELECT 
    m.id,
    m.name,
    m.mark_type,
    m.booth_number,
    e.name as event_name,
    CASE 
        WHEN m.mark_type = 1 THEN 'Game'
        WHEN m.mark_type = 2 THEN 'Exhibition'
        WHEN m.mark_type = 3 THEN 'Event'
        WHEN m.mark_type = 4 THEN 'Restaurant'
        WHEN m.mark_type = 5 THEN 'Shop'
        WHEN m.mark_type = 6 THEN 'Hotel'
        WHEN m.mark_type = 7 THEN 'Other'
        ELSE 'Unknown'
    END as mark_type_name
FROM t_mark m 
JOIN t_event e ON m.event_id = e.id 
ORDER BY m.event_id, m.mark_type, m.id 
LIMIT 10;

-- Optional: Add some sample booth numbers for exhibition markers
-- This is commented out as it should be done manually through the admin interface
/*
UPDATE t_mark 
SET booth_number = CONCAT('A-', LPAD(id, 3, '0'))
WHERE mark_type = 2 AND event_id = 1 AND id BETWEEN 1 AND 20;

UPDATE t_mark 
SET booth_number = CONCAT('B-', LPAD(id - 20, 3, '0'))
WHERE mark_type = 2 AND event_id = 1 AND id BETWEEN 21 AND 40;
*/

-- Show statistics of booth numbers by marker type
SELECT 
    mark_type,
    CASE 
        WHEN mark_type = 1 THEN 'Game'
        WHEN mark_type = 2 THEN 'Exhibition'
        WHEN mark_type = 3 THEN 'Event'
        WHEN mark_type = 4 THEN 'Restaurant'
        WHEN mark_type = 5 THEN 'Shop'
        WHEN mark_type = 6 THEN 'Hotel'
        WHEN mark_type = 7 THEN 'Other'
        ELSE 'Unknown'
    END as mark_type_name,
    COUNT(*) as total_markers,
    COUNT(booth_number) as markers_with_booth_numbers,
    COUNT(*) - COUNT(booth_number) as markers_without_booth_numbers
FROM t_mark 
GROUP BY mark_type 
ORDER BY mark_type;
