-- Event Areas (Floors/Zones)
-- This script creates different areas within events

USE open_portal_expo;

-- Areas for Open Portal Expo 2024 (Event ID 1)
INSERT INTO t_area (event_id, name, width, height, x_axis, y_axis, user_id, create_at, update_at) VALUES
-- Main Exhibition Hall areas
(1, 'Main Entrance', 300, 200, 50, 50, 1, NOW(), NOW()),
(1, 'Tech Pavilion', 400, 300, 400, 100, 1, NOW(), NOW()),
(1, 'Startup Corner', 350, 250, 900, 150, 1, NOW(), NOW()),
(1, 'Keynote Stage', 500, 200, 300, 500, 1, NOW(), NOW()),
(1, 'Networking Lounge', 300, 300, 1200, 400, 1, NOW(), NOW()),
(1, 'Information Desk', 150, 100, 100, 300, 1, NOW(), NOW()),

-- Conference Center areas
(1, 'Conference Room A', 250, 200, 200, 200, 1, NOW(), NOW()),
(1, 'Conference Room B', 250, 200, 500, 200, 1, NOW(), NOW()),
(1, 'Conference Room C', 250, 200, 800, 200, 1, NOW(), NOW()),
(1, 'Workshop Space 1', 300, 250, 200, 450, 1, NOW(), NOW()),
(1, 'Workshop Space 2', 300, 250, 550, 450, 1, NOW(), NOW()),

-- Food Court & Networking areas
(1, 'Food Court Central', 400, 300, 400, 300, 1, NOW(), NOW()),
(1, 'Coffee Corner', 200, 150, 150, 400, 1, NOW(), NOW()),
(1, 'Casual Seating', 300, 200, 900, 350, 1, NOW(), NOW()),
(1, 'VIP Lounge', 250, 200, 1300, 300, 1, NOW(), NOW()),

-- Demo Zone areas
(1, 'Interactive Demos', 350, 300, 300, 250, 1, NOW(), NOW()),
(1, 'Product Showcase', 400, 250, 750, 300, 1, NOW(), NOW()),
(1, 'Gaming Zone', 300, 200, 1200, 250, 1, NOW(), NOW());

-- Areas for Tech Innovation Summit (Event ID 2)
INSERT INTO t_area (event_id, name, width, height, x_axis, y_axis, user_id, create_at, update_at) VALUES
(2, 'AI & Machine Learning', 400, 300, 300, 200, 1, NOW(), NOW()),
(2, 'Blockchain Hub', 350, 250, 800, 200, 1, NOW(), NOW()),
(2, 'IoT Showcase', 300, 300, 1200, 150, 1, NOW(), NOW()),
(2, 'Innovation Lab', 450, 200, 400, 500, 1, NOW(), NOW()),
(2, 'Tech Talks Stage', 500, 250, 200, 750, 1, NOW(), NOW());

-- Areas for Startup Showcase (Event ID 3)
INSERT INTO t_area (event_id, name, width, height, x_axis, y_axis, user_id, create_at, update_at) VALUES
(3, 'Early Stage Startups', 400, 300, 200, 200, 1, NOW(), NOW()),
(3, 'Growth Stage', 350, 300, 650, 200, 1, NOW(), NOW()),
(3, 'Investor Lounge', 300, 200, 1100, 250, 1, NOW(), NOW()),
(3, 'Pitch Competition', 500, 300, 400, 550, 1, NOW(), NOW()),
(3, 'Mentorship Corner', 250, 200, 1000, 600, 1, NOW(), NOW());

-- Areas for Digital Marketing Expo (Event ID 4)
INSERT INTO t_area (event_id, name, width, height, x_axis, y_axis, user_id, create_at, update_at) VALUES
(4, 'Social Media Zone', 350, 300, 300, 200, 1, NOW(), NOW()),
(4, 'Content Marketing', 400, 250, 750, 250, 1, NOW(), NOW()),
(4, 'Analytics Corner', 300, 200, 1200, 300, 1, NOW(), NOW()),
(4, 'Influencer Hub', 350, 250, 400, 550, 1, NOW(), NOW()),
(4, 'Marketing Tools', 400, 300, 800, 500, 1, NOW(), NOW());

-- Areas for Future of Work Conference (Event ID 5)
INSERT INTO t_area (event_id, name, width, height, x_axis, y_axis, user_id, create_at, update_at) VALUES
(5, 'Remote Work Tools', 400, 300, 250, 200, 1, NOW(), NOW()),
(5, 'Collaboration Space', 350, 250, 700, 200, 1, NOW(), NOW()),
(5, 'Digital Wellness', 300, 200, 1100, 250, 1, NOW(), NOW()),
(5, 'Future Office Demo', 450, 300, 400, 500, 1, NOW(), NOW()),
(5, 'HR Tech Zone', 350, 250, 900, 550, 1, NOW(), NOW());

-- Verify the data
SELECT a.id, a.name, e.name as event_name, a.width, a.height, a.x_axis, a.y_axis 
FROM t_area a 
JOIN t_event e ON a.event_id = e.id 
ORDER BY a.event_id, a.id;
