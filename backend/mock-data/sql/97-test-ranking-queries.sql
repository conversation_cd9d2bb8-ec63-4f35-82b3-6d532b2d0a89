-- Test Ranking System Queries
-- This script tests the MySQL-based ranking queries to ensure they work correctly

USE open_portal_expo;

-- Test 1: Get top 10 rankings (equivalent to getTopNRankings)
SELECT 
    'Test 1: Top 10 Rankings' as test_name;

SELECT
    ranked_users.`rank`,
    ranked_users.ranking_score as score,
    u.nickname as nickName
FROM (
    SELECT
        us.user_id,
        us.ranking_score,
        ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as `rank`
    FROM t_user_score us
    WHERE us.ranking_score > 0
    ORDER BY us.ranking_score DESC
    LIMIT 10
) ranked_users
JOIN t_user u ON ranked_users.user_id = u.id
ORDER BY ranked_users.`rank`;

-- Test 2: Get specific user's rank (equivalent to getUserRank)
-- Note: Replace user_id = 1 with an actual user ID from your data
SELECT 
    'Test 2: Specific User Rank (User ID = 1)' as test_name;

SELECT
    ranked_users.`rank`,
    ranked_users.ranking_score as score,
    ranked_users.user_id as userId
FROM (
    SELECT
        us.user_id,
        us.ranking_score,
        ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as `rank`
    FROM t_user_score us
    WHERE us.ranking_score > 0
) ranked_users
WHERE ranked_users.user_id = 1;

-- Test 3: Get rankings around a user (equivalent to getRankingsAroundUser)
-- This gets 2 users before and after user ID 1
SELECT 
    'Test 3: Rankings Around User (User ID = 1, offset = 2)' as test_name;

WITH user_rank AS (
    SELECT 
        us.user_id,
        us.ranking_score,
        ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as rank
    FROM t_user_score us 
    WHERE us.ranking_score > 0
),
target_user AS (
    SELECT rank FROM user_rank WHERE user_id = 1
)
SELECT 
    ur.rank,
    ur.ranking_score as score,
    ur.user_id as userId
FROM user_rank ur, target_user tu
WHERE ur.rank BETWEEN (tu.rank - 2) AND (tu.rank + 2)
AND ur.rank > 0
ORDER BY ur.rank;

-- Test 4: Performance test - show query execution plan
SELECT 
    'Test 4: Query Performance Analysis' as test_name;

EXPLAIN SELECT 
    ranked_users.rank,
    ranked_users.ranking_score as score,
    u.nickname as nickName
FROM (
    SELECT 
        us.user_id,
        us.ranking_score,
        ROW_NUMBER() OVER (ORDER BY us.ranking_score DESC) as rank
    FROM t_user_score us 
    WHERE us.ranking_score > 0
    ORDER BY us.ranking_score DESC 
    LIMIT 100
) ranked_users
JOIN t_user u ON ranked_users.user_id = u.id
ORDER BY ranked_users.rank;

-- Test 5: Data validation - check user score data
SELECT 
    'Test 5: User Score Data Summary' as test_name;

SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN ranking_score > 0 THEN 1 END) as users_with_scores,
    MAX(ranking_score) as highest_score,
    MIN(ranking_score) as lowest_score,
    AVG(ranking_score) as average_score
FROM t_user_score;

-- Test 6: Check for any data inconsistencies
SELECT 
    'Test 6: Data Consistency Check' as test_name;

SELECT 
    'Users with negative ranking scores' as issue,
    COUNT(*) as count
FROM t_user_score 
WHERE ranking_score < 0

UNION ALL

SELECT 
    'Users with ranking_score > total_score' as issue,
    COUNT(*) as count
FROM t_user_score 
WHERE ranking_score > total_score

UNION ALL

SELECT 
    'Users without user records' as issue,
    COUNT(*) as count
FROM t_user_score us
LEFT JOIN t_user u ON us.user_id = u.id
WHERE u.id IS NULL;

SELECT 'All ranking system tests completed!' as status;
