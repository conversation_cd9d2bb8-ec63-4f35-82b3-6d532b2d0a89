-- Shop Information
-- This script creates shop data linked to retail/commercial map markers

USE open_portal_expo;

-- Note: Many marks already have exhibitors, so we'll create shops for exhibitor marks
-- that could also have retail components, plus some service marks

INSERT INTO t_shop (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
-- Tech company shops (these exhibitors can also have retail stores)
(4, 'Microsoft Store', 'https://picsum.photos/300/200?random=501', 'Official Microsoft merchandise and technology store. Latest Surface devices, Xbox accessories, and exclusive expo merchandise available.', 1, NOW(), NOW()),

(5, 'Google Gear Shop', 'https://picsum.photos/300/200?random=502', 'Google branded merchandise and accessories. Pixel devices, Nest products, and limited edition expo collectibles.', 1, NOW(), NOW()),

(6, 'AWS Swag Store', 'https://picsum.photos/300/200?random=503', 'Amazon Web Services merchandise and cloud computing accessories. Developer tools, branded apparel, and tech gadgets.', 1, NOW(), NOW()),

(7, 'Meta Reality Store', 'https://picsum.photos/300/200?random=504', 'Virtual and augmented reality equipment store. VR headsets, AR glasses, and immersive technology accessories.', 1, NOW(), NOW()),

(8, 'NVIDIA Tech Shop', 'https://picsum.photos/300/200?random=505', 'High-performance computing and AI hardware store. Graphics cards, development kits, and professional workstation accessories.', 1, NOW(), NOW()),

-- Startup merchandise shops
(9, 'TechStart Merchandise', 'https://picsum.photos/300/200?random=506', 'Startup-themed merchandise and innovation accessories. Entrepreneur apparel, productivity tools, and motivational items.', 1, NOW(), NOW()),

(10, 'InnovateLab Store', 'https://picsum.photos/300/200?random=507', 'Research and development tools store. Laboratory equipment, prototyping materials, and scientific instruments.', 1, NOW(), NOW()),

(11, 'NextGen Eco Shop', 'https://picsum.photos/300/200?random=508', 'Sustainable technology and eco-friendly products. Solar gadgets, recycled materials, and green technology accessories.', 1, NOW(), NOW()),

(12, 'AI Pioneer Shop', 'https://picsum.photos/300/200?random=509', 'Artificial intelligence and machine learning tools. Development boards, sensors, and AI-powered gadgets.', 1, NOW(), NOW()),

(13, 'Blockchain Boutique', 'https://picsum.photos/300/200?random=510', 'Cryptocurrency and blockchain merchandise. Hardware wallets, mining equipment, and crypto-themed collectibles.', 1, NOW(), NOW());

-- Verify the shop data
SELECT s.id, s.name, m.name as mark_location, ev.name as event_name
FROM t_shop s
JOIN t_mark m ON s.mark_id = m.id
JOIN t_event ev ON m.event_id = ev.id
ORDER BY ev.id, s.id;
