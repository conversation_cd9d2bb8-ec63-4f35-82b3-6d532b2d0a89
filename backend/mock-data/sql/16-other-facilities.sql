-- Other Facilities Information (General Venue Services)
-- This script creates other facility data linked to map markers for various venue services and infrastructure

USE open_portal_expo;

-- Other facilities for Open Portal Expo 2024 (Event ID 1)
-- These include toilets, information booths, first aid, ATMs, and other general venue services

INSERT INTO t_other (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
-- Main stage facilities
(14, 'Information Booth', 'https://picsum.photos/300/200?random=901', 'Main information desk providing event schedules, directions, and general assistance to expo visitors.', 1, NOW(), NOW()),

-- General venue facilities
(4, 'Restroom Facilities', 'https://picsum.photos/300/200?random=902', 'Clean and accessible restroom facilities with baby changing stations and accessibility features.', 1, NOW(), NOW()),

(5, 'First Aid Station', 'https://picsum.photos/300/200?random=903', 'Medical assistance station staffed with qualified first aid personnel for visitor safety and health needs.', 1, NOW(), NOW()),

(6, 'ATM Services', 'https://picsum.photos/300/200?random=904', 'Convenient ATM machines for cash withdrawal and banking services during the expo.', 1, NOW(), NOW()),

(7, 'Lost & Found', 'https://picsum.photos/300/200?random=905', 'Central lost and found service to help visitors recover misplaced items during the event.', 1, NOW(), NOW()),

(8, 'Security Checkpoint', 'https://picsum.photos/300/200?random=906', 'Security screening area ensuring visitor safety and event security protocols.', 1, NOW(), NOW()),

-- Service areas
(9, 'Visitor Services', 'https://picsum.photos/300/200?random=907', 'Comprehensive visitor assistance including registration help, badge printing, and general inquiries.', 1, NOW(), NOW()),

(10, 'Emergency Exit', 'https://picsum.photos/300/200?random=908', 'Clearly marked emergency exit with safety instructions and evacuation procedures.', 1, NOW(), NOW()),

(11, 'Accessibility Services', 'https://picsum.photos/300/200?random=909', 'Wheelchair access, mobility assistance, and other accessibility support services.', 1, NOW(), NOW()),

(12, 'Phone Charging Station', 'https://picsum.photos/300/200?random=910', 'Free device charging stations with multiple connector types for visitor convenience.', 1, NOW(), NOW()),

(13, 'Wi-Fi Help Desk', 'https://picsum.photos/300/200?random=911', 'Technical support for expo Wi-Fi connectivity and internet access assistance.', 1, NOW(), NOW()),

-- Additional facilities
(17, 'Coat Check', 'https://picsum.photos/300/200?random=912', 'Secure coat and bag storage service for visitor convenience during the expo.', 1, NOW(), NOW()),

(18, 'Water Fountain', 'https://picsum.photos/300/200?random=913', 'Drinking water stations with bottle filling capabilities for visitor hydration.', 1, NOW(), NOW()),

-- Support services
(22, 'Translation Services', 'https://picsum.photos/300/200?random=914', 'Multilingual support and translation assistance for international visitors.', 1, NOW(), NOW()),

(23, 'Quiet Zone', 'https://picsum.photos/300/200?random=915', 'Designated quiet area for rest, phone calls, and peaceful breaks from expo activities.', 1, NOW(), NOW()),

(24, 'Business Center', 'https://picsum.photos/300/200?random=916', 'Printing, copying, and basic office services for business visitors and exhibitors.', 1, NOW(), NOW()),

-- Technical support
(25, 'Technical Support', 'https://picsum.photos/300/200?random=917', 'IT and technical assistance for exhibitors and visitors with technology needs.', 1, NOW(), NOW()),

(26, 'Maintenance Office', 'https://picsum.photos/300/200?random=918', 'Facility maintenance and cleaning services coordination center.', 1, NOW(), NOW());

-- Other facilities for additional events (selected key locations)

-- Tech Innovation Summit facilities
INSERT INTO t_other (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(43, 'Event Registration', 'https://picsum.photos/300/200?random=919', 'Registration and check-in desk for event attendees with badge printing and welcome materials.', 1, NOW(), NOW()),

(47, 'Networking Lounge', 'https://picsum.photos/300/200?random=920', 'Comfortable seating area for informal networking and business discussions between sessions.', 1, NOW(), NOW());

-- Startup Showcase facilities
INSERT INTO t_other (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(51, 'Speaker Green Room', 'https://picsum.photos/300/200?random=921', 'Private preparation area for speakers and presenters with refreshments and AV equipment testing.', 1, NOW(), NOW());

-- Digital Marketing Expo facilities
INSERT INTO t_other (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(55, 'Media Center', 'https://picsum.photos/300/200?random=922', 'Press and media workspace with high-speed internet, printing facilities, and interview spaces.', 1, NOW(), NOW()),

(57, 'Storage Area', 'https://picsum.photos/300/200?random=923', 'Secure storage facility for exhibitor materials, promotional items, and personal belongings.', 1, NOW(), NOW());

-- Future of Work Conference facilities
INSERT INTO t_other (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(62, 'Lactation Room', 'https://picsum.photos/300/200?random=924', 'Private, comfortable space for nursing mothers with seating, electrical outlets, and privacy.', 1, NOW(), NOW()),

(64, 'Prayer/Meditation Room', 'https://picsum.photos/300/200?random=925', 'Quiet, multi-faith space for prayer, meditation, and spiritual reflection during the event.', 1, NOW(), NOW()),

(65, 'Recycling Center', 'https://picsum.photos/300/200?random=926', 'Eco-friendly waste sorting and recycling station promoting environmental sustainability.', 1, NOW(), NOW());

-- Verify the other facilities data
SELECT o.id, o.name, o.description, m.name as mark_location, ev.name as event_name
FROM t_other o 
JOIN t_mark m ON o.mark_id = m.id 
JOIN t_event ev ON m.event_id = ev.id 
ORDER BY ev.id, o.id 
LIMIT 10;
