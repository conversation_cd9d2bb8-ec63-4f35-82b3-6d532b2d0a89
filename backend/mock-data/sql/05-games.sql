-- Interactive Games and Missions
-- This script creates gamification elements for user engagement

USE open_portal_expo;

-- Games for Open Portal Expo 2024 (Event ID 1)
INSERT INTO t_game (event_id, name, description, game_url, game_level, mark_id, area_id, user_id, create_at, update_at) VALUES
-- Easy level games (Level 1)
(1, 'Welcome Check-in', 'Check in at the welcome desk to start your expo journey and earn your first points!', 'https://game.example.com/checkin', 1, 1, 1, 1, NOW(), NOW()),
(1, 'Expo Explorer', 'Visit 5 different booths in the Tech Pavilion to complete this exploration mission.', 'https://game.example.com/explorer', 1, 4, 2, 1, NOW(), NOW()),
(1, 'Coffee Break Challenge', 'Find and visit the coffee station for a quick networking break.', 'https://game.example.com/coffee', 1, 21, 13, 1, NOW(), NOW()),
(1, 'Information Hunter', 'Collect information from 3 different information points around the expo.', 'https://game.example.com/info', 1, 3, 1, 1, NOW(), NOW()),

-- Medium level games (Level 2)
(1, 'Tech Trivia Challenge', 'Answer technology-related questions at various booths to test your knowledge.', 'https://game.example.com/trivia', 2, 5, 2, 1, NOW(), NOW()),
(1, 'Startup Pitch Bingo', 'Listen to startup pitches and mark off common buzzwords on your bingo card.', 'https://game.example.com/bingo', 2, 10, 3, 1, NOW(), NOW()),
(1, 'Networking Master', 'Connect with 10 different attendees and collect their virtual business cards.', 'https://game.example.com/networking', 2, 14, 5, 1, NOW(), NOW()),
(1, 'Workshop Warrior', 'Attend 3 different workshops and complete the associated quizzes.', 'https://game.example.com/workshop', 2, 18, 10, 1, NOW(), NOW()),

-- Hard level games (Level 3)
(1, 'Innovation Scavenger Hunt', 'Find hidden QR codes throughout the expo and solve technology puzzles.', 'https://game.example.com/scavenger', 3, 8, 2, 1, NOW(), NOW()),
(1, 'Demo Master Challenge', 'Successfully complete hands-on demos at 5 different technology stations.', 'https://game.example.com/demo', 3, 25, 15, 1, NOW(), NOW()),
(1, 'Keynote Knowledge Test', 'Watch the keynote presentation and answer detailed questions about the content.', 'https://game.example.com/keynote', 3, 12, 4, 1, NOW(), NOW()),
(1, 'VR Experience Champion', 'Complete the full VR experience and achieve the highest score in the virtual challenge.', 'https://game.example.com/vr', 3, 26, 16, 1, NOW(), NOW());

-- Games for Tech Innovation Summit (Event ID 2)
INSERT INTO t_game (event_id, name, description, game_url, game_level, mark_id, area_id, user_id, create_at, update_at) VALUES
-- AI/ML focused games
(2, 'AI Ethics Quiz', 'Test your knowledge of AI ethics and responsible machine learning practices.', 'https://game.example.com/ai-ethics', 2, 29, 18, 1, NOW(), NOW()),
(2, 'Machine Learning Basics', 'Complete an interactive tutorial on machine learning fundamentals.', 'https://game.example.com/ml-basics', 1, 30, 18, 1, NOW(), NOW()),
(2, 'Neural Network Builder', 'Build and train a simple neural network in this interactive challenge.', 'https://game.example.com/neural', 3, 31, 18, 1, NOW(), NOW()),

-- Blockchain games
(2, 'Crypto Wallet Setup', 'Learn how to set up and secure a cryptocurrency wallet safely.', 'https://game.example.com/wallet', 1, 32, 19, 1, NOW(), NOW()),
(2, 'Smart Contract Basics', 'Understand the fundamentals of smart contracts and their applications.', 'https://game.example.com/smart-contract', 2, 33, 19, 1, NOW(), NOW()),
(2, 'DeFi Explorer', 'Navigate the world of decentralized finance and complete trading simulations.', 'https://game.example.com/defi', 3, 34, 19, 1, NOW(), NOW()),

-- IoT games
(2, 'Smart Home Setup', 'Configure a virtual smart home system with various IoT devices.', 'https://game.example.com/smart-home', 2, 35, 20, 1, NOW(), NOW()),
(2, 'IoT Security Challenge', 'Identify and fix security vulnerabilities in IoT device networks.', 'https://game.example.com/iot-security', 3, 36, 20, 1, NOW(), NOW());

-- Games for Startup Showcase (Event ID 3)
INSERT INTO t_game (event_id, name, description, game_url, game_level, mark_id, area_id, user_id, create_at, update_at) VALUES
-- Startup-focused games
(3, 'Pitch Perfect', 'Rate startup pitches and predict which ones will succeed.', 'https://game.example.com/pitch-perfect', 2, 43, 26, 1, NOW(), NOW()),
(3, 'Startup Valuation Game', 'Learn to evaluate startup valuations and investment potential.', 'https://game.example.com/valuation', 3, 38, 23, 1, NOW(), NOW()),
(3, 'Entrepreneur Quiz', 'Test your entrepreneurial knowledge and business acumen.', 'https://game.example.com/entrepreneur', 1, 39, 23, 1, NOW(), NOW()),
(3, 'Investment Simulator', 'Make virtual investments in startups and track your portfolio performance.', 'https://game.example.com/investment', 3, 41, 24, 1, NOW(), NOW());

-- Games for Digital Marketing Expo (Event ID 4)
INSERT INTO t_game (event_id, name, description, game_url, game_level, mark_id, area_id, user_id, create_at, update_at) VALUES
-- Marketing games
(4, 'Social Media Strategy', 'Create a comprehensive social media strategy for a fictional brand.', 'https://game.example.com/social-strategy', 2, 44, 28, 1, NOW(), NOW()),
(4, 'Content Creation Challenge', 'Produce engaging content for different social media platforms.', 'https://game.example.com/content', 2, 48, 29, 1, NOW(), NOW()),
(4, 'Analytics Interpretation', 'Analyze marketing data and draw actionable insights from the results.', 'https://game.example.com/analytics', 3, 50, 30, 1, NOW(), NOW()),
(4, 'Influencer Matching', 'Match brands with appropriate influencers based on audience demographics.', 'https://game.example.com/influencer', 2, 45, 28, 1, NOW(), NOW());

-- Games for Future of Work Conference (Event ID 5)
INSERT INTO t_game (event_id, name, description, game_url, game_level, mark_id, area_id, user_id, create_at, update_at) VALUES
-- Future of work games
(5, 'Remote Team Building', 'Complete virtual team building exercises and collaboration challenges.', 'https://game.example.com/team-building', 1, 52, 33, 1, NOW(), NOW()),
(5, 'Productivity Optimizer', 'Learn and implement productivity techniques for remote work environments.', 'https://game.example.com/productivity', 2, 55, 34, 1, NOW(), NOW()),
(5, 'Digital Wellness Check', 'Assess and improve your digital wellness and work-life balance.', 'https://game.example.com/wellness', 1, 56, 35, 1, NOW(), NOW()),
(5, 'Future Office Designer', 'Design the ideal hybrid office space using virtual planning tools.', 'https://game.example.com/office-design', 3, 57, 36, 1, NOW(), NOW());

-- Verify the data
SELECT g.id, g.name, g.game_level, e.name as event_name, a.name as area_name, m.name as mark_name
FROM t_game g 
JOIN t_event e ON g.event_id = e.id 
LEFT JOIN t_area a ON g.area_id = a.id 
LEFT JOIN t_mark m ON g.mark_id = m.id 
ORDER BY g.event_id, g.game_level, g.id 
LIMIT 15;
