-- Reward Information
-- This script creates reward data for each event with point-based redemption system

USE open_portal_expo;

-- Rewards are linked to events and have point costs, inventory, and purchase tracking
INSERT INTO t_reward (event_id, name, description, detail_description, image_url, point_price, inventory, bought, user_id, create_at, update_at) VALUES
-- Open Portal Expo 2024 Rewards (Event ID 1)
(1, 'Expo 2024 T-Shirt', 'Official Open Portal Expo 2024 commemorative t-shirt', 'High-quality cotton t-shirt featuring the official expo logo and design. Available in multiple sizes and colors. Perfect souvenir to remember your expo experience.', 'https://picsum.photos/300/200?random=701', 50, 100, 15, 1, NOW(), NOW()),

(1, 'Tech Innovation Mug', 'Premium ceramic mug with tech-themed design', 'Durable ceramic mug featuring innovative technology graphics and expo branding. Microwave and dishwasher safe. Great for your morning coffee while coding.', 'https://picsum.photos/300/200?random=702', 30, 150, 8, 1, NOW(), NOW()),

(1, 'VIP Networking Pass', 'Exclusive access to VIP networking events', 'Upgrade your expo experience with access to exclusive VIP networking sessions, premium lounges, and special meet-and-greet opportunities with industry leaders.', 'https://picsum.photos/300/200?random=703', 200, 25, 12, 1, NOW(), NOW()),

(1, 'Wireless Charging Pad', 'Expo-branded wireless charging station', 'Sleek wireless charging pad with expo branding. Compatible with all Qi-enabled devices. Perfect for keeping your devices powered during long expo days.', 'https://picsum.photos/300/200?random=704', 75, 80, 22, 1, NOW(), NOW()),

(1, 'Digital Swag Bag', 'Collection of digital tools and resources', 'Comprehensive digital package including software licenses, e-books, online course access, and exclusive digital content from expo partners.', 'https://picsum.photos/300/200?random=705', 100, 200, 45, 1, NOW(), NOW()),

(1, 'Expo Hoodie', 'Premium hoodie with expo branding', 'Comfortable and stylish hoodie featuring the expo logo. Made from high-quality materials, perfect for casual wear and showing your tech enthusiasm.', 'https://picsum.photos/300/200?random=706', 120, 60, 18, 1, NOW(), NOW()),

-- Tech Innovation Summit 2024 Rewards (Event ID 2)
(2, 'AI Innovation Badge', 'Digital badge recognizing AI expertise', 'Official digital credential recognizing your participation in AI innovation sessions. Shareable on LinkedIn and other professional platforms.', 'https://picsum.photos/300/200?random=707', 40, 500, 67, 1, NOW(), NOW()),

(2, 'Blockchain Starter Kit', 'Educational blockchain development kit', 'Complete starter kit for blockchain development including documentation, sample code, and access to development tools and platforms.', 'https://picsum.photos/300/200?random=708', 150, 50, 8, 1, NOW(), NOW()),

(2, 'IoT Sensor Pack', 'Collection of IoT sensors for prototyping', 'Assorted IoT sensors and development boards perfect for building smart device prototypes. Includes temperature, humidity, motion, and light sensors.', 'https://picsum.photos/300/200?random=709', 180, 30, 5, 1, NOW(), NOW()),

(2, 'Tech Summit Water Bottle', 'Insulated water bottle with smart features', 'Smart water bottle with temperature display and hydration tracking. Features summit branding and connects to mobile app for health monitoring.', 'https://picsum.photos/300/200?random=710', 60, 100, 25, 1, NOW(), NOW()),

-- Startup Showcase Fall 2024 Rewards (Event ID 3)
(3, 'Entrepreneur Toolkit', 'Essential tools for startup founders', 'Comprehensive toolkit including business plan templates, pitch deck guides, legal document templates, and access to startup resources.', 'https://picsum.photos/300/200?random=711', 90, 75, 12, 1, NOW(), NOW()),

(3, 'Investor Network Access', 'One-month access to investor network', 'Premium access to exclusive investor network platform. Connect with VCs, angel investors, and potential partners for your startup.', 'https://picsum.photos/300/200?random=712', 250, 20, 3, 1, NOW(), NOW()),

(3, 'Startup Showcase Backpack', 'Professional backpack for entrepreneurs', 'High-quality backpack designed for busy entrepreneurs. Multiple compartments for laptop, documents, and business essentials.', 'https://picsum.photos/300/200?random=713', 80, 90, 28, 1, NOW(), NOW()),

(3, 'Mentorship Session', 'One-on-one session with industry mentor', 'Exclusive one-hour mentorship session with successful entrepreneurs and industry experts. Personalized advice for your startup journey.', 'https://picsum.photos/300/200?random=714', 300, 15, 7, 1, NOW(), NOW()),

-- Digital Marketing Expo 2024 Rewards (Event ID 4)
(4, 'Marketing Analytics Course', 'Online course on marketing analytics', 'Comprehensive online course covering advanced marketing analytics, data interpretation, and campaign optimization strategies.', 'https://picsum.photos/300/200?random=715', 120, 100, 35, 1, NOW(), NOW()),

(4, 'Social Media Toolkit', 'Complete social media management toolkit', 'Professional toolkit including content templates, scheduling tools, analytics dashboards, and social media strategy guides.', 'https://picsum.photos/300/200?random=716', 85, 80, 19, 1, NOW(), NOW()),

(4, 'Influencer Collaboration Guide', 'Guide to successful influencer partnerships', 'Detailed guide covering influencer identification, outreach strategies, campaign management, and ROI measurement for influencer marketing.', 'https://picsum.photos/300/200?random=717', 65, 120, 42, 1, NOW(), NOW()),

(4, 'Content Creator Camera Kit', 'Professional camera equipment for content', 'Portable camera kit including ring light, tripod, and smartphone accessories. Perfect for creating high-quality marketing content.', 'https://picsum.photos/300/200?random=718', 200, 40, 11, 1, NOW(), NOW()),

-- Future of Work Conference Rewards (Event ID 5)
(5, 'Remote Work Setup Guide', 'Complete guide to remote work optimization', 'Comprehensive guide covering home office setup, productivity tools, time management, and work-life balance strategies for remote workers.', 'https://picsum.photos/300/200?random=719', 45, 150, 38, 1, NOW(), NOW()),

(5, 'Productivity Software Bundle', 'Collection of productivity applications', 'Premium software bundle including project management tools, time tracking apps, collaboration platforms, and productivity enhancement software.', 'https://picsum.photos/300/200?random=720', 160, 60, 14, 1, NOW(), NOW()),

(5, 'Ergonomic Desk Accessories', 'Ergonomic accessories for home office', 'Set of ergonomic accessories including laptop stand, wireless keyboard, ergonomic mouse, and posture support items for healthy remote work.', 'https://picsum.photos/300/200?random=721', 110, 70, 21, 1, NOW(), NOW()),

(5, 'Digital Wellness Course', 'Course on maintaining digital wellness', 'Online course focusing on digital wellness, screen time management, stress reduction, and maintaining mental health in digital work environments.', 'https://picsum.photos/300/200?random=722', 75, 100, 29, 1, NOW(), NOW());

-- Verify the reward data
SELECT r.id, r.name, r.point_price, r.inventory, r.bought, ev.name as event_name
FROM t_reward r
JOIN t_event ev ON r.event_id = ev.id
ORDER BY r.event_id, r.point_price;
