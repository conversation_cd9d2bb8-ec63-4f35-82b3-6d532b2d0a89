-- Exhibitor Information
-- This script creates exhibitor/company data linked to map markers

USE open_portal_expo;

-- Exhibitors for Open Portal Expo 2024 (Event ID 1)
-- Major Tech Companies
INSERT INTO t_exhibitor (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(4, 'Microsoft Corporation', 'https://picsum.photos/300/200?random=101', 'Leading technology company specializing in cloud computing, productivity software, and AI solutions. Discover the latest innovations in Azure, Microsoft 365, and cutting-edge AI technologies.', 1, NOW(), NOW()),
(5, 'Google Cloud', 'https://picsum.photos/300/200?random=102', 'Google''s comprehensive cloud computing platform offering scalable infrastructure, machine learning tools, and data analytics solutions for businesses of all sizes.', 1, NOW(), NOW()),
(6, 'Amazon Web Services', 'https://picsum.photos/300/200?random=103', 'The world''s most comprehensive and broadly adopted cloud platform, offering over 200 fully featured services from data centers globally.', 1, NOW(), NOW()),
(7, 'Meta Reality Labs', 'https://picsum.photos/300/200?random=104', 'Pioneering the future of virtual and augmented reality experiences. Explore immersive technologies that will transform how we connect, work, and play.', 1, NOW(), NOW()),
(8, 'NVIDIA Corporation', 'https://picsum.photos/300/200?random=105', 'Leading AI computing company powering breakthroughs in gaming, data centers, professional visualization, and autonomous vehicles.', 1, NOW(), NOW()),

-- Startup Companies
(9, 'TechStart Alpha', 'https://picsum.photos/300/200?random=106', 'Innovative startup developing next-generation productivity tools powered by artificial intelligence. Our mission is to revolutionize workplace efficiency.', 1, NOW(), NOW()),
(10, 'InnovateLab', 'https://picsum.photos/300/200?random=107', 'Research and development company focused on breakthrough technologies in quantum computing and advanced materials science.', 1, NOW(), NOW()),
(11, 'NextGen Solutions', 'https://picsum.photos/300/200?random=108', 'Sustainable technology company creating eco-friendly solutions for smart cities and renewable energy management systems.', 1, NOW(), NOW()),
(12, 'AI Pioneers', 'https://picsum.photos/300/200?random=109', 'Cutting-edge artificial intelligence company specializing in natural language processing and computer vision applications for healthcare.', 1, NOW(), NOW()),
(13, 'BlockChain Ventures', 'https://picsum.photos/300/200?random=110', 'Blockchain technology company building decentralized applications and smart contract solutions for financial services and supply chain management.', 1, NOW(), NOW()),

-- Service Providers
(19, 'Virtual Cafe Central', 'https://picsum.photos/300/200?random=111', 'Premium virtual dining experience offering gourmet coffee, artisanal pastries, and networking opportunities in a digital environment.', 1, NOW(), NOW()),
(20, 'International Cuisine Hub', 'https://picsum.photos/300/200?random=112', 'Diverse culinary experience featuring authentic dishes from around the world, bringing global flavors to the virtual expo space.', 1, NOW(), NOW()),
(21, 'Espresso Bar Digital', 'https://picsum.photos/300/200?random=113', 'Specialty coffee experience with expert baristas, premium beans, and the perfect environment for casual business conversations.', 1, NOW(), NOW());

-- Exhibitors for Tech Innovation Summit (Event ID 2)
INSERT INTO t_exhibitor (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(29, 'OpenAI Technologies', 'https://picsum.photos/300/200?random=114', 'Leading artificial intelligence research company developing safe and beneficial AI systems. Experience the latest in large language models and AI safety.', 1, NOW(), NOW()),
(30, 'TensorFlow Community', 'https://picsum.photos/300/200?random=115', 'Open-source machine learning platform enabling developers and researchers to build and deploy ML applications at scale across various industries.', 1, NOW(), NOW()),
(32, 'Ethereum Foundation', 'https://picsum.photos/300/200?random=116', 'Non-profit organization supporting Ethereum ecosystem development, promoting decentralized applications and blockchain innovation worldwide.', 1, NOW(), NOW()),
(33, 'DeFi Innovations Inc', 'https://picsum.photos/300/200?random=117', 'Pioneering decentralized finance solutions that democratize access to financial services through blockchain technology and smart contracts.', 1, NOW(), NOW()),
(34, 'NFT Gallery Collective', 'https://picsum.photos/300/200?random=118', 'Digital art marketplace and community platform showcasing unique NFT collections from emerging and established digital artists globally.', 1, NOW(), NOW()),
(36, 'Industrial IoT Solutions', 'https://picsum.photos/300/200?random=119', 'Enterprise IoT platform provider specializing in industrial automation, predictive maintenance, and smart manufacturing solutions.', 1, NOW(), NOW()),
(37, 'Connected Devices Corp', 'https://picsum.photos/300/200?random=120', 'Consumer IoT company creating smart home devices that seamlessly integrate with existing ecosystems for enhanced living experiences.', 1, NOW(), NOW());

-- Exhibitors for Startup Showcase (Event ID 3)
INSERT INTO t_exhibitor (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(38, 'HealthTech Innovations', 'https://picsum.photos/300/200?random=121', 'Revolutionary healthcare startup developing AI-powered diagnostic tools and telemedicine platforms to improve patient outcomes and accessibility.', 1, NOW(), NOW()),
(39, 'EdTech Revolution', 'https://picsum.photos/300/200?random=122', 'Educational technology company creating personalized learning experiences through adaptive AI and immersive virtual reality environments.', 1, NOW(), NOW()),
(40, 'FinTech Pioneer Solutions', 'https://picsum.photos/300/200?random=123', 'Next-generation financial technology startup offering digital banking solutions, cryptocurrency integration, and AI-driven investment advice.', 1, NOW(), NOW()),
(41, 'ScaleUp Success Stories', 'https://picsum.photos/300/200?random=124', 'Growth-stage company providing business intelligence and analytics platforms that help enterprises make data-driven decisions at scale.', 1, NOW(), NOW()),
(42, 'Series B Spotlight', 'https://picsum.photos/300/200?random=125', 'Rapidly growing startup in the logistics sector, revolutionizing supply chain management through autonomous vehicles and AI optimization.', 1, NOW(), NOW());

-- Exhibitors for Digital Marketing Expo (Event ID 4)
INSERT INTO t_exhibitor (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(44, 'Instagram Creator Studio', 'https://picsum.photos/300/200?random=126', 'Official Instagram business solutions helping creators and brands build authentic connections through innovative content creation and analytics tools.', 1, NOW(), NOW()),
(45, 'TikTok for Business', 'https://picsum.photos/300/200?random=127', 'TikTok''s comprehensive business platform offering advertising solutions, creator partnerships, and trend analytics for brand growth.', 1, NOW(), NOW()),
(46, 'LinkedIn Marketing Solutions', 'https://picsum.photos/300/200?random=128', 'Professional networking platform''s marketing division providing B2B advertising, lead generation, and professional audience targeting capabilities.', 1, NOW(), NOW()),
(48, 'Content Strategy Hub', 'https://picsum.photos/300/200?random=129', 'Full-service content marketing agency specializing in strategic content planning, creation, and distribution across multiple digital channels.', 1, NOW(), NOW()),
(50, 'Google Analytics Pro', 'https://picsum.photos/300/200?random=130', 'Advanced web analytics platform providing deep insights into user behavior, conversion tracking, and ROI measurement for digital marketing campaigns.', 1, NOW(), NOW());

-- Exhibitors for Future of Work Conference (Event ID 5)
INSERT INTO t_exhibitor (mark_id, name, thumbnail_url, description, user_id, create_at, update_at) VALUES
(52, 'Zoom Workplace Solutions', 'https://picsum.photos/300/200?random=131', 'Leading video communications platform offering comprehensive remote work solutions including meetings, webinars, and collaborative workspaces.', 1, NOW(), NOW()),
(53, 'Slack Technologies', 'https://picsum.photos/300/200?random=132', 'Business communication platform transforming how teams collaborate through channels, integrations, and workflow automation tools.', 1, NOW(), NOW()),
(54, 'Microsoft Teams Enterprise', 'https://picsum.photos/300/200?random=133', 'Integrated collaboration platform combining chat, video meetings, file storage, and application integration for seamless teamwork.', 1, NOW(), NOW()),
(56, 'Project Management Pro', 'https://picsum.photos/300/200?random=134', 'Advanced project management software providing agile workflows, resource planning, and team collaboration tools for distributed teams.', 1, NOW(), NOW()),
(58, 'Smart Office Technologies', 'https://picsum.photos/300/200?random=135', 'IoT-enabled office solutions creating intelligent workspaces that adapt to employee needs and optimize productivity through data-driven insights.', 1, NOW(), NOW());

-- Verify the data
SELECT e.id, e.name, e.thumbnail_url, m.name as mark_name, ev.name as event_name
FROM t_exhibitor e 
JOIN t_mark m ON e.mark_id = m.id 
JOIN t_event ev ON m.event_id = ev.id 
ORDER BY ev.id, e.id 
LIMIT 15;
