-- Bulletin Information (Event Announcements and News)
-- This script creates bulletin/announcement data for events

USE open_portal_expo;

-- Bulletins for Open Portal Expo 2024 (Event ID 1)
INSERT INTO t_bulletin (event_id, title, brief, page_url, publish_at, published, user_id, create_at, update_at) VALUES
-- Pre-event announcements
(1, 'Welcome to Open Portal Expo 2024!', 'Join us for three days of cutting-edge technology, innovation, and networking opportunities.', 'https://expo.example.com/welcome', '2024-06-10 09:00:00', true, 1, NOW(), NOW()),

(1, 'Keynote Speaker Lineup Announced', 'Featuring CEOs from Microsoft, Google, AWS, and Meta discussing the future of technology.', 'https://expo.example.com/speakers', '2024-06-12 14:00:00', true, 1, NOW(), NOW()),

(1, 'Mobile App Now Available', 'Download the official Open Portal Expo app for interactive maps, schedules, and networking features.', 'https://expo.example.com/app', '2024-06-13 10:00:00', true, 1, NOW(), NOW()),

-- During event announcements
(1, 'Live Stream: AI Revolution Panel', 'Join the live discussion on AI ethics and implementation strategies. Streaming now on main stage.', 'https://expo.example.com/live-ai', '2024-06-15 11:00:00', true, 1, NOW(), NOW()),

(1, 'Startup Pitch Competition Results', 'Congratulations to our top 10 finalists! Final presentations begin at 2 PM in the main auditorium.', 'https://expo.example.com/pitch-results', '2024-06-15 13:30:00', true, 1, NOW(), NOW()),

(1, 'Networking Reception Tonight', 'Join us for cocktails and networking at the VIP Lounge from 6-8 PM. All attendees welcome!', 'https://expo.example.com/reception', '2024-06-15 16:00:00', true, 1, NOW(), NOW()),

(1, 'Workshop Materials Available', 'Download presentation slides and code samples from today\'s workshops in your attendee portal.', 'https://expo.example.com/materials', '2024-06-16 09:00:00', true, 1, NOW(), NOW()),

(1, 'Final Day Highlights', 'Don\'t miss the closing ceremony and awards presentation at 4 PM. Thank you for an amazing expo!', 'https://expo.example.com/closing', '2024-06-17 12:00:00', true, 1, NOW(), NOW()),

-- Post-event follow-up
(1, 'Thank You for Attending!', 'Event recordings, presentation materials, and networking contacts are now available in your portal.', 'https://expo.example.com/thank-you', '2024-06-18 10:00:00', true, 1, NOW(), NOW());

-- Bulletins for Tech Innovation Summit 2024 (Event ID 2)
INSERT INTO t_bulletin (event_id, title, brief, page_url, publish_at, published, user_id, create_at, update_at) VALUES
(2, 'AI Innovation Showcase Opens', 'Explore the latest breakthroughs in artificial intelligence and machine learning technologies.', 'https://summit.example.com/ai-showcase', '2024-07-20 08:00:00', true, 1, NOW(), NOW()),

(2, 'Blockchain Workshop Full', 'The hands-on blockchain development workshop has reached capacity. Waitlist available.', 'https://summit.example.com/blockchain-full', '2024-07-20 10:30:00', true, 1, NOW(), NOW()),

(2, 'IoT Demo Zone Now Open', 'Experience smart city technologies and industrial IoT solutions in our interactive demo area.', 'https://summit.example.com/iot-demo', '2024-07-20 11:00:00', true, 1, NOW(), NOW()),

(2, 'Innovation Awards Ceremony', 'Join us tonight for the recognition of outstanding technological innovations and breakthrough research.', 'https://summit.example.com/awards', '2024-07-21 18:00:00', true, 1, NOW(), NOW());

-- Bulletins for Startup Showcase Fall 2024 (Event ID 3)
INSERT INTO t_bulletin (event_id, title, brief, page_url, publish_at, published, user_id, create_at, update_at) VALUES
(3, 'Investor Meetups Begin', 'One-on-one investor meetings are now underway. Check your schedule for confirmed appointments.', 'https://startup.example.com/investor-meetups', '2024-09-10 09:00:00', true, 1, NOW(), NOW()),

(3, 'Pitch Competition Guidelines', 'Final reminders for pitch presentations: 5 minutes + 3 minutes Q&A. Slides due by noon.', 'https://startup.example.com/pitch-guidelines', '2024-09-10 10:00:00', true, 1, NOW(), NOW()),

(3, 'Mentorship Sessions Available', 'Book your free 30-minute session with industry mentors. Limited slots remaining.', 'https://startup.example.com/mentorship', '2024-09-11 14:00:00', true, 1, NOW(), NOW()),

(3, 'Demo Day Success Stories', 'Celebrating our alumni: 15 startups have raised over $50M since participating in our showcase.', 'https://startup.example.com/success-stories', '2024-09-12 16:00:00', true, 1, NOW(), NOW());

-- Bulletins for Digital Marketing Expo 2024 (Event ID 4)
INSERT INTO t_bulletin (event_id, title, brief, page_url, publish_at, published, user_id, create_at, update_at) VALUES
(4, 'Content Creator Masterclass', 'Learn from top influencers and content creators. Interactive workshop starts in Conference Room A.', 'https://marketing.example.com/creator-masterclass', '2024-10-05 10:00:00', true, 1, NOW(), NOW()),

(4, 'Social Media Trends Report', 'Download our exclusive 2024 social media trends report, featuring insights from 1000+ marketers.', 'https://marketing.example.com/trends-report', '2024-10-05 13:00:00', true, 1, NOW(), NOW()),

(4, 'Analytics Workshop Extended', 'Due to popular demand, we\'ve added an additional analytics deep-dive session tomorrow at 2 PM.', 'https://marketing.example.com/analytics-extended', '2024-10-06 11:00:00', true, 1, NOW(), NOW()),

(4, 'Networking Happy Hour', 'Join fellow marketers for drinks and discussion at the Content Studio from 5-7 PM today.', 'https://marketing.example.com/happy-hour', '2024-10-06 15:00:00', true, 1, NOW(), NOW());

-- Bulletins for Future of Work Conference (Event ID 5)
INSERT INTO t_bulletin (event_id, title, brief, page_url, publish_at, published, user_id, create_at, update_at) VALUES
(5, 'Remote Work Survey Results', 'New research reveals 85% of workers prefer hybrid models. Full report available in the resource center.', 'https://future-work.example.com/survey-results', '2024-11-15 09:00:00', true, 1, NOW(), NOW()),

(5, 'Virtual Office Tour', 'Experience the future of workspaces with our VR office tour. Available at the collaboration hub.', 'https://future-work.example.com/vr-tour', '2024-11-15 11:00:00', true, 1, NOW(), NOW()),

(5, 'Productivity Tools Showcase', 'Discover the latest tools for remote team collaboration and productivity optimization.', 'https://future-work.example.com/tools-showcase', '2024-11-15 14:00:00', true, 1, NOW(), NOW()),

(5, 'Digital Wellness Workshop', 'Learn strategies for maintaining work-life balance in digital environments. Workshop starts at 3 PM.', 'https://future-work.example.com/wellness-workshop', '2024-11-15 15:00:00', true, 1, NOW(), NOW());

-- Verify the bulletin data
SELECT b.id, b.title, b.brief, b.published, ev.name as event_name, b.publish_at
FROM t_bulletin b 
JOIN t_event ev ON b.event_id = ev.id 
ORDER BY b.event_id, b.publish_at 
LIMIT 10;
