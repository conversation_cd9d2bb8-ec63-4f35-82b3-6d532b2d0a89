-- Event Tickets and Access Codes
-- This script creates various ticket types for events

USE open_portal_expo;

-- Tickets for Open Portal Expo 2024 (Event ID 1)
INSERT INTO t_ticket (event_id, name, code, bound_at, checked_at, user_id, create_at, update_at) VALUES
-- VIP Tickets
(1, 'VIP All-Access Pass', 'VIP2024-001', NULL, NULL, 1, NOW(), NOW()),
(1, 'VIP All-Access Pass', 'VIP2024-002', NULL, NULL, 1, NOW(), NOW()),
(1, 'VIP All-Access Pass', 'VIP2024-003', NULL, NULL, 1, NOW(), NOW()),
(1, 'VIP All-Access Pass', 'VIP2024-004', NULL, NULL, 1, NOW(), NOW()),
(1, 'VIP All-Access Pass', 'VIP2024-005', NULL, NULL, 1, NOW(), NOW()),

-- General Admission
(1, 'General Admission', 'GA2024-001', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-002', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-003', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-004', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-005', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-006', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-007', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-008', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-009', NULL, NULL, 1, NOW(), NOW()),
(1, 'General Admission', 'GA2024-010', NULL, NULL, 1, NOW(), NOW()),

-- Speaker Passes
(1, 'Speaker Pass', 'SPK2024-001', NULL, NULL, 1, NOW(), NOW()),
(1, 'Speaker Pass', 'SPK2024-002', NULL, NULL, 1, NOW(), NOW()),
(1, 'Speaker Pass', 'SPK2024-003', NULL, NULL, 1, NOW(), NOW()),
(1, 'Speaker Pass', 'SPK2024-004', NULL, NULL, 1, NOW(), NOW()),

-- Staff Passes
(1, 'Staff Pass', 'STF2024-001', NULL, NULL, 1, NOW(), NOW()),
(1, 'Staff Pass', 'STF2024-002', NULL, NULL, 1, NOW(), NOW()),
(1, 'Staff Pass', 'STF2024-003', NULL, NULL, 1, NOW(), NOW()),

-- Press Passes
(1, 'Press Pass', 'PRS2024-001', NULL, NULL, 1, NOW(), NOW()),
(1, 'Press Pass', 'PRS2024-002', NULL, NULL, 1, NOW(), NOW()),

-- Student Tickets
(1, 'Student Ticket', 'STU2024-001', NULL, NULL, 1, NOW(), NOW()),
(1, 'Student Ticket', 'STU2024-002', NULL, NULL, 1, NOW(), NOW()),
(1, 'Student Ticket', 'STU2024-003', NULL, NULL, 1, NOW(), NOW()),
(1, 'Student Ticket', 'STU2024-004', NULL, NULL, 1, NOW(), NOW()),
(1, 'Student Ticket', 'STU2024-005', NULL, NULL, 1, NOW(), NOW());

-- Tickets for Tech Innovation Summit (Event ID 2)
INSERT INTO t_ticket (event_id, name, code, bound_at, checked_at, user_id, create_at, update_at) VALUES
-- Premium Tickets
(2, 'Premium Access', 'PREM-TIS-001', NULL, NULL, 1, NOW(), NOW()),
(2, 'Premium Access', 'PREM-TIS-002', NULL, NULL, 1, NOW(), NOW()),
(2, 'Premium Access', 'PREM-TIS-003', NULL, NULL, 1, NOW(), NOW()),

-- Standard Tickets
(2, 'Standard Access', 'STD-TIS-001', NULL, NULL, 1, NOW(), NOW()),
(2, 'Standard Access', 'STD-TIS-002', NULL, NULL, 1, NOW(), NOW()),
(2, 'Standard Access', 'STD-TIS-003', NULL, NULL, 1, NOW(), NOW()),
(2, 'Standard Access', 'STD-TIS-004', NULL, NULL, 1, NOW(), NOW()),
(2, 'Standard Access', 'STD-TIS-005', NULL, NULL, 1, NOW(), NOW()),

-- Developer Passes
(2, 'Developer Pass', 'DEV-TIS-001', NULL, NULL, 1, NOW(), NOW()),
(2, 'Developer Pass', 'DEV-TIS-002', NULL, NULL, 1, NOW(), NOW()),
(2, 'Developer Pass', 'DEV-TIS-003', NULL, NULL, 1, NOW(), NOW());

-- Tickets for Startup Showcase (Event ID 3)
INSERT INTO t_ticket (event_id, name, code, bound_at, checked_at, user_id, create_at, update_at) VALUES
-- Investor Passes
(3, 'Investor Pass', 'INV-SS-001', NULL, NULL, 1, NOW(), NOW()),
(3, 'Investor Pass', 'INV-SS-002', NULL, NULL, 1, NOW(), NOW()),
(3, 'Investor Pass', 'INV-SS-003', NULL, NULL, 1, NOW(), NOW()),

-- Entrepreneur Tickets
(3, 'Entrepreneur Ticket', 'ENT-SS-001', NULL, NULL, 1, NOW(), NOW()),
(3, 'Entrepreneur Ticket', 'ENT-SS-002', NULL, NULL, 1, NOW(), NOW()),
(3, 'Entrepreneur Ticket', 'ENT-SS-003', NULL, NULL, 1, NOW(), NOW()),
(3, 'Entrepreneur Ticket', 'ENT-SS-004', NULL, NULL, 1, NOW(), NOW()),
(3, 'Entrepreneur Ticket', 'ENT-SS-005', NULL, NULL, 1, NOW(), NOW()),

-- Mentor Passes
(3, 'Mentor Pass', 'MEN-SS-001', NULL, NULL, 1, NOW(), NOW()),
(3, 'Mentor Pass', 'MEN-SS-002', NULL, NULL, 1, NOW(), NOW()),

-- General Attendee
(3, 'General Attendee', 'GEN-SS-001', NULL, NULL, 1, NOW(), NOW()),
(3, 'General Attendee', 'GEN-SS-002', NULL, NULL, 1, NOW(), NOW()),
(3, 'General Attendee', 'GEN-SS-003', NULL, NULL, 1, NOW(), NOW());

-- Tickets for Digital Marketing Expo (Event ID 4)
INSERT INTO t_ticket (event_id, name, code, bound_at, checked_at, user_id, create_at, update_at) VALUES
-- Marketing Professional
(4, 'Marketing Professional', 'MKT-DME-001', NULL, NULL, 1, NOW(), NOW()),
(4, 'Marketing Professional', 'MKT-DME-002', NULL, NULL, 1, NOW(), NOW()),
(4, 'Marketing Professional', 'MKT-DME-003', NULL, NULL, 1, NOW(), NOW()),
(4, 'Marketing Professional', 'MKT-DME-004', NULL, NULL, 1, NOW(), NOW()),

-- Agency Pass
(4, 'Agency Pass', 'AGY-DME-001', NULL, NULL, 1, NOW(), NOW()),
(4, 'Agency Pass', 'AGY-DME-002', NULL, NULL, 1, NOW(), NOW()),
(4, 'Agency Pass', 'AGY-DME-003', NULL, NULL, 1, NOW(), NOW()),

-- Influencer Pass
(4, 'Influencer Pass', 'INF-DME-001', NULL, NULL, 1, NOW(), NOW()),
(4, 'Influencer Pass', 'INF-DME-002', NULL, NULL, 1, NOW(), NOW()),

-- Brand Manager
(4, 'Brand Manager', 'BRD-DME-001', NULL, NULL, 1, NOW(), NOW()),
(4, 'Brand Manager', 'BRD-DME-002', NULL, NULL, 1, NOW(), NOW()),
(4, 'Brand Manager', 'BRD-DME-003', NULL, NULL, 1, NOW(), NOW());

-- Tickets for Future of Work Conference (Event ID 5)
INSERT INTO t_ticket (event_id, name, code, bound_at, checked_at, user_id, create_at, update_at) VALUES
-- Executive Pass
(5, 'Executive Pass', 'EXE-FOW-001', NULL, NULL, 1, NOW(), NOW()),
(5, 'Executive Pass', 'EXE-FOW-002', NULL, NULL, 1, NOW(), NOW()),
(5, 'Executive Pass', 'EXE-FOW-003', NULL, NULL, 1, NOW(), NOW()),

-- HR Professional
(5, 'HR Professional', 'HRP-FOW-001', NULL, NULL, 1, NOW(), NOW()),
(5, 'HR Professional', 'HRP-FOW-002', NULL, NULL, 1, NOW(), NOW()),
(5, 'HR Professional', 'HRP-FOW-003', NULL, NULL, 1, NOW(), NOW()),
(5, 'HR Professional', 'HRP-FOW-004', NULL, NULL, 1, NOW(), NOW()),

-- Remote Worker
(5, 'Remote Worker', 'RMT-FOW-001', NULL, NULL, 1, NOW(), NOW()),
(5, 'Remote Worker', 'RMT-FOW-002', NULL, NULL, 1, NOW(), NOW()),
(5, 'Remote Worker', 'RMT-FOW-003', NULL, NULL, 1, NOW(), NOW()),
(5, 'Remote Worker', 'RMT-FOW-004', NULL, NULL, 1, NOW(), NOW()),
(5, 'Remote Worker', 'RMT-FOW-005', NULL, NULL, 1, NOW(), NOW()),

-- Team Lead
(5, 'Team Lead', 'TLD-FOW-001', NULL, NULL, 1, NOW(), NOW()),
(5, 'Team Lead', 'TLD-FOW-002', NULL, NULL, 1, NOW(), NOW()),
(5, 'Team Lead', 'TLD-FOW-003', NULL, NULL, 1, NOW(), NOW());

-- Verify the data
SELECT t.id, t.name, t.code, e.name as event_name, t.bound_at, t.checked_at
FROM t_ticket t 
JOIN t_event e ON t.event_id = e.id 
ORDER BY t.event_id, t.name, t.id 
LIMIT 20;
