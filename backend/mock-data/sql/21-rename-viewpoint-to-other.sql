-- Rename Other to Other
-- This script renames the t_other table to t_other to make marker type 7 more generic
-- for expo administrators to mark various venue facilities

USE open_portal_expo;

-- Check if t_other table exists
SELECT 'Checking if t_other table exists...' as info;
SELECT COUNT(*) as other_table_exists 
FROM information_schema.tables 
WHERE table_schema = 'open_portal_expo' 
AND table_name = 't_other';

-- Check current data in t_other
SELECT 'Current data in t_other table:' as info;
SELECT COUNT(*) as total_others FROM t_other;

-- Rename the table from t_other to t_other
SELECT 'Renaming t_other table to t_other...' as info;
RENAME TABLE t_other TO t_other;

-- Verify the rename was successful
SELECT 'Verifying table rename...' as info;
SELECT COUNT(*) as other_table_exists 
FROM information_schema.tables 
WHERE table_schema = 'open_portal_expo' 
AND table_name = 't_other';

-- Check that data was preserved
SELECT 'Data preserved after rename:' as info;
SELECT COUNT(*) as total_others FROM t_other;

-- Show sample data to verify structure is intact
SELECT 'Sample data from t_other table:' as info;
SELECT id, mark_id, name, description, thumbnail_url 
FROM t_other 
LIMIT 5;

-- Verify that existing markers with type 7 still work
SELECT 'Markers with type 7 (now Other):' as info;
SELECT m.id, m.name, m.mark_type, o.name as other_name, o.description
FROM t_mark m
LEFT JOIN t_other o ON m.id = o.mark_id
WHERE m.mark_type = 7
LIMIT 10;

SELECT 'Migration completed successfully!' as info;
