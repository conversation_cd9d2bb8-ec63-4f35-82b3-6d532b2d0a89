#!/bin/bash

# Load All Mock Data Script
# This script loads all mock data into the database in the correct order

set -e  # Exit on any error

# Configuration - Using staging database for local development
DB_HOST="ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com"
DB_PORT="3306"
DB_NAME="open_portal_expo"
DB_USER="ope_backend"
DB_PASS="ope_backend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL file
execute_sql() {
    local sql_file=$1
    local description=$2
    
    print_status "Loading $description..."
    
    if [ ! -f "$sql_file" ]; then
        print_error "SQL file not found: $sql_file"
        exit 1
    fi
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$sql_file" 2>/dev/null; then
        print_success "$description loaded successfully"
    else
        print_error "Failed to load $description"
        exit 1
    fi
}

# Main execution
echo "=========================================="
echo "  Open Portal Expo - Mock Data Loader"
echo "=========================================="
echo ""

print_status "Starting mock data loading process..."
print_status "Database: $DB_NAME on $DB_HOST:$DB_PORT"
echo ""

# Check if MySQL is accessible
print_status "Checking database connection..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
    print_error "Cannot connect to database. Please check your MySQL configuration."
    exit 1
fi
print_success "Database connection verified"
echo ""

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="$(dirname "$SCRIPT_DIR")/sql"

print_status "SQL files directory: $SQL_DIR"
echo ""

# Load data in correct order
execute_sql "$SQL_DIR/01-enhanced-events.sql" "Enhanced Events"
execute_sql "$SQL_DIR/02-event-maps.sql" "Event Maps"
execute_sql "$SQL_DIR/03-areas.sql" "Event Areas"
execute_sql "$SQL_DIR/04-marks.sql" "Map Markers"
execute_sql "$SQL_DIR/05-games.sql" "Interactive Games"
execute_sql "$SQL_DIR/06-exhibitors.sql" "Exhibitor Information"
execute_sql "$SQL_DIR/07-tickets.sql" "Event Tickets"
execute_sql "$SQL_DIR/08-additional-exhibitors.sql" "Additional Exhibitors"
execute_sql "$SQL_DIR/09-hotels.sql" "Hotel Information"
execute_sql "$SQL_DIR/10-restaurants.sql" "Restaurant Information"
execute_sql "$SQL_DIR/11-shops.sql" "Shop Information"
execute_sql "$SQL_DIR/12-shows.sql" "Show Information"
execute_sql "$SQL_DIR/13-rewards.sql" "Reward Information"
execute_sql "$SQL_DIR/14-reward-images.sql" "Reward Images"
execute_sql "$SQL_DIR/15-bulletins.sql" "Bulletin Information"
execute_sql "$SQL_DIR/16-others.sql" "Other Information"
execute_sql "$SQL_DIR/17-upload-files.sql" "Upload File Information"
execute_sql "$SQL_DIR/18-fix-mark-types.sql" "Mark Type Alignment"
execute_sql "$SQL_DIR/19-fix-mark-type-issues.sql" "Mark Type Issue Fixes"
execute_sql "$SQL_DIR/20-final-mark-type-fix.sql" "Final Mark Type Verification"

echo ""
print_status "Verifying data integrity..."

# Verify data was loaded correctly
VERIFICATION_RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    'Events' as table_name, COUNT(*) as record_count FROM t_event
UNION ALL
SELECT 
    'Event Maps' as table_name, COUNT(*) as record_count FROM t_event_map
UNION ALL
SELECT 
    'Areas' as table_name, COUNT(*) as record_count FROM t_area
UNION ALL
SELECT 
    'Marks' as table_name, COUNT(*) as record_count FROM t_mark
UNION ALL
SELECT 
    'Games' as table_name, COUNT(*) as record_count FROM t_game
UNION ALL
SELECT 
    'Tickets' as table_name, COUNT(*) as record_count FROM t_ticket
UNION ALL
SELECT 
    'Exhibitors' as table_name, COUNT(*) as record_count FROM t_exhibitor;
" 2>/dev/null)

echo ""
print_success "Data verification results:"
echo "$VERIFICATION_RESULT"

echo ""
echo "=========================================="
print_success "Mock data loading completed successfully!"
echo "=========================================="
echo ""
print_status "Next steps:"
echo "  1. Test the backend APIs: http://localhost:8082/swagger-ui.html"
echo "  2. Run verification script: ./verify-data.sh"
echo "  3. Test mobile app connectivity"
echo "  4. Install Directus for admin interface"
echo ""
print_warning "Note: Image URLs in the mock data point to placeholder services."
print_warning "Replace with actual CDN URLs for production use."
echo ""
