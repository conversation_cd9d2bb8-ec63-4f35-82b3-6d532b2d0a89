#!/bin/bash

# Verify Mock Data Script
# This script verifies the integrity and completeness of mock data

set -e  # Exit on any error

# Configuration - Using staging database for local development
DB_HOST="ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com"
DB_PORT="3306"
DB_NAME="open_portal_expo"
DB_USER="ope_backend"
DB_PASS="ope_backend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to execute SQL query and return result
execute_query() {
    local query=$1
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "$query" 2>/dev/null
}

echo "=========================================="
echo "  Open Portal Expo - Data Verification"
echo "=========================================="
echo ""

print_status "Starting data verification process..."
print_status "Database: $DB_NAME on $DB_HOST:$DB_PORT"
echo ""

# Check if MySQL is accessible
print_status "Checking database connection..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
    print_error "Cannot connect to database. Please check your MySQL configuration."
    exit 1
fi
print_success "Database connection verified"
echo ""

# 1. Basic record counts
print_status "1. Checking basic record counts..."
RECORD_COUNTS=$(execute_query "
SELECT
    'Events' as table_name, COUNT(*) as record_count FROM t_event
UNION ALL
SELECT
    'Event Maps' as table_name, COUNT(*) as record_count FROM t_event_map
UNION ALL
SELECT
    'Areas' as table_name, COUNT(*) as record_count FROM t_area
UNION ALL
SELECT
    'Marks' as table_name, COUNT(*) as record_count FROM t_mark
UNION ALL
SELECT
    'Games' as table_name, COUNT(*) as record_count FROM t_game
UNION ALL
SELECT
    'Tickets' as table_name, COUNT(*) as record_count FROM t_ticket
UNION ALL
SELECT
    'Exhibitors' as table_name, COUNT(*) as record_count FROM t_exhibitor
UNION ALL
SELECT
    'Hotels' as table_name, COUNT(*) as record_count FROM t_hotel
UNION ALL
SELECT
    'Restaurants' as table_name, COUNT(*) as record_count FROM t_restaurant
UNION ALL
SELECT
    'Shops' as table_name, COUNT(*) as record_count FROM t_shop
UNION ALL
SELECT
    'Shows' as table_name, COUNT(*) as record_count FROM t_show
UNION ALL
SELECT
    'Rewards' as table_name, COUNT(*) as record_count FROM t_reward
UNION ALL
SELECT
    'Reward Images' as table_name, COUNT(*) as record_count FROM t_reward_image
UNION ALL
SELECT
    'Bulletins' as table_name, COUNT(*) as record_count FROM t_bulletin
UNION ALL
SELECT
    'Others' as table_name, COUNT(*) as record_count FROM t_other
UNION ALL
SELECT
    'Upload Files' as table_name, COUNT(*) as record_count FROM t_upload_file;
")
echo "$RECORD_COUNTS"
echo ""

# 2. Check data relationships
print_status "2. Verifying data relationships..."

# Check if all event maps have valid event IDs
ORPHANED_MAPS=$(execute_query "
SELECT COUNT(*)
FROM t_event_map em
LEFT JOIN t_event e ON em.event_id = e.id
WHERE e.id IS NULL;
" | tail -1)
echo "Orphaned event maps: $ORPHANED_MAPS"

# Check if all areas have valid event IDs
ORPHANED_AREAS=$(execute_query "
SELECT COUNT(*)
FROM t_area a
LEFT JOIN t_event e ON a.event_id = e.id
WHERE e.id IS NULL;
" | tail -1)
echo "Orphaned areas: $ORPHANED_AREAS"

# Check if all marks have valid event and area IDs
ORPHANED_MARKS=$(execute_query "
SELECT COUNT(*)
FROM t_mark m
LEFT JOIN t_event e ON m.event_id = e.id
LEFT JOIN t_area a ON m.area_id = a.id
WHERE e.id IS NULL OR a.id IS NULL;
" | tail -1)
echo "Orphaned marks: $ORPHANED_MARKS"

# Check if all games have valid event IDs
ORPHANED_GAMES=$(execute_query "
SELECT COUNT(*)
FROM t_game g
LEFT JOIN t_event e ON g.event_id = e.id
WHERE e.id IS NULL;
" | tail -1)
echo "Orphaned games: $ORPHANED_GAMES"

# Check if all exhibitors have valid mark IDs
ORPHANED_EXHIBITORS=$(execute_query "
SELECT COUNT(*)
FROM t_exhibitor ex
LEFT JOIN t_mark m ON ex.mark_id = m.id
WHERE m.id IS NULL;
" | tail -1)
echo "Orphaned exhibitors: $ORPHANED_EXHIBITORS"

# Check if all tickets have valid event IDs
ORPHANED_TICKETS=$(execute_query "
SELECT COUNT(*)
FROM t_ticket t
LEFT JOIN t_event e ON t.event_id = e.id
WHERE e.id IS NULL;
" | tail -1)
echo "Orphaned tickets: $ORPHANED_TICKETS"
echo ""

# 3. Check data distribution per event
print_status "3. Checking data distribution per event..."
EVENT_DISTRIBUTION=$(execute_query "
SELECT 
    e.id as event_id,
    e.name as event_name,
    COUNT(DISTINCT em.id) as maps,
    COUNT(DISTINCT a.id) as areas,
    COUNT(DISTINCT m.id) as marks,
    COUNT(DISTINCT g.id) as games,
    COUNT(DISTINCT t.id) as tickets
FROM t_event e
LEFT JOIN t_event_map em ON e.id = em.event_id
LEFT JOIN t_area a ON e.id = a.event_id
LEFT JOIN t_mark m ON e.id = m.event_id
LEFT JOIN t_game g ON e.id = g.event_id
LEFT JOIN t_ticket t ON e.id = t.event_id
GROUP BY e.id, e.name
ORDER BY e.id;
")
echo "$EVENT_DISTRIBUTION"
echo ""

# 4. Check for data quality issues
print_status "4. Checking data quality..."

# Check for events with missing dates
EVENTS_MISSING_DATES=$(execute_query "
SELECT COUNT(*)
FROM t_event
WHERE start_date IS NULL OR end_date IS NULL;
" | tail -1)
echo "Events with missing dates: $EVENTS_MISSING_DATES"

# Check for marks with invalid coordinates
INVALID_COORDINATES=$(execute_query "
SELECT COUNT(*)
FROM t_mark
WHERE x_axis < 0 OR y_axis < 0 OR x_axis > 2000 OR y_axis > 1200;
" | tail -1)
echo "Marks with invalid coordinates: $INVALID_COORDINATES"

# Check for duplicate ticket codes
DUPLICATE_TICKETS=$(execute_query "
SELECT COUNT(*)
FROM (
    SELECT code, event_id, COUNT(*) as cnt
    FROM t_ticket
    GROUP BY code, event_id
    HAVING cnt > 1
) as duplicates;
" | tail -1)
echo "Duplicate ticket codes: $DUPLICATE_TICKETS"
echo ""

# 5. Sample data preview
print_status "5. Sample data preview..."
print_status "Recent events:"
RECENT_EVENTS=$(execute_query "
SELECT id, name, start_date, end_date 
FROM t_event 
ORDER BY id 
LIMIT 3;
")
echo "$RECENT_EVENTS"
echo ""

print_status "Sample exhibitors:"
SAMPLE_EXHIBITORS=$(execute_query "
SELECT e.name as exhibitor, m.name as mark_location, ev.name as event_name
FROM t_exhibitor e
JOIN t_mark m ON e.mark_id = m.id
JOIN t_event ev ON m.event_id = ev.id
ORDER BY ev.id, e.id
LIMIT 5;
")
echo "$SAMPLE_EXHIBITORS"
echo ""

# 6. API endpoint testing suggestions
print_status "6. API Testing Suggestions..."
echo "Test these endpoints with the loaded data:"
echo ""
echo "Event Information:"
echo "  POST http://localhost:8082/event/v1/event"
echo "  Body: {\"eventId\": 1}"
echo ""
echo "Map Markers:"
echo "  GET http://localhost:8082/mark/v1/marks?eventId=1"
echo "  (Requires authentication token)"
echo ""
echo "Admin Event List:"
echo "  GET http://localhost:8083/admin/event/page?pageNum=1&pageSize=10"
echo ""

# Summary
echo ""
echo "=========================================="
if [[ "$ORPHANED_MAPS" == "0" && "$ORPHANED_AREAS" == "0" && "$ORPHANED_MARKS" == "0" && 
      "$ORPHANED_GAMES" == "0" && "$ORPHANED_EXHIBITORS" == "0" && "$ORPHANED_TICKETS" == "0" && 
      "$DUPLICATE_TICKETS" == "0" ]]; then
    print_success "Data verification completed successfully!"
    print_success "All relationships are valid and no data quality issues found."
else
    print_warning "Data verification completed with some issues."
    print_warning "Please review the orphaned records and data quality issues above."
fi
echo "=========================================="
echo ""
