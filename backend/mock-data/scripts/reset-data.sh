#!/bin/bash

# Reset Mock Data Script
# This script removes all mock data and resets the database to clean state

set -e  # Exit on any error

# Configuration - Using staging database for local development
DB_HOST="ls-e63163dcb9191ad13b29fb954f8114637c00c469.c10y0wcs8pzy.ap-northeast-1.rds.amazonaws.com"
DB_PORT="3306"
DB_NAME="open_portal_expo"
DB_USER="ope_backend"
DB_PASS="ope_backend"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Confirmation prompt
echo "=========================================="
echo "  Open Portal Expo - Data Reset Tool"
echo "=========================================="
echo ""
print_warning "This will DELETE ALL mock data from the database!"
print_warning "Database: $DB_NAME on $DB_HOST:$DB_PORT"
echo ""
read -p "Are you sure you want to continue? (yes/no): " -r
echo ""

if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    print_status "Operation cancelled by user."
    exit 0
fi

print_status "Starting data reset process..."

# Check if MySQL is accessible
print_status "Checking database connection..."
if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
    print_error "Cannot connect to database. Please check your MySQL configuration."
    exit 1
fi
print_success "Database connection verified"

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SQL_DIR="$(dirname "$SCRIPT_DIR")/sql"
CLEANUP_SQL="$SQL_DIR/99-cleanup.sql"

print_status "Executing cleanup script..."

if [ ! -f "$CLEANUP_SQL" ]; then
    print_error "Cleanup SQL file not found: $CLEANUP_SQL"
    exit 1
fi

if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < "$CLEANUP_SQL" 2>/dev/null; then
    print_success "Database cleanup completed successfully"
else
    print_error "Failed to execute cleanup script"
    exit 1
fi

echo ""
print_status "Verifying cleanup results..."

# Verify cleanup was successful
VERIFICATION_RESULT=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 
    'Events' as table_name, COUNT(*) as record_count FROM t_event
UNION ALL
SELECT 
    'Event Maps' as table_name, COUNT(*) as record_count FROM t_event_map
UNION ALL
SELECT 
    'Areas' as table_name, COUNT(*) as record_count FROM t_area
UNION ALL
SELECT 
    'Marks' as table_name, COUNT(*) as record_count FROM t_mark
UNION ALL
SELECT 
    'Games' as table_name, COUNT(*) as record_count FROM t_game
UNION ALL
SELECT 
    'Tickets' as table_name, COUNT(*) as record_count FROM t_ticket
UNION ALL
SELECT 
    'Exhibitors' as table_name, COUNT(*) as record_count FROM t_exhibitor;
" 2>/dev/null)

echo ""
print_success "Cleanup verification results:"
echo "$VERIFICATION_RESULT"

echo ""
echo "=========================================="
print_success "Database reset completed successfully!"
echo "=========================================="
echo ""
print_status "The database is now in a clean state."
print_status "To reload mock data, run: ./load-all-data.sh"
echo ""
