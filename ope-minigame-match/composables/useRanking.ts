// Ranking data interface
export interface RankingData {
  rank: number
  score: number
  nickName: string
}
export const useRanking = () => {
  const config = useRuntimeConfig()
  const { authenticatedFetch, isAuthenticated } = useAuth()


  // Fetch current user's ranking position and ranking score
  const fetchRanking = async (): Promise<{ success: boolean; data?: RankingData; message?: string }> => {
    if (!isAuthenticated()) {
      console.warn('No authentication token available for fetching ranking')
      return { success: false, message: 'Not authenticated' }
    }

    try {
      const url = `${config.public.backendUrl}/rank/v1/rank`
      console.log('Fetching ranking from URL:', url)
      
      const response = await authenticatedFetch(url)
      console.log('Ranking response status:', response.status)

      const result = await response.json()
      console.log('Ranking API Response:', result)

      if (result.code === 200 && result.data) {
        console.log('Ranking fetched successfully:', result.data)
        return {
          success: true,
          data: {
            rank: result.data.rank || 1000,
            score: result.data.score || 0,
            nickName: result.data.nickName || 'Player'
          }
        }
      } else {
        console.error('Failed to fetch ranking:', result.msg)
        return { success: false, message: result.msg || 'Failed to fetch ranking' }
      }
    } catch (error) {
      console.error('Error fetching ranking:', error)
      return { success: false, message: 'Network error' }
    }
  }
  return {
    fetchRanking
  }
}
