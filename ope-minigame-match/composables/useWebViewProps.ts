/**
 * Composable for accessing WebView injected properties reactively
 */
export const useWebViewProps = () => {
  // Reactive reference to the token
  const token = ref<string | null>(null)
  const isReady = ref(false)

  // Check for token availability
  const checkToken = () => {
    if (typeof window !== 'undefined' && window.globalProps?.token) {
      token.value = window.globalProps.token
      isReady.value = true
      return true
    }
    return false
  }

  // Initialize on mount
  onMounted(() => {
    // Check immediately
    if (checkToken()) {
      return
    }

    // If not available immediately, poll for it
    // (in case the injection happens after component mount)
    const pollInterval = setInterval(() => {
      if (checkToken()) {
        clearInterval(pollInterval)
      }
    }, 100)

    // Clear interval after 5 seconds to avoid infinite polling
    setTimeout(() => {
      clearInterval(pollInterval)
      if (!isReady.value) {
        console.warn('WebView token not found after 5 seconds')
      }
    }, 5000)
  })

  return {
    token: readonly(token),
    isReady: readonly(isReady),
    checkToken
  }
}
