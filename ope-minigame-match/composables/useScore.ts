/**
 * Composable for handling score submission to the backend via mark system
 */
export const useScore = () => {
  const config = useRuntimeConfig()
  const { authenticatedFetch, isAuthenticated } = useAuth()

  // Fetch mark details including score value
  const fetchMarkScore = async (markId: string | number | null) => {
    if (!isAuthenticated()) {
      console.warn('No authentication token available for fetching mark details')
      return { success: false, message: 'Not authenticated', score: 0 }
    }

    if (!markId) {
      console.warn('No mark ID available for fetching mark details')
      return { success: false, message: 'No mark ID', score: 0 }
    }

    try {
      const url = `${config.public.backendUrl}/mark/v1/mark?markId=${markId}`
      console.log('Fetching from URL:', url)

      const response = await authenticatedFetch(url)
      console.log('Response status:', response.status)

      const result = await response.json()
      console.log('API Response:', result)

      if (result.code === 200 && result.data) {
        console.log('Mark details fetched successfully:', result.data)
        console.log('Mark score from API:', result.data.markScore)
        return {
          success: true,
          message: 'Mark details fetched successfully',
          score: result.data.markScore || 0,
          data: result.data
        }
      } else {
        console.error('Failed to fetch mark details:', result.msg)
        return { success: false, message: result.msg || 'Failed to fetch mark details', score: 0 }
      }
    } catch (error) {
      console.error('Error fetching mark details:', error)
      return { success: false, message: 'Network error', score: 0 }
    }
  }

  // Submit score through mark system (proper anti-spam protected flow)
  const submitMarkScore = async (markId: string | number | null) => {

    // Validation checks
    if (!isAuthenticated()) {
      console.warn('No authentication token available for score submission')
      return { success: false, message: 'Not authenticated' }
    }

    if (!markId) {
      console.warn('No mark ID available for score submission')
      return { success: false, message: 'No mark ID' }
    }

    try {
      const response = await authenticatedFetch(
        `${config.public.backendUrl}/mark/v1/addMarkScore`,
        {
          method: 'PUT',
          body: JSON.stringify({
            markId: parseInt(markId.toString(), 10)
          })
        }
      )

      const result = await response.json()

      if (result.code === 200 && result.data === true) {
        console.log('Mark score submitted successfully for markId:', markId)
        return { success: true, message: 'Score submitted successfully', data: result.data }
      } else {
        console.error('Mark score submission failed:', result.msg)
        return { success: false, message: result.msg || 'Score submission failed', data: result.data }
      }
    } catch (error) {
      console.error('Error submitting mark score:', error)
      return { success: false, message: 'Network error' }
    }
  }

  return {
    fetchMarkScore,
    submitMarkScore
  }
}
