/**
 * Composable for handling authentication in the minigame
 * Accesses token from mobile app's injected globalProps
 */
export const useAuth = () => {
  // Get authentication token from mobile app
  const getToken = (): string | null => {
    if (typeof window !== 'undefined' && window.globalProps?.token) {
      console.log('Using injected token from mobile app:', window.globalProps.token)
      return window.globalProps.token
    }
    // Fallback to your real token for testing
    console.log('Using fallback token for testing')
    return 'Bearer c9dceea4-6125-43a9-91de-20c0a3edd11d'
  }

  // Check if user is authenticated
  const isAuthenticated = (): boolean => {
    return !!getToken()
  }

  // Get headers for authenticated requests
  const getAuthHeaders = (): Record<string, string> => {
    const token = getToken()
    return {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': token })
    }
  }

  // Make authenticated API request
  const authenticatedFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
    const headers = {
      ...getAuthHeaders(),
      ...options.headers
    }

    return fetch(url, {
      ...options,
      headers
    })
  }

  return {
    getToken,
    isAuthenticated,
    getAuthHeaders,
    authenticatedFetch
  }
}
