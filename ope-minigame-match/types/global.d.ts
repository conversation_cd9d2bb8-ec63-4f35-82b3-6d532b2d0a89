// Global type declarations for WebView bridge
declare global {
  interface Window {
    globalProps?: {
      token?: string
    }
    ReactNativeWebView?: {
      postMessage: (message: string) => void
    }
    webkit?: {
      messageHandlers?: {
        [key: string]: {
          postMessage: (message: any) => void
        }
      }
    }
    Android?: {
      [key: string]: (message: string) => void
    }
  }
}

export {}
