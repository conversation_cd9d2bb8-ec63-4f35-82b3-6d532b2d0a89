<template>
  <div id="game" class="relative w-screen h-screen overflow-hidden" ref="gameEl">
    <img id="game-bg" src="/images/mission-bg.png" class="absolute -z-10 w-full h-full object-cover">
    
    <!-- Game intro section: A furutistic top and bottom gate with game info in the middle-->
    <div id="game-intro" class="absolute z-20 w-full h-full" v-if="gameState === 'not-started'">
      <img id="gate-top" ref="gateTop" src="/images/gate-top.png" class="row-start-1 row-end-1 w-full">
      <img id="gate-bottom" ref="gateBtm" src="/images/gate-bottom.png" class="row-start-2 row-end-2 w-full">

      <!-- Game Intro Info: Centered middle of screen -->  
       <div ref="gameInfo" class="grid items-center h-full w-full absolute p-12 place-content-center gap-8 place-items-center">
         <img src="/images/intro-text-1.png">
         <img src="/images/intro-thumbnail.png">
         <img src="/images/intro-text-2.png">
         <img @click="startGame" src="/images/start-button.png">
         <p @click="navigateToHome" class="text-center uppercase font-bold text-lg text-gray-600 underline">{{ getLocaledString('cancel')}}</p>
        </div>
    </div>


    <!-- Game Starts: Gates slid open top and bottom to reveal game UI -->
    <div id="game-header" class="relative top-4 w-full h-24 flex items-center justify-between p-12 mt-4" 
         v-if="gameState === 'in-progress'">
      <EnergyBadge>{{ gameScore ?? 0 }}</EnergyBadge>
      <div id="circle-timer">
       <!-- Add counter at the top -->
        <div class="ring-3 ring-gray-200 right-8 bg-white bg-opacity-80 border-2 border-purple-500 rounded-full w-10 h-10 flex items-center justify-center">
          <span class="text-2xl font-extrabold font-mono bg-gradient-to-r from-purple-900 to-purple-400 text-transparent bg-clip-text">{{ remaining }}</span>
        </div>
      </div>
    </div>

    <!-- Ready-go splash banner middle of screen -->
    <div id="ready-go-splash" v-if="readyGo !== 'none'" class="absolute inset-0 z-30 flex flex-col items-center justify-center">
      <img src="/images/ready-splash-bg.png" class="w-full h-auto max-w-screen">
      <img v-if="readyGo === 'ready'" src="/images/text-ready.png" class="absolute max-w-xs p-4 w-3/4">
      <img v-if="readyGo === 'go'" src="/images/text-go.png" class="absolute max-w-xs p-4 w-3/4">
    </div>
    
    <div class="grid place-content-center-safe place-items-center-safe w-full h-full p-12 gap-8 -mt-24" 
         v-if="gameState === 'in-progress'">
      <img id="portaro-character" :src="`/images/p-${targetCharacter}.png`" class="max-w-32"/>

      <img id="instruction" src="/images/instruction.png" />

      <div id="image-grid-3x3" class="grid grid-cols-3 gap-4 w-full max-w-md mx-auto">
        <div id="portaro-card" v-for="card in shuffledCards" :key="card.id" class="relative" @click="checkMatch(card)" :class="readyGo !== 'none' ? 'blur-xs' : ''">
          <img id="portaro-card-bg" src="/images/portaro-card.png" class="w-full h-auto">
          <img id="portaro-image" :src="`/images/p-${card.value}.png`" class="absolute inset-0 w-full h-full object-contain p-3">
        </div>
      </div>
    </div>

    <!-- Game over / Game won UI -->
    <div id="game-result" class="absolute z-20 w-full h-full" v-if="gameState === 'game-over' || gameState === 'game-won'">
      <img id="gate-top-result" ref="gateTopResult" src="/images/gate-top.png" class="w-full">
      <img id="gate-bottom-result" ref="gateBtmResult" src="/images/gate-bottom.png" class="w-full">

      <!-- Result Info: Centered middle of screen -->
      <div v-if="showResultContent && gameState === 'game-won'" ref="resultInfo" class="-mt-24 grid gap-8 sitems-center h-full w-full absolute p-12 place-content-center place-items-center opacity-0">
          <img src="/images/text-clear.png"/>
          <img src="/images/energy-bundle.png" class="max-w-3/4 animate__animated animate__pulse animate__repeat-2"/>
          <span class="text-4xl font-extrabold font-mono bg-gradient-to-r from-purple-900 to-purple-600 text-transparent bg-clip-text">+ {{ gameScore }}</span>
        </div>
      <div v-else-if="showResultContent" ref="resultInfo" class="-mt-24  grid gap-8 sitems-center h-full w-full absolute p-12 place-content-center place-items-center opacity-0">
          <img src="/images/text-failed.png" class="mb-20" />
          <p class="text-xl font-bold text-gray-800 mb-6 text-center">{{ getLocaledString('would-you-like-to-play-again') }}</p>
          <div class="flex flex-row gap-8">
            <button class="rounded-full py-2 w-32 text-white font-bold bg-gray-600" @click="navigateToHome">{{ getLocaledString('finish') }}</button>
            <button class="rounded-full py-2 w-32 text-white font-bold bg-[#6400FF]" @click="startGame">{{ getLocaledString('play-again') }}</button>
          </div>
      </div>
      <img v-if="showResultContent" :src="gameState === 'game-won' ? `/images/yuragi-happy.png` : `/images/yuragi-sad.png`" class="absolute bottom-0 left-0 max-w-1/2 animate__animated animate__delay-1s animate__fadeInLeft" />
      <img v-if="showResultContent" :src="gameState === 'game-won' ? `/images/portaro-happy.png` : `/images/portaro-sad.png`" class="absolute bottom-36 right-8 max-w-22 animate__animated animate__delay animate__fadeInTopRight animate__slow" />
      <ChatBubble v-if="showResultContent" class="w-40 h-14 absolute bottom-20 right-4 animate__animated animate__delay-1s animate__fadeIn">
        <p class="p-4 text-white text-sm line-clamp-2 leading-4">{{ gameState === 'game-won' ? getLocaledString('you-did-it') : getLocaledString('your-abilities-arent-like-this-are-they') }}</p>
      </ChatBubble>
    </div>

    <!-- Won: Ranking up result modal with backdrop overlay -->
    <div id="ranking-result-container" v-if="showRankingResult" class="absolute inset-0 z-50 flex items-center justify-center bg-black/60" @click="navigateToHome">
      <div id="ranking-result" class="relative flex flex-col items-center gap-2 p-2 max-w-xs" @click.stop>
        <img src="/images/ranking-bg.png" class="w-full h-full absolute top-0 -z-10"/>
        <button id="close-button" @click="navigateToHome" class="absolute top-2 right-2 w-6 h-6 bg-gray-200 text-gray-800 rounded-full flex items-center justify-center hover:bg-gray-300">
          <span class="text-sm font-bold leading-none">&times;</span>
        </button>
        <p class="text-white mt-2 font-semibold">{{ getLocaledString('ranking-result') }}</p>
        <div class="flex flex-col gap-6 bg-white rounded-xl p-2 py-16 mt-2 items-center">
          <div id="new-rank" class="flex flex-row gap-3 p-3 bg-gray-200 rounded-xl ">
            <p>{{ newRanking?.rank }}</p>
            <p >{{ newRanking?.nickName }}</p>
            <EnergyBadge class="scale-75 h-0 mt-6">{{ newRanking?.score }}</EnergyBadge>
          </div>
          <img src="/images/arrow-up.png" class="w-6 h-3"/> 
          <div id="current-rank" class="flex flex-row gap-4 p-3 bg-gray-200 rounded-xl">
            <p>{{ oldRanking?.rank }}</p>
            <p>{{ oldRanking?.nickName }}</p>
            <EnergyBadge class="scale-75 h-0 mt-6">{{ oldRanking?.score }}</EnergyBadge>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { navigateToHome } from '~/utils/webViewBridge';
const route = useRoute()
const { getToken } = useAuth()

const markId = route.query.markId as string ?? ''
const lang = route.query.lang ?? 'en'

// Use composables for authentication and score submission
const { isAuthenticated } = useAuth()
const { fetchMarkScore, submitMarkScore } = useScore()
const { fetchRanking } = useRanking()

// Check authentication status
const isUserAuthenticated = isAuthenticated()

// Game score from mark
const gameScore = ref(0)

// Ranking data
const oldRanking = ref<RankingData | null>(null)
const newRanking = ref<RankingData | null>(null)

// Fetch mark score and initial ranking on component mount
onMounted(async () => {
  if (markId && isUserAuthenticated) {
    // Fetch mark score
    const scoreResult = await fetchMarkScore(markId)
    if (scoreResult.success) {
      gameScore.value = scoreResult.score
    }

    // Fetch initial ranking data
    const rankingResult = await fetchRanking()
    if (rankingResult.success && rankingResult.data) {
      oldRanking.value = rankingResult.data
      newRanking.value = rankingResult.data
    }
  }
})

const gameEl = useTemplateRef<HTMLElement>('gameEl')
const gateTop = useTemplateRef<HTMLElement>('gateTop')
const gateBtm = useTemplateRef<HTMLElement>('gateBtm')
const gameInfo = useTemplateRef<HTMLElement>('gameInfo')
const gateTopResult = useTemplateRef<HTMLElement>('gateTopResult')
const gateBtmResult = useTemplateRef<HTMLElement>('gateBtmResult')
const resultInfo = useTemplateRef<HTMLElement>('resultInfo')

// Animation setup
const gameInfoAnim = useAnimate(gameInfo, { opacity: 0 }, { 
  immediate: false, 
  duration: 200,
  fill: 'forwards'
})
const topGateAnim = useAnimate(gateTop, { transform: 'translateY(-100%)' }, { 
  immediate: false, 
  duration: 500,
  easing: 'ease-in',
  fill: 'forwards'
})
const bottomGateAnim = useAnimate(gateBtm, { transform: 'translateY(100%)' }, { 
  immediate: false, 
  duration: 500,
  easing: 'ease-in',
  fill: 'forwards'
})
const resultInfoAnim = useAnimate(resultInfo, { opacity: 1 }, {
  immediate: false,
  duration: 300,
  fill: 'forwards'
})
const topGateResultAnim = useAnimate(gateTopResult, { transform: 'translateY(0)' }, { 
  immediate: true,
  duration: 500,
  easing: 'ease-out',
  fill: 'forwards'
})
const bottomGateResultAnim = useAnimate(gateBtmResult, { transform: 'translateY(0)' }, { 
  immediate: true,
  duration: 500,
  easing: 'ease-out',
  fill: 'forwards'
})

const readyGo = ref<'none' | 'ready' | 'go'>('none')
const gameState = ref<GameState>('not-started')
const targetCharacter = ref(1)
const shuffledCards = ref<{id: number, value: number}[]>([])
const showResultContent = ref(false)
const showRankingResult = ref(false)
const countdown = shallowRef(5)
const { start, reset, pause, remaining } = useCountdown(countdown, {
  immediate: false,
  onComplete() {
    showGameResult('game-over')
  }
})

// Shuffle array function
const shuffleArray = <T>(array: T[]): T[] => {
  const newArray = [...array]
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]]
  }
  return newArray
}

// Initialize game with random target and shuffled grid
const initializeGame = () => {
  // Pick random character (1-9)
  targetCharacter.value = Math.floor(Math.random() * 9) + 1
  
  // Create array of card objects
  const cards = Array.from({ length: 9 }, (_, i) => ({
    id: i + 1,
    value: i + 1
  }))
  
  // Shuffle the cards
  shuffledCards.value = shuffleArray(cards)
}

const checkMatch = (card: {id: number, value: number}) => {
  if (card.value === targetCharacter.value) {
    pause();
    showGameResult('game-won')
  }
}

const showGameResult = async (result: 'game-won' | 'game-over') => {
  gameState.value = result
  showResultContent.value = false
  showRankingResult.value = false

  // Play closing animations
  topGateResultAnim.play()
  bottomGateResultAnim.play()

  // Wait for gate animations to complete (500ms) plus a small buffer
  await new Promise(resolve => setTimeout(resolve, 600))

  // Now show the result content
  showResultContent.value = true
  resultInfoAnim.play()

  if(result === 'game-won') {
    // Submit score through mark system (proper anti-spam protected flow)
    const scoreResult = await submitMarkScore(markId)
    if (scoreResult.success) {
      console.log('Score submitted successfully through mark system!')

      // Fetch updated ranking data after score submission
      const updatedRankingResult = await fetchRanking()
      if (updatedRankingResult.success && updatedRankingResult.data) {
        newRanking.value = updatedRankingResult.data
        
        // Show ranking modal after 2 seconds
        setTimeout(() => {
          showRankingResult.value = true
        }, 2000)
      }
    } else {
      console.error('Failed to submit score:', scoreResult.message)
      // Show user-friendly error message
      alert(`You have already completed this game before, no energy awarded`)
      navigateToHome()
    }
  }
}

const startGame = async () => {
  // Reset result content visibility
  showResultContent.value = false

  if(gameState.value === 'in-progress') return

  if(gameState.value === 'game-over') {
    bottomGateResultAnim.reverse()
    topGateResultAnim.reverse()
  }

  gameInfoAnim.play()
  await new Promise(resolve => setTimeout(resolve, 200))
  topGateAnim.play()
  bottomGateAnim.play()
  await new Promise(resolve => setTimeout(resolve, 500))

  // Initialize game with random values
  initializeGame()

  // Update game state
  gameState.value = 'in-progress'

  // await new Promise(resolve => setTimeout(resolve, 400))
  readyGo.value = 'ready'
  await new Promise(resolve => setTimeout(resolve, 1800))
  readyGo.value = 'go'
  await new Promise(resolve => setTimeout(resolve, 1000))
  readyGo.value = 'none'
  
  // Countdown starts
  reset();
  start();
}

const translations = {
  en: {
    'finish': 'FINISH',
    'play-again': 'PLAY AGAIN',
    'cancel': 'CANCEL',
    'clear': 'CLEAR',
    'failed': 'FAILED',
    'would-you-like-to-play-again': 'Would you like to play again?',
    'you-did-it': 'I knew you could do it!',
    'your-abilities-arent-like-this-are-they': "Your abilities aren't like this, are they?",
    'ranking-result': 'Ranking Results',
  },
  ko: {
    'finish': '끝내기',
    'play-again': '다시하기',
    'cancel': '취소',
    'clear': '클리어',
    'failed': '실패',
    'would-you-like-to-play-again': '다시 하시겠습니까?',
    'you-did-it': '성공!',
    'your-abilities-arent-like-this-are-they': '당신의 능력이 이 정도는 아니죠?',
    'ranking-result': '랭킹 결과',
  }
} as const

type TranslationKey = keyof typeof translations.en
type LanguageCode = keyof typeof translations

const getLocaledString = (key: TranslationKey): string => {
  const languageCode = (lang as LanguageCode) in translations ? (lang as LanguageCode) : 'en'
  return translations[languageCode]?.[key] || translations.en[key]
}
</script>

<style lang="css" scoped>
#gate-top, #gate-bottom {
  position: absolute;
  left: 0;
  width: 100%;
  height: calc(50% + 2rem);
  object-fit: cover;
  object-position: top;
}
#gate-top {top: 0;}
#gate-bottom {bottom: 0;}

#ready-go-splash img {
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0; }
  50% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

#gate-top-result, #gate-bottom-result {
  position: absolute;
  left: 0;
  width: 100%;
  height: calc(50% + 2rem);
  object-fit: cover;
  object-position: top;
  transform: translateY(-100%); /* Start off-screen */
}
#gate-top-result {top: 0;}
#gate-bottom-result {
  bottom: 0;
  transform: translateY(100%); /* Start off-screen */
}
</style>
