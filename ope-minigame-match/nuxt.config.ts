// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  ssr: false,
  devtools: { enabled: true },
  modules: ['@vueuse/nuxt', '@nuxtjs/tailwindcss'],
  css: ['~/assets/css/main.css','~/assets/css/animate.css'],
  nitro: {
    preset: 'static'
  },

  runtimeConfig: {
      public: {
        backendUrl: 'https://media.openportalexpo.com',
      },
    },

  vite:{
    server:{
      allowedHosts: ['6ce0-2405-6580-22e0-3300-8129-c5c6-a4c0-242d.ngrok-free.app']
    }
  },
  
  $development:{
    runtimeConfig: {
      public: {
        backendUrl: 'http://localhost:8082',
      },
    },
  },

    $env:{
    staging:{
      runtimeConfig: {
        public: {
          backendUrl: 'https://ope-backend-stg.zafar.dev',
        },
      },
    },
  }
})