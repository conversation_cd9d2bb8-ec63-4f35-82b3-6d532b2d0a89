// Types for WebView bridge communication
interface BridgeMessage {
  method: string;
  params: any;
  id?: string;
}

// Check if running in a WebView environment
export const isInWebView = (): boolean => {
  return !!(
    window.ReactNativeWebView ||
    (window.webkit && window.webkit.messageHandlers) ||
    window.Android
  );
};

// Send message to native app
export const sendToNative = (method: string, params: any = {}): void => {
  // Format message according to the expected structure
  const message: BridgeMessage = {
    method,
    params: typeof params === 'string' ? params : JSON.stringify(params),
    id: Date.now().toString()
  };
  
  // React Native WebView
  if (window.ReactNativeWebView) {
    window.ReactNativeWebView.postMessage(JSON.stringify(message));
    return;
  }
  
  // iOS WebView (WKWebView)
  if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers[method]) {
    window.webkit.messageHandlers[method].postMessage(message);
    return;
  }
  
  // Android WebView
  if (window.Android && typeof window.Android[method] === 'function') {
    window.Android[method](JSON.stringify(message));
    return;
  }
  
  // Fallback for browser testing
  alert(`WebView bridge: ${method}`, params);
};

// Convenience methods for common actions
export const closeWebView = (): void => sendToNative('close');
export const navigateToHome = (): void => sendToNative('toHome');
export const showToast = (message: string): void => sendToNative('toast', { message });
export const shareContent = (url: string, message: string): void => sendToNative('share', { url, message });