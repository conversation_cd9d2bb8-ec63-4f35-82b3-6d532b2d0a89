# Open Portal Expo Admin Interface

This admin interface is a Client-side Nuxt application designed to manage all data for the Open Portal Expo (OPE) mobile app. It will provide comprehensive CRUD operations for events, users, content, and system configuration.

It is built using the following technologies:

- Nuxt 3
- NuxtUI Pro
- TypeScript
- Directus CMS
- MySQL Database

To gain access to this admin, please register on the OPE Directus platform first as an admin or event staff.

[![Nuxt UI Pro](https://img.shields.io/badge/Made%20with-Nuxt%20UI%20Pro-00DC82?logo=nuxt&labelColor=020420)](https://ui.nuxt.com/pro)

- [Documentation](https://ui.nuxt.com/getting-started/installation/pro/nuxt)

## Setup

Make sure to install the dependencies:

```bash
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
bun dev
```

## Production

Build the application for production:

```bash
bun build
```

Check out the [deployment documentation](https://nuxt.com/docs/getting-started/deployment) for more information.

## Renovate integration

Install [Renovate GitHub app](https://github.com/apps/renovate/installations/select_target) on your repository and you are good to go.
