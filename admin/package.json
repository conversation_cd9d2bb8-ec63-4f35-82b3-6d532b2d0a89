{"name": "open-portal-admin", "version": "0.9.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --fix", "typecheck": "nuxt typecheck"}, "dependencies": {"@aws-sdk/client-s3": "^3.830.0", "@aws-sdk/lib-storage": "^3.830.0", "@iconify-json/lucide": "^1.2.44", "@iconify-json/simple-icons": "^1.2.35", "@nuxt/ui-pro": "^3.1.3", "@sentry/nuxt": "^9", "@unovis/ts": "^1.5.2", "@unovis/vue": "^1.5.2", "@vueuse/nuxt": "^13.2.0", "date-fns": "^4.1.0", "konva": "^9.3.20", "nanoid": "^5.1.5", "nuxt": "^3.17.5", "qrcode-vue3": "^1.7.1", "vue-konva": "^3.2.1", "vue-qrcode-reader": "^5.7.2", "zod": "^3.25.28"}, "devDependencies": {"@nuxt/eslint": "^1.4.1", "eslint": "^9.27.0", "typescript": "^5.8.3", "vue-tsc": "^2.2.10"}, "pnpm": {"ignoredBuiltDependencies": ["@parcel/watcher", "esbuild", "maplibre-gl", "vue-demi"]}, "overrides": {"@vercel/nft": "^0.27.4"}}