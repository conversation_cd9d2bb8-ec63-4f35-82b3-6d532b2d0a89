// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  modules: ['@nuxt/eslint', '@nuxt/ui-pro', '@vueuse/nuxt', '@sentry/nuxt/module'],
  ssr: false,

  devtools: {
    enabled: true
  },

  css: ['~/assets/css/main.css'],

  runtimeConfig: {
    // Private keys (only available on the server-side)
    awsAccessKeyId: process.env.NUXT_AWS_ACCESS_KEY_ID,
    awsSecretAccessKey: process.env.ANUXT_WS_SECRET_ACCESS_KEY,
    awsRegion: process.env.NUXT_AWS_REGION || 'ap-northeast-1',
    awsS3BucketName: process.env.NUXT_AWS_S3_BUCKET_NAME || 'open-portal-expo-prod',
    awsS3PublicUrl: process.env.NUXT_AWS_S3_PUBLIC_URL || 'https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com',

    public: {
      directusUrl: process.env.NUXT_PUBLIC_DIRECTUS_URL || 'MISSING-URL',
      backendApiUrl: process.env.NUXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8082',
      sentry: {
        dsn: process.env.NUXT_PUBLIC_SENTRY_DSN || 'https://<EMAIL>/3'
      }
    }
  },

  routeRules: {
    '/api/**': {
      cors: true
    }
  },

  sourcemap: { client: 'hidden' },

  future: {
    compatibilityVersion: 4
  },

  compatibilityDate: '2025-05-05',

  nitro: {
    compatibilityDate: '2025-05-05',
    preset: 'cloudflare_module',
    cloudflare: {
      deployConfig: true,
      nodeCompat: true,
      wrangler: {
        observability: {
          logs: {
            enabled: true
          }
        }
      }
    }
  },

  eslint: {
    config: {
      stylistic: {
        commaDangle: 'never',
        braceStyle: '1tbs'
      }
    }
  },

  sentry: {
    sourceMapsUploadOptions: {
      org: 'zafar',
      project: 'ope-admin-2',
      url: 'https://glitchtip.zafar.dev/'
    }
  }
})
