export interface S3UploadResponse {
  success: boolean
  url?: string
  key?: string
  error?: string
}

export interface S3UploadRequest {
  file: File
}

export const ALLOWED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
] as const

export type AllowedImageType = typeof ALLOWED_IMAGE_TYPES[number]

export const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export function isAllowedImageType(mimeType: string): mimeType is AllowedImageType {
  return ALLOWED_IMAGE_TYPES.includes(mimeType as AllowedImageType)
}

export function validateImageFile(file: File): { valid: boolean, error?: string } {
  if (!isAllowedImageType(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} not allowed. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`
    }
  }

  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size too large. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB`
    }
  }

  return { valid: true }
}
