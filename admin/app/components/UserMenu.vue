<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui'

defineProps<{
  collapsed?: boolean
}>()

const colorMode = useColorMode()
const appConfig = useAppConfig()
const { currentUser, logout } = useAuth()
const { roleName } = useRoleAccess()

// Computed user info from auth
const user = computed(() => {
  if (currentUser.value) {
    const fullName = `${currentUser.value.first_name || ''} ${
      currentUser.value.last_name || ''
    }`.trim()
    return {
      name: fullName || currentUser.value.email,
      avatar: {
        src: `https://ui-avatars.com/api/?name=${encodeURIComponent(
          fullName || currentUser.value.email
        )}&background=random`,
        alt: fullName || currentUser.value.email
      }
    }
  }
  return {
    name: 'Admin User',
    avatar: {
      src: 'https://ui-avatars.com/api/?name=Admin&background=random',
      alt: 'Admin User'
    }
  }
})

// Handle logout
const handleLogout = async () => {
  await logout()
}

const items = computed<DropdownMenuItem[][]>(() => [
  [
    {
      type: 'label',
      label: user.value.name,
      avatar: user.value.avatar
    }
  ],
  [
    {
      type: 'label',
      label: roleName.value + ' Access',
      icon: 'i-lucide-shield-check',
      class: 'opacity-50 font-light'
    },
    {
      label: 'Settings',
      icon: 'i-lucide-settings',
      to: '/settings'
    }
  ],
  [
    {
      label: 'Log out',
      icon: 'i-lucide-log-out',
      onSelect: handleLogout
    }
  ]
])
</script>

<template>
  <UDropdownMenu
    :items="items"
    :content="{ align: 'center', collisionPadding: 12 }"
    :ui="{
      content: collapsed ? 'w-48' : 'w-(--reka-dropdown-menu-trigger-width)'
    }"
  >
    <UButton
      v-bind="{
        ...user,
        label: collapsed ? undefined : user?.name,
        trailingIcon: collapsed ? undefined : 'i-lucide-chevrons-up-down'
      }"
      color="neutral"
      variant="ghost"
      block
      :square="collapsed"
      class="data-[state=open]:bg-elevated"
      :ui="{
        trailingIcon: 'text-dimmed'
      }"
    />

    <template #chip-leading="{ item }">
      <span
        :style="{
          '--chip-light': `var(--color-${(item as any).chip}-500)`,
          '--chip-dark': `var(--color-${(item as any).chip}-400)`
        }"
        class="ms-0.5 size-2 rounded-full bg-(--chip-light) dark:bg-(--chip-dark)"
      />
    </template>
  </UDropdownMenu>
</template>
