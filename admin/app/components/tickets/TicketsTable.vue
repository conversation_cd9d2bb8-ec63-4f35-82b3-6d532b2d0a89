<script setup lang="ts">
const props = defineProps<{
  tickets: TTicket[]
  loading?: boolean
  showUser?: boolean
}>()

const emit = defineEmits(['select'])

const router = useRouter()

// Table columns
const columns = computed<TableColumn<any>[]>(() => {
  const cols: TableColumn<any>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) =>
        h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
    },
    {
      accessorKey: 'name',
      header: 'Name',
      cell: ({ row }) => h('span', row.original.name || '—')
    },
    {
      accessorKey: 'code',
      header: 'Ticket Code',
      cell: ({ row }) =>
        h('span', { class: 'font-mono' }, row.original.code || 'N/A')
    }
  ]

  if (props.showUser) {
    cols.push({
      accessorKey: 'user_id',
      header: 'User ID',
      cell: ({ row }) => {
        const userId = row.original.user_id
        if (!userId) return h('span', { class: 'text-gray-400' }, '—')

        return h(
          'div',
          {
            class: 'font-mono text-sm',
            onClick: (e) => {
              e.stopPropagation()
              router.push(`/users/${userId}`)
            }
          },
          [
            h(
              'span',
              { class: 'hover:underline hover:text-primary cursor-pointer' },
              `#${userId}`
            )
          ]
        )
      }
    })
  }

  cols.push(
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const isBound = !!row.original.bound_at
        const isChecked = !!row.original.checked_at

        let status = 'Available'
        let color = 'gray'

        if (isBound && isChecked) {
          status = 'Used'
          color = 'green'
        } else if (isBound) {
          status = 'Bound'
          color = 'blue'
        }

        return h(
          'div',
          {
            class: ['inline-flex items-center gap-1.5', `text-${color}-600`]
          },
          [
            h('span', {
              class: ['w-2 h-2 rounded-full', `bg-${color}-500`]
            }),
            h('span', status)
          ]
        )
      }
    },
    {
      accessorKey: 'bound_at',
      header: 'Bound At',
      cell: ({ row }) => {
        const date = row.original.bound_at
        return date ? new Date(date).toLocaleString() : '—'
      }
    },
    {
      accessorKey: 'checked_at',
      header: 'Checked At',
      cell: ({ row }) => {
        const date = row.original.checked_at
        return date ? new Date(date).toLocaleString() : '—'
      }
    },
    {
      accessorKey: 'create_at',
      header: 'Created',
      cell: ({ row }) => {
        const date = row.original.create_at
        return date ? new Date(date).toLocaleDateString() : '—'
      }
    }
  )

  return cols
})

// Handle row click
const onRowSelect = (row: any) => {
  emit('select', row)
}
</script>

<template>
  <UTable
    :data="tickets"
    :columns="columns"
    :loading="loading"
    class="w-full h-full"
    :ui="{
      thead: 'sticky top-0 z-10 bg-white dark:bg-gray-900',
      th: { padding: 'py-3 px-4' },
      td: { padding: 'py-3 px-4' },
      table: 'min-w-full divide-y divide-gray-200 dark:divide-gray-700',
      wrapper: 'w-full'
    }"
    @select="onRowSelect"
  >
    <template #empty-state>
      <div class="flex flex-col items-center justify-center py-12 gap-3">
        <UIcon name="i-lucide-ticket" class="w-10 h-10 text-gray-400" />
        <p class="text-sm text-gray-500">
          No tickets found
        </p>
        <p class="text-xs text-gray-400">
          Try adjusting your search or filter criteria
        </p>
      </div>
    </template>

    <template #loading-state>
      <div class="flex items-center justify-center py-8">
        <UIcon
          name="i-lucide-loader-2"
          class="animate-spin h-6 w-6 text-primary-500"
        />
        <span class="ml-2 text-gray-500">Loading tickets...</span>
      </div>
    </template>
  </UTable>
</template>
