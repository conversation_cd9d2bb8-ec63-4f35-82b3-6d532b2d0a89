<template>
  <div class="space-y-4">
    <!-- Current Image Display -->
    <div v-if="modelValue" class="relative">
      <img
        :src="modelValue"
        :alt="alt || 'Uploaded image'"
        class="w-full max-w-md h-auto rounded-lg shadow-sm border border-gray-200"
      >
      <UButton
        v-if="!disabled"
        icon="i-lucide-x"
        color="error"
        variant="solid"
        size="xs"
        class="absolute top-2 right-2"
        @click="removeImage"
      />
    </div>

    <!-- Upload Area -->
    <div v-if="!modelValue || allowReplace" class="space-y-3">
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors"
        :class="{
          'border-blue-400 bg-blue-50': isDragOver,
          'opacity-50 cursor-not-allowed': disabled || uploading
        }"
        @dragover.prevent="isDragOver = true"
        @dragleave.prevent="isDragOver = false"
        @drop.prevent="handleDrop"
      >
        <div class="space-y-2">
          <UIcon
            name="i-lucide-image"
            class="w-8 h-8 mx-auto text-gray-400"
            :class="{ 'text-blue-500': isDragOver }"
          />
          <div>
            <p class="text-sm text-gray-600">
              {{ modelValue ? 'Replace image' : 'Drop an image here, or' }}
              <UButton
                variant="link"
                :disabled="disabled || uploading"
                class="p-0 h-auto text-sm"
                @click="triggerFileInput"
              >
                browse files
              </UButton>
            </p>
            <p class="text-xs text-gray-500 mt-1">
              {{ allowedTypesText }} • Max {{ maxSizeMB }}MB
            </p>
          </div>
        </div>
      </div>

      <!-- Hidden File Input -->
      <input
        ref="fileInput"
        type="file"
        :accept="acceptedTypes"
        class="hidden"
        :disabled="disabled || uploading"
        @change="handleFileSelect"
      >

      <!-- Upload Progress -->
      <div v-if="uploading" class="space-y-2">
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600">Uploading...</span>
          <span class="text-gray-600">{{ uploadProgress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${uploadProgress}%` }"
          />
        </div>
      </div>

      <!-- Error Display -->
      <UAlert
        v-if="error"
        color="error"
        variant="soft"
        :title="'Upload Failed'"
        :description="error"
        :close-button="{ icon: 'i-lucide-x', color: 'error', variant: 'link' }"
        @close="clearError"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { S3UploadResponse } from '~/types/s3'
import { ALLOWED_IMAGE_TYPES, MAX_FILE_SIZE } from '~/types/s3'

interface Props {
  modelValue?: string | null
  disabled?: boolean
  allowReplace?: boolean
  alt?: string
}

interface Emits {
  (e: 'update:modelValue', value: string | null): void
  (e: 'upload-success', response: S3UploadResponse): void
  (e: 'upload-error', error: string): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  allowReplace: true,
  alt: 'Image'
})

const emit = defineEmits<Emits>()

const fileInput = ref<HTMLInputElement>()
const isDragOver = ref(false)

const { uploading, uploadProgress, error, uploadFile, reset } = useS3Upload()

// Computed properties
const acceptedTypes = computed(() => ALLOWED_IMAGE_TYPES.join(','))
const maxSizeMB = computed(() => Math.round(MAX_FILE_SIZE / 1024 / 1024))
const allowedTypesText = computed(() => {
  const types = ALLOWED_IMAGE_TYPES.map(type => type.split('/')[1].toUpperCase())
  return types.join(', ')
})

// Methods
function triggerFileInput() {
  if (props.disabled || uploading.value) return
  fileInput.value?.click()
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    handleFileUpload(file)
  }
}

function handleDrop(event: DragEvent) {
  isDragOver.value = false
  if (props.disabled || uploading.value) return

  const file = event.dataTransfer?.files[0]
  if (file) {
    handleFileUpload(file)
  }
}

async function handleFileUpload(file: File) {
  try {
    reset()
    const result = await uploadFile(file)

    if (result.success && result.url) {
      emit('update:modelValue', result.url)
      emit('upload-success', result)

      // Clear file input
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  } catch (err: any) {
    console.error('Upload error:', err)
    emit('upload-error', err.message || 'Upload failed')
  }
}

function removeImage() {
  if (props.disabled) return
  emit('update:modelValue', null)
  reset()
}

function clearError() {
  reset()
}
</script>
