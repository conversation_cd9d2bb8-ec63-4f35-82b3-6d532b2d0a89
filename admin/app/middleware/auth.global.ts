export default defineNuxtRouteMiddleware(async (to) => {
  const {
    isAuthenticated,
    accessToken,
    refreshToken,
    initAuth,
    refreshAccessToken
  } = useAuth()

  // Initialize auth state
  await initAuth()

  // Routes that don't require authentication
  const publicRoutes = ['/login']
  const isPublicRoute = publicRoutes.includes(to.path)

  console.log('Checking authentication...')

  // Handle token refresh if needed
  if (isAuthenticated.value && !isPublicRoute) {
    console.log('Checking token expiration...')
    // Token check logic would go here
  } else if (!accessToken.value && refreshToken.value) {
    console.log('Token expired, refreshing...')
    await refreshAccessToken()
  }

  // Handle redirects based on authentication state
  if (!isAuthenticated.value && !isPublicRoute) {
    console.log('User is not authenticated, redirecting to login...')
    return navigateTo('/login')
  } else if (isAuthenticated.value && to.path === '/login') {
    console.log('User is authenticated, redirecting to dashboard...')
    return navigateTo('/')
  }
})
