<script setup lang="ts">
import { useImage } from 'vue-konva'
import { useDebounceFn, useEventListener } from '@vueuse/core'
import { getContainingAreaId as getContainingAreaIdUtil } from '@/utils/collision'

// Route params
const route = useRoute()
const eventId = parseInt(route.params.id as string)
const toast = useToast()
const { useDirectusFetch, dFetch } = useDirectus()
const editorRef = useTemplateRef('editor')
const { width, height } = useElementSize(editorRef)

// Watch for stage dimension changes
watch([width, height], () => {
  // Debug logging can be re-enabled here if needed
})
const selectedShapeName = ref('')
const selectedDataType = useState<'area' | 'marker' | null>(
  'selectedDataType',
  () => null
)
const selectedData = useState<any>('selectedData', () => null)

// Debounced update function for area data
const debouncedUpdateArea = useDebounceFn(() => {
  if (selectedData.value?.type === 'area') {
    updateAreaData()
  }
}, 500) // 500ms debounce delay

// Watch for changes to selectedData.name and save when it changes
watch(() => selectedData.value?.name, (newName, oldName) => {
  if (newName !== oldName && newName !== undefined) {
    debouncedUpdateArea()
  }
})
const openAreaDeleteModal = ref(false)
const openMarkerDeleteModal = ref(false)
const creationMode = useState<'area' | 'marker' | null>(
  'creationMode',
  () => null
)

const isLoading = ref(false)

// Coordinate scaling utilities
const getScalingFactors = () => {
  const mapInfo = mapData.value?.[0]
  if (!mapInfo || !mapInfo.map_width || !mapInfo.map_height) {
    return { scaleX: 1, scaleY: 1 }
  }

  return {
    scaleX: width.value / mapInfo.map_width,
    scaleY: height.value / mapInfo.map_height
  }
}

const scaleCoordinatesFromCanvas = (
  x: number,
  y: number,
  w?: number,
  h?: number
) => {
  const { scaleX, scaleY } = getScalingFactors()
  return scaleFromCanvas(x, y, scaleX, scaleY, w, h)
}

// Page metadata
definePageMeta({
  title: 'Map Editor',
  description: 'Interactive map editor for event areas and markers'
})

// Fetch Event Map
const { data: mapData } = await useDirectusFetch<TEventMap[]>(
  '/items/t_event_map',
  {
    key: `map-${eventId}`,
    params: {
      fields: ['*'],
      filter: {
        event_id: {
          _eq: eventId
        }
      }
    }
  }
)

// Fetch Event Areas
const { data: areasData, refresh: refreshAreas } = await useDirectusFetch<
  TArea[]
>('/items/t_area', {
  key: `areas-${eventId}`,
  params: {
    fields: ['*'],
    filter: {
      event_id: {
        _eq: eventId
      }
    }
  }
})

// Fetch Event Markers
const { data: markersData, refresh: refreshMarkers } = await useDirectusFetch<
  TMark[]
>('/items/t_mark', {
  key: `markers-${eventId}`,
  params: {
    fields: ['*'],
    filter: {
      event_id: {
        _eq: eventId
      }
    }
  }
})

// Fetch Available Games for dropdown
const { data: availableGames } = await useDirectusFetch<TGameAvailable[]>(
  '/items/t_game_available',
  {
    key: 'available-games'
  }
)

// Marker extra composable (after refreshMarkers is defined)
const {
  selectedMarkerExtra,
  isExtraSaving,
  saveMarkerExtra
} = useMarkerExtra(selectedData)

// S3 upload composable for image uploads
const { uploading: isImageUploading, uploadFile } = useS3Upload()

const [backgroundImage] = useImage(
  mapData.value && mapData.value[0] && mapData.value[0].map_url
    ? mapData.value[0].map_url
    : ''
)

const areas = computed(() => {
  const { scaleX, scaleY } = getScalingFactors()
  return formatAreas(areasData.value || [], scaleX, scaleY)
})

const markers = computed(() => {
  const { scaleX, scaleY } = getScalingFactors()
  return formatMarkers(markersData.value || [], scaleX, scaleY)
})

// Available games options for dropdown
const gameOptions = computed(() => {
  if (!availableGames.value) return []
  return availableGames.value.map(game => ({
    value: game.id,
    label: game.name || 'Unnamed Game',
    description: game.description
  }))
})

// Debug: Log available games when they load
watch(availableGames, (games) => {
  if (games) {
    console.log('Available games loaded:', games.length, 'games')
  }
}, { immediate: true })

// Get selected game details
const selectedGameDetails = computed(() => {
  if (!selectedMarkerExtra.value?.game_available_id || !availableGames.value) return null
  return availableGames.value.find(game => game.id === selectedMarkerExtra.value.game_available_id)
})

// Check if game marker is properly configured
const isGameMarkerComplete = computed(() => {
  if (selectedData.value?.mark_type !== 1) return true // Not a game marker

  // For game markers, we need: game_available_id, description, and mark_score
  return !!(
    selectedMarkerExtra.value?.game_available_id
    && selectedMarkerExtra.value?.description?.trim()
    && selectedData.value?.mark_score
    && selectedData.value.mark_score > 0
  )
})

// Wrapper to use utility collision detection with reactive areas
const getContainingAreaId = (
  canvasX: number,
  canvasY: number
): number | null => {
  return getContainingAreaIdUtil(areas.value, canvasX, canvasY)
}

// Group markers by their area_id ("null" key for those without area)
const markersByArea = computed<Record<string, TMark[]>>(() => {
  const grouped: Record<string, TMark[]> = {};
  (markersData.value || []).forEach((m) => {
    const key = m.area_id != null ? m.area_id.toString() : 'null'
    if (!grouped[key]) grouped[key] = []
    grouped[key].push(m)
  })
  return grouped
})

// Helper to select area or marker from sidebar list
const selectAreaFromList = (area: TArea) => {
  selectedShapeName.value = `area-${area.id}`
  selectedData.value = { type: 'area', ...area }
  nextTick(() => updateTransformer())
}

const selectMarkerFromList = (marker: TMark) => {
  selectedShapeName.value = `marker-${marker.id}`
  selectedData.value = { type: 'marker', ...marker }
  nextTick(() => updateTransformer())
}

const transformer = ref<any>(null)

const handleTransformEnd = async (e: any) => {
  const selectedName = selectedShapeName.value
  const node = e.target

  // Check if it's an area
  if (selectedName.startsWith('area-')) {
    const rect = areas.value.find(r => r.name === selectedName)
    if (!rect) return

    // Calculate new dimensions based on scale
    // node.width() and node.height() return the ORIGINAL dimensions, not scaled ones
    const newWidth = node.width() * node.scaleX()
    const newHeight = node.height() * node.scaleY()

    // Update the state with new properties
    rect.x = node.x()
    rect.y = node.y()
    rect.width = newWidth
    rect.height = newHeight
    rect.scaleX = 1
    rect.scaleY = 1

    // Reset scale on the actual node immediately
    node.scaleX(1)
    node.scaleY(1)

    // Convert canvas coordinates back to database coordinates
    const dbCoords = scaleCoordinatesFromCanvas(
      node.x(),
      node.y(),
      newWidth,
      newHeight
    )

    // Update Area Data in Directus
    await updateAreaData(
      dbCoords.x,
      dbCoords.y,
      dbCoords.width,
      dbCoords.height
    )
  } else if (selectedName.startsWith('marker-')) {
    const marker = markers.value.find(m => m.name === selectedName)
    if (!marker) return

    // Update marker position only (no resizing for markers)
    marker.x = node.x()
    marker.y = node.y()

    // Convert canvas coordinates back to database coordinates
    const dbCoords = scaleCoordinatesFromCanvas(node.x(), node.y())

    // Determine if the marker lies within any area and fetch its id
    const containingAreaId = getContainingAreaId(node.x(), node.y())

    // Update Marker Data in Directus (also update area_id if applicable)
    await updateMarkerData(
      dbCoords.x,
      dbCoords.y,
      containingAreaId ?? undefined
    )
  }
}

const updateTransformer = () => {
  if (!transformer.value) return

  const transformerNode = transformer.value.getNode()
  const stage = transformerNode.getStage()
  const selected = selectedShapeName.value

  const selectedNode = stage.findOne('.' + selected)
  // do nothing if selected node is already attached
  if (selectedNode === transformerNode.node()) {
    return
  }

  if (selectedNode) {
    transformerNode.nodes([selectedNode])
    transformerNode.rotateEnabled(false)
    transformerNode.enabledAnchors([
      'top-left',
      'top-right',
      'bottom-left',
      'bottom-right',
      'middle-left',
      'middle-right',
      'top-center',
      'bottom-center'
    ])
    if (selected.startsWith('marker-')) {
      // For markers: disable resizing, only allow moving
      transformerNode.enabledAnchors([])
    }
  } else {
    // remove transformer
    transformerNode.nodes([])
  }
}

const handleStageMouseDown = (e: any) => {
  // creation mode handling first
  if (creationMode.value === 'marker') {
    const pointer = e.target.getStage().getPointerPosition()
    if (pointer) {
      createMarkerAt(pointer.x, pointer.y)
      creationMode.value = null
    }
    return // stop further processing so area/marker is not selected
  }

  if (creationMode.value === 'area') {
    const stage = e.target.getStage()
    const pointer = stage.getPointerPosition()
    if (pointer) {
      createAreaAt(pointer.x, pointer.y)
      creationMode.value = null
    }
    return
  }

  // clicked on stage - clear selection
  if (e.target === e.target.getStage()) {
    selectedShapeName.value = ''
    selectedData.value = null
    updateTransformer()
    return
  }

  // clicked on transformer - do nothing
  const clickedOnTransformer = e.target.getParent().className === 'Transformer'
  if (clickedOnTransformer) {
    return
  }

  // find clicked element by its name
  const name = e.target.name()

  // Check if it's an area
  const area = areas.value.find(r => r.name === name)
  if (area) {
    selectedShapeName.value = name
    selectedData.value = {
      type: 'area',
      ...area.data
    }
    updateTransformer()
    return
  }

  // Check if it's a marker
  const marker = markers.value.find(m => m.name === name)
  if (marker) {
    selectedShapeName.value = name
    selectedData.value = {
      type: 'marker',
      ...marker.data
    }
    updateTransformer()
    return
  }

  // Nothing was selected
  selectedShapeName.value = ''
  selectedData.value = null
  updateTransformer()
}

// API helpers for creating new records
const createAreaAt = async (canvasX: number, canvasY: number) => {
  const dbCoords = scaleCoordinatesFromCanvas(canvasX, canvasY)
  // default size 150x150 (db coordinates)
  const defaultWidth = 150
  const defaultHeight = 150
  await dFetch('/items/t_area', {
    method: 'POST',
    body: {
      event_id: eventId,
      name: 'New Area',
      x_axis: dbCoords.x,
      y_axis: dbCoords.y,
      width: defaultWidth,
      height: defaultHeight
    }
  })
  await refreshAreas()
}

const createMarkerAt = async (canvasX: number, canvasY: number) => {
  const dbCoords = scaleCoordinatesFromCanvas(canvasX, canvasY)
  const containingAreaId = getContainingAreaId(canvasX, canvasY)

  // Create marker with default type 1 (Game) - linked data will be created only when user saves
  const newMarker = await dFetch('/items/t_mark', {
    method: 'POST',
    body: {
      event_id: eventId,
      name: 'New Marker',
      mark_type: 1,
      x_axis: dbCoords.x,
      y_axis: dbCoords.y,
      area_id: containingAreaId
    }
  })

  await refreshMarkers()

  // Auto-select the newly created marker for immediate editing
  if (newMarker && newMarker.id) {
    const markerData = markersData.value?.find(m => m.id === newMarker.id)
    if (markerData) {
      selectedShapeName.value = `marker-${markerData.id}`
      selectedData.value = { type: 'marker', ...markerData }
      nextTick(() => updateTransformer())
    }
  }
}

// Update Area Data
const updateAreaData = async (
  x?: number,
  y?: number,
  width?: number,
  height?: number
) => {
  isLoading.value = true
  try {
    await dFetch(`/items/t_area/${selectedData.value.id}`, {
      method: 'PATCH',
      body: {
        ...selectedData.value,
        x_axis: x,
        y_axis: y,
        width,
        height
      }
    })
  } catch (error) {
    console.error('Failed to update area:', error)
    return
  }
  await refreshAreas()
  dataUpdatedToast('Area', 'updated')
  isLoading.value = false
}

// Delete Area
const deleteArea = async () => {
  await dFetch(`/items/t_area/${selectedData.value.id}`, {
    method: 'DELETE'
  })
  await refreshAreas()
  dataUpdatedToast('Area', 'deleted')
  selectedData.value = null
  selectedShapeName.value = ''
  selectedDataType.value = null
  openAreaDeleteModal.value = false
}

// Update Marker Data (for position/area changes only)
const updateMarkerData = async (
  x?: number,
  y?: number,
  areaId?: number | null
) => {
  isLoading.value = true
  // Build patch body conditionally to avoid overriding when not provided
  const body: any = { ...selectedData.value }
  if (x !== undefined) body.x_axis = x
  if (y !== undefined) body.y_axis = y
  if (areaId !== undefined) body.area_id = areaId

  await dFetch(`/items/t_mark/${selectedData.value.id}`, {
    method: 'PATCH',
    body
  })

  await refreshMarkers()
  dataUpdatedToast('Marker', 'updated')
  isLoading.value = false
}

// Update both Marker and Linked Data together
const updateMarkerAndLinkedData = async () => {
  isLoading.value = true

  try {
    // Update marker data
    await dFetch(`/items/t_mark/${selectedData.value.id}`, {
      method: 'PATCH',
      body: selectedData.value
    })

    // Update linked data if it exists
    if (selectedMarkerExtra.value) {
      await saveMarkerExtra()
    }

    await refreshMarkers()
    toast.add({
      title: 'Marker & linked data updated',
      color: 'success',
      duration: 1000
    })
  } catch (error) {
    console.error('Failed to update marker and linked data:', error)
    toast.add({
      title: 'Update failed',
      color: 'error',
      duration: 2000
    })
  } finally {
    isLoading.value = false
  }
}

const dataUpdatedToast = (text: string, action: string) => {
  toast.add({
    title: `${text} ${action}`,
    description: `${text} has been successfully ${action}.`,
    color: 'success',
    duration: 1000
  })
}

// Handle image file selection and upload to S3
const handleImageFileSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  try {
    const result = await uploadFile(file)

    if (result.success && result.url) {
      // Update the marker extra data with the S3 URL (both fields for compatibility)
      selectedMarkerExtra.value.image = result.url
      selectedMarkerExtra.value.thumbnail_url = result.url

      console.log('Updating marker extra with image URL:', result.url)
      console.log('Selected marker extra before save:', selectedMarkerExtra.value)

      // Save the marker extra data
      await saveMarkerExtra()

      // Refresh markers to see the changes
      await refreshMarkers()

      toast.add({
        title: 'Image uploaded',
        description: 'Marker image has been uploaded and saved successfully.',
        color: 'success',
        duration: 2000
      })
    }
  } catch (error: any) {
    console.error('Upload error:', error)
    toast.add({
      title: 'Upload failed',
      description: error.message || 'Failed to upload marker image.',
      color: 'error',
      duration: 3000
    })
  } finally {
    // Clear the file input
    if (target) {
      target.value = ''
    }
  }
}

// Delete Marker and its linked data
const deleteMarker = async () => {
  try {
    // Delete linked data first if it exists
    if (selectedMarkerExtra.value) {
      const table = markerTypeTableMap[selectedData.value.mark_type as keyof typeof markerTypeTableMap]
      if (table) {
        await dFetch(`/items/${table}/${selectedMarkerExtra.value.id}`, {
          method: 'DELETE'
        })
      }
    }

    // Delete the marker
    await dFetch(`/items/t_mark/${selectedData.value.id}`, {
      method: 'DELETE'
    })

    await refreshMarkers()
    toast.add({
      title: 'Marker & linked data deleted',
      description: 'Marker and its linked data have been successfully deleted.',
      color: 'success',
      duration: 1000
    })
  } catch (error) {
    console.error('Failed to delete marker and linked data:', error)
    toast.add({
      title: 'Delete failed',
      description: 'Failed to delete marker and linked data.',
      color: 'error',
      duration: 2000
    })
  } finally {
    selectedData.value = null
    selectedShapeName.value = ''
    selectedDataType.value = null
    openMarkerDeleteModal.value = false
  }
}

// Keyboard shortcuts
useEventListener('keydown', (event: KeyboardEvent) => {
  // Check for Command key on Mac or Ctrl key on Windows/Linux
  const isModifierPressed = event.metaKey || event.ctrlKey

  if (!isModifierPressed) return

  // Command/Ctrl + S: Save/Update marker or area
  if (event.key.toLowerCase() === 's') {
    event.preventDefault()

    // Only save if something is actually selected
    if (selectedData.value) {
      if (selectedData.value.type === 'marker') {
        updateMarkerAndLinkedData()
      } else if (selectedData.value.type === 'area') {
        updateAreaData()
      }
    }
    // Do nothing if nothing is selected
  }

  // Command/Ctrl + M: Toggle new marker mode
  if (event.key.toLowerCase() === 'm') {
    event.preventDefault()

    if (creationMode.value === 'marker') {
      creationMode.value = null
    } else {
      creationMode.value = 'marker'
    }
  }
})
</script>

<template>
  <UDashboardPanel id="events">
    <template #header>
      <UDashboardNavbar title="Map Editor">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Area"
            trailing-icon="i-lucide-plus"
            color="error"
            variant="outline"
            class="hidden lg:flex"
            @click="creationMode = 'area'"
          />
          <UButton
            label="New Marker"
            trailing-icon="i-lucide-plus"
            color="primary"
            class="hidden lg:flex rounded-full"
            @click="creationMode = 'marker'"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="grid grid-cols-12 gap-4 w-full h-full">
        <div class="col-span-12 lg:col-span-2 overflow-x-hidden">
          <!-- Area or Marker Details -->
          <div v-if="selectedShapeName">
            <div v-if="selectedData.type === 'marker'" class="grid gap-2">
              <h3 class="text-lg font-bold">
                Marker Details {{ selectedData.booth_number }}
              </h3>
              <hr>
              <UFormField label="Name">
                <UTextarea
                  v-model="selectedData.name"
                  class="w-full"
                />
              </UFormField>
              <UFormField label="Marker Type">
                <USelect
                  v-model="selectedData.mark_type"
                  :items="markerTypeOptions"
                  class="w-full"
                />
                <template v-if="selectedData.mark_type === 1" #help>
                  <div class="text-xs text-blue-600 mt-1">
                    <strong>Game Marker:</strong> This marker will launch a minigame when scanned by users.
                  </div>
                </template>
              </UFormField>
              <UFormField label="Booth Number">
                <UInput
                  v-model="selectedData.booth_number"
                  class="w-full"
                  placeholder="e.g., A-101, Hall-B-25, Booth-42"
                />
                <template #help>
                  <div class="text-xs text-neutral-500 mt-1">
                    Optional booth/location identifier for this marker
                  </div>
                </template>
              </UFormField>
              <UButton
                v-if="selectedData.mark_type === 1"
                :to="`/qr?url=${selectedGameDetails?.game_url}?markerId=${selectedData.id}`"
                target="_blank"
                color="info"
                trailing-icon="i-lucide-external-link"
              >
                Generate QR Code
              </UButton>
              <!-- Linked data form -->
              <div v-if="selectedMarkerExtra" class="mt-6 space-y-2">
                <h4 class="font-bold text-sm">
                  Linked {{ getMarkerTypeName(selectedData.mark_type) }} Info
                </h4>

                <UFormField label="Description">
                  <UTextarea v-model="selectedMarkerExtra.description" class="w-full" />
                </UFormField>

                <!-- Game selection field for Game markers only -->
                <UFormField v-if="selectedData.mark_type === 1" label="Selected Game">
                  <USelect
                    v-model="selectedMarkerExtra.game_available_id"
                    :items="gameOptions"
                    :loading="!availableGames"
                    class="w-full"
                    placeholder="Select a game"
                    :disabled="!gameOptions.length"
                  />
                  <!-- <template #help>
                    <span class="text-xs text-neutral-500">
                      Choose which minigame this marker will launch
                      <span v-if="!gameOptions.length" class="text-orange-500">
                        (No games available)
                      </span>
                    </span>
                  </template> -->
                </UFormField>

                <!-- Score field for Game markers only -->
                <UFormField v-if="selectedData.mark_type === 1" label="Energy Reward">
                  <UInput
                    v-model="selectedData.mark_score"
                    type="number"
                    min="1"
                    max="1000"
                    class="w-full"
                    placeholder="Enter points (e.g., 100)"
                  />
                  <template #help>
                    <span class="text-xs text-neutral-500">
                      Points awarded to users when they complete this game
                    </span>
                  </template>
                </UFormField>

                <!-- Game level field for Game markers only -->
                <!-- <UFormField v-if="selectedData.mark_type === 1" label="Game Level">
                  <USelect
                    v-model="selectedMarkerExtra.game_level"
                    :items="[
                      { value: 1, label: 'Easy' },
                      { value: 2, label: 'Medium' },
                      { value: 3, label: 'Hard' }
                    ]"
                    class="w-full"
                    placeholder="Select difficulty level"
                  />
                </UFormField> -->

                <!-- Game preview section for Game markers -->
                <div v-if="selectedData.mark_type === 1" class="mt-2">
                  <!-- Selected game preview -->
                  <div v-if="selectedGameDetails" class="p-3 bg-green-50 border border-green-200 rounded">
                    <h5 class="font-semibold text-sm text-green-700 mb-1">
                      ✓ Selected Game
                    </h5>
                    <p class="text-xs text-green-600 mb-1">
                      <strong>{{ selectedGameDetails.name }}</strong>
                    </p>
                    <p v-if="selectedGameDetails.description" class="text-xs text-green-500 mb-1">
                      {{ selectedGameDetails.description }}
                    </p>
                    <p v-if="selectedGameDetails.game_url" class="text-xs text-blue-600 break-all">
                      <NuxtLink :to="selectedGameDetails.game_url" target="_blank" rel="noopener noreferrer">Preview URL: {{ selectedGameDetails.game_url }}</NuxtLink>
                    </p>
                  </div>

                  <!-- No game selected message -->
                  <div v-else-if="selectedMarkerExtra && !selectedMarkerExtra.game_available_id" class="p-3 bg-yellow-50 border border-yellow-200 rounded">
                    <h5 class="font-semibold text-sm text-yellow-700 mb-1">
                      ⚠ No Game Selected
                    </h5>
                    <p class="text-xs text-yellow-600">
                      Please select a minigame from the dropdown above to complete the game marker setup.
                    </p>
                  </div>
                </div>

                <!-- Image field - hidden for Game markers -->
                <UFormField v-if="selectedData.mark_type !== 1" label="Image">
                  <template #default>
                    <div class="space-y-2">
                      <!-- Preview current image if URL exists -->
                      <img
                        v-if="selectedMarkerExtra.image || selectedMarkerExtra.thumbnail_url"
                        :src="selectedMarkerExtra.image || selectedMarkerExtra.thumbnail_url"
                        alt="Marker image preview"
                        class="w-full max-w-sm object-cover rounded border"
                      >
                      <!-- File upload input -->
                      <UInput
                        type="file"
                        accept="image/*"
                        :disabled="isImageUploading || isExtraSaving"
                        class="block w-full"
                        @change="handleImageFileSelect"
                      />
                      <!-- Show upload progress -->
                      <div v-if="isImageUploading" class="text-sm text-blue-600">
                        Uploading image...
                      </div>
                      <!-- Show current image URL -->
                      <UInput
                        v-if="selectedMarkerExtra.image || selectedMarkerExtra.thumbnail_url"
                        :model-value="selectedMarkerExtra.image || selectedMarkerExtra.thumbnail_url"
                        type="url"
                        readonly
                        class="w-full text-xs"
                        placeholder="S3 URL will appear here after upload"
                      />
                    </div>
                  </template>
                </UFormField>
                <!-- Show-specific fields -->
                <UFormField v-if="selectedData.mark_type === 3" label="Begin Time">
                  <UInput v-model="selectedMarkerExtra.begin_time" type="datetime-local" class="w-full" />
                </UFormField>
                <UFormField v-if="selectedData.mark_type === 3" label="End Time">
                  <UInput v-model="selectedMarkerExtra.end_time" type="datetime-local" class="w-full" />
                </UFormField>
                <!-- Tags field for non-Show and non-Game markers -->
                <UFormField v-if="selectedData.mark_type !== 3 && selectedData.mark_type !== 1" label="Tags">
                  <UInput v-model="selectedMarkerExtra.tags" class="w-full" placeholder="Enter tags (comma-separated)" />
                </UFormField>
                <!-- Website URL field for non-Show and non-Game markers -->
                <UFormField v-if="selectedData.mark_type !== 3 && selectedData.mark_type !== 1" label="Website URL">
                  <UInput v-model="selectedMarkerExtra.website_url" class="w-full" placeholder="https://example.com" />
                </UFormField>
              </div>
              <!-- Save button with validation warning -->
              <div class="mt-3 space-y-2">
                <UButton
                  :label="isGameMarkerComplete ? 'Save' : 'Save (Incomplete Game Setup)'"
                  :color="isGameMarkerComplete ? 'primary' : 'warning'"
                  :loading="isLoading || isExtraSaving"
                  class="w-full"
                  accesskey="meta,s"
                  @click="updateMarkerAndLinkedData()"
                >
                  <template #trailing>
                    <UKbd value="meta" class="ml-auto" />
                    <UKbd value="s" />
                  </template>
                </UButton>
                <div v-if="!isGameMarkerComplete" class="text-xs text-orange-600">
                  ⚠ This game marker is missing required game configuration
                </div>
              </div>
              <UModal v-model:open="openMarkerDeleteModal">
                <UButton label="Delete Marker" color="error" variant="solid" />
                <template #header>
                  <h3>Confirm Delete Marker</h3>
                </template>
                <template #body>
                  <div class="flex gap-2">
                    <UButton :label="`Delete ${selectedData.name}`" color="error" @click="deleteMarker" />
                    <UButton
                      label="Close"
                      variant="outline"
                      color="neutral"
                      class="ml-auto"
                      @click="openMarkerDeleteModal = false"
                    />
                  </div>
                </template>
              </UModal>
            </div>
            <div v-if="selectedData.type === 'area'" class="grid gap-2">
              <h3 class="text-lg font-bold">
                Area Details {{ selectedData.id }}
              </h3>
              <hr>
              <UFormField label="Name">
                <UTextarea
                  v-model="selectedData.name"
                  type="text"
                  class="w-full"
                />
              </UFormField>
              <UModal v-model:open="openAreaDeleteModal">
                <UButton
                  label="Delete Area"
                  color="error"
                  variant="solid"
                  class="mt-12"
                />
                <template #header>
                  <h3>Confirm Delete Area</h3>
                </template>
                <template #body>
                  <UButton :label="`Delete ${selectedData.name}`" color="error" @click="deleteArea" />
                </template>
              </UModal>
            </div>
          </div>
          <div v-else id="map-stats">
            <div v-if="creationMode === 'area'" class="space-y-2">
              <p>Creating a new area</p>
              <hr>
              <p class="text-lg text-error">
                Click anywhere on the map to start
              </p>
            </div>

            <!-- If new Marker mode, show marker form (name, type) and a create button which will add the marker to the center of the map -->
            <div v-if="creationMode === 'marker'" class="space-y-2">
              <p>Creating a new marker</p>
              <hr>
              <p class="text-lg text-error">
                Click anywhere on the map to drop a new marker
              </p>
            </div>

            <!-- Area & Marker list when not in creation mode -->
            <div v-if="!creationMode" class="space-y-4 max-h-[80vh] overflow-y-auto">
              <!-- Sort areas by name -->
              <div
                v-for="area in [...(areasData || [])].sort((a, b) => (a.name || '').localeCompare(b.name || ''))"
                :key="area.id"
                class="border-b pb-2"
              >
                <h4
                  class="font-bold cursor-pointer hover:underline break-words"
                  @click="selectAreaFromList(area)"
                >
                  {{ area.name }}
                </h4>
                <!-- Sort markers within area by name -->
                <ul class="ml-4 list-disc text-sm">
                  <li
                    v-for="marker in [...(markersByArea[area.id] || [])].sort((a, b) => (a.name || '').localeCompare(b.name || ''))"
                    :key="marker.id"
                    class="cursor-pointer hover:underline break-words"
                    @click="selectMarkerFromList(marker)"
                  >
                    {{ marker.name }}
                  </li>
                </ul>
              </div>

              <!-- Markers without area -->
              <div v-if="(markersByArea['null'] || []).length" class="pt-2">
                <h4 class="font-bold">
                  Other Markers
                </h4>
                <ul class="ml-4 list-disc text-sm">
                  <li
                    v-for="marker in [...(markersByArea['null'] || [])].sort((a, b) => (a.name || '').localeCompare(b.name || ''))"
                    :key="marker.id"
                    class="cursor-pointer hover:underline break-words"
                    @click="selectMarkerFromList(marker)"
                  >
                    {{ marker.name }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div ref="editor" class="col-span-12 lg:col-span-10" style="position: relative; z-index: 1; background-color: rgba(0,0,0,0.03);">
          <!-- In-Progress: Konva Map Editor -->
          <v-stage
            :config="{ width, height }"
            style="outline: 1px dashed #ccc;"
            @mousedown="handleStageMouseDown"
            @touchstart="handleStageMouseDown"
          >
            <!-- Event Map as Background Image -->
            <v-layer>
              <v-image
                :config="{
                  x: 0,
                  y: 0,
                  image: backgroundImage,
                  width: width,
                  height: height
                }"
              />
            </v-layer>
            <v-layer>
              <v-text
                v-for="area in areas"
                :key="area.name"
                :config="{
                  ...area,
                  name: `text-area-${area.name}`,
                  fontSize: 16,
                  fill: 'white',
                  align: 'left',
                  strokeWidth: 0,
                  x: area.x + 20,
                  y: area.y + 20,
                  opacity: 0.5,
                  draggable: false
                }"
              />
              <v-text
                v-for="marker in markers"
                :key="marker.name"
                :config="{
                  ...marker,
                  name: `text-marker-${marker.name}`,
                  fontSize: 8,
                  fill: 'white',
                  strokeWidth: 0,
                  x: marker.x + 12,
                  y: marker.y - 12,
                  width: 42,
                  draggable: false
                }"
              />
            </v-layer>
            <v-layer>
              <!-- Areas -->
              <v-rect
                v-for="area in areas"
                :key="area.name"
                :config="area"
                @dragend="handleTransformEnd"
                @transformend="handleTransformEnd"
              />
              <!-- Markers -->
              <v-circle
                v-for="marker in markers"
                :key="marker.name"
                :config="marker"
                @dragend="handleTransformEnd"
              />
              <v-transformer ref="transformer" />
            </v-layer>
          </v-stage>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
