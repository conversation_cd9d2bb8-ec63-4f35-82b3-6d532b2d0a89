<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'

const { useDirectusFetch } = useDirectus()

const UButton = resolveComponent('UButton')
const UBadge = resolveComponent('UBadge')

const table = useTemplateRef('table')

// Fetch events from Directus using the composable
const { data: events } = await useDirectusFetch<TEvent[]>('/items/t_event', {
  key: 'all-events'
})

const onRowSelect = (row: TableRow<TEvent>) => {
  navigateTo(`events/${row.original.id}`)
}

const columns: TableColumn<TEvent>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'name',
    header: 'Event Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.name || 'Untitled Event')
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.original.description
      if (!description)
        return h('span', { class: 'text-neutral-400' }, 'No description')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        description.length > 50
          ? `${description.substring(0, 50)}...`
          : description
      )
    }
  },
  {
    accessorKey: 'start_date',
    header: 'Start Date',
    cell: ({ row }) => formatDate(row.original.start_date)
  },
  {
    accessorKey: 'end_date',
    header: 'End Date',
    cell: ({ row }) => formatDate(row.original.end_date)
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = getEventStatus(row.original)
      const color = getStatusColor(status)

      return h(
        UBadge,
        {
          class: 'capitalize',
          variant: 'subtle',
          color
        },
        () => status
      )
    }
  }
]

// Page metadata
definePageMeta({
  title: 'Events'
})
</script>

<template>
  <UDashboardPanel id="events">
    <template #header>
      <UDashboardNavbar title="Events">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Event"
            trailing-icon="i-lucide-plus"
            color="primary"
            to="/events/new"
            class="hidden lg:flex"
          />
          <UButton
            icon="i-lucide-plus"
            color="primary"
            to="/events/new"
            class="lg:hidden"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        ref="table"
        :data="events"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon
              name="i-lucide-calendar-x"
              class="w-8 h-8 text-neutral-400"
            />
            <p class="text-sm text-neutral-500">
              No events found.
            </p>
            <UButton
              label="Create your first event"
              color="primary"
              to="/events/new"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
