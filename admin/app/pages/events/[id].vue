<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const route = useRoute()
const router = useRouter()
const toast = useToast()
const { useDirectusFetch, dFetch } = useDirectus()

const eventId = computed(() => route.params.id as string)

// Fetch event data
const {
  data: event,
  status,
  refresh
} = await useDirectusFetch<TEvent>(`/items/t_event/${eventId.value}`, {
  key: `event-${eventId.value}`,
  params: {
    fields: ['*']
  }
})

// Handle case where event is not found
watch(status, (newStatus) => {
  if (newStatus === 'error') {
    toast.add({
      title: 'Event Not Found',
      description: 'The requested event could not be found.',
      color: 'error'
    })
    router.push('/events')
  }
})

// Form validation schema (same as create)
const schema = z
  .object({
    name: z
      .string()
      .min(1, 'Event name is required')
      .max(255, 'Event name is too long'),
    description: z.string().optional(),
    start_date: z.string().min(1, 'Start date is required'),
    end_date: z.string().min(1, 'End date is required')
  })
  .refine(
    (data) => {
      if (data.start_date && data.end_date) {
        return new Date(data.start_date) <= new Date(data.end_date)
      }
      return true
    },
    {
      message: 'End date must be after start date',
      path: ['end_date']
    }
  )

type Schema = z.output<typeof schema>

// Form state
const isLoading = ref(false)
const isDeleting = ref(false)
const state = reactive<Partial<Schema>>({
  name: '',
  description: '',
  start_date: '',
  end_date: ''
})

// Image upload state
const eventImageUrl = ref<string | null>(null)

// Watch for event data and populate form
watch(
  event,
  (newEvent) => {
    if (newEvent) {
      state.name = newEvent.name || ''
      state.description = newEvent.description || ''
      state.start_date = newEvent.start_date || ''
      state.end_date = newEvent.end_date || ''
      eventImageUrl.value = newEvent.image_url || null
    }
  },
  { immediate: true }
)

// Update event
async function onSubmit(event: FormSubmitEvent<Schema>) {
  isLoading.value = true

  try {
    const eventData: Partial<TEvent> = {
      name: event.data.name.trim(),
      description: event.data.description?.trim() || null,
      start_date: event.data.start_date,
      end_date: event.data.end_date,
      image_url: eventImageUrl.value
    }

    await dFetch(`/items/t_event/${eventId.value}`, {
      method: 'PATCH',
      body: eventData
    })
    await refresh()

    toast.add({
      title: 'Event Updated',
      description: `Event "${event.data.name}" has been updated successfully.`,
      color: 'success'
    })
  } catch (error) {
    console.error('Error updating event:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to update event. Please try again.',
      color: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// Delete event
async function handleDelete() {
  if (!event.value) return

  isDeleting.value = true

  try {
    await dFetch(`/items/t_event/${eventId.value}`, {
      method: 'DELETE'
    })

    toast.add({
      title: 'Event Deleted',
      description: `Event "${event.value.name}" has been deleted successfully.`,
      color: 'success'
    })

    await router.push('/events')
  } catch (error) {
    console.error('Error deleting event:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to delete event. Please try again.',
      color: 'error'
    })
  } finally {
    isDeleting.value = false
  }
}

function handleCancel() {
  router.push('/events')
}

// Image upload handlers
function handleImageUploadSuccess(response: any) {
  toast.add({
    title: 'Image Uploaded',
    description: 'Event image has been uploaded successfully.',
    color: 'success'
  })
}

function handleImageUploadError(error: string) {
  toast.add({
    title: 'Upload Failed',
    description: error,
    color: 'error'
  })
}

// Page metadata
definePageMeta({
  title: 'Edit Event'
})

useSeoMeta({
  title: () =>
    event.value
      ? `Edit ${event.value.name} - Open Portal Expo Admin`
      : 'Edit Event - Open Portal Expo Admin'
})
</script>

<template>
  <UDashboardPanel id="edit-event">
    <template #header>
      <UDashboardNavbar :title="`Edit: ${event?.name || 'Event'}`">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Cancel"
            color="neutral"
            variant="ghost"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div v-if="event">
        <UForm :schema="schema" :state="state" @submit="onSubmit">
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-lg font-semibold">
                    Edit Event
                  </h2>
                  <p class="text-sm text-neutral-500 mt-1">
                    Update the event information below.
                  </p>
                </div>
                <div class="text-sm text-neutral-500">
                  ID: #{{ event.id }}
                </div>
              </div>
            </template>

            <div class="grid gap-6">
              <!-- Event Name -->
              <UFormField
                label="Event Name"
                description="The display name for your event"
                name="name"
                required
              >
                <UInput
                  v-model="state.name"
                  placeholder="Enter event name..."
                  :disabled="isLoading"
                  size="lg"
                  class="w-full max-w-2xl"
                />
              </UFormField>

              <!-- Event Description -->
              <UFormField
                label="Description"
                description="A brief description of the event (optional)"
                name="description"
              >
                <UTextarea
                  v-model="state.description"
                  placeholder="Enter event description..."
                  :disabled="isLoading"
                  :rows="4"
                  class="w-full"
                />
              </UFormField>

              <!-- Event Image -->
              <UFormField label="Event Image" description="Upload an image for the event (optional)" name="image">
                <ImageUpload
                  v-model="eventImageUrl"
                  :disabled="isLoading"
                  alt="Event image"
                  @upload-success="handleImageUploadSuccess"
                  @upload-error="handleImageUploadError"
                />
              </UFormField>

              <!-- Date Range -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <UFormField
                  label="Start Date & Time"
                  description="When the event begins"
                  name="start_date"
                  required
                >
                  <UInput v-model="state.start_date" type="datetime-local" :disabled="isLoading" />
                </UFormField>

                <UFormField
                  label="End Date & Time"
                  description="When the event ends"
                  name="end_date"
                  required
                >
                  <UInput v-model="state.end_date" type="datetime-local" :disabled="isLoading" />
                </UFormField>
              </div>
            </div>

            <template #footer>
              <div class="flex justify-between">
                <UButton
                  label="Delete Event"
                  color="error"
                  variant="outline"
                  icon="i-lucide-trash"
                  :loading="isDeleting"
                  @click="handleDelete"
                />

                <div class="flex gap-3">
                  <UButton
                    label="Cancel"
                    color="neutral"
                    variant="outline"
                    :disabled="isLoading"
                    @click="handleCancel"
                  />
                  <UButton
                    label="Update Event"
                    color="primary"
                    type="submit"
                    :loading="isLoading"
                  />
                </div>
              </div>
            </template>
          </UCard>
        </UForm>

        <!-- Quick Actions -->
        <UCard class="mt-6">
          <template #header>
            <h2 class="text-lg font-semibold flex items-center gap-2">
              <UIcon name="i-lucide-zap" class="w-5 h-5" />
              Quick Actions
            </h2>
          </template>

          <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <UButton
              label="Manage Maps"
              icon="i-lucide-map"
              color="primary"
              variant="subtle"
              :to="`/events/maps/${eventId}`"
              block
            />
            <UButton
              label="Back to Events"
              icon="i-lucide-arrow-left"
              color="neutral"
              variant="outline"
              to="/events"
              block
            />
          </div>
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>
