<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const toast = useToast()
const router = useRouter()
const { dFetch } = useDirectus()

// Form validation schema
const schema = z
  .object({
    name: z
      .string()
      .min(1, 'Event name is required')
      .max(255, 'Event name is too long'),
    description: z.string().optional(),
    start_date: z.string().min(1, 'Start date is required'),
    end_date: z.string().min(1, 'End date is required')
  })
  .refine(
    (data) => {
      if (data.start_date && data.end_date) {
        return new Date(data.start_date) <= new Date(data.end_date)
      }
      return true
    },
    {
      message: 'End date must be after start date',
      path: ['end_date']
    }
  )

type Schema = z.output<typeof schema>

// Form state
const isLoading = ref(false)
const state = reactive<Partial<Schema>>({
  name: '',
  description: '',
  start_date: '',
  end_date: ''
})

// Image upload state
const eventImageUrl = ref<string | null>(null)

async function onSubmit(event: FormSubmitEvent<Schema>) {
  isLoading.value = true

  try {
    const eventData: Partial<TEvent> = {
      name: event.data.name.trim(),
      description: event.data.description?.trim() || null,
      start_date: event.data.start_date,
      end_date: event.data.end_date,
      image_url: eventImageUrl.value,
      user_id: 1 // TODO: Get from authenticated user
    }

    await dFetch('/items/t_event', { method: 'POST', body: eventData })

    toast.add({
      title: 'Event Created',
      description: `Event "${event.data.name}" has been created successfully.`,
      color: 'success'
    })

    // Redirect to events list or the new event detail page
    await router.push('/events')
  } catch (error) {
    console.error('Error creating event:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to create event. Please try again.',
      color: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

function handleCancel() {
  router.push('/events')
}

// Image upload handlers
function handleImageUploadSuccess(response: any) {
  toast.add({
    title: 'Image Uploaded',
    description: 'Event image has been uploaded successfully.',
    color: 'success'
  })
}

function handleImageUploadError(error: string) {
  toast.add({
    title: 'Upload Failed',
    description: error,
    color: 'error'
  })
}

// Page metadata
definePageMeta({
  title: 'New Event'
})

useSeoMeta({
  title: 'New Event - Open Portal Expo Admin'
})
</script>

<template>
  <UDashboardPanel id="new-event">
    <template #header>
      <UDashboardNavbar title="Create New Event">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Cancel"
            color="neutral"
            variant="ghost"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div>
        <UForm :schema="schema" :state="state" @submit="onSubmit">
          <UCard>
            <template #header>
              <h2 class="text-lg font-semibold">
                Event Information
              </h2>
              <p class="text-sm text-neutral-500 mt-1">
                Create a new event for the Open Portal Expo platform.
              </p>
            </template>

            <div class="grid gap-6">
              <!-- Event Name -->
              <UFormField
                label="Event Name"
                description="The display name for your event"
                name="name"
                required
              >
                <UInput
                  v-model="state.name"
                  placeholder="Enter event name..."
                  :disabled="isLoading"
                  size="lg"
                  class="w-full max-w-2xl"
                />
              </UFormField>

              <!-- Event Description -->
              <UFormField
                label="Description"
                description="A brief description of the event (optional)"
                name="description"
              >
                <UTextarea
                  v-model="state.description"
                  placeholder="Enter event description..."
                  :disabled="isLoading"
                  :rows="4"
                  class="w-full"
                />
              </UFormField>

              <!-- Event Image -->
              <UFormField label="Event Image" description="Upload an image for the event (optional)" name="image">
                <ImageUpload
                  v-model="eventImageUrl"
                  :disabled="isLoading"
                  alt="Event image"
                  @upload-success="handleImageUploadSuccess"
                  @upload-error="handleImageUploadError"
                />
              </UFormField>

              <!-- Date Range -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <UFormField
                  label="Start Date & Time"
                  description="When the event begins"
                  name="start_date"
                  required
                >
                  <UInput v-model="state.start_date" type="datetime-local" :disabled="isLoading" />
                </UFormField>

                <UFormField
                  label="End Date & Time"
                  description="When the event ends"
                  name="end_date"
                  required
                >
                  <UInput v-model="state.end_date" type="datetime-local" :disabled="isLoading" />
                </UFormField>
              </div>
            </div>

            <template #footer>
              <div class="flex justify-end gap-3">
                <UButton
                  label="Cancel"
                  color="neutral"
                  variant="outline"
                  :disabled="isLoading"
                  @click="handleCancel"
                />
                <UButton
                  label="Create Event"
                  color="primary"
                  type="submit"
                  :loading="isLoading"
                />
              </div>
            </template>
          </UCard>
        </UForm>

        <!-- Preview Card -->
        <UCard v-if="state.name" class="mt-6">
          <template #header>
            <h3 class="text-md font-medium">
              Preview
            </h3>
          </template>

          <div>
            <div>
              <h4 class="font-semibold text-lg">
                {{ state.name }}
              </h4>
              <p v-if="state.description" class="text-neutral-600 mt-1">
                {{ state.description }}
              </p>
              <div v-if="eventImageUrl" class="mt-3">
                <img :src="eventImageUrl" alt="Event preview" class="w-full max-w-md h-auto rounded-lg shadow-sm">
              </div>
            </div>

            <div
              v-if="state.start_date || state.end_date"
              class="flex flex-col sm:flex-row gap-2 text-sm text-neutral-500"
            >
              <div v-if="state.start_date" class="flex items-center gap-1">
                <UIcon name="i-lucide-calendar" class="w-4 h-4" />
                <span>Starts:
                  {{ new Date(state.start_date).toLocaleString() }}</span>
              </div>
              <div v-if="state.end_date" class="flex items-center gap-1">
                <UIcon name="i-lucide-calendar" class="w-4 h-4" />
                <span>Ends: {{ new Date(state.end_date).toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>
