<script setup lang="ts">
const { useDirectusFetch, dFetch } = useDirectus()

const { data: profile } = await useDirectusFetch<DirectusUsers>('/users/me')

const toast = useToast()
async function onSubmit() {
  // TODO: implement name update
  toast.add({
    title: 'Setup Needed',
    description: 'AWS SES setup incomplete',
    icon: 'i-lucide-help-circle',
    color: 'warning'
  })
}

const resetPassword = async () => {
  // TODO: implement password reset
  await dFetch('/auth/password/request', {
    method: 'POST',
    body: {
      email: profile.value!.email
    }
  })

  toast.add({
    title: 'Setup Needed',
    description: 'AWS SES setup incomplete',
    icon: 'i-lucide-help-circle',
    color: 'warning'
  })
}
</script>

<template>
  <UForm
    id="settings"
    :state="profile"
    @submit="onSubmit"
  >
    <UPageCard
      title="Profile"
      description="These informations will be displayed publicly."
      variant="naked"
      orientation="horizontal"
      class="mb-4"
    >
      <UButton
        form="settings"
        label="Save changes"
        color="neutral"
        type="submit"
        class="w-fit lg:ms-auto"
      />
    </UPageCard>

    <UPageCard variant="subtle">
      <UFormField
        name="first_name"
        label="First Name"
        description="Will appear on receipts, invoices, and other communication."
        required
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <UInput v-model="profile!.first_name" autocomplete="off" />
      </UFormField>
      <USeparator />
      <UFormField
        name="last_name"
        label="Last Name"
        description="Will appear on receipts, invoices, and other communication."
        required
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <UInput v-model="profile!.last_name" autocomplete="off" />
      </UFormField>
      <USeparator />
      <UFormField
        name="email"
        label="Email"
        description="Used to sign in, for email receipts and product updates."
        required
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <UInput disabled :value="profile!.email" type="email" />
      </UFormField>

      <USeparator />
      <UFormField
        name="reset-password"
        label="Password"
        description="Used to sign in, for email receipts and product updates."
        required
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <UButton
          variant="outline"
          color="error"
          class="cursor-pointer"
          @click="resetPassword"
        >
          Reset Password
        </UButton>
      </UFormField>
    </UPageCard>
  </UForm>
</template>
