<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'

const { useDirectusFetch, dFetch } = useDirectus()
const toast = useToast()
const router = useRouter()

// Fetch others from Directus
const { data: others, refresh: refreshOthers } = await useDirectusFetch<
  any[]
>('/items/t_other', {
  key: 'all-others'
})

const UButton = resolveComponent('UButton')
const UDropdownMenu = resolveComponent('UDropdownMenu')

// Handle create new other
const handleCreate = () => {
  router.push('/content/others/new')
}

// Handle edit other
const handleEdit = (other: any) => {
  router.push(`/content/others/${other.id}`)
}

// Handle delete other with confirmation
const handleDelete = async (other: any) => {
  const confirmed = confirm(
    `Are you sure you want to delete "${other.name}"? This action cannot be undone.`
  )
  if (confirmed) {
    try {
      await dFetch(`/items/t_other/${other.id}`, {
        method: 'DELETE'
      })
      await refreshOthers()
      toast.add({
        title: 'Success',
        description: 'Other deleted successfully',
        color: 'success'
      })
    } catch (error) {
      console.error('Error deleting other:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete other',
        color: 'error'
      })
    }
  }
}

const onRowSelect = (row: TableRow<any>) => {
  handleEdit(row.original)
}

// Table columns
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.name || 'Untitled')
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.original.description
      if (!description)
        return h('span', { class: 'text-neutral-400' }, 'No description')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        description.length > 50
          ? `${description.substring(0, 50)}...`
          : description
      )
    }
  },
  {
    accessorKey: 'mark_id',
    header: 'Marker ID',
    cell: ({ row }) => row.original.mark_id || 'Not set'
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const items = [
        [
          {
            label: 'Edit',
            icon: 'i-lucide-pencil',
            onSelect: () => handleEdit(row.original)
          }
        ],
        [
          {
            label: 'Delete',
            icon: 'i-lucide-trash-2',
            color: 'error' as const,
            onSelect: () => handleDelete(row.original)
          }
        ]
      ]

      return h(
        'div',
        { class: 'text-right' },
        h(
          UDropdownMenu,
          {
            items,
            content: { align: 'end' }
          },
          () =>
            h(UButton, {
              icon: 'i-lucide-ellipsis-vertical',
              color: 'neutral',
              variant: 'ghost',
              class: 'ml-auto'
            })
        )
      )
    }
  }
]
</script>

<template>
  <UDashboardPanel id="others">
    <template #header>
      <UDashboardNavbar title="Others">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Other"
            trailing-icon="i-lucide-plus"
            color="primary"
            class="hidden lg:flex"
            @click="handleCreate"
          />
          <UButton
            icon="i-lucide-plus"
            color="primary"
            class="lg:hidden"
            @click="handleCreate"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        :data="others"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon name="i-lucide-camera" class="w-8 h-8 text-neutral-400" />
            <p class="text-sm text-neutral-500">
              No others found.
            </p>
            <UButton
              label="Create your first other"
              color="primary"
              @click="handleCreate"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
