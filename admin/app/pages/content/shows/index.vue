<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'
import { formatVenueDateTime } from '~/utils/venues'

const { useDirectusFetch, dFetch } = useDirectus()
const toast = useToast()
const router = useRouter()

// Fetch shows from Directus
const { data: shows, refresh: refreshShows } = await useDirectusFetch<any[]>(
  '/items/t_show',
  {
    key: 'all-shows'
  }
)

const UButton = resolveComponent('UButton')
const UDropdownMenu = resolveComponent('UDropdownMenu')

// Handle create new show
const handleCreate = () => {
  router.push('/content/shows/new')
}

// Handle edit show
const handleEdit = (show: any) => {
  router.push(`/content/shows/${show.id}`)
}

// Handle delete show with confirmation
const handleDelete = async (show: any) => {
  const confirmed = confirm(
    `Are you sure you want to delete "${show.name}"? This action cannot be undone.`
  )
  if (confirmed) {
    try {
      await dFetch(`/items/t_show/${show.id}`, {
        method: 'DELETE'
      })
      await refreshShows()
      toast.add({
        title: 'Success',
        description: 'Show deleted successfully',
        color: 'success'
      })
    } catch (error) {
      console.error('Error deleting show:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete show',
        color: 'error'
      })
    }
  }
}

const onRowSelect = (row: TableRow<any>) => {
  handleEdit(row.original)
}

// Table columns
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.name || 'Untitled')
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.original.description
      if (!description)
        return h('span', { class: 'text-neutral-400' }, 'No description')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        description.length > 50
          ? `${description.substring(0, 50)}...`
          : description
      )
    }
  },
  {
    accessorKey: 'begin_time',
    header: 'Start Time',
    cell: ({ row }) => formatVenueDateTime(row.original.begin_time)
  },
  {
    accessorKey: 'end_time',
    header: 'End Time',
    cell: ({ row }) => formatVenueDateTime(row.original.end_time)
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const items = [
        [
          {
            label: 'Edit',
            icon: 'i-lucide-pencil',
            onSelect: () => handleEdit(row.original)
          }
        ],
        [
          {
            label: 'Delete',
            icon: 'i-lucide-trash-2',
            color: 'error' as const,
            onSelect: () => handleDelete(row.original)
          }
        ]
      ]

      return h(
        'div',
        { class: 'text-right' },
        h(
          UDropdownMenu,
          {
            items,
            content: { align: 'end' }
          },
          () =>
            h(UButton, {
              icon: 'i-lucide-ellipsis-vertical',
              color: 'neutral',
              variant: 'ghost',
              class: 'ml-auto'
            })
        )
      )
    }
  }
]
</script>

<template>
  <UDashboardPanel id="shows">
    <template #header>
      <UDashboardNavbar title="Shows">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Show"
            trailing-icon="i-lucide-plus"
            color="primary"
            class="hidden lg:flex"
            @click="handleCreate"
          />
          <UButton
            icon="i-lucide-plus"
            color="primary"
            class="lg:hidden"
            @click="handleCreate"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        :data="shows"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon
              name="i-lucide-play-circle"
              class="w-8 h-8 text-neutral-400"
            />
            <p class="text-sm text-neutral-500">
              No shows found.
            </p>
            <UButton
              label="Create your first show"
              color="primary"
              @click="handleCreate"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
