<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { dFetch } = useDirectus()
const toast = useToast()

const isNew = route.params.id === 'new'
const showId = isNew ? null : parseInt(route.params.id as string)

// Load existing show data if editing
const show = ref<any>({})
const loading = ref(false)

if (!isNew && showId) {
  loading.value = true
  try {
    show.value = await dFetch(`/items/t_show/${showId}`)
    // Convert datetime strings to the format expected by datetime-local input
    if (show.value.begin_time) {
      show.value.begin_time = new Date(show.value.begin_time)
        .toISOString()
        .slice(0, 16)
    }
    if (show.value.end_time) {
      show.value.end_time = new Date(show.value.end_time)
        .toISOString()
        .slice(0, 16)
    }
  } catch (error) {
    console.error('Error loading show:', error)
    router.push('/content/shows')
  } finally {
    loading.value = false
  }
}

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    // Convert datetime-local format back to ISO string
    if (data.begin_time) {
      data.begin_time = new Date(data.begin_time).toISOString()
    }
    if (data.end_time) {
      data.end_time = new Date(data.end_time).toISOString()
    }

    if (isNew) {
      await dFetch('/items/t_show', {
        method: 'POST',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Show created successfully',
        color: 'success'
      })
    } else {
      await dFetch(`/items/t_show/${showId}`, {
        method: 'PATCH',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Show updated successfully',
        color: 'success'
      })
    }
    router.push('/content/shows')
  } catch (error) {
    console.error('Error saving show:', error)
    toast.add({
      title: 'Error',
      description: `Failed to ${isNew ? 'create' : 'update'} show`,
      color: 'error'
    })
  }
}

// Handle cancel
const handleCancel = () => {
  router.push('/content/shows')
}

// Page title
const pageTitle = computed(() => {
  if (isNew) return 'New Show'
  return show.value.name ? `Edit ${show.value.name}` : 'Edit Show'
})
</script>

<template>
  <UDashboardPanel id="show-form">
    <template #header>
      <UDashboardNavbar :title="pageTitle">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Back to Shows"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="subtle"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="max-w-2xl mx-auto w-full py-8">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div class="mb-6">
            <h1 class="text-xl font-semibold">
              {{ isNew ? "Create New Show" : "Edit Show" }}
            </h1>
            <p class="text-sm text-neutral-500 mt-1">
              {{
                isNew
                  ? "Schedule a new show for your event"
                  : "Update show information and schedule"
              }}
            </p>
          </div>

          <div v-if="loading" class="flex justify-center py-8">
            <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin" />
          </div>

          <UForm
            v-else
            :state="show"
            class="space-y-6"
            @submit="handleSubmit"
          >
            <div class="grid grid-cols-1 gap-6">
              <UFormField label="Name" name="name" required>
                <UInput
                  v-model="show.name"
                  placeholder="Enter show name"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Description" name="description">
                <UTextarea
                  v-model="show.description"
                  placeholder="Enter show description"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Thumbnail URL" name="thumbnail_url">
                <UInput
                  v-model="show.thumbnail_url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Start Time" name="begin_time" required>
                <UInput
                  v-model="show.begin_time"
                  type="datetime-local"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="End Time" name="end_time" required>
                <UInput
                  v-model="show.end_time"
                  type="datetime-local"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Map Marker ID" name="mark_id">
                <UInput
                  v-model="show.mark_id"
                  placeholder="Enter marker ID"
                  class="w-full"
                />
              </UFormField>
            </div>

            <div
              class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700"
            >
              <UButton
                label="Cancel"
                color="neutral"
                variant="subtle"
                @click="handleCancel"
              />
              <UButton
                :label="isNew ? 'Create' : 'Update'"
                color="primary"
                variant="solid"
                type="submit"
              />
            </div>
          </UForm>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
