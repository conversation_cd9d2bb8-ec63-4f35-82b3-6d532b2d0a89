<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { dFetch } = useDirectus()
const toast = useToast()

const isNew = route.params.id === 'new'
const shopId = isNew ? null : parseInt(route.params.id as string)

// Load existing shop data if editing
const shop = ref<any>({})
const loading = ref(false)

if (!isNew && shopId) {
  loading.value = true
  try {
    shop.value = await dFetch(`/items/t_shop/${shopId}`)
  } catch (error) {
    console.error('Error loading shop:', error)
    router.push('/content/shops')
  } finally {
    loading.value = false
  }
}

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    if (isNew) {
      await dFetch('/items/t_shop', {
        method: 'POST',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Shop created successfully',
        color: 'success'
      })
    } else {
      await dFetch(`/items/t_shop/${shopId}`, {
        method: 'PATCH',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Shop updated successfully',
        color: 'success'
      })
    }
    router.push('/content/shops')
  } catch (error) {
    console.error('Error saving shop:', error)
    toast.add({
      title: 'Error',
      description: `Failed to ${isNew ? 'create' : 'update'} shop`,
      color: 'error'
    })
  }
}

// Handle cancel
const handleCancel = () => {
  router.push('/content/shops')
}

// Page title
const pageTitle = computed(() => {
  if (isNew) return 'New Shop'
  return shop.value.name ? `Edit ${shop.value.name}` : 'Edit Shop'
})
</script>

<template>
  <UDashboardPanel id="shop-form">
    <template #header>
      <UDashboardNavbar :title="pageTitle">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Back to Shops"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="subtle"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="max-w-2xl w-full mx-auto py-8">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div class="mb-6">
            <h1 class="text-xl font-semibold">
              {{ isNew ? "Create New Shop" : "Edit Shop" }}
            </h1>
            <p class="text-sm text-neutral-500 mt-1">
              {{
                isNew
                  ? "Add a new shop to your venue collection"
                  : "Update shop information"
              }}
            </p>
          </div>

          <div v-if="loading" class="flex justify-center py-8">
            <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin" />
          </div>

          <UForm
            v-else
            :state="shop"
            class="space-y-6"
            @submit="handleSubmit"
          >
            <div class="grid grid-cols-1 gap-6">
              <UFormField label="Name" name="name" required>
                <UInput
                  v-model="shop.name"
                  placeholder="Enter shop name"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Description" name="description">
                <UTextarea
                  v-model="shop.description"
                  placeholder="Enter shop description"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Thumbnail URL" name="thumbnail_url">
                <UInput
                  v-model="shop.thumbnail_url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Map Marker ID" name="mark_id">
                <UInput
                  v-model="shop.mark_id"
                  type="number"
                  placeholder="Enter marker ID"
                  class="w-full"
                />
              </UFormField>
            </div>

            <div class="grid grid-cols-2 gap-2">
              <UButton
                label="Cancel"
                color="neutral"
                variant="subtle"
                @click="handleCancel"
              />
              <UButton
                :label="isNew ? 'Create' : 'Update'"
                color="primary"
                variant="solid"
                type="submit"
              />
            </div>
          </UForm>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
