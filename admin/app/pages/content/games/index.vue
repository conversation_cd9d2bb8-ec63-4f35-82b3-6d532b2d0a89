<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'

const { useDirectusFetch, dFetch } = useDirectus()
const toast = useToast()
const router = useRouter()

// Fetch games from Directus
const { data: games, refresh: refreshGames } = await useDirectusFetch<any[]>(
  '/items/t_game',
  {
    key: 'all-games'
  }
)

const UButton = resolveComponent('UButton')
const UDropdownMenu = resolveComponent('UDropdownMenu')

// Handle create new game
const handleCreate = () => {
  router.push('/content/games/new')
}

// Handle edit game
const handleEdit = (game: any) => {
  router.push(`/content/games/${game.id}`)
}

// Handle delete game with confirmation
const handleDelete = async (game: any) => {
  const confirmed = confirm(
    `Are you sure you want to delete "${game.name}"? This action cannot be undone.`
  )
  if (confirmed) {
    try {
      await dFetch(`/items/t_game/${game.id}`, {
        method: 'DELETE'
      })
      await refreshGames()
      toast.add({
        title: 'Success',
        description: 'Game deleted successfully',
        color: 'success'
      })
    } catch (error) {
      console.error('Error deleting game:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete game',
        color: 'error'
      })
    }
  }
}

const onRowSelect = (row: TableRow<any>) => {
  handleEdit(row.original)
}

// Table columns
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.name || 'Untitled')
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.original.description
      if (!description)
        return h('span', { class: 'text-neutral-400' }, 'No description')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        description.length > 50
          ? `${description.substring(0, 50)}...`
          : description
      )
    }
  },
  {
    accessorKey: 'game_level',
    header: 'Level',
    cell: ({ row }) => {
      const level = row.original.game_level
      const levelMap: Record<number, string> = {
        1: 'Easy',
        2: 'Medium',
        3: 'Hard'
      }
      return levelMap[level] || 'Unknown'
    }
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const items = [
        [
          {
            label: 'Edit',
            icon: 'i-lucide-pencil',
            onSelect: () => handleEdit(row.original)
          }
        ],
        [
          {
            label: 'Delete',
            icon: 'i-lucide-trash-2',
            color: 'error' as const,
            onSelect: () => handleDelete(row.original)
          }
        ]
      ]

      return h(
        'div',
        { class: 'text-right' },
        h(
          UDropdownMenu,
          {
            items,
            content: { align: 'end' }
          },
          () =>
            h(UButton, {
              icon: 'i-lucide-ellipsis-vertical',
              color: 'neutral',
              variant: 'ghost',
              class: 'ml-auto'
            })
        )
      )
    }
  }
]
</script>

<template>
  <UDashboardPanel id="games">
    <template #header>
      <UDashboardNavbar title="Games">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Game"
            trailing-icon="i-lucide-plus"
            color="primary"
            class="hidden lg:flex"
            @click="handleCreate"
          />
          <UButton
            icon="i-lucide-plus"
            color="primary"
            class="lg:hidden"
            @click="handleCreate"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        :data="games"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon name="i-lucide-gamepad-2" class="w-8 h-8 text-neutral-400" />
            <p class="text-sm text-neutral-500">
              No games found.
            </p>
            <UButton
              label="Create your first game"
              color="primary"
              @click="handleCreate"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
