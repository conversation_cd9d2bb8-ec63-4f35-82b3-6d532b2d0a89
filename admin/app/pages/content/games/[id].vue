<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { dFetch } = useDirectus()
const toast = useToast()

const isNew = route.params.id === 'new'
const gameId = isNew ? null : parseInt(route.params.id as string)

// Load existing game data if editing
const game = ref<any>({})
const loading = ref(false)

if (!isNew && gameId) {
  loading.value = true
  try {
    game.value = await dFetch(`/items/t_game/${gameId}`)
  } catch (error) {
    console.error('Error loading game:', error)
    router.push('/content/games')
  } finally {
    loading.value = false
  }
}

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    if (isNew) {
      await dFetch('/items/t_game', {
        method: 'POST',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Game created successfully',
        color: 'success'
      })
    } else {
      await dFetch(`/items/t_game/${gameId}`, {
        method: 'PATCH',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Game updated successfully',
        color: 'success'
      })
    }
    router.push('/content/games')
  } catch (error) {
    console.error('Error saving game:', error)
    toast.add({
      title: 'Error',
      description: `Failed to ${isNew ? 'create' : 'update'} game`,
      color: 'error'
    })
  }
}

// Handle cancel
const handleCancel = () => {
  router.push('/content/games')
}

// Page title
const pageTitle = computed(() => {
  if (isNew) return 'New Game'
  return game.value.name ? `Edit ${game.value.name}` : 'Edit Game'
})
</script>

<template>
  <UDashboardPanel id="game-form">
    <template #header>
      <UDashboardNavbar :title="pageTitle">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Back to Games"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="subtle"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="max-w-2xl mx-auto w-full py-8">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div class="mb-6">
            <h1 class="text-xl font-semibold">
              {{ isNew ? "Create New Game" : "Edit Game" }}
            </h1>
            <p class="text-sm text-neutral-500 mt-1">
              {{
                isNew
                  ? "Add a new interactive game to your event"
                  : "Update game configuration and settings"
              }}
            </p>
          </div>

          <div v-if="loading" class="flex justify-center py-8">
            <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin" />
          </div>

          <UForm
            v-else
            :state="game"
            class="space-y-6"
            @submit="handleSubmit"
          >
            <div class="grid grid-cols-1 gap-6">
              <UFormField label="Name" name="name" required>
                <UInput
                  v-model="game.name"
                  placeholder="Enter game name"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Description" name="description">
                <UTextarea
                  v-model="game.description"
                  placeholder="Enter game description"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Game URL" name="game_url">
                <UInput
                  v-model="game.game_url"
                  type="url"
                  placeholder="https://example.com/game"
                  class="w-full"
                />
              </UFormField>
              <!--
              <UFormField label="Difficulty Level" name="game_level">
                <USelect
                  v-model="game.game_level"
                  class="w-full"
                  :options="[
                    { label: 'Easy', value: 1 },
                    { label: 'Medium', value: 2 },
                    { label: 'Hard', value: 3 },
                  ]"
                />
              </UFormField> -->

              <UFormField label="Event ID" name="event_id">
                <UInput
                  v-model="game.event_id"
                  class="w-full"
                  type="number"
                  placeholder="Enter event ID"
                />
              </UFormField>

              <UFormField label="Area ID" name="area_id">
                <UInput
                  v-model="game.area_id"
                  class="w-full"
                  type="number"
                  placeholder="Enter area ID"
                />
              </UFormField>

              <UFormField label="Map Marker ID" name="mark_id">
                <UInput
                  v-model="game.mark_id"
                  class="w-full"
                  type="number"
                  placeholder="Enter marker ID"
                />
              </UFormField>
            </div>

            <div
              class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700"
            >
              <UButton
                label="Cancel"
                color="neutral"
                variant="subtle"
                @click="handleCancel"
              />
              <UButton
                :label="isNew ? 'Create' : 'Update'"
                color="primary"
                variant="solid"
                type="submit"
              />
            </div>
          </UForm>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
