<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { dFetch } = useDirectus()
const toast = useToast()

const isNew = route.params.id === 'new'
const bulletinId = isNew ? null : parseInt(route.params.id as string)

// Load existing bulletin data if editing
const bulletin = ref<any>({})
const loading = ref(false)

if (!isNew && bulletinId) {
  loading.value = true
  try {
    bulletin.value = await dFetch(`/items/t_bulletin/${bulletinId}`)
    // Convert datetime string to the format expected by datetime-local input
    if (bulletin.value.publish_at) {
      bulletin.value.publish_at = new Date(bulletin.value.publish_at)
        .toISOString()
        .slice(0, 16)
    }
  } catch (error) {
    console.error('Error loading bulletin:', error)
    router.push('/content/bulletins')
  } finally {
    loading.value = false
  }
}

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    // Convert datetime-local format back to ISO string
    if (data.publish_at) {
      data.publish_at = new Date(data.publish_at).toISOString()
    }

    if (isNew) {
      await dFetch('/items/t_bulletin', {
        method: 'POST',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Bulletin created successfully',
        color: 'success'
      })
    } else {
      await dFetch(`/items/t_bulletin/${bulletinId}`, {
        method: 'PATCH',
        body: data
      })
      toast.add({
        title: 'Success',
        description: 'Bulletin updated successfully',
        color: 'success'
      })
    }
    router.push('/content/bulletins')
  } catch (error) {
    console.error('Error saving bulletin:', error)
    toast.add({
      title: 'Error',
      description: `Failed to ${isNew ? 'create' : 'update'} bulletin`,
      color: 'error'
    })
  }
}

// Handle cancel
const handleCancel = () => {
  router.push('/content/bulletins')
}

// Page title
const pageTitle = computed(() => {
  if (isNew) return 'New Bulletin'
  return bulletin.value.title
    ? `Edit ${bulletin.value.title}`
    : 'Edit Bulletin'
})
</script>

<template>
  <UDashboardPanel id="bulletin-form">
    <template #header>
      <UDashboardNavbar :title="pageTitle">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Back to Bulletins"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="subtle"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="max-w-2xl mx-auto w-full py-8">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div class="mb-6">
            <h1 class="text-xl font-semibold">
              {{ isNew ? "Create New Bulletin" : "Edit Bulletin" }}
            </h1>
            <p class="text-sm text-neutral-500 mt-1">
              {{
                isNew
                  ? "Create a new announcement or bulletin for your event"
                  : "Update bulletin content and settings"
              }}
            </p>
          </div>

          <div v-if="loading" class="flex justify-center py-8">
            <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin" />
          </div>

          <UForm
            v-else
            :state="bulletin"
            class="space-y-6"
            @submit="handleSubmit"
          >
            <div class="grid grid-cols-1 gap-6">
              <UFormField label="Title" name="title" required>
                <UInput
                  v-model="bulletin.title"
                  placeholder="Enter bulletin title"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Brief" name="brief">
                <UTextarea
                  v-model="bulletin.brief"
                  placeholder="Enter brief description"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Page URL" name="page_url">
                <UInput
                  v-model="bulletin.page_url"
                  type="url"
                  placeholder="https://example.com/page"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Publish Date" name="publish_at">
                <UInput
                  v-model="bulletin.publish_at"
                  type="datetime-local"
                  class="w-full"
                />
              </UFormField>

              <UFormField label="Published" name="published">
                <UToggle v-model="bulletin.published" />
              </UFormField>

              <UFormField label="Event ID" name="event_id">
                <UInput
                  v-model="bulletin.event_id"
                  type="number"
                  placeholder="Enter event ID"
                  class="w-full"
                />
              </UFormField>
            </div>

            <div
              class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700"
            >
              <UButton
                label="Cancel"
                color="neutral"
                variant="subtle"
                @click="handleCancel"
              />
              <UButton
                :label="isNew ? 'Create' : 'Update'"
                color="primary"
                variant="solid"
                type="submit"
              />
            </div>
          </UForm>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
