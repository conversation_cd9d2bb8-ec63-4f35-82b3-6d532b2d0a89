<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'
import { formatVenueDateTime } from '~/utils/venues'

const { useDirectusFetch, dFetch } = useDirectus()
const toast = useToast()
const router = useRouter()

// Fetch bulletins from Directus
const { data: bulletins, refresh: refreshBulletins } = await useDirectusFetch<
  any[]
>('/items/t_bulletin', {
  key: 'all-bulletins'
})

const UButton = resolveComponent('UButton')
const UDropdownMenu = resolveComponent('UDropdownMenu')
const UBadge = resolveComponent('UBadge')

// Handle create new bulletin
const handleCreate = () => {
  router.push('/content/bulletins/new')
}

// Handle edit bulletin
const handleEdit = (bulletin: any) => {
  router.push(`/content/bulletins/${bulletin.id}`)
}

// Handle delete bulletin with confirmation
const handleDelete = async (bulletin: any) => {
  const confirmed = confirm(
    `Are you sure you want to delete "${bulletin.title}"? This action cannot be undone.`
  )
  if (confirmed) {
    try {
      await dFetch(`/items/t_bulletin/${bulletin.id}`, {
        method: 'DELETE'
      })
      await refreshBulletins()
      toast.add({
        title: 'Success',
        description: 'Bulletin deleted successfully',
        color: 'success'
      })
    } catch (error) {
      console.error('Error deleting bulletin:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete bulletin',
        color: 'error'
      })
    }
  }
}

const onRowSelect = (row: TableRow<any>) => {
  handleEdit(row.original)
}

// Table columns
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'title',
    header: 'Title',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.title || 'Untitled')
  },
  {
    accessorKey: 'brief',
    header: 'Brief',
    cell: ({ row }) => {
      const brief = row.original.brief
      if (!brief) return h('span', { class: 'text-neutral-400' }, 'No brief')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        brief.length > 50 ? `${brief.substring(0, 50)}...` : brief
      )
    }
  },
  {
    accessorKey: 'published',
    header: 'Status',
    cell: ({ row }) => {
      const published = row.original.published
      return h(
        UBadge,
        {
          color: published ? 'success' : 'neutral',
          variant: 'subtle'
        },
        () => (published ? 'Published' : 'Draft')
      )
    }
  },
  {
    accessorKey: 'publish_at',
    header: 'Publish Date',
    cell: ({ row }) => formatVenueDateTime(row.original.publish_at)
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const items = [
        [
          {
            label: 'Edit',
            icon: 'i-lucide-pencil',
            onSelect: () => handleEdit(row.original)
          }
        ],
        [
          {
            label: 'Delete',
            icon: 'i-lucide-trash-2',
            color: 'error' as const,
            onSelect: () => handleDelete(row.original)
          }
        ]
      ]

      return h(
        'div',
        { class: 'text-right' },
        h(
          UDropdownMenu,
          {
            items,
            content: { align: 'end' }
          },
          () =>
            h(UButton, {
              icon: 'i-lucide-ellipsis-vertical',
              color: 'neutral',
              variant: 'ghost',
              class: 'ml-auto'
            })
        )
      )
    }
  }
]
</script>

<template>
  <UDashboardPanel id="bulletins">
    <template #header>
      <UDashboardNavbar title="Bulletins">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Bulletin"
            trailing-icon="i-lucide-plus"
            color="primary"
            class="hidden lg:flex"
            @click="handleCreate"
          />
          <UButton
            icon="i-lucide-plus"
            color="primary"
            class="lg:hidden"
            @click="handleCreate"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        :data="bulletins"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon name="i-lucide-megaphone" class="w-8 h-8 text-neutral-400" />
            <p class="text-sm text-neutral-500">
              No bulletins found.
            </p>
            <UButton
              label="Create your first bulletin"
              color="primary"
              @click="handleCreate"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
