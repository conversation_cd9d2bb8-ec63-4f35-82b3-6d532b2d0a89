<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const { dFetch, useDirectusFetch } = useDirectus()
const toast = useToast()

const exhibitorId = parseInt(route.params.id as string)
const loading = ref(false)

// Load exhibitor data
const { data: exhibitor } = await useDirectusFetch(
  `/items/t_exhibitor/${exhibitorId}`,
  {
    key: `exhibitor-${exhibitorId}`
  }
)

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    await dFetch(`/items/t_exhibitor/${exhibitorId}`, {
      method: 'PATCH',
      body: data.data
    })
    toast.add({
      title: 'Success',
      description: 'Exhibitor updated successfully',
      color: 'success'
    })
    router.push('/content/exhibitors')
  } catch (error) {
    console.error('Error updating exhibitor:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to update exhibitor',
      color: 'error'
    })
  }
}

// <PERSON>le cancel
const handleCancel = () => {
  router.push('/content/exhibitors')
}

// Page title
const pageTitle = computed(() => {
  return exhibitor.value.name
    ? `Edit ${exhibitor.value.name}`
    : 'Edit Exhibitor'
})
</script>

<template>
  <UDashboardPanel id="exhibitor-form">
    <template #header>
      <UDashboardNavbar :title="pageTitle">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Back to exhibitors"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="subtle"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="max-w-2xl mx-auto w-full py-8">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div class="mb-6">
            <h1 class="text-xl font-semibold">
              Edit Exhibitor
            </h1>
            <p class="text-sm text-neutral-500 mt-1">
              Update exhibitor information and settings
            </p>
          </div>

          <div v-if="loading" class="flex justify-center py-8">
            <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin" />
          </div>

          <UForm
            v-else
            :state="exhibitor"
            class="space-y-6"
            @submit="handleSubmit"
          >
            <div class="grid grid-cols-1 gap-6">
              <!-- Exhibitor Name -->
              <UFormField label="Name" name="name" required>
                <UInput
                  v-model="exhibitor.name"
                  placeholder="Enter exhibitor name"
                  class="w-full"
                />
              </UFormField>

              <!-- Exhibitor Description -->
              <UFormField label="Description" name="description">
                <UTextarea
                  v-model="exhibitor.description"
                  placeholder="Enter exhibitor description"
                  class="w-full"
                  :rows="4"
                />
              </UFormField>

              <!-- Thumbnail URL -->
              <UFormField label="Thumbnail URL" name="thumbnail_url">
                <!-- Current Thumbnail Image -->
                <img
                  v-if="exhibitor.thumbnail_url"
                  :src="exhibitor.thumbnail_url"
                  alt="Current Thumbnail"
                  class="w-full max-w-full object-cover rounded mb-2"
                >

                <UInput
                  v-model="exhibitor.thumbnail_url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  class="w-full"
                />
              </UFormField>

              <!-- Tags -->
              <UFormField label="Tags" name="tags">
                <UInput
                  v-model="exhibitor.tags"
                  placeholder="Enter tags (comma-separated)"
                  class="w-full"
                />
              </UFormField>

              <!-- Website URL -->
              <UFormField
                label="Website URL"
                name="website_url"
                description="Optional: Website URL for exhibition content sharing"
              >
                <UInput
                  v-model="exhibitor.website_url"
                  type="url"
                  placeholder="https://example.com/exhibition-page"
                  class="w-full"
                />
              </UFormField>
            </div>

            <div
              class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700"
            >
              <UButton
                label="Cancel"
                color="neutral"
                variant="subtle"
                @click="handleCancel"
              />
              <UButton
                label="Update"
                color="primary"
                variant="solid"
                type="submit"
              />
            </div>
          </UForm>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
