<script setup lang="ts">
const router = useRouter()
const { dFetch } = useDirectus()
const toast = useToast()

// Form state for new exhibitor
const exhibitor = ref({
  name: '',
  description: '',
  thumbnail_url: '',
  tags: '',
  website_url: '',
  mark_id: null
})

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    await dFetch('/items/t_exhibitor', {
      method: 'POST',
      body: data
    })
    toast.add({
      title: 'Success',
      description: 'Exhibitor created successfully',
      color: 'success'
    })
    router.push('/content/exhibitors')
  } catch (error) {
    console.error('Error creating exhibitor:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to create exhibitor',
      color: 'error'
    })
  }
}

// Handle cancel
const handleCancel = () => {
  router.push('/content/exhibitors')
}
</script>

<template>
  <UDashboardPanel id="exhibitor-new">
    <template #header>
      <UDashboardNavbar title="New Exhibitor">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Back to exhibitors"
            icon="i-lucide-arrow-left"
            color="neutral"
            variant="subtle"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="max-w-2xl mx-auto w-full py-8">
        <div
          class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <div class="mb-6">
            <h1 class="text-xl font-semibold">
              Create New Exhibitor
            </h1>
            <p class="text-sm text-neutral-500 mt-1">
              Add a new exhibitor to showcase at your event
            </p>
          </div>

          <UForm
            :state="exhibitor"
            class="space-y-6"
            @submit="handleSubmit"
          >
            <div class="grid grid-cols-1 gap-6">
              <!-- Exhibitor Name -->
              <UFormField label="Name" name="name" required>
                <UInput
                  placeholder="Enter exhibitor name"
                  class="w-full"
                />
              </UFormField>

              <!-- Exhibitor Description -->
              <UFormField label="Description" name="description">
                <UTextarea
                  placeholder="Enter exhibitor description"
                  class="w-full"
                  :rows="4"
                />
              </UFormField>

              <!-- Thumbnail URL -->
              <UFormField label="Thumbnail URL" name="thumbnail_url">
                <UInput
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  class="w-full"
                />
              </UFormField>

              <!-- Tags -->
              <UFormField label="Tags" name="tags">
                <UInput
                  placeholder="Enter tags (comma-separated)"
                  class="w-full"
                />
              </UFormField>

              <!-- Website URL -->
              <UFormField
                label="Website URL"
                name="website_url"
                description="Optional: Website URL for exhibition content sharing"
              >
                <UInput
                  type="url"
                  placeholder="https://example.com/exhibition-page"
                  class="w-full"
                />
              </UFormField>
            </div>

            <div
              class="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700"
            >
              <UButton
                label="Cancel"
                color="neutral"
                variant="subtle"
                @click="handleCancel"
              />
              <UButton
                label="Create"
                color="primary"
                variant="solid"
                type="submit"
              />
            </div>
          </UForm>
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
