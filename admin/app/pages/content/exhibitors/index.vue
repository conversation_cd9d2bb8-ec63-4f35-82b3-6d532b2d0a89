<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'

const { useDirectusFetch, dFetch } = useDirectus()
const toast = useToast()
const router = useRouter()

// Fetch exhibitors from Directus
const { data: exhibitors, refresh: refreshexhibitors } = await useDirectusFetch<any[]>(
  '/items/t_exhibitor',
  {
    key: 'all-exhibitors',
    params: {
      sort: '-update_at'
    }
  }
)

const UButton = resolveComponent('UButton')
const UDropdownMenu = resolveComponent('UDropdownMenu')

// Handle create new exhibitor
const handleCreate = () => {
  router.push('/content/exhibitors/new')
}

// Handle edit exhibitor
const handleEdit = (exhibitor: any) => {
  router.push(`/content/exhibitors/${exhibitor.id}`)
}

// Handle delete exhibitor with confirmation
const handleDelete = async (exhibitor: any) => {
  const confirmed = confirm(
    `Are you sure you want to delete "${exhibitor.name}"? This action cannot be undone.`
  )
  if (confirmed) {
    try {
      await dFetch(`/items/t_exhibitor/${exhibitor.id}`, {
        method: 'DELETE'
      })
      await refreshexhibitors()
      toast.add({
        title: 'Success',
        description: 'exhibitor deleted successfully',
        color: 'success'
      })
    } catch (error) {
      console.error('Error deleting exhibitor:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete exhibitor',
        color: 'error'
      })
    }
  }
}

const onRowSelect = (row: TableRow<any>) => {
  handleEdit(row.original)
}

// Table columns
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'image',
    header: 'Image',
    cell: ({ row }) => {
      const imageUrl = row.original.thumbnail_url
      if (!imageUrl)
        return h('span', { class: 'text-neutral-400' }, 'No image')
      return h('img', {
        src: imageUrl,
        class: 'w-10 h-10 rounded-full'
      })
    }
  },
  {
    accessorKey: 'name',
    header: 'Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.name || 'Untitled')
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.original.description
      if (!description)
        return h('span', { class: 'text-neutral-400' }, 'No description')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        description.length > 50
          ? `${description.substring(0, 50)}...`
          : description
      )
    }
  },
  {
    accessorKey: 'tags',
    header: 'Tags',
    cell: ({ row }) => {
      const tags = row.original.tags
      if (!tags)
        return h('span', { class: 'text-neutral-400' }, 'No tags')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        tags.length > 50 ? `${tags.substring(0, 50)}...` : tags
      )
    }
  },
  {
    accessorKey: 'website_url',
    header: 'Website',
    cell: ({ row }) => {
      const websiteUrl = row.original.website_url
      if (!websiteUrl) {
        return h('span', { class: 'text-neutral-400' }, 'No website')
      }
      return h(
        'a',
        {
          href: websiteUrl,
          target: '_blank',
          rel: 'noopener noreferrer',
          class: 'text-blue-600 hover:text-blue-800 underline truncate max-w-xs block'
        },
        websiteUrl.length > 30 ? `${websiteUrl.substring(0, 30)}...` : websiteUrl
      )
    }
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const items = [
        [
          {
            label: 'Edit',
            icon: 'i-lucide-pencil',
            onSelect: () => handleEdit(row.original)
          }
        ],
        [
          {
            label: 'Delete',
            icon: 'i-lucide-trash-2',
            color: 'error' as const,
            onSelect: () => handleDelete(row.original)
          }
        ]
      ]

      return h(
        'div',
        { class: 'text-right' },
        h(
          UDropdownMenu,
          {
            items,
            content: { align: 'end' }
          },
          () =>
            h(UButton, {
              icon: 'i-lucide-ellipsis-vertical',
              color: 'neutral',
              variant: 'ghost',
              class: 'ml-auto'
            })
        )
      )
    }
  }
]
</script>

<template>
  <UDashboardPanel id="exhibitors">
    <template #header>
      <UDashboardNavbar title="exhibitors">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <!-- <UButton
            label="New exhibitor"
            trailing-icon="i-lucide-plus"
            color="primary"
            class="hidden lg:flex"
            @click="handleCreate"
          /> -->
          <UButton
            icon="i-lucide-plus"
            color="primary"
            class="lg:hidden"
            @click="handleCreate"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        :data="exhibitors"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon name="i-lucide-exhibitorpad-2" class="w-8 h-8 text-neutral-400" />
            <p class="text-sm text-neutral-500">
              No exhibitors found.
            </p>
            <UButton
              label="Create your first exhibitor"
              color="primary"
              @click="handleCreate"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
