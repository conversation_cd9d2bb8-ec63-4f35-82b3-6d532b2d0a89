<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'
import type { TGameRecord } from '~/utils/directus'

const { useDirectusFetch } = useDirectus()
const router = useRouter()

// Search functionality
const searchQuery = ref('')
const pagination = ref({ page: 1, pageSize: 20 })

// Fetch game records with user and game information
const { data: gameRecords, refresh: refreshGameRecords } = await useDirectusFetch<TGameRecord[]>(
  '/items/t_game_record',
  {
    key: 'all-game-records',
    params: {
      fields: ['*', 'user_id.id', 'user_id.nickname', 'user_id.location'],
      sort: ['-finished_at', '-create_at'],
      limit: -1
    }
  }
)

// Computed filtered records based on search
const filteredRecords = computed(() => {
  if (!gameRecords.value) return []

  if (!searchQuery.value.trim()) {
    return gameRecords.value
  }

  const query = searchQuery.value.toLowerCase()
  return gameRecords.value.filter((record) => {
    const user = record.user_id as any
    const userName = user?.nickname || `User #${record.user_id}`
    const gameName = record.game_name || ''

    return (
      userName.toLowerCase().includes(query)
      || gameName.toLowerCase().includes(query)
      || record.id.toString().includes(query)
    )
  })
})

// Handle row selection
const onRowSelect = (row: TableRow<TGameRecord>) => {
  const userId = typeof row.original.user_id === 'object'
    ? (row.original.user_id as any)?.id
    : row.original.user_id

  if (userId) {
    router.push(`/users/${userId}`)
  }
}

// Table columns
const columns: TableColumn<TGameRecord>[] = [
  {
    accessorKey: 'id',
    header: 'Record ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'user_id',
    header: 'Player',
    cell: ({ row }) => {
      const user = row.original.user_id as any
      const userName = user?.nickname || `User #${row.original.user_id}`
      const userLocation = user?.location

      return h('div', { class: 'flex flex-col' }, [
        h('span', { class: 'font-medium' }, userName),
        userLocation
          ? h('span', { class: 'text-xs text-neutral-500' }, userLocation)
          : null
      ].filter(Boolean))
    }
  },
  {
    accessorKey: 'game_name',
    header: 'Game',
    cell: ({ row }) => {
      const gameName = row.original.game_name || 'Unknown Game'
      const gameLevel = row.original.game_level

      return h('div', { class: 'flex flex-col' }, [
        h('span', { class: 'font-medium' }, gameName),
        gameLevel
          ? h('span', {
              class: [
                'text-xs px-2 py-0.5 rounded-full w-fit',
                gameLevel === 1
                  ? 'bg-green-100 text-green-700'
                  : gameLevel === 2
                    ? 'bg-yellow-100 text-yellow-700'
                    : gameLevel === 3
                      ? 'bg-red-100 text-red-700'
                      : 'bg-neutral-100 text-neutral-700'
              ]
            }, `Level ${gameLevel}`)
          : null
      ].filter(Boolean))
    }
  },
  {
    accessorKey: 'score',
    header: 'Score',
    cell: ({ row }) => {
      const score = row.original.score
      return score !== null && score !== undefined
        ? h('span', { class: 'font-mono font-semibold text-primary-600' }, score.toString())
        : h('span', { class: 'text-neutral-400' }, 'N/A')
    }
  },
  {
    accessorKey: 'finished_at',
    header: 'Completed',
    cell: ({ row }) => {
      const finishedAt = row.original.finished_at
      const createdAt = row.original.create_at
      const date = finishedAt || createdAt

      if (!date) return h('span', { class: 'text-neutral-400' }, 'N/A')

      const dateObj = new Date(date)
      return h('div', { class: 'flex flex-col' }, [
        h('span', { class: 'text-sm' }, dateObj.toLocaleDateString()),
        h('span', { class: 'text-xs text-neutral-500' }, dateObj.toLocaleTimeString())
      ])
    }
  },
  {
    accessorKey: 'is_public',
    header: 'Visibility',
    cell: ({ row }) => {
      const isPublic = row.original.is_public
      const isPublicBool = isPublic === 1

      return h('div', {
        class: [
          'inline-flex items-center gap-1.5',
          isPublicBool ? 'text-green-600' : 'text-neutral-500'
        ]
      }, [
        h('span', {
          class: ['w-2 h-2 rounded-full', isPublicBool ? 'bg-green-500' : 'bg-neutral-400']
        }),
        h('span', { class: 'text-sm' }, isPublicBool ? 'Public' : 'Private')
      ])
    }
  }
]

// Handle search with debounce
const debouncedSearch = useDebounceFn(() => {
  pagination.value.page = 1
}, 500)

// Stats computation
const stats = computed(() => {
  if (!gameRecords.value) return { total: 0, avgScore: 0, completedToday: 0 }

  const total = gameRecords.value.length
  const scores = gameRecords.value
    .map(r => r.score)
    .filter(s => s !== null && s !== undefined) as number[]

  const avgScore = scores.length > 0
    ? Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length)
    : 0

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  const completedToday = gameRecords.value.filter((record) => {
    const date = record.finished_at || record.create_at
    if (!date) return false
    const recordDate = new Date(date)
    recordDate.setHours(0, 0, 0, 0)
    return recordDate.getTime() === today.getTime()
  }).length

  return { total, avgScore, completedToday }
})
</script>

<template>
  <UDashboardPanel id="game-records">
    <template #header>
      <UDashboardNavbar title="Game Records">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="flex flex-col space-y-6">
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <UCard>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-neutral-500">Total Records</p>
                <p class="text-2xl font-bold">{{ stats.total.toLocaleString() }}</p>
              </div>
              <UIcon name="i-lucide-gamepad-2" class="w-8 h-8 text-primary-500" />
            </div>
          </UCard>

          <UCard>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-neutral-500">Average Score</p>
                <p class="text-2xl font-bold">{{ stats.avgScore.toLocaleString() }}</p>
              </div>
              <UIcon name="i-lucide-trophy" class="w-8 h-8 text-yellow-500" />
            </div>
          </UCard>

          <UCard>
            <div class="flex items-center justify-between">
              <div>
                <p class="text-sm text-neutral-500">Completed Today</p>
                <p class="text-2xl font-bold">{{ stats.completedToday.toLocaleString() }}</p>
              </div>
              <UIcon name="i-lucide-calendar-check" class="w-8 h-8 text-green-500" />
            </div>
          </UCard>
        </div>

        <!-- Search and Filters -->
        <div class="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          <UInput
            v-model="searchQuery"
            placeholder="Search by player name, game name, or record ID..."
            icon="i-heroicons-magnifying-glass"
            class="w-full sm:max-w-md"
            @input="debouncedSearch"
          />

          <UButton
            icon="i-heroicons-arrow-path"
            variant="outline"
            @click="() => refreshGameRecords()"
          >
            Refresh
          </UButton>
        </div>

        <!-- Game Records Table -->
        <UTable
          :data="filteredRecords"
          :columns="columns"
          class="cursor-pointer"
          @select="onRowSelect"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-lucide-gamepad-2" class="w-8 h-8 text-neutral-400" />
              <p class="text-sm text-neutral-500">
                No game records found.
              </p>
              <p class="text-xs text-neutral-400">
                {{ searchQuery ? 'Try adjusting your search criteria' : 'No game records have been created yet' }}
              </p>
            </div>
          </template>
        </UTable>
      </div>
    </template>
  </UDashboardPanel>
</template>
