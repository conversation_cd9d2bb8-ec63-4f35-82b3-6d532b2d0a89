<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'

const { useDirectusFetch, dFetch } = useDirectus()
const toast = useToast()
const router = useRouter()

// Search functionality
const searchQuery = ref('')

// Fetch users from Directus
const { data: allUsers, refresh: refreshUsers } = await useDirectusFetch<any[]>(
  '/items/t_user',
  {
    key: 'all-users',
    params: {
      fields: ['*'],
      limit: 100
    }
  }
)

// Computed filtered users based on search
const users = computed(() => {
  if (!allUsers.value) return []

  if (!searchQuery.value.trim()) {
    return allUsers.value
  }

  const query = searchQuery.value.toLowerCase()
  return allUsers.value.filter((user) => {
    const nickname = user.nickname || ''
    const location = user.location || ''
    const id = user.id.toString()

    return (
      nickname.toLowerCase().includes(query) ||
      location.toLowerCase().includes(query) ||
      id.includes(query)
    )
  })
})

// Handle edit user
const handleEdit = (user: any) => {
  router.push(`/users/${user.id}`)
}

// Handle delete user with confirmation
const handleDelete = async (user: any) => {
  const confirmed = confirm(
    `Are you sure you want to delete user "${user.nickname || '#' + user.id}"? This action cannot be undone.`
  )
  if (confirmed) {
    try {
      await dFetch(`/items/t_user/${user.id}`, {
        method: 'DELETE'
      })
      await refreshUsers()
      toast.add({
        title: 'Success',
        description: 'User deleted successfully',
        color: 'success'
      })
    } catch (error) {
      console.error('Error deleting user:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete user',
        color: 'error'
      })
    }
  }
}

const onRowSelect = (row: TableRow<any>) => {
  handleEdit(row.original)
}

// Table columns
const columns: TableColumn<any>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    accessorKey: 'nickname',
    header: 'Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.nickname || 'Guest')
  },
  {
    accessorKey: 'gender',
    header: 'Gender',
    cell: ({ row }) => {
      const gender = row.original.gender
      const genderMap: Record<number, string> = {
        0: 'Male',
        1: 'Female'
      }
      return genderMap[gender] || 'Unknown'
    }
  },
  {
    accessorKey: 'location',
    header: 'Location',
    cell: ({ row }) => {
      const location = row.original.location
      return location || h('span', { class: 'text-neutral-400' }, 'N/A')
    }
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const isActive = !!row.original.auth_token
      return h('div', {
        class: [
          'inline-flex items-center gap-1.5',
          isActive ? 'text-green-600' : 'text-gray-500'
        ]
      }, [
        h('span', {
          class: ['w-2 h-2 rounded-full', isActive ? 'bg-green-500' : 'bg-gray-400']
        }),
        h('span', isActive ? 'Active' : 'Inactive')
      ])
    }
  },
  {
    accessorKey: 'create_at',
    header: 'Joined',
    cell: ({ row }) => {
      const date = row.original.create_at
      return date ? new Date(date).toLocaleDateString() : 'N/A'
    }
  }
]

// Handle search - no need for debouncing with client-side filtering
// but keeping it for consistency with other pages
const debouncedSearch = useDebounceFn(() => {
  // Search is handled by computed property, no action needed
}, 500)
</script>

<template>
  <UDashboardPanel id="users">
    <template #header>
      <UDashboardNavbar title="User Management">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
        <!-- Create user button removed as per requirements -->
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="flex flex-col space-y-4">
        <!-- Search and Filters -->
        <div class="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          <UInput
            v-model="searchQuery"
            placeholder="Search users..."
            icon="i-heroicons-magnifying-glass"
            class="w-full sm:max-w-md"
            @input="debouncedSearch"
          />
        </div>

        <!-- Users Table -->
        <UTable
          :data="users"
          :columns="columns"
          class="cursor-pointer"
          @select="onRowSelect"
        >
          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-lucide-users" class="w-8 h-8 text-neutral-400" />
              <p class="text-sm text-neutral-500">
                No users found.
              </p>
              <p class="text-sm text-gray-500">
                No users found in the system
              </p>
            </div>
          </template>
        </UTable>
      </div>
    </template>
  </UDashboardPanel>
</template>
