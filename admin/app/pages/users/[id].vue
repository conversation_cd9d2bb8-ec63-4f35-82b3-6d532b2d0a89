<script setup lang="ts">
import TicketsTable from '~/components/tickets/TicketsTable.vue'

const route = useRoute()
const router = useRouter()
const { dFetch } = useDirectus()
const toast = useToast()

const userId = computed(() => route.params.id as string)

// Loading states
const isLoading = ref(true)
const isUpdating = ref(false)

// User data
const user = ref<TUser | null>(null)
const userLogin = ref<TUserLogin | null>(null)
const personalInfo = ref<TPersonalinfo | null>(null)
const userScore = ref<TUserScore | null>(null)
const tickets = ref<TTicket[]>([])
const gameRecords = ref<TGameRecord[]>([])

// Fetch user details
async function fetchUserDetails() {
  try {
    isLoading.value = true

    // Fetch main user data
    const userData = await dFetch<TUser>(`/items/t_user/${userId.value}`)
    user.value = userData

    // Fetch related data in parallel
    const [loginData, personalData, scoreData, ticketsData, gameRecordsData]
      = await Promise.allSettled([
        dFetch<TUserLogin[]>(
          `/items/t_user_login?filter[user_id][_eq]=${userId.value}`
        ),
        dFetch<TPersonalinfo[]>(
          `/items/t_personalinfo?filter[user_id][_eq]=${userId.value}`
        ),
        dFetch<TUserScore[]>(
          `/items/t_user_score?filter[user_id][_eq]=${userId.value}`
        ),
        dFetch<TTicket[]>(`/items/t_ticket?filter[user_id][_eq]=${userId.value}&fields=*`),
        dFetch<TGameRecord[]>(
          `/items/t_game_record?filter[user_id][_eq]=${userId.value}&sort=-create_at&limit=10`
        )
      ])

    // Process results
    if (loginData.status === 'fulfilled' && loginData.value?.data?.length > 0) {
      userLogin.value = loginData.value.data[0]
    }

    if (
      personalData.status === 'fulfilled'
      && personalData.value?.data?.length > 0
    ) {
      personalInfo.value = personalData.value.data[0]
    }

    if (scoreData.status === 'fulfilled' && scoreData.value?.data?.length > 0) {
      userScore.value = scoreData.value.data[0]
    }

    // Process tickets data
    if (ticketsData.status === 'fulfilled') {
      // dFetch already extracts the data property
      tickets.value = ticketsData.value || []
      console.log('Processed tickets array:', tickets.value)
    } else {
      console.log('Tickets data not fulfilled or empty:', ticketsData)
      tickets.value = []
    }

    if (gameRecordsData.status === 'fulfilled') {
      gameRecords.value = gameRecordsData.value?.data || []
    }
  } catch (error) {
    console.error('Error fetching user details:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to fetch user details. Please try again.',
      color: 'error'
    })
    router.push('/users')
  } finally {
    isLoading.value = false
  }
}

// Update user status
async function updateUserStatus(status: number) {
  try {
    isUpdating.value = true

    await dFetch(`/items/t_user/${userId.value}`, {
      method: 'PATCH',
      body: { status }
    })

    if (user.value) {
      user.value.status = status
    }

    toast.add({
      title: 'Success',
      description: 'User status updated successfully.',
      color: 'success'
    })
  } catch (error) {
    console.error('Error updating user status:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to update user status. Please try again.',
      color: 'error'
    })
  } finally {
    isUpdating.value = false
  }
}

// Delete user
async function deleteUser() {
  if (
    !confirm(
      'Are you sure you want to delete this user? This action cannot be undone.'
    )
  ) {
    return
  }

  try {
    await dFetch(`/items/t_user/${userId.value}`, {
      method: 'DELETE'
    })

    toast.add({
      title: 'Success',
      description: 'User deleted successfully.',
      color: 'success'
    })

    router.push('/users')
  } catch (error) {
    console.error('Error deleting user:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to delete user. Please try again.',
      color: 'error'
    })
  }
}

// Format date helper
function formatDate(dateString: string | null | undefined) {
  if (!dateString) return '—'
  return new Date(dateString).toLocaleString()
}

// Handle ticket selection
function onTicketSelect(ticket: any) {
  console.log('Selected ticket:', ticket)
  // You could add a detail view or edit functionality here
}

// Format gender helper
function formatGender(gender: number | null | undefined) {
  if (gender === 1) return 'Male'
  if (gender === 2) return 'Female'
  return 'Not specified'
}

// Watch for tickets data changes
watch(tickets, (newTickets) => {
  console.log('Tickets data changed:', newTickets)
}, { immediate: true, deep: true })

// Initialize
fetchUserDetails()
</script>

<template>
  <UDashboardPanel id="user-details">
    <template #header>
      <UDashboardNavbar>
        <template #title>
          <div class="flex items-center gap-2">
            <span>User Details</span>
            <span v-if="!isLoading && user">- {{ user.nickname ?? user.id }}</span>
            <USkeleton v-else-if="isLoading" class="h-4 w-40" />
          </div>
        </template>
        <template #leading>
          <UButton
            icon="i-heroicons-arrow-left"
            variant="ghost"
            @click="router.push('/users')"
          >
            Back to Users
          </UButton>
        </template>

        <template #right>
          <div class="flex items-center gap-2">
            <div class="flex items-center gap-2 px-3 py-1.5 rounded-md" :class="user?.auth_token ? 'bg-green-50 text-green-700' : 'bg-gray-50 text-gray-700'">
              <span class="w-2 h-2 rounded-full" :class="user?.auth_token ? 'bg-green-500' : 'bg-gray-400'" />
              <span class="text-sm font-medium">{{ user?.auth_token ? 'Active' : 'Inactive' }}</span>
            </div>

            <UButton
              icon="i-heroicons-trash"
              color="red"
              variant="soft"
              @click="deleteUser"
            >
              Delete User
            </UButton>
          </div>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div v-if="isLoading" class="flex flex-col items-center justify-center h-96 gap-4">
        <UIcon name="i-heroicons-user-circle" class="h-12 w-12 text-gray-400 animate-pulse" />
        <div class="text-center space-y-2">
          <div class="h-6 w-48 bg-gray-200 rounded-md animate-pulse mx-auto" />
          <p class="text-sm text-gray-500">
            Loading user details...
          </p>
        </div>
      </div>

      <div v-else-if="user" class="space-y-6">
        <!-- User Overview Card -->
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">
                User Overview
              </h3>
              <div class="flex items-center gap-1.5">
                <span class="w-2 h-2 rounded-full" :class="user.auth_token ? 'bg-green-500' : 'bg-gray-400'" />
                <span class="text-sm font-medium" :class="user.auth_token ? 'text-green-700' : 'text-gray-700'">
                  {{ user.auth_token ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label class="text-sm font-medium text-gray-500">User ID</label>
              <p class="mt-1 text-sm">
                {{ user.id }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Nickname</label>
              <p class="mt-1 text-sm">
                {{ user.nickname || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Gender</label>
              <p class="mt-1 text-sm">
                {{ formatGender(user.gender) }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Birthday</label>
              <p class="mt-1 text-sm">
                {{
                  user.birthday
                    ? new Date(user.birthday).toLocaleDateString()
                    : "N/A"
                }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Location</label>
              <p class="mt-1 text-sm">
                {{ user.location || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Points</label>
              <p class="mt-1 text-sm font-semibold text-blue-600">
                {{ user.point || 0 }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Signature</label>
              <p class="mt-1 text-sm">
                {{ user.signature || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Created At</label>
              <p class="mt-1 text-sm">
                {{ formatDate(user.create_at) }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Updated At</label>
              <p class="mt-1 text-sm">
                {{ formatDate(user.update_at) }}
              </p>
            </div>
          </div>
        </UCard>

        <!-- Login Information Card -->
        <UCard v-if="userLogin">
          <template #header>
            <h3 class="text-lg font-semibold">
              Login Information
            </h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="text-sm font-medium text-gray-500">Email</label>
              <p class="mt-1 text-sm">
                {{ userLogin.email || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Phone</label>
              <p class="mt-1 text-sm">
                {{ userLogin.phone || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Third Party Login</label>
              <p class="mt-1 text-sm">
                {{ userLogin.is_third_party === 1 ? "Yes" : "No" }}
              </p>
            </div>

            <div v-if="userLogin.is_third_party === 1">
              <label class="text-sm font-medium text-gray-500">Third Party Type</label>
              <p class="mt-1 text-sm">
                {{ userLogin.third_party_type || "N/A" }}
              </p>
            </div>
          </div>
        </UCard>

        <!-- Personal Information Card -->
        <UCard v-if="personalInfo">
          <template #header>
            <h3 class="text-lg font-semibold">
              Personal Information
            </h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="text-sm font-medium text-gray-500">Full Name</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.name || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Phone</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.phone || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Country</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.country || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Province</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.province || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">City</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.city || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Area</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.area || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Postcode</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.postcode || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Address</label>
              <p class="mt-1 text-sm">
                {{ personalInfo.address || "N/A" }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Identity Status</label>
              <UBadge
                :color="
                  personalInfo.identify_status === 1 ? 'success' : 'neutral'
                "
                variant="soft"
              >
                {{
                  personalInfo.identify_status === 1 ? "Verified" : "Unverified"
                }}
              </UBadge>
            </div>
          </div>
        </UCard>

        <!-- Score Information Card -->
        <UCard v-if="userScore">
          <template #header>
            <h3 class="text-lg font-semibold">
              Score Information
            </h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="text-sm font-medium text-gray-500">Total Score</label>
              <p class="mt-1 text-2xl font-bold text-blue-600">
                {{ userScore.total_score }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Ranking Score</label>
              <p class="mt-1 text-2xl font-bold text-green-600">
                {{ userScore.ranking_score }}
              </p>
            </div>

            <div>
              <label class="text-sm font-medium text-gray-500">Score Updated At</label>
              <p class="mt-1 text-sm">
                {{ formatDate(userScore.update_at) }}
              </p>
            </div>
          </div>
        </UCard>

        <!-- Tickets Card -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">
              Event Tickets ({{ tickets.length }})
            </h3>
          </template>
          <TicketsTable
            class="h-96"
            :tickets="tickets"
            :loading="isLoading"
            :show-user="false"
            @select="onTicketSelect"
          />
        </UCard>

        <!-- Game Records Card -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-semibold">
              Recent Game Records ({{ gameRecords.length }})
            </h3>
          </template>

          <div
            v-if="gameRecords.length === 0"
            class="text-center py-8 text-gray-500"
          >
            No game records found for this user.
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="record in gameRecords"
              :key="record.id"
              class="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label class="text-sm font-medium text-gray-500">Game Name</label>
                  <p class="mt-1 text-sm font-semibold">
                    {{ record.game_name || "N/A" }}
                  </p>
                </div>

                <div>
                  <label class="text-sm font-medium text-gray-500">Score</label>
                  <p class="mt-1 text-sm font-bold text-blue-600">
                    {{ record.score || 0 }}
                  </p>
                </div>

                <div>
                  <label class="text-sm font-medium text-gray-500">Level</label>
                  <UBadge
                    :color="
                      record.game_level === 1
                        ? 'green'
                        : record.game_level === 2
                          ? 'yellow'
                          : 'red'
                    "
                    variant="soft"
                  >
                    {{
                      record.game_level === 1
                        ? "Easy"
                        : record.game_level === 2
                          ? "Medium"
                          : "Hard"
                    }}
                  </UBadge>
                </div>

                <div>
                  <label class="text-sm font-medium text-gray-500">Finished At</label>
                  <p class="mt-1 text-sm">
                    {{ formatDate(record.finished_at) }}
                  </p>
                </div>

                <div>
                  <label class="text-sm font-medium text-gray-500">Public</label>
                  <UBadge
                    :color="record.is_public === 1 ? 'success' : 'neutral'"
                    variant="soft"
                  >
                    {{ record.is_public === 1 ? "Public" : "Private" }}
                  </UBadge>
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <div v-else class="text-center py-8">
        <p class="text-gray-500">
          User not found.
        </p>
      </div>
    </template>
  </UDashboardPanel>
</template>
