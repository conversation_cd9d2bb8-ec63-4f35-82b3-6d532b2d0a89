<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import type { RankResult } from '~/composables/useBackendApi'

// Composables
const { fetchRankings: fetchRankingsApi } = useBackendApi()

// State
const rankings = ref<RankResult[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const topN = ref(50) // Default to top 50
const lastUpdated = ref<Date | null>(null)

// Fetch rankings from backend API
const fetchRankings = async () => {
  loading.value = true
  error.value = null

  try {
    rankings.value = await fetchRankingsApi(topN.value)
    lastUpdated.value = new Date()
  } catch (err) {
    console.error('Error fetching rankings:', err)
    error.value = err instanceof Error ? err.message : 'Failed to connect to backend service'
  } finally {
    loading.value = false
  }
}

// Load rankings on mount
onMounted(() => {
  fetchRankings()
})

// Table columns
const columns: TableColumn<RankResult>[] = [
  {
    accessorKey: 'rank',
    header: 'Rank',
    cell: ({ row }) => {
      const rank = row.original.rank
      let icon = ''

      if (rank === 1) {
        icon = '🥇'
      } else if (rank === 2) {
        icon = '🥈'
      } else if (rank === 3) {
        icon = '🥉'
      }

      return h('div', { class: 'flex items-center gap-2' }, [
        h('span', { class: 'text-lg' }, icon),
        h('span', { class: 'font-bold text-lg' }, `#${rank}`)
      ])
    }
  },
  {
    accessorKey: 'nickName',
    header: 'Player Name',
    cell: ({ row }) =>
      h('div', { class: 'font-medium' }, row.original.nickName || 'Anonymous')
  },
  {
    accessorKey: 'score',
    header: 'Score',
    cell: ({ row }) =>
      h('div', { class: 'font-mono text-lg font-semibold text-green-600' },
        row.original.score.toLocaleString())
  }
]

// Handle refresh
const handleRefresh = () => {
  fetchRankings()
}

// Handle top N change
const handleTopNChange = () => {
  fetchRankings()
}
</script>

<template>
  <UDashboardPanel id="rankings">
    <template #header>
      <UDashboardNavbar title="User Rankings">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            icon="i-heroicons-arrow-path"
            :loading="loading"
            @click="handleRefresh"
          >
            Refresh
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="flex flex-col space-y-4">
        <!-- Controls -->
        <div class="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          <div class="text-sm text-neutral-500 space-y-1">
            <div>Showing {{ rankings.length }} players</div>
            <div v-if="lastUpdated" class="text-xs">
              Last updated: {{ lastUpdated.toLocaleTimeString() }}
            </div>
          </div>
        </div>

        <!-- Error State -->
        <UAlert
          v-if="error"
          icon="i-heroicons-exclamation-triangle"
          color="error"
          variant="soft"
          :title="error"
          :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'gray', variant: 'link', padded: false }"
          @close="error = null"
        />

        <!-- Rankings Table -->
        <UTable
          :data="rankings"
          :columns="columns"
          :loading="loading"
          class="w-full"
        >
          <template #loading-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 text-neutral-400 animate-spin" />
              <p class="text-sm text-neutral-500">
                Loading rankings...
              </p>
            </div>
          </template>

          <template #empty-state>
            <div class="flex flex-col items-center justify-center py-6 gap-3">
              <UIcon name="i-heroicons-trophy" class="w-8 h-8 text-neutral-400" />
              <p class="text-sm text-neutral-500">
                No rankings found.
              </p>
              <p class="text-sm text-gray-500">
                No users have earned points yet
              </p>
            </div>
          </template>
        </UTable>
      </div>
    </template>
  </UDashboardPanel>
</template>
