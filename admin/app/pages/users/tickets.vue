<script setup lang="ts">
const { useDirectusFetch } = useDirectus()
const router = useRouter()

// State
const searchQuery = ref('')
const statusFilter = ref('all')

// Fetch tickets with pagination and filters
const {
  data: tickets,
  pending,
  refresh
} = await useDirectusFetch<TTicket[]>('/items/t_ticket', {
  key: () => `tickets-${searchQuery.value}-${statusFilter.value}`,
  params: {
    fields: ['*'],
    sort: '-create_at',
    search: searchQuery.value,
    filter: getStatusFilter(),
    limit: 1000 // High limit to get all tickets
  }
})

// Get status filter based on selected status
function getStatusFilter() {
  if (statusFilter.value === 'available') {
    return { bound_at: { _null: true } }
  } else if (statusFilter.value === 'bound') {
    return {
      _and: [{ bound_at: { _nnull: true } }, { checked_at: { _null: true } }]
    }
  } else if (statusFilter.value === 'used') {
    return { checked_at: { _nnull: true } }
  }
  return {}
}

// Debounced search
const debouncedSearch = useDebounceFn(() => {
  refresh()
}, 300)

// Watch for filter changes
watch([statusFilter], () => {
  refresh()
})

// Handle ticket selection
const onTicketSelect = (ticket: TTicket) => {
  console.log('Selected ticket:', ticket)
  // You could add a detail view or edit functionality here
}
</script>

<template>
  <UDashboardPanel id="tickets">
    <template #header>
      <UDashboardNavbar title="Ticket Management">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="flex flex-col h-full">
        <!-- Search and Filters -->
        <div
          class="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center pb-4"
        >
          <UInput
            v-model="searchQuery"
            icon="i-heroicons-magnifying-glass"
            placeholder="Search tickets..."
            class="w-full sm:max-w-md"
            @update:model-value="debouncedSearch"
          />

          <div class="flex gap-2">
            <USelect
              v-model="statusFilter"
              :options="[
                { value: 'all', label: 'All Status' },
                { value: 'available', label: 'Available' },
                { value: 'bound', label: 'Bound' },
                { value: 'used', label: 'Used' }
              ]"
              option-attribute="label"
              class="w-40"
              @update:model-value="refresh"
            />
          </div>
        </div>

        <!-- Tickets Table -->
        <div class="flex-1 min-h-0">
          <TicketsTable
            :tickets="tickets || []"
            :loading="pending"
            :show-user="true"
            class="h-full"
            @select="onTicketSelect"
          />
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
