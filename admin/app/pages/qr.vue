<!-- eslint-disable vue/attribute-hyphenation -->
<template>
  <ClientOnly>
    <QRCodeVue3
      :width="800"
      :height="800"
      value="https://scholtz.sk"
      :dotsOptions="{
        type: 'square',
        color: '#3d009c'
      }"
      :cornersSquareOptions="{
        type: 'square',
        color: 'black'
      }"
      :qrOptions="{ typeNumber: 0, mode: 'Byte', errorCorrectionLevel: 'H' }"
      :imageOptions="{ hideBackgroundDots: true, imageSize: 0.2, margin: 0 }"
      image="/energy-icon.svg"
      fileExt="png"
    />
  </ClientOnly>
</template>

<script lang="ts" setup>
import QRCodeVue3 from 'qrcode-vue3'

definePageMeta({
  layout: 'blank'
})

const route = useRoute()
const url = route.query.url as string
</script>
