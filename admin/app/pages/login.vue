<template>
  <div
    class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2
          class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white"
        >
          Sign in to Admin Portal
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
          Open Portal Expo Administration
        </p>
      </div>

      <UCard class="p-6">
        <UForm
          ref="form"
          :schema="schema"
          :state="state"
          class="grid gap-6"
          @submit="onSubmit"
        >
          <UFormGroup label="Email" name="email" required>
            <UInput
              v-model="state.email"
              type="email"
              placeholder="<EMAIL>"
              icon="i-heroicons-envelope"
              size="lg"
              :disabled="loading"
              class="w-full"
            />
          </UFormGroup>

          <UFormGroup label="Password" name="password" required>
            <UInput
              v-model="state.password"
              type="password"
              placeholder="Enter your password"
              icon="i-heroicons-lock-closed"
              size="lg"
              :disabled="loading"
              class="w-full"
            />
          </UFormGroup>

          <div v-if="error" class="text-red-500 text-sm text-center">
            {{ error }}
          </div>

          <UButton
            type="submit"
            color="primary"
            variant="solid"
            size="lg"
            block
            :loading="loading"
            :disabled="loading"
            class="w-full"
          >
            <template v-if="loading">
              Signing in...
            </template>
            <template v-else>
              Sign in
            </template>
          </UButton>
        </UForm>
      </UCard>

      <div class="text-center text-xs text-gray-500 dark:text-gray-400">
        <p>Directus Admin Authentication</p>
        <p class="mt-1">
          Contact your administrator for access
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'

// Define page meta
definePageMeta({
  layout: false,
  auth: false
})

// Schema for form validation
const schema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required')
})

// Reactive state
const state = reactive({
  email: '',
  password: ''
})

const loading = ref(false)
const error = ref('')
const form = ref()

// Auth composable
const { login, isAuthenticated } = useAuth()

// Redirect if already authenticated
watch(
  isAuthenticated,
  (authenticated) => {
    if (authenticated) {
      navigateTo('/')
    }
  },
  { immediate: true }
)

// Form submission
const onSubmit = async (event: any) => {
  loading.value = true
  error.value = ''

  const data = event.data

  try {
    const result = await login({
      email: data.email,
      password: data.password
    })

    if (result.success) {
      // Show success message
      const toast = useToast()
      toast.add({
        title: 'Welcome back!',
        description: 'You have been successfully logged in.',
        color: 'success'
      })

      // Redirect to dashboard
      await navigateTo('/')
    } else {
      error.value = result.error || 'Login failed. Please try again.'
    }
  } catch (err) {
    console.error('Login error:', err)
    error.value = 'An unexpected error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}

// Clear error when user starts typing
watch([() => state.email, () => state.password], () => {
  if (error.value) {
    error.value = ''
  }
})
</script>
