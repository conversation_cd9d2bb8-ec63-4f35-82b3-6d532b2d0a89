<script setup lang="ts">
import type { DropdownMenuItem } from '@nuxt/ui'

const { canSeePage, isFloorStaff } = useRoleAccess()

const allItems = [
  [
    {
      label: 'New Event',
      icon: 'i-lucide-send',
      to: '/events/new'
    },
    {
      label: 'Edit OPE Map',
      icon: 'i-lucide-map',
      to: '/events/maps/1'
    },
    {
      label: 'QR Scanner',
      icon: 'i-lucide-qr-code',
      to: '/rewards/scanner'
    }
  ]
] satisfies DropdownMenuItem[][]

// Filter items based on user role
const items = computed(() => {
  return allItems
    .map(itemGroup => itemGroup.filter(item => canSeePage(item.to)))
    .filter(group => group.length > 0) // Remove empty groups
})
</script>

<template>
  <UDashboardPanel id="home">
    <template #header>
      <UDashboardNavbar title="Home" :ui="{ right: 'gap-3' }">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UDropdownMenu v-if="items.length > 0" :items="items">
            <UButton icon="i-lucide-plus" size="md" class="rounded-full" />
          </UDropdownMenu>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="space-y-6">
        <div>
          <h1 class="text-2xl font-bold text-neutral-900 dark:text-white">
            {{
              isFloorStaff
                ? "Welcome Floor Staff"
                : "Welcome to the Open Portal Expo Admin Interface"
            }}
          </h1>
          <p class="text-neutral-600 dark:text-neutral-400 mt-2">
            {{
              isFloorStaff
                ? "Manage reward claims and assist users with QR scanning"
                : "Quick access to key features and tools"
            }}
          </p>
        </div>

        <!-- Quick Actions Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- QR Scanner Shortcut -->
          <UCard
            v-if="canSeePage('/rewards/scanner')"
            class="hover:shadow-lg transition-shadow cursor-pointer"
            @click="navigateTo('/rewards/scanner')"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center"
                >
                  <UIcon
                    name="i-lucide-scan"
                    class="w-6 h-6 text-primary-600 dark:text-primary-400"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  class="text-lg font-semibold text-neutral-900 dark:text-white"
                >
                  Reward QR Scanner
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-400">
                  Scan QR codes for rewards and verification
                </p>
              </div>
              <UIcon
                name="i-lucide-arrow-right"
                class="w-5 h-5 text-neutral-400"
              />
            </div>
          </UCard>

          <!-- Event Map Shortcut -->
          <UCard
            v-if="canSeePage('/events/maps/1')"
            class="hover:shadow-lg transition-shadow cursor-pointer"
            @click="navigateTo('/events/maps/1')"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-success-100 dark:bg-success-900 rounded-lg flex items-center justify-center"
                >
                  <UIcon
                    name="i-lucide-map"
                    class="w-6 h-6 text-success-600 dark:text-success-400"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  class="text-lg font-semibold text-neutral-900 dark:text-white"
                >
                  Open Portal Expo Event Map
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-400">
                  Edit markers and locations on the expo map
                </p>
              </div>
              <UIcon
                name="i-lucide-arrow-right"
                class="w-5 h-5 text-neutral-400"
              />
            </div>
          </UCard>

          <!-- User List Shortcut -->
          <UCard
            v-if="canSeePage('/users')"
            class="hover:shadow-lg transition-shadow cursor-pointer"
            @click="navigateTo('/users')"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-warning-100 dark:bg-warning-900 rounded-lg flex items-center justify-center"
                >
                  <UIcon
                    name="i-lucide-users"
                    class="w-6 h-6 text-warning-600 dark:text-warning-400"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  class="text-lg font-semibold text-neutral-900 dark:text-white"
                >
                  User Management
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-400">
                  View and manage mobile app users
                </p>
              </div>
              <UIcon
                name="i-lucide-arrow-right"
                class="w-5 h-5 text-neutral-400"
              />
            </div>
          </UCard>

          <!-- Events Shortcut -->
          <UCard
            v-if="canSeePage('/events')"
            class="hover:shadow-lg transition-shadow cursor-pointer"
            @click="navigateTo('/events')"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-error-100 dark:bg-error-900 rounded-lg flex items-center justify-center"
                >
                  <UIcon
                    name="i-lucide-calendar"
                    class="w-6 h-6 text-error-600 dark:text-error-400"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  class="text-lg font-semibold text-neutral-900 dark:text-white"
                >
                  Events
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-400">
                  Manage expo events and schedules
                </p>
              </div>
              <UIcon
                name="i-lucide-arrow-right"
                class="w-5 h-5 text-neutral-400"
              />
            </div>
          </UCard>

          <!-- Rewards Shortcut -->
          <UCard
            v-if="canSeePage('/rewards')"
            class="hover:shadow-lg transition-shadow cursor-pointer"
            @click="navigateTo('/rewards')"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center"
                >
                  <UIcon
                    name="i-lucide-gift"
                    class="w-6 h-6 text-purple-600 dark:text-purple-400"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  class="text-lg font-semibold text-neutral-900 dark:text-white"
                >
                  Rewards
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-400">
                  Manage user rewards and incentives
                </p>
              </div>
              <UIcon
                name="i-lucide-arrow-right"
                class="w-5 h-5 text-neutral-400"
              />
            </div>
          </UCard>

          <!-- Markers Shortcut -->
          <!-- <UCard
            v-if="canSeePage('/markers')"
            class="hover:shadow-lg transition-shadow cursor-pointer"
            @click="navigateTo('/markers')"
          >
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div
                  class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center"
                >
                  <UIcon
                    name="i-lucide-map-pin"
                    class="w-6 h-6 text-blue-600 dark:text-blue-400"
                  />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <h3
                  class="text-lg font-semibold text-neutral-900 dark:text-white"
                >
                  Markers
                </h3>
                <p class="text-sm text-neutral-600 dark:text-neutral-400">
                  Manage map markers and locations
                </p>
              </div>
              <UIcon
                name="i-lucide-arrow-right"
                class="w-5 h-5 text-neutral-400"
              />
            </div>
          </UCard> -->
        </div>
      </div>
    </template>
  </UDashboardPanel>
</template>
