<template>
  <div class="p-6 max-w-2xl mx-auto">
    <h1 class="text-2xl font-bold mb-6">
      S3 Upload Test
    </h1>

    <UCard>
      <template #header>
        <h2 class="text-lg font-semibold">
          Upload Image to S3
        </h2>
      </template>

      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">Select Image File</label>
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            @change="handleFileSelect"
          >
        </div>

        <div v-if="selectedFile" class="text-sm text-gray-600">
          Selected: {{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})
        </div>

        <UButton
          :disabled="!selectedFile || uploading"
          :loading="uploading"
          class="w-full"
          @click="uploadFile"
        >
          {{ uploading ? 'Uploading...' : 'Upload to S3' }}
        </UButton>

        <div v-if="uploading && uploadProgress > 0" class="mt-4">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${uploadProgress}%` }"
            />
          </div>
          <p class="text-sm text-gray-600 mt-1">
            {{ uploadProgress }}%
          </p>
        </div>

        <div v-if="uploadResult" class="mt-4">
          <UAlert
            :color="uploadResult.success ? 'green' : 'red'"
            :title="uploadResult.success ? 'Upload Successful!' : 'Upload Failed'"
            :description="uploadResult.success ? `File uploaded successfully` : uploadResult.error"
          />

          <div v-if="uploadResult.success && uploadResult.url" class="mt-4">
            <p class="text-sm font-medium mb-2">
              Public URL:
            </p>
            <div class="bg-gray-50 p-3 rounded-md">
              <code class="text-sm break-all">{{ uploadResult.url }}</code>
            </div>

            <div class="mt-4">
              <img :src="uploadResult.url" alt="Uploaded image" class="max-w-full h-auto rounded-md shadow-sm">
            </div>
          </div>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { S3UploadResponse } from '~/types/s3'

const fileInput = ref<HTMLInputElement>()
const selectedFile = ref<File | null>(null)
const uploadResult = ref<S3UploadResponse | null>(null)

const { uploading, uploadProgress, error, uploadFile: upload, reset } = useS3Upload()

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    selectedFile.value = file
    uploadResult.value = null
    reset()
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

async function uploadFile() {
  if (!selectedFile.value) return

  try {
    const result = await upload(selectedFile.value)
    uploadResult.value = result

    // Clear the file input on successful upload
    if (result.success) {
      selectedFile.value = null
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  } catch (err: any) {
    console.error('Upload error:', err)
    uploadResult.value = {
      success: false,
      error: err.message || 'Upload failed'
    }
  }
}
</script>
