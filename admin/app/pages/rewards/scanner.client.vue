<script setup lang="ts">
import { QrcodeStream } from 'vue-qrcode-reader'

const toast = useToast()
const { dFetch } = useDirectus()

// Reactive state
const isScanning = ref(true) // Start scanning immediately
const scannedData = ref<string | null>(null)
const error = ref<string | null>(null)
const cameraReady = ref(false)
const torchEnabled = ref(false)
const facingMode = ref<'user' | 'environment'>('environment')
const alreadyClaimed = ref(false)
const rewardRecord = ref<TUserReward | null>(null)
const verificationResult = ref<{
  userReward: TUserReward | null
  reward: TReward | null
  user: TUser | null
  isValid: boolean
  message: string
}>({
  userReward: null,
  reward: null,
  user: null,
  isValid: false,
  message: ''
})

// Camera constraints
const constraints = computed(() => ({
  facingMode: facingMode.value
}))

// Event handlers
const onDetect = async (detectedCodes: any[]) => {
  if (detectedCodes.length > 0) {
    // Only consider the first detected code
    const qrCode = detectedCodes[0]
    scannedData.value = qrCode.rawValue

    // Decrypt the scanned data
    const decryptedData = JSON.parse(xorDecrypt(scannedData.value!, '09r4t3pre89n'))
    isScanning.value = false

    // Get reward record
    const userReward = await dFetch(`/items/t_user_reward`, {
      params: {
        filter: {
          _and: [
            { id: { _eq: decryptedData.r } },
            { user_id: { _eq: decryptedData.u } }
          ]
        }
      }
    })
    verificationResult.value!.userReward = userReward[0]

    // If not yet claimed
    if (!userReward[0].exchanged_at) {
      verificationResult.value.isValid = true
      verificationResult.value.message = 'Reward is valid and can be exchanged.'
    }

    try {
      // Get full Reward details using the reward_id from the user_reward record
      verificationResult.value!.reward = await dFetch<TReward>(`/items/t_reward/${verificationResult.value.userReward?.reward_id}`)

      // Get User infomation
      const user = await dFetch<TUser>(`/items/t_user/${verificationResult.value.userReward?.user_id}`)
      verificationResult.value!.user = user
    } catch (fetchError) {
      console.error('Error fetching reward details:', fetchError)
      toast.add({
        title: 'Error',
        description: 'Failed to fetch reward details. Please try again.',
        color: 'error'
      })
      isScanning.value = true
      return
    }
  }
}

const onCameraOn = async (capabilities: any) => {
  cameraReady.value = true
  error.value = null

  // Check if torch is supported
  const torchSupported = !!capabilities.torch
  console.log('Camera ready. Torch supported:', torchSupported)
}

const onCameraOff = () => {
  cameraReady.value = false
}

const onError = (err: Error) => {
  error.value = err.message
  cameraReady.value = false
  let errorMessage = 'Camera error occurred'

  if (err.name === 'NotAllowedError') {
    errorMessage
      = 'Camera access denied. Please allow camera access and refresh the page.'
  } else if (err.name === 'NotFoundError') {
    errorMessage = 'No camera device found on this device.'
  } else if (err.name === 'NotSupportedError') {
    errorMessage = 'Camera not supported. Please use HTTPS or localhost.'
  } else if (err.name === 'NotReadableError') {
    errorMessage = 'Camera is already in use by another application.'
  } else if (err.name === 'OverconstrainedError') {
    errorMessage = 'Requested camera not available. Trying alternative camera.'
  }

  toast.add({
    title: 'Camera Error',
    description: errorMessage,
    color: 'error'
  })
}

// Control functions
const toggleCamera = () => {
  facingMode.value
    = facingMode.value === 'environment' ? 'user' : 'environment'
}

const toggleTorch = () => {
  torchEnabled.value = !torchEnabled.value
}

const resetScanner = () => {
  scannedData.value = null
  error.value = null
  verificationResult.value = {
    userReward: null,
    reward: null,
    user: null,
    isValid: false,
    message: ''
  }
  isScanning.value = true
}

// Reward confirmation functions
const confirmReward = async () => {
  if (!rewardRecord.value) return

  try {
    // Update the user reward record to mark as exchanged
    await dFetch(`/items/t_user_reward/${rewardRecord.value.id}`, {
      method: 'PATCH',
      body: {
        exchanged_at: new Date().toISOString()
      }
    })
    toast.add({
      title: 'Reward Confirmed',
      description: 'The reward has been successfully exchanged.',
      color: 'success'
    })

    // Reset scanner after successful confirmation
    resetScanner()
  } catch (error) {
    console.error('Error confirming reward:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to confirm reward. Please try again.',
      color: 'error'
    })
  }
}

const rejectReward = () => {
  toast.add({
    title: 'Reward Rejected',
    description: 'The reward verification was rejected.',
    color: 'warning'
  })

  // Reset scanner after rejection
  resetScanner()
}

// Helper function to format dates
const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return '—'
  return new Date(dateString).toLocaleString()
}

definePageMeta({
  title: 'Reward Scanner'
})
</script>

<template>
  <UDashboardPanel id="reward-scanner">
    <template #header>
      <UDashboardNavbar title="Reward Verification Scanner">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>
        <template #right>
          <UButton
            v-if="!isScanning"
            icon="i-lucide-refresh-cw"
            variant="outline"
            size="sm"
            @click="resetScanner"
          >
            Scan Again
          </UButton>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div class="space-y-6">
        <!-- Camera Controls -->
        <div v-if="cameraReady && isScanning" class="flex gap-3 justify-center">
          <UButton
            icon="i-lucide-rotate-cw"
            variant="outline"
            @click="toggleCamera"
          >
            Switch Camera
          </UButton>

          <UButton
            :icon="
              torchEnabled ? 'i-lucide-flashlight-off' : 'i-lucide-flashlight'
            "
            variant="outline"
            @click="toggleTorch"
          >
            {{ torchEnabled ? "Turn Off" : "Turn On" }} Flash
          </UButton>
        </div>

        <!-- QR Scanner -->
        <div v-if="isScanning">
          <div class="relative">
            <QrcodeStream
              :constraints="constraints"
              :torch="torchEnabled"
              :paused="!isScanning"
              class="w-full max-w-md mx-auto rounded-lg overflow-hidden"
              @detect="onDetect"
              @camera-on="onCameraOn"
              @camera-off="onCameraOff"
              @error="onError"
            >
              <!-- Overlay content -->
              <div
                class="absolute inset-0 flex items-center justify-center pointer-events-none"
              >
                <div
                  class="w-64 h-64 border-2 border-primary-500 border-dashed rounded-lg flex items-center justify-center"
                >
                  <div
                    class="text-center text-white bg-black bg-opacity- px-3 py-2 rounded"
                  >
                    <UIcon name="i-lucide-scan" class="w-8 h-8 mx-auto mb-2" />
                    <p class="text-sm">
                      Position QR code within frame
                    </p>
                  </div>
                </div>
              </div>
            </QrcodeStream>
          </div>
        </div>

        <!-- Scanned Result -->
        <UCard v-if="scannedData">
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold">
                #{{ verificationResult.reward?.id }} - {{ verificationResult.reward?.name }}
              </h3>
              <UBadge>
                {{ verificationResult.userReward?.exchanged_at ? 'Already Exchanged' : 'Ready to Exchange' }}
              </UBadge>
            </div>
          </template>

          <template #default>
            <!-- Reward Details -->
            <div v-if="verificationResult.reward" class="space-y-6">
              <!-- Reward Image and Basic Info -->
              <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-shrink-0">
                  <img
                    v-if="verificationResult.reward.image_url"
                    :src="verificationResult.reward.image_url"
                    :alt="verificationResult.reward.name || 'Reward'"
                    class="w-1/2 aspect-square md:w-32 md:h-32 rounded-lg object-cover border mx-auto"
                  >
                  <div
                    v-else
                    class="w-24 h-24 sm:w-32 sm:h-32 rounded-lg bg-gray-100 flex items-center justify-center border"
                  >
                    <UIcon name="i-lucide-gift" class="w-8 h-8 text-gray-400" />
                  </div>
                </div>

                <div class="flex-1 space-y-2">
                  <p v-if="verificationResult.reward.description" class="text-gray-600">
                    {{ verificationResult.reward.description }}
                  </p>
                  <div class="flex items-center gap-4 text-sm text-gray-500">
                    <span v-if="verificationResult.reward.point_price" class="flex items-center gap-1">
                      <UIcon name="i-lucide-coins" class="w-4 h-4" />
                      {{ verificationResult.reward.point_price }} points
                    </span>
                    <span v-if="verificationResult.reward.inventory" class="flex items-center gap-1">
                      <UIcon name="i-lucide-package" class="w-4 h-4" />
                      {{ verificationResult.reward.inventory }} available
                    </span>
                  </div>
                  <div>
                    <span class="text-gray-500">Purchased:</span>
                    <span class="ml-2">{{ formatDate(verificationResult.userReward!.create_at) }}</span>
                  </div>
                </div>
              </div>

              <!-- Detailed Reward Info -->
              <!-- <UCollapsible v-if="rewardRecord" class="flex flex-col gap-2"> -->
              <!-- <UButton
                  class="group"
                  label="Show Reward Details"
                  color="neutral"
                  variant="subtle"
                  trailing-icon="i-lucide-chevron-down"
                  :ui="{
                    trailingIcon: 'group-data-[state=open]:rotate-180 transition-transform duration-200'
                  }"
                  block
                />
                <template #content> -->
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <!-- Detailed Description -->
                <div v-if="verificationResult.reward.detail_description" class="border-t pt-4">
                  <p class="text-gray-600 whitespace-pre-wrap">
                    {{ verificationResult.reward.detail_description }}
                  </p>
                </div>
                <div>
                  <span class="text-gray-500">Reward ID:</span>
                  <span class="ml-2 font-mono">#{{ verificationResult.userReward!.id }}</span>
                </div>
                <div>
                  <span class="text-gray-500">User ID:</span>
                  <span class="ml-2 font-mono">#{{ verificationResult.user!.id }} {{ verificationResult.user!.nickname }} | {{ verificationResult.user!.gender === 0 ? 'Male' : 'Female' }}</span>
                </div>
              </div>
              <!-- </template>
              </UCollapsible> -->

              <!-- Action Buttons -->
              <div class="border-t pt-4 flex gap-3 justify-end">
                <UButton
                  variant="outline"
                  color="error"
                  icon="i-lucide-x"
                  @click="rejectReward"
                >
                  Cancel
                </UButton>
                <UButton
                  color="success"
                  icon="i-lucide-check"
                  :disabled="!!rewardRecord?.exchanged_at"
                  @click="confirmReward"
                >
                  {{ verificationResult.userReward?.exchanged_at ? 'Already Exchanged' : 'Confirm Exchange' }}
                </UButton>
              </div>
            </div>

            <!-- Error State -->
            <div v-else-if="alreadyClaimed" class="text-center py-6">
              <UIcon name="i-lucide-alert-circle" class="w-12 h-12 text-red-500 mx-auto mb-3" />
              <h4 class="text-lg font-semibold text-red-600 mb-2">
                Reward Already Claimed
              </h4>
              <p class="text-gray-600 mb-4">
                This reward has already been exchanged and cannot be processed again.
              </p>
              <UButton
                variant="outline"
                icon="i-lucide-refresh-cw"
                @click="resetScanner"
              >
                Scan Another Code
              </UButton>
            </div>

            <!-- Loading State -->
            <div v-else class="text-center py-6">
              <UIcon name="i-lucide-loader-2" class="w-8 h-8 text-primary-500 mx-auto mb-3 animate-spin" />
              <p class="text-gray-600">
                Loading reward details...
              </p>
            </div>
          </template>
        </UCard>
      </div>
    </template>
  </UDashboardPanel>
</template>
