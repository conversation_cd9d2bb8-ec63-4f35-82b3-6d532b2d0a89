<script setup lang="ts">
import type { TableColumn, TableRow } from '@nuxt/ui'

const { useDirectusFetch } = useDirectus()

const UButton = resolveComponent('UButton')
const UBadge = resolveComponent('UBadge')

const table = useTemplateRef('table')

// Fetch rewards from Directus using the composable
const { data: rewards } = await useDirectusFetch<TReward[]>('/items/t_reward', {
  key: 'all-rewards'
})

const onRowSelect = (row: TableRow<TReward>) => {
  navigateTo(`/rewards/${row.original.id}`)
}

const columns: TableColumn<TReward>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
    cell: ({ row }) =>
      h('span', { class: 'font-mono text-xs' }, `#${row.original.id}`)
  },
  {
    // Image
    accessorKey: 'image_url',
    header: 'Image',
    cell: ({ row }) => {
      const imageUrl = row.original.image_url
      if (!imageUrl)
        return h('span', { class: 'text-neutral-400' }, 'No image')
      return h('img', {
        src: imageUrl,
        class: 'w-10 h-10 rounded-full'
      })
    }
  },
  {
    accessorKey: 'name',
    header: 'Reward Name',
    cell: ({ row }) =>
      h(
        'div',
        { class: 'font-medium' },
        row.original.name || 'Untitled Reward'
      )
  },
  {
    accessorKey: 'description',
    header: 'Description',
    cell: ({ row }) => {
      const description = row.original.description
      if (!description)
        return h('span', { class: 'text-neutral-400' }, 'No description')
      return h(
        'span',
        { class: 'truncate max-w-xs' },
        description.length > 50
          ? `${description.substring(0, 50)}...`
          : description
      )
    }
  },
  {
    accessorKey: 'point_price',
    header: 'Point Price',
    cell: ({ row }) => row.original.point_price || 0
  },
  {
    accessorKey: 'inventory',
    header: 'Inventory',
    cell: ({ row }) => row.original.inventory || 0
  },
  {
    // bought
    accessorKey: 'bought',
    header: 'Bought',
    cell: ({ row }) => row.original.bought || 0
  }
]

// Page metadata
definePageMeta({
  title: 'Rewards'
})
</script>

<template>
  <UDashboardPanel id="rewards">
    <template #header>
      <UDashboardNavbar title="Rewards">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="New Reward"
            trailing-icon="i-lucide-plus"
            color="primary"
            to="/rewards/new"
            class="hidden lg:flex"
          />
          <UButton
            icon="i-lucide-plus"
            color="primary"
            to="/rewards/new"
            class="lg:hidden"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <UTable
        ref="table"
        :data="rewards"
        :columns="columns"
        class="cursor-pointer"
        @select="onRowSelect"
      >
        <template #empty-state>
          <div class="flex flex-col items-center justify-center py-6 gap-3">
            <UIcon
              name="i-lucide-calendar-x"
              class="w-8 h-8 text-neutral-400"
            />
            <p class="text-sm text-neutral-500">
              No rewards found.
            </p>
            <UButton
              label="Create your first reward"
              color="primary"
              to="/rewards/new"
            />
          </div>
        </template>
      </UTable>
    </template>
  </UDashboardPanel>
</template>
