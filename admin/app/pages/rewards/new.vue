<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const toast = useToast()
const router = useRouter()
const { dFetch } = useDirectus()

// Form validation schema
const schema = z.object({
  name: z
    .string()
    .min(1, 'Reward name is required')
    .max(255, 'Reward name is too long'),
  description: z.string().optional(),
  detail_description: z.string().optional(),
  point_price: z
    .number()
    .min(0, 'Point price must be 0 or greater')
    .optional(),
  inventory: z
    .number()
    .min(0, 'Inventory must be 0 or greater')
    .optional()
})

type Schema = z.output<typeof schema>

// Form state
const isLoading = ref(false)
const state = reactive<Partial<Schema>>({
  name: '',
  description: '',
  detail_description: '',
  point_price: 0,
  inventory: 0
})

// Image upload state
const rewardImageUrl = ref<string | null>(null)

// Methods
const handleCancel = () => {
  router.push('/rewards')
}

const handleImageUploadSuccess = (response: any) => {
  rewardImageUrl.value = response.url
}

const handleImageUploadError = (error: string) => {
  toast.add({
    title: 'Upload Failed',
    description: error,
    color: 'error'
  })
}

async function onSubmit(event: FormSubmitEvent<Schema>) {
  isLoading.value = true
  
  try {
    const createData = {
      ...event.data,
      image_url: rewardImageUrl.value
    }

    const newReward = await dFetch('/items/t_reward', {
      method: 'POST',
      body: createData
    })

    toast.add({
      title: 'Success',
      description: 'Reward created successfully',
      color: 'success'
    })

    // Navigate to the new reward's detail page
    router.push(`/rewards/${newReward.id}`)
  } catch (error) {
    console.error('Error creating reward:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to create reward',
      color: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// Page metadata
definePageMeta({
  title: 'New Reward'
})
</script>

<template>
  <UDashboardPanel id="new-reward">
    <template #header>
      <UDashboardNavbar title="Create New Reward">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <UButton
            label="Cancel"
            color="neutral"
            variant="ghost"
            @click="handleCancel"
          />
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div>
        <UForm :schema="schema" :state="state" @submit="onSubmit">
          <UCard>
            <template #header>
              <h2 class="text-lg font-semibold">
                Reward Information
              </h2>
              <p class="text-sm text-neutral-500 mt-1">
                Create a new reward for the Open Portal Expo platform.
              </p>
            </template>

            <div class="space-y-6">
              <!-- Reward Image -->
              <UFormField label="Reward Image" description="Upload an image for the reward (optional)" name="image">
                <ImageUpload
                  v-model="rewardImageUrl"
                  :disabled="isLoading"
                  alt="Reward image"
                  @upload-success="handleImageUploadSuccess"
                  @upload-error="handleImageUploadError"
                />
              </UFormField>

              <!-- Form Fields -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <UFormField label="Reward Name" name="name" required>
                  <UInput
                    v-model="state.name"
                    placeholder="Enter reward name"
                    :disabled="isLoading"
                  />
                </UFormField>

                <UFormField label="Point Price" name="point_price">
                  <UInput
                    v-model="state.point_price"
                    type="number"
                    min="0"
                    placeholder="0"
                    :disabled="isLoading"
                  />
                </UFormField>

                <UFormField label="Inventory" name="inventory">
                  <UInput
                    v-model="state.inventory"
                    type="number"
                    min="0"
                    placeholder="0"
                    :disabled="isLoading"
                  />
                </UFormField>
              </div>

              <UFormField label="Description" name="description">
                <UTextarea
                  v-model="state.description"
                  placeholder="Enter reward description"
                  :rows="3"
                  :disabled="isLoading"
                />
              </UFormField>

              <UFormField label="Detailed Description" name="detail_description">
                <UTextarea
                  v-model="state.detail_description"
                  placeholder="Enter detailed description"
                  :rows="4"
                  :disabled="isLoading"
                />
              </UFormField>
            </div>

            <template #footer>
              <div class="flex justify-end gap-3">
                <UButton
                  type="button"
                  color="neutral"
                  variant="ghost"
                  :disabled="isLoading"
                  @click="handleCancel"
                >
                  Cancel
                </UButton>
                <UButton
                  type="submit"
                  color="primary"
                  :loading="isLoading"
                >
                  Create Reward
                </UButton>
              </div>
            </template>
          </UCard>
        </UForm>
      </div>
    </template>
  </UDashboardPanel>
</template>
