<script setup lang="ts">
import { z } from 'zod'
import type { FormSubmitEvent } from '#ui/types'

const { useDirectusFetch, dFetch } = useDirectus()
const route = useRoute()
const router = useRouter()
const toast = useToast()

// Fetch reward from Directus using the composable
const { data: reward, refresh: refreshReward } = await useDirectusFetch<TReward>(
  `/items/t_reward/${route.params.id}`,
  {
    key: `reward-${route.params.id}`
  }
)

// Reactive state
const isEditing = ref(false)
const isLoading = ref(false)
const isDeleting = ref(false)

// Form validation schema
const schema = z.object({
  name: z
    .string()
    .min(1, 'Reward name is required')
    .max(255, 'Reward name is too long'),
  description: z.string().optional(),
  detail_description: z.string().optional(),
  point_price: z
    .number()
    .min(0, 'Point price must be 0 or greater')
    .optional(),
  inventory: z
    .number()
    .min(0, 'Inventory must be 0 or greater')
    .optional()
})

type Schema = z.output<typeof schema>

// Form state
const state = reactive<Partial<Schema>>({
  name: '',
  description: '',
  detail_description: '',
  point_price: 0,
  inventory: 0
})

// Image upload state
const rewardImageUrl = ref<string | null>(null)

// Initialize form state when reward data is available
watchEffect(() => {
  if (reward.value) {
    state.name = reward.value.name || ''
    state.description = reward.value.description || ''
    state.detail_description = reward.value.detail_description || ''
    state.point_price = reward.value.point_price || 0
    state.inventory = reward.value.inventory || 0
    rewardImageUrl.value = reward.value.image_url || null
  }
})

// Computed properties
const pageTitle = computed(() => {
  if (isEditing.value) {
    return reward.value?.name ? `Edit ${reward.value.name}` : 'Edit Reward'
  }
  return reward.value?.name || 'Reward Details'
})

const canEdit = computed(() => !!reward.value && !isLoading.value)
const canDelete = computed(() => !!reward.value && !isLoading.value && !isDeleting.value)

// Methods
const handleEdit = () => {
  isEditing.value = true
}

const handleCancel = () => {
  if (isEditing.value) {
    // Reset form state
    if (reward.value) {
      state.name = reward.value.name || ''
      state.description = reward.value.description || ''
      state.detail_description = reward.value.detail_description || ''
      state.point_price = reward.value.point_price || 0
      state.inventory = reward.value.inventory || 0
      rewardImageUrl.value = reward.value.image_url || null
    }
    isEditing.value = false
  } else {
    router.push('/rewards')
  }
}

const handleImageUploadSuccess = (response: any) => {
  rewardImageUrl.value = response.url
}

const handleImageUploadError = (error: string) => {
  toast.add({
    title: 'Upload Failed',
    description: error,
    color: 'error'
  })
}

async function onSubmit(event: FormSubmitEvent<Schema>) {
  if (!reward.value) return

  isLoading.value = true

  try {
    const updateData = {
      ...event.data,
      image_url: rewardImageUrl.value
    }

    await dFetch(`/items/t_reward/${reward.value.id}`, {
      method: 'PATCH',
      body: updateData
    })

    await refreshReward()
    isEditing.value = false

    toast.add({
      title: 'Success',
      description: 'Reward updated successfully',
      color: 'success'
    })
  } catch (error) {
    console.error('Error updating reward:', error)
    toast.add({
      title: 'Error',
      description: 'Failed to update reward',
      color: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

const handleDelete = async () => {
  if (!reward.value) return

  const confirmed = confirm(
    `Are you sure you want to delete "${reward.value.name}"? This action cannot be undone.`
  )

  if (confirmed) {
    isDeleting.value = true

    try {
      await dFetch(`/items/t_reward/${reward.value.id}`, {
        method: 'DELETE'
      })

      toast.add({
        title: 'Success',
        description: 'Reward deleted successfully',
        color: 'success'
      })

      router.push('/rewards')
    } catch (error) {
      console.error('Error deleting reward:', error)
      toast.add({
        title: 'Error',
        description: 'Failed to delete reward',
        color: 'error'
      })
    } finally {
      isDeleting.value = false
    }
  }
}

// Page metadata
definePageMeta({
  title: 'Reward Details'
})
</script>

<template>
  <UDashboardPanel id="reward-detail">
    <template #header>
      <UDashboardNavbar :title="pageTitle">
        <template #leading>
          <UDashboardSidebarCollapse />
        </template>

        <template #right>
          <div class="flex items-center gap-2">
            <UButton
              v-if="!isEditing"
              label="Back to Rewards"
              icon="i-lucide-arrow-left"
              color="neutral"
              variant="subtle"
              @click="handleCancel"
            />
            <UButton
              v-if="isEditing"
              label="Cancel"
              color="neutral"
              variant="ghost"
              @click="handleCancel"
            />
          </div>
        </template>
      </UDashboardNavbar>
    </template>

    <template #body>
      <div v-if="reward">
        <!-- View Mode -->
        <div v-if="!isEditing">
          <UCard>
            <template #header>
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-lg font-semibold">
                    {{ reward.name || 'Untitled Reward' }}
                  </h2>
                  <p class="text-sm text-neutral-500 mt-1">
                    Reward details and information
                  </p>
                </div>
                <div class="flex items-center gap-2">
                  <div class="text-sm text-neutral-500">
                    ID: #{{ reward.id }}
                  </div>
                  <UButton
                    icon="i-lucide-pencil"
                    color="primary"
                    variant="outline"
                    size="sm"
                    :disabled="!canEdit"
                    @click="handleEdit"
                  >
                    Edit
                  </UButton>
                  <UButton
                    icon="i-lucide-trash-2"
                    color="error"
                    variant="outline"
                    size="sm"
                    :disabled="!canDelete"
                    :loading="isDeleting"
                    @click="handleDelete"
                  >
                    Delete
                  </UButton>
                </div>
              </div>
            </template>

            <div class="space-y-6">
              <!-- Reward Image -->
              <div v-if="reward.image_url" class="flex justify-center">
                <img
                  :src="reward.image_url"
                  :alt="reward.name || 'Reward image'"
                  class="max-w-md w-full h-auto rounded-lg shadow-sm border border-gray-200"
                >
              </div>
              <div v-else class="flex justify-center py-8">
                <div class="text-center text-neutral-400">
                  <UIcon name="i-lucide-image" class="w-12 h-12 mx-auto mb-2" />
                  <p>No image available</p>
                </div>
              </div>

              <!-- Reward Information -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                  <p class="text-sm text-gray-900">{{ reward.name || 'N/A' }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Point Price</label>
                  <p class="text-sm text-gray-900">{{ reward.point_price || 0 }} points</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Inventory</label>
                  <p class="text-sm text-gray-900">{{ reward.inventory || 0 }} items</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Bought</label>
                  <p class="text-sm text-gray-900">{{ reward.bought || 0 }} times</p>
                </div>
              </div>

              <div v-if="reward.description">
                <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ reward.description }}</p>
              </div>

              <div v-if="reward.detail_description">
                <label class="block text-sm font-medium text-gray-700 mb-1">Detailed Description</label>
                <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ reward.detail_description }}</p>
              </div>

              <!-- Timestamps -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
                <div v-if="reward.create_at">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Created</label>
                  <p class="text-sm text-gray-500">{{ new Date(reward.create_at).toLocaleString() }}</p>
                </div>

                <div v-if="reward.update_at">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Last Updated</label>
                  <p class="text-sm text-gray-500">{{ new Date(reward.update_at).toLocaleString() }}</p>
                </div>
              </div>
            </div>
          </UCard>
        </div>

        <!-- Edit Mode -->
        <div v-else>
          <UForm :schema="schema" :state="state" @submit="onSubmit">
            <UCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <div>
                    <h2 class="text-lg font-semibold">
                      Edit Reward
                    </h2>
                    <p class="text-sm text-neutral-500 mt-1">
                      Update the reward information below.
                    </p>
                  </div>
                  <div class="text-sm text-neutral-500">
                    ID: #{{ reward.id }}
                  </div>
                </div>
              </template>

              <div class="space-y-6">
                <!-- Reward Image -->
                <UFormField label="Reward Image" description="Upload an image for the reward (optional)" name="image">
                  <ImageUpload
                    v-model="rewardImageUrl"
                    :disabled="isLoading"
                    alt="Reward image"
                    @upload-success="handleImageUploadSuccess"
                    @upload-error="handleImageUploadError"
                  />
                </UFormField>

                <!-- Form Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <UFormField label="Reward Name" name="name" required>
                    <UInput
                      v-model="state.name"
                      placeholder="Enter reward name"
                      :disabled="isLoading"
                    />
                  </UFormField>

                  <UFormField label="Point Price" name="point_price">
                    <UInput
                      v-model="state.point_price"
                      type="number"
                      min="0"
                      placeholder="0"
                      :disabled="isLoading"
                    />
                  </UFormField>

                  <UFormField label="Inventory" name="inventory">
                    <UInput
                      v-model="state.inventory"
                      type="number"
                      min="0"
                      placeholder="0"
                      :disabled="isLoading"
                    />
                  </UFormField>
                </div>

                <UFormField label="Description" name="description">
                  <UTextarea
                    v-model="state.description"
                    placeholder="Enter reward description"
                    :rows="3"
                    :disabled="isLoading"
                  />
                </UFormField>

                <UFormField label="Detailed Description" name="detail_description">
                  <UTextarea
                    v-model="state.detail_description"
                    placeholder="Enter detailed description"
                    :rows="4"
                    :disabled="isLoading"
                  />
                </UFormField>
              </div>

              <template #footer>
                <div class="flex justify-end gap-3">
                  <UButton
                    type="button"
                    color="neutral"
                    variant="ghost"
                    :disabled="isLoading"
                    @click="handleCancel"
                  >
                    Cancel
                  </UButton>
                  <UButton
                    type="submit"
                    color="primary"
                    :loading="isLoading"
                  >
                    Save Changes
                  </UButton>
                </div>
              </template>
            </UCard>
          </UForm>
        </div>
      </div>

      <!-- Loading State -->
      <div v-else class="flex justify-center py-8">
        <UIcon name="i-lucide-loader-2" class="w-6 h-6 animate-spin" />
      </div>
    </template>
  </UDashboardPanel>
</template>
