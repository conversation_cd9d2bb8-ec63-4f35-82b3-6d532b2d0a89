import { createFetch } from 'ofetch'

// Types for backend API responses
export interface BackendApiResponse<T> {
  code: number
  msg: string
  data: T
  success: boolean
}

export interface RankResult {
  rank: number
  score: number
  nickName: string
}

export interface RankResponse {
  rankResults: RankResult[]
}

export default function useBackendApi() {
  const config = useRuntimeConfig()

  // Create fetch instance for backend API
  const backendFetch = createFetch({
    defaults: {
      baseURL: config.public.backendApiUrl,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  })

  // Fetch rankings
  const fetchRankings = async (n: number = 50): Promise<RankResult[]> => {
    try {
      const response = await backendFetch<BackendApiResponse<RankResponse>>(`/rank/v1/showTopN?n=${n}`)
      if (response.code === 200 && response.data) {
        return response.data.rankResults || []
      } else {
        throw new Error(response.msg || 'Failed to fetch rankings')
      }
    } catch (error) {
      console.error('Error fetching rankings:', error)
      throw error
    }
  }

  return {
    fetchRankings,
    backendFetch
  }
}
