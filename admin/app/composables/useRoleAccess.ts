/**
 * Simple role-based access control using Directus role UUIDs
 */
export const useRoleAccess = () => {
  const { currentUser } = useAuth()

  // Define role UUIDs
  const FLOOR_STAFF_ROLE_UUID = 'efb617fd-f42b-469f-a364-f3e4a703e1b8'
  const ADMIN_ROLE_UUID = 'babdf169-7a9d-4147-a8e5-f73ff5d28a8e'

  // Get current user's role UUID
  const userRoleId = computed(() => {
    return currentUser.value?.role || null
  })

  // Helper to log current user's role UUID (for development)
  watch(userRoleId, (roleId) => {
    if (import.meta.dev && roleId) {
      console.log('Current user role UUID:', roleId)
    }
  }, { immediate: true })

  // Check if user is floor staff
  const isFloorStaff = computed(() => {
    return userRoleId.value === FLOOR_STAFF_ROLE_UUID
  })

  // Check if user is admin
  const isAdmin = computed(() => {
    return userRoleId.value === ADMIN_ROLE_UUID
  })

  // Get user role name for display
  const roleName = computed(() => {
    if (isFloorStaff.value) return 'Floor Staff'
    if (isAdmin.value) return 'Administrator'
    return 'Staff' // Default for other roles
  })

  // Check if user should see a navigation section
  const canSeeSection = (section: string): boolean => {
    if (isFloorStaff.value) {
      // Floor staff can only see rewards section
      return section === 'rewards'
    }

    // All other roles can see everything
    return true
  }

  // Check if user should see a specific page/route
  const canSeePage = (page: string): boolean => {
    if (isFloorStaff.value) {
      // Floor staff allowed pages
      const allowedPages = [
        '/',
        '/rewards',
        '/rewards/scanner',
        '/users' // Can view users to help with reward claims
      ]

      // Check exact match or if page starts with allowed path
      return allowedPages.some((allowedPage) => {
        if (allowedPage === '/') return page === '/'
        return page === allowedPage || page.startsWith(allowedPage + '/')
      })
    }

    // All other roles can see everything
    return true
  }

  return {
    userRoleId,
    isFloorStaff,
    isAdmin,
    roleName,
    canSeeSection,
    canSeePage
  }
}
