import type { S3UploadResponse } from '~/types/s3'
import { validateImageFile } from '~/types/s3'

export function useS3Upload() {
  const uploading = ref(false)
  const uploadProgress = ref(0)
  const error = ref<string | null>(null)

  /**
   * Upload a file to S3
   * @param file - The file to upload
   * @returns Promise with upload result
   */
  async function uploadFile(file: File): Promise<S3UploadResponse> {
    // Reset state
    uploading.value = true
    uploadProgress.value = 0
    error.value = null

    try {
      // Validate file before upload
      const validation = validateImageFile(file)
      if (!validation.valid) {
        throw new Error(validation.error)
      }

      // Create form data
      const formData = new FormData()
      formData.append('file', file)

      // Upload to S3
      const response = await $fetch<S3UploadResponse>('/api/s3/public-upload', {
        method: 'POST',
        body: formData
      })

      if (!response.success) {
        throw new Error(response.error || 'Upload failed')
      }

      uploadProgress.value = 100
      return response
    } catch (err: any) {
      const errorMessage = err.data?.message || err.message || 'Upload failed'
      error.value = errorMessage
      throw new Error(errorMessage)
    } finally {
      uploading.value = false
    }
  }

  /**
   * Upload multiple files to S3
   * @param files - Array of files to upload
   * @returns Promise with array of upload results
   */
  async function uploadFiles(files: File[]): Promise<S3UploadResponse[]> {
    const results: S3UploadResponse[] = []

    for (const file of files) {
      try {
        const result = await uploadFile(file)
        results.push(result)
      } catch (err) {
        results.push({
          success: false,
          error: err instanceof Error ? err.message : 'Upload failed'
        })
      }
    }

    return results
  }

  /**
   * Reset upload state
   */
  function reset() {
    uploading.value = false
    uploadProgress.value = 0
    error.value = null
  }

  return {
    uploading: readonly(uploading),
    uploadProgress: readonly(uploadProgress),
    error: readonly(error),
    uploadFile,
    uploadFiles,
    reset
  }
}
