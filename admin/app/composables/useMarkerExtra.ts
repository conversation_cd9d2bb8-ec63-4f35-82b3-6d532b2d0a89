/**
 * Encapsulates loading, saving and image upload for a marker's linked data row
 * (exhibitor, game, shop, etc.).
 *
 * Usage:
 * const {
 *   selectedMarkerExtra,
 *   isExtraSaving,
 *   isImageUploading,
 *   saveMarkerExtra,
 *   onLinkedImageChange,
 * } = useMarkerExtra(selectedData, refreshMarkersCallback)
 */
export function useMarkerExtra(selectedData: Ref<any>) {
  const { dFetch } = useDirectus()
  const runtimeCfg = useRuntimeConfig()

  const selectedMarkerExtra = ref<any>(null)
  const isExtraSaving = ref(false)

  // Internal helper to load existing linked data for a given marker
  const loadMarkerExtra = async (marker: TMark) => {
    const table
      = markerTypeTableMap[marker.mark_type as keyof typeof markerTypeTableMap]
    if (!table) {
      selectedMarkerExtra.value = null
      return
    }

    // Query by mark_id
    const extraData = await dFetch<any[]>(`/items/${table}` as any, {
      params: {
        filter: { mark_id: { _eq: marker.id } },
        limit: 1
      }
    })

    if (extraData && extraData.length) {
      selectedMarkerExtra.value = extraData[0]
    } else {
      // Don't create immediately - create a placeholder for the form
      const baseFields = {
        mark_id: marker.id,
        name: marker.name,
        description: '',
        _isNew: true // Flag to indicate this needs to be created
      }

      // Add type-specific fields
      if (marker.mark_type === 1) {
        // Game markers - only need game-specific fields
        selectedMarkerExtra.value = {
          ...baseFields,
          game_available_id: null,
          game_level: 1 // Default to Easy level
        }
      } else {
        // Other marker types - include standard fields
        selectedMarkerExtra.value = {
          ...baseFields,
          tags: '',
          website_url: '',
          image: null,
          thumbnail_url: null
        }
      }
    }
  }

  const saveMarkerExtra = async () => {
    if (!selectedMarkerExtra.value || !selectedData.value) return
    const table
      = markerTypeTableMap[
        selectedData.value.mark_type as keyof typeof markerTypeTableMap
      ]
    if (!table) return
    isExtraSaving.value = true

    try {
      // Always use the marker name for the linked entity
      const bodyWithMarkerName = {
        ...selectedMarkerExtra.value,
        name: selectedData.value.name
      }

      // Remove internal flags from the body
      delete bodyWithMarkerName._isNew

      if (selectedMarkerExtra.value._isNew) {
        // Create new linked data entry
        const newEntry = await dFetch(`/items/${table}` as any, {
          method: 'POST',
          body: bodyWithMarkerName
        })
        selectedMarkerExtra.value = { ...newEntry, _isNew: false }
      } else {
        // Update existing linked data entry
        await dFetch(`/items/${table}/${selectedMarkerExtra.value.id}` as any, {
          method: 'PATCH',
          body: bodyWithMarkerName
        })
      }
    } catch (error) {
      console.error('Failed to save marker extra data:', error)
      throw error
    } finally {
      isExtraSaving.value = false
    }
  }



  // Watch selection changes and marker type changes
  watch(
    () => selectedData.value,
    async (val, oldVal) => {
      if (val?.type === 'marker') {
        // Check if marker type changed and cleanup old linked data
        if (oldVal?.type === 'marker' && oldVal.id === val.id && oldVal.mark_type !== val.mark_type) {
          await cleanupOldLinkedData(oldVal as TMark)
        }
        await loadMarkerExtra(val as TMark)
      } else {
        selectedMarkerExtra.value = null
      }
    },
    { immediate: false, deep: true }
  )

  // Function to cleanup old linked data when marker type changes
  const cleanupOldLinkedData = async (oldMarker: TMark) => {
    const oldTable = markerTypeTableMap[oldMarker.mark_type as keyof typeof markerTypeTableMap]
    if (!oldTable) return

    try {
      // Find and delete old linked data entry
      const oldExtraData = await dFetch<any[]>(`/items/${oldTable}` as any, {
        params: {
          filter: { mark_id: { _eq: oldMarker.id } },
          limit: 1
        }
      })

      if (oldExtraData && oldExtraData.length > 0) {
        await dFetch(`/items/${oldTable}/${oldExtraData[0].id}` as any, {
          method: 'DELETE'
        })
      }
    } catch (error) {
      console.error('Failed to cleanup old linked data:', error)
    }
  }

  return {
    selectedMarkerExtra,
    isExtraSaving,
    saveMarkerExtra,
    directusUrl: runtimeCfg.public.directusUrl
  }
}
