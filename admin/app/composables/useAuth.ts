interface LoginCredentials {
  email: string
  password: string
}

interface AuthUser {
  id: string
  email: string
  first_name: string
  last_name: string
  role: string
}

interface LoginResponse {
  access_token: string
  expires: number
  refresh_token: string
}

export const useAuth = () => {
  const config = useRuntimeConfig()

  // Track if we're currently refreshing to avoid multiple simultaneous refresh attempts
  let isRefreshing = false
  let refreshPromise: Promise<boolean> | null = null

  // Cookies for storing tokens
  const accessToken = useCookie<string | null>('directus_access_token', {
    default: () => null,
    maxAge: 60 * 15, // 15 minutes
    secure: true,
    sameSite: 'strict'
  })

  const refreshToken = useCookie<string | null>('directus_refresh_token', {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7 days
    secure: true,
    sameSite: 'strict'
  })

  const user = useState<AuthUser | null>('directus_user', () => null)

  // Computed state
  const isAuthenticated = computed(() => !!accessToken.value)
  const currentUser = computed(() => user.value)

  // Get current token
  const getToken = () => accessToken.value

  // Login function
  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await $fetch<{ data: LoginResponse }>('/auth/login', {
        method: 'POST',
        baseURL: config.public.directusUrl,
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          email: credentials.email,
          password: credentials.password
        }
      })

      if (response.data) {
        // Store tokens
        accessToken.value = response.data.access_token
        refreshToken.value = response.data.refresh_token

        // Fetch user info
        await fetchUser()

        return { success: true }
      }

      return { success: false, error: 'Invalid response from server' }
    } catch (error: any) {
      console.error('Login error:', error)
      return {
        success: false,
        error:
          error.data?.errors?.[0]?.message
          || 'Login failed. Please check your credentials.'
      }
    }
  }

  // Fetch current user info
  const fetchUser = async () => {
    if (!accessToken.value) return

    try {
      const response = await $fetch<{ data: AuthUser }>('/users/me', {
        baseURL: config.public.directusUrl,
        headers: {
          Authorization: `Bearer ${accessToken.value}`
        }
      })

      if (response.data) {
        user.value = response.data
      }
    } catch (error) {
      console.error('Failed to fetch user:', error)
      // If fetching user fails, the token might be invalid
      await logout()
    }
  }

  // Refresh access token
  const refreshAccessToken = async () => {
    // If already refreshing, wait for the existing promise
    if (isRefreshing && refreshPromise) {
      return await refreshPromise
    }

    if (!refreshToken.value) {
      await logout()
      return false
    }
    console.log('Refreshing token:', refreshToken.value)

    // Set refreshing state and create promise
    isRefreshing = true
    refreshPromise = (async () => {
      try {
        const response = await $fetch<{ data: LoginResponse }>(
          '/auth/refresh',
          {
            method: 'POST',
            baseURL: config.public.directusUrl,
            body: {
              refresh_token: refreshToken.value
            }
          }
        )

        if (response.data) {
          console.warn('Refresh response:', response.data)
          accessToken.value = response.data.access_token
          refreshToken.value = response.data.refresh_token
          return true
        }

        await logout()
        return false
      } catch (error) {
        console.error('Token refresh failed:', error)
        await logout()
        return false
      } finally {
        // Reset refreshing state
        isRefreshing = false
        refreshPromise = null
      }
    })()

    return await refreshPromise
  }

  // Logout function
  const logout = async () => {
    try {
      if (refreshToken.value) {
        await $fetch('/auth/logout', {
          method: 'POST',
          baseURL: config.public.directusUrl,
          body: {
            refresh_token: refreshToken.value,
            mode: 'json'
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear all stored data
      accessToken.value = null
      refreshToken.value = null
      user.value = null

      // Redirect to login
      await navigateTo('/login')
    }
  }

  // Initialize auth state on app start
  const initAuth = async () => {
    if (accessToken.value && !user.value) {
      await fetchUser()
    }
  }

  return {
    // State
    isAuthenticated,
    currentUser,
    accessToken,
    refreshToken,

    // Methods
    login,
    logout,
    getToken,
    refreshAccessToken,
    fetchUser,
    initAuth
  }
}
