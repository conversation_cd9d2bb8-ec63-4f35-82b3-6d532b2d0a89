import { defu } from 'defu'
import { createFetch } from 'ofetch'
import type { UseFetchOptions } from '#app'

export default function () {
  const config = useRuntimeConfig()
  const { getToken } = useAuth()

  // Equivalent to useFetch
  const useDirectusFetch = <T>(
    url: string | (() => string),
    options: UseFetchOptions<T> = {}
  ) => {
    const token = getToken()

    const headers: Record<string, string> = {}
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    const defaults: UseFetchOptions<T> = {
      baseURL: config.public.directusUrl,
      headers,
      onResponse(_ctx) {
        _ctx.response._data = _ctx.response._data.data
      }
    }

    const params = defu(options, defaults)

    return useFetch(url, params)
  }

  // Equivalent to $fetch
  const dFetch = createFetch({
    defaults: {
      baseURL: config.public.directusUrl,
      onRequest(_ctx) {
        const token = getToken()
        if (token) {
          _ctx.options.headers = {
            ..._ctx.options.headers,
            Authorization: `Bearer ${token}`
          } as any
        }
      },
      onResponse(_ctx) {
        if (_ctx.response._data) {
          _ctx.response._data = _ctx.response._data.data
        }
      }
    }
  })

  return {
    useDirectusFetch,
    dFetch
  }
}
