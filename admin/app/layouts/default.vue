<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui'

const toast = useToast()
const { canSeeSection } = useRoleAccess()

const open = ref(false)

const links = [
  [
    {
      label: 'Dashboard',
      icon: 'i-lucide-layout-dashboard',
      to: '/',
      onSelect: () => {
        open.value = false
      }
    },
    {
      label: 'Events',
      icon: 'i-lucide-calendar',
      to: '/events',
      type: 'trigger',
      defaultOpen: false,
      children: [
        {
          label: 'All Events',
          to: '/events',
          exact: true,
          onSelect: () => {
            open.value = false
          }
        }
      ]
    },
    // {
    //   label: 'Content',
    //   icon: 'i-lucide-layers',
    //   type: 'trigger',
    //   defaultOpen: false,
    //   children: [
    //     {
    //       label: 'Exhibitors',
    //       to: '/content/exhibitors',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Hotels',
    //       to: '/content/hotels',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Restaurants',
    //       to: '/content/restaurants',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Shops',
    //       to: '/content/shops',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Others',
    //       to: '/content/others',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Shows',
    //       to: '/content/shows',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Games',
    //       to: '/content/games',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Bulletins',
    //       to: '/content/bulletins',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     }
    //   ]
    // },
    {
      label: 'Users',
      icon: 'i-lucide-users',
      to: '/users',
      type: 'trigger',
      defaultOpen: false,
      children: [
        {
          label: 'All Users',
          to: '/users',
          exact: true,
          onSelect: () => {
            open.value = false
          }
        },
        // {
        //   label: 'Tickets',
        //   to: '/users/tickets',
        //   onSelect: () => {
        //     open.value = false
        //   }
        // },
        {
          label: 'Rankings',
          to: '/users/rankings',
          onSelect: () => {
            open.value = false
          }
        },
        {
          label: 'Game Records',
          to: '/users/game-records',
          onSelect: () => {
            open.value = false
          }
        }
      ]
    },
    // {
    //   label: 'System',
    //   to: '/system',
    //   icon: 'i-lucide-settings',
    //   type: 'trigger',
    //   defaultOpen: false,
    //   children: [
    //     {
    //       label: 'Files',
    //       to: '/system/files',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Settings',
    //       to: '/system/settings',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     },
    //     {
    //       label: 'Admin Users',
    //       to: '/system/admin-users',
    //       onSelect: () => {
    //         open.value = false
    //       }
    //     }
    //   ]
    // },
    {
      label: 'Rewards',
      icon: 'i-lucide-gift',
      to: '/rewards',
      onSelect: () => {
        open.value = false
      },
      children: [
        {
          label: 'All Rewards',
          to: '/rewards',
          onSelect: () => {
            open.value = false
          }
        },
        {
          label: 'QR Reward Scanner',
          to: '/rewards/scanner',
          onSelect: () => {
            open.value = false
          }
        }
      ]
    }
  ]
] satisfies NavigationMenuItem[][]

// Filter navigation items based on user role
const filteredLinks = computed(() => {
  return links.map(linkGroup =>
    linkGroup.filter((item) => {
      // Check section access based on label
      const sectionName = item.label.toLowerCase()
      return canSeeSection(sectionName)
    })
  )
})

const groups = computed(() => [
  {
    id: 'navigation',
    label: 'Navigation',
    items: filteredLinks.value.flat()
  },
  {
    id: 'actions',
    label: 'Quick Actions',
    items: [
      {
        id: 'new-event',
        label: 'Create New Event',
        icon: 'i-lucide-plus',
        to: '/events/new'
      },
      {
        id: 'new-exhibitor',
        label: 'Add Exhibitor',
        icon: 'i-lucide-plus',
        to: '/content/exhibitors/new'
      },
      {
        id: 'event-maps',
        label: 'Manage Event Maps',
        icon: 'i-lucide-map',
        to: '/events/maps'
      }
    ]
  }
])

onMounted(async () => {
  const cookie = useCookie('cookie-consent')
  if (cookie.value === 'accepted') {
    return
  }

  toast.add({
    title:
      'We use first-party cookies to enhance your experience on our website.',
    duration: 0,
    close: false,
    actions: [
      {
        label: 'Accept',
        color: 'neutral',
        variant: 'outline',
        onClick: () => {
          cookie.value = 'accepted'
        }
      },
      {
        label: 'Opt out',
        color: 'neutral',
        variant: 'ghost'
      }
    ]
  })
})
</script>

<template>
  <UDashboardGroup unit="rem">
    <UDashboardSidebar
      id="default"
      v-model:open="open"
      collapsible
      resizable
      class="bg-elevated/25"
      :ui="{ footer: 'lg:border-t lg:border-default' }"
    >
      <template #header="{ collapsed }">
        <TeamsMenu :collapsed="collapsed" />
        <NuxtLink class="cursor-pointer" to="/">
          <img src="/energy-icon.svg" alt="Open Portal Expo" class="h-8">
        </NuxtLink>
      </template>

      <template #default="{ collapsed }">
        <UDashboardSearchButton
          :collapsed="collapsed"
          class="bg-transparent ring-default"
        />

        <UNavigationMenu
          :collapsed="collapsed"
          :items="filteredLinks[0]"
          orientation="vertical"
          tooltip
          popover
        />

        <UNavigationMenu
          :collapsed="collapsed"
          :items="filteredLinks[1]"
          orientation="vertical"
          tooltip
          class="mt-auto"
        />
      </template>

      <template #footer="{ collapsed }">
        <UserMenu :collapsed="collapsed" />
      </template>
    </UDashboardSidebar>

    <UDashboardSearch :groups="groups" />

    <slot />

    <NotificationsSlideover />
  </UDashboardGroup>
</template>
