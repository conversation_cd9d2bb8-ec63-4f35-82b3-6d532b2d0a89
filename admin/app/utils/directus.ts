export type DirectusAccess = {
  id: string
  policy: string | DirectusPolicies
  role?: string | DirectusRoles | null
  sort?: number | null
  user?: string | DirectusUsers | null
}

export type DirectusActivity = {
  action: string
  collection: string
  id: number
  ip?: string | null
  item: string
  origin?: string | null
  revisions: any[] | DirectusRevisions[]
  timestamp: string
  user?: string | DirectusUsers | null
  user_agent?: string | null
}

export type DirectusCollections = {
  accountability?: string | null
  archive_app_filter: boolean
  archive_field?: string | null
  archive_value?: string | null
  collapse: string
  collection: string
  color?: string | null
  display_template?: string | null
  group?: string | DirectusCollections | null
  hidden: boolean
  icon?: string | null
  item_duplication_fields?: unknown | null
  note?: string | null
  preview_url?: string | null
  singleton: boolean
  sort?: number | null
  sort_field?: string | null
  translations?: unknown | null
  unarchive_value?: string | null
  versioning: boolean
}

export type DirectusComments = {
  collection: string | DirectusCollections
  comment: string
  date_created?: string | null
  date_updated?: string | null
  id: string
  item: string
  user_created?: string | DirectusUsers | null
  user_updated?: string | DirectusUsers | null
}

export type DirectusDashboards = {
  color?: string | null
  date_created?: string | null
  icon: string
  id: string
  name: string
  note?: string | null
  panels: any[] | DirectusPanels[]
  user_created?: string | DirectusUsers | null
}

export type DirectusExtensions = {
  bundle?: string | null
  enabled: boolean
  folder: string
  id: string
  source: string
}

export type DirectusFields = {
  collection: string | DirectusCollections
  conditions?: unknown | null
  display?: string | null
  display_options?: unknown | null
  field: string
  group?: string | DirectusFields | null
  hidden: boolean
  id: number
  interface?: string | null
  note?: string | null
  options?: unknown | null
  readonly: boolean
  required?: boolean | null
  sort?: number | null
  special?: unknown | null
  translations?: unknown | null
  validation?: unknown | null
  validation_message?: string | null
  width?: string | null
}

export type DirectusFiles = {
  charset?: string | null
  created_on: string
  description?: string | null
  duration?: number | null
  embed?: string | null
  filename_disk?: string | null
  filename_download: string
  filesize?: number | null
  focal_point_x?: number | null
  focal_point_y?: number | null
  folder?: string | DirectusFolders | null
  height?: number | null
  id: string
  location?: string | null
  metadata?: unknown | null
  modified_by?: string | DirectusUsers | null
  modified_on: string
  storage: string
  tags?: unknown | null
  title?: string | null
  tus_data?: unknown | null
  tus_id?: string | null
  type?: string | null
  uploaded_by?: string | DirectusUsers | null
  uploaded_on?: string | null
  width?: number | null
}

export type DirectusFlows = {
  accountability?: string | null
  color?: string | null
  date_created?: string | null
  description?: string | null
  icon?: string | null
  id: string
  name: string
  operation?: string | DirectusOperations | null
  operations: any[] | DirectusOperations[]
  options?: unknown | null
  status: string
  trigger?: string | null
  user_created?: string | DirectusUsers | null
}

export type DirectusFolders = {
  id: string
  name: string
  parent?: string | DirectusFolders | null
}

export type DirectusMigrations = {
  name: string
  timestamp?: string | null
  version: string
}

export type DirectusNotifications = {
  collection?: string | null
  id: number
  item?: string | null
  message?: string | null
  recipient: string | DirectusUsers
  sender?: string | DirectusUsers | null
  status?: string | null
  subject: string
  timestamp?: string | null
}

export type DirectusOperations = {
  date_created?: string | null
  flow: string | DirectusFlows
  id: string
  key: string
  name?: string | null
  options?: unknown | null
  position_x: number
  position_y: number
  reject?: string | DirectusOperations | null
  resolve?: string | DirectusOperations | null
  type: string
  user_created?: string | DirectusUsers | null
}

export type DirectusPanels = {
  color?: string | null
  dashboard: string | DirectusDashboards
  date_created?: string | null
  height: number
  icon?: string | null
  id: string
  name?: string | null
  note?: string | null
  options?: unknown | null
  position_x: number
  position_y: number
  show_header: boolean
  type: string
  user_created?: string | DirectusUsers | null
  width: number
}

export type DirectusPermissions = {
  action: string
  collection: string
  fields?: unknown | null
  id: number
  permissions?: unknown | null
  policy: string | DirectusPolicies
  presets?: unknown | null
  validation?: unknown | null
}

export type DirectusPolicies = {
  admin_access: boolean
  app_access: boolean
  description?: string | null
  enforce_tfa: boolean
  icon: string
  id: string
  ip_access?: unknown | null
  name: string
  permissions: any[] | DirectusPermissions[]
  roles: any[] | DirectusAccess[]
  users: any[] | DirectusAccess[]
}

export type DirectusPresets = {
  bookmark?: string | null
  collection?: string | null
  color?: string | null
  filter?: unknown | null
  icon?: string | null
  id: number
  layout?: string | null
  layout_options?: unknown | null
  layout_query?: unknown | null
  refresh_interval?: number | null
  role?: string | DirectusRoles | null
  search?: string | null
  user?: string | DirectusUsers | null
}

export type DirectusRelations = {
  id: number
  junction_field?: string | null
  many_collection: string
  many_field: string
  one_allowed_collections?: unknown | null
  one_collection?: string | null
  one_collection_field?: string | null
  one_deselect_action: string
  one_field?: string | null
  sort_field?: string | null
}

export type DirectusRevisions = {
  activity: number | DirectusActivity
  collection: string
  data?: unknown | null
  delta?: unknown | null
  id: number
  item: string
  parent?: number | DirectusRevisions | null
  version?: string | DirectusVersions | null
}

export type DirectusRoles = {
  children: any[] | DirectusRoles[]
  description?: string | null
  icon: string
  id: string
  name: string
  parent?: string | DirectusRoles | null
  policies: any[] | DirectusAccess[]
  users: any[] | DirectusUsers[]
  users_group: string
}

export type DirectusSessions = {
  expires: string
  ip?: string | null
  next_token?: string | null
  origin?: string | null
  share?: string | DirectusShares | null
  token: string
  user?: string | DirectusUsers | null
  user_agent?: string | null
}

export type DirectusSettings = {
  auth_login_attempts?: number | null
  auth_password_policy?: string | null
  basemaps?: unknown | null
  custom_aspect_ratios?: unknown | null
  custom_css?: string | null
  default_appearance: string
  default_language: string
  default_theme_dark?: string | null
  default_theme_light?: string | null
  id: number
  mapbox_key?: string | null
  module_bar?: unknown | null
  project_color: string
  project_descriptor?: string | null
  project_logo?: string | DirectusFiles | null
  project_name: string
  project_url?: string | null
  public_background?: string | DirectusFiles | null
  public_favicon?: string | DirectusFiles | null
  public_foreground?: string | DirectusFiles | null
  public_note?: string | null
  public_registration: boolean
  public_registration_email_filter?: unknown | null
  public_registration_role?: string | DirectusRoles | null
  public_registration_verify_email: boolean
  report_bug_url?: string | null
  report_error_url?: string | null
  report_feature_url?: string | null
  storage_asset_presets?: unknown | null
  storage_asset_transform?: string | null
  storage_default_folder?: string | DirectusFolders | null
  theme_dark_overrides?: unknown | null
  theme_light_overrides?: unknown | null
  theming_group: string
  visual_editor_urls?: unknown | null
}

export type DirectusShares = {
  collection: string | DirectusCollections
  date_created?: string | null
  date_end?: string | null
  date_start?: string | null
  id: string
  item: string
  max_uses?: number | null
  name?: string | null
  password?: string | null
  role?: string | DirectusRoles | null
  times_used?: number | null
  user_created?: string | DirectusUsers | null
}

export type DirectusTranslations = {
  id: string
  key: string
  language: string
  value: string
}

export type DirectusUsers = {
  appearance?: string | null
  auth_data?: unknown | null
  avatar?: string | DirectusFiles | null
  description?: string | null
  email?: string | null
  email_notifications?: boolean | null
  external_identifier?: string | null
  first_name?: string | null
  id: string
  language?: string | null
  last_access?: string | null
  last_name?: string | null
  last_page?: string | null
  location?: string | null
  password?: string | null
  policies: any[] | DirectusAccess[]
  provider: string
  role?: string | DirectusRoles | null
  status: string
  tags?: unknown | null
  tfa_secret?: string | null
  theme_dark?: string | null
  theme_dark_overrides?: unknown | null
  theme_light?: string | null
  theme_light_overrides?: unknown | null
  title?: string | null
  token?: string | null
}

export type DirectusVersions = {
  collection: string | DirectusCollections
  date_created?: string | null
  date_updated?: string | null
  delta?: unknown | null
  hash?: string | null
  id: string
  item: string
  key: string
  name?: string | null
  user_created?: string | DirectusUsers | null
  user_updated?: string | DirectusUsers | null
}

export type DirectusWebhooks = {
  actions: unknown
  collections: unknown
  data: boolean
  headers?: unknown | null
  id: number
  method: string
  migrated_flow?: string | DirectusFlows | null
  name: string
  status: string
  url: string
  was_active_before_deprecation: boolean
}

export type TArea = {
  create_at?: string | null
  event_id?: number | null
  height?: number | null
  id: number
  name?: string | null
  update_at?: string | null
  user_id?: number | null
  width?: number | null
  x_axis?: number | null
  y_axis?: number | null
}

export type TBulletin = {
  brief?: string | null
  create_at?: string | null
  event_id?: number | null
  id: number
  page_url?: string | null
  publish_at?: string | null
  published?: boolean | null
  title?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TEvent = {
  create_at?: string | null
  description?: string | null
  end_date?: string | null
  id: number
  image_url?: string | null
  name?: string | null
  start_date?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TEventMap = {
  create_at?: string | null
  description?: string | null
  event_id?: number | null
  id: number
  map_height?: number | null
  map_url?: string | null
  map_width?: number | null
  name?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TExhibitor = {
  create_at?: string | null
  description?: string | null
  id: number
  mark_id?: number | null
  name?: string | null
  thumbnail_url?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TGame = {
  area_id?: number | null
  create_at?: string | null
  description?: string | null
  event_id?: number | null
  game_level?: number | null
  game_url?: string | null
  game_available_id?: number | null
  id: number
  mark_id?: number | null
  name?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TGameAvailable = {
  create_at?: string | null
  description?: string | null
  game_url?: string | null
  id: number
  name?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TGameRecord = {
  create_at?: string | null
  finished_at?: string | null
  game_id?: number | null
  game_level?: number | null
  game_name?: string | null
  id: number
  is_public?: number | null
  mark_id?: number | null
  score?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type THotel = {
  create_at?: string | null
  description?: string | null
  id: number
  mark_id?: number | null
  name?: string | null
  thumbnail_url?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TMark = {
  area_id?: number | null
  booth_number?: string | null
  create_at?: string | null
  event_id?: number | null
  id: number
  mark_score?: number | null
  mark_type?: number | null
  name?: string | null
  update_at?: string | null
  user_id?: number | null
  x_axis?: number | null
  y_axis?: number | null
}

export type TMarkStatus = {
  collected?: boolean | null
  create_at?: string | null
  got_score?: boolean | null
  id: number
  mark_id?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TPersonalinfo = {
  address?: string | null
  area?: string | null
  city?: string | null
  country?: string | null
  create_at?: string | null
  id: number
  identify_status?: number | null
  name?: string | null
  phone?: string | null
  postcode?: string | null
  province?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TRestaurant = {
  create_at?: string | null
  description?: string | null
  id: number
  mark_id?: number | null
  name?: string | null
  thumbnail_url?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TReward = {
  bought?: number | null
  create_at?: string | null
  description?: string | null
  detail_description?: string | null
  event_id?: number | null
  id: number
  image_url?: string | null
  inventory?: number | null
  name?: string | null
  point_price?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TRewardImage = {
  create_at?: string | null
  id: number
  image_url?: string | null
  reward_id?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TScoreRecord = {
  add_or_subtract?: number | null
  create_at?: string | null
  id: number
  score?: string | null
  score_source?: number | null
  source_id?: number | null
  source_name?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TSendVerifyCodeRecord = {
  create_at?: string | null
  error_info?: string | null
  id: number
  phone_num?: string | null
  send_info?: string | null
  send_type?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TShop = {
  create_at?: string | null
  description?: string | null
  id: number
  mark_id?: number | null
  name?: string | null
  thumbnail_url?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TShow = {
  begin_time?: string | null
  create_at?: string | null
  description?: string | null
  end_time?: string | null
  id: number
  mark_id?: string | null
  name?: string | null
  thumbnail_url?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TSystemSettings = {
  create_at?: string | null
  id: number
  push_notification_accept?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TTicket = {
  bound_at?: string | null
  checked_at?: string | null
  code?: string | null
  create_at?: string | null
  event_id?: number | null
  id: number
  name?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type TUploadFile = {
  content_type?: string | null
  create_at?: string | null
  file_key?: string | null
  file_name?: string | null
  file_size?: number | null
  id: number
  media_type?: number | null
  signed_url?: string | null
  update_at?: string | null
  upload_state?: number | null
  user_id?: number | null
}

export type TUser = {
  address_list_id?: number | null
  admin_level_id?: number | null
  auth_token?: string | null
  birthday?: string | null
  blacklist_id?: number | null
  coupon_collector_id?: number | null
  create_at?: string | null
  gender?: number | null
  id: number
  interest_list_id?: number | null
  location?: string | null
  nickname?: string | null
  permission_id?: number | null
  personal_info_id?: number | null
  point?: number | null
  shop_id?: number | null
  signature?: string | null
  status?: number | null
  system_setting_id?: number | null
  update_at?: string | null
}

export type TUserDevice = {
  create_at?: string | null
  device_id?: string | null
  device_type?: number | null
  id: number
  update_at?: string | null
  user_id?: number | null
}

export type TUserLogin = {
  create_at?: string | null
  email?: string | null
  id: number
  is_third_party?: number | null
  nickname?: string | null
  phone?: string | null
  third_party_id?: string | null
  third_party_type?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TUserProfileImg = {
  create_at?: string | null
  file_name?: string | null
  id: number
  profile_img?: string | null
  status?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TUserReward = {
  create_at?: string | null
  exchanged_at?: string | null
  id: number
  reward_id?: number | null
  update_at?: string | null
  user_id?: number | null
}

export type TUserScore = {
  create_at?: string | null
  id: number
  ranking_score: number
  total_score: number
  update_at?: string | null
  user_id?: number | null
}

export type TOther = {
  create_at?: string | null
  description?: string | null
  id: number
  mark_id?: number | null
  name?: string | null
  thumbnail_url?: string | null
  update_at?: string | null
  user_id?: number | null
}

export type CustomDirectusTypes = {
  directus_access: DirectusAccess[]
  directus_activity: DirectusActivity[]
  directus_collections: DirectusCollections[]
  directus_comments: DirectusComments[]
  directus_dashboards: DirectusDashboards[]
  directus_extensions: DirectusExtensions[]
  directus_fields: DirectusFields[]
  directus_files: DirectusFiles[]
  directus_flows: DirectusFlows[]
  directus_folders: DirectusFolders[]
  directus_migrations: DirectusMigrations[]
  directus_notifications: DirectusNotifications[]
  directus_operations: DirectusOperations[]
  directus_panels: DirectusPanels[]
  directus_permissions: DirectusPermissions[]
  directus_policies: DirectusPolicies[]
  directus_presets: DirectusPresets[]
  directus_relations: DirectusRelations[]
  directus_revisions: DirectusRevisions[]
  directus_roles: DirectusRoles[]
  directus_sessions: DirectusSessions[]
  directus_settings: DirectusSettings
  directus_shares: DirectusShares[]
  directus_translations: DirectusTranslations[]
  directus_users: DirectusUsers[]
  directus_versions: DirectusVersions[]
  directus_webhooks: DirectusWebhooks[]
  t_area: TArea[]
  t_bulletin: TBulletin[]
  t_event: TEvent[]
  t_event_map: TEventMap[]
  t_exhibitor: TExhibitor[]
  t_game: TGame[]
  t_game_record: TGameRecord[]
  t_hotel: THotel[]
  t_mark: TMark[]
  t_mark_status: TMarkStatus[]
  t_personalinfo: TPersonalinfo[]
  t_restaurant: TRestaurant[]
  t_reward: TReward[]
  t_reward_image: TRewardImage[]
  t_score_record: TScoreRecord[]
  t_send_verify_code_record: TSendVerifyCodeRecord[]
  t_shop: TShop[]
  t_show: TShow[]
  t_system_settings: TSystemSettings[]
  t_ticket: TTicket[]
  t_upload_file: TUploadFile[]
  t_user: TUser[]
  t_user_device: TUserDevice[]
  t_user_login: TUserLogin[]
  t_user_profile_img: TUserProfileImg[]
  t_user_reward: TUserReward[]
  t_user_score: TUserScore[]
  t_other: TOther[]
}
