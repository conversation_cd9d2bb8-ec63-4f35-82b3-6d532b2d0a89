export interface VenueConfig {
  collection: string
  title: string
  singularTitle: string
  icon: string
  fields: VenueField[]
}

export interface VenueField {
  key: string
  label: string
  type:
    | 'text'
    | 'textarea'
    | 'url'
    | 'number'
    | 'datetime'
    | 'select'
    | 'boolean'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string, value: any }>
}

export const venueConfigs: Record<string, VenueConfig> = {
  hotels: {
    collection: 't_hotel',
    title: 'Hotels',
    singularTitle: 'Hotel',
    icon: 'i-lucide-bed',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        placeholder: 'Enter hotel name'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Enter hotel description'
      },
      {
        key: 'thumbnail_url',
        label: 'Thumbnail URL',
        type: 'url',
        placeholder: 'https://example.com/image.jpg'
      },
      {
        key: 'mark_id',
        label: 'Map Marker ID',
        type: 'number',
        placeholder: 'Enter marker ID'
      }
    ]
  },
  restaurants: {
    collection: 't_restaurant',
    title: 'Restaurants',
    singularTitle: 'Restaurant',
    icon: 'i-lucide-utensils',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        placeholder: 'Enter restaurant name'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Enter restaurant description'
      },
      {
        key: 'thumbnail_url',
        label: 'Thumbnail URL',
        type: 'url',
        placeholder: 'https://example.com/image.jpg'
      },
      {
        key: 'mark_id',
        label: 'Map Marker ID',
        type: 'number',
        placeholder: 'Enter marker ID'
      }
    ]
  },
  shops: {
    collection: 't_shop',
    title: 'Shops',
    singularTitle: 'Shop',
    icon: 'i-lucide-shopping-bag',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        placeholder: 'Enter shop name'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Enter shop description'
      },
      {
        key: 'thumbnail_url',
        label: 'Thumbnail URL',
        type: 'url',
        placeholder: 'https://example.com/image.jpg'
      },
      {
        key: 'mark_id',
        label: 'Map Marker ID',
        type: 'number',
        placeholder: 'Enter marker ID'
      }
    ]
  },
  others: {
    collection: 't_other',
    title: 'Others',
    singularTitle: 'Other',
    icon: 'i-lucide-camera',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        placeholder: 'Enter other name'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Enter other description'
      },
      {
        key: 'thumbnail_url',
        label: 'Thumbnail URL',
        type: 'url',
        placeholder: 'https://example.com/image.jpg'
      },
      {
        key: 'mark_id',
        label: 'Map Marker ID',
        type: 'number',
        placeholder: 'Enter marker ID'
      }
    ]
  }
}

export const specialConfigs: Record<string, VenueConfig> = {
  shows: {
    collection: 't_show',
    title: 'Shows',
    singularTitle: 'Show',
    icon: 'i-lucide-play-circle',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        placeholder: 'Enter show name'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Enter show description'
      },
      {
        key: 'thumbnail_url',
        label: 'Thumbnail URL',
        type: 'url',
        placeholder: 'https://example.com/image.jpg'
      },
      {
        key: 'begin_time',
        label: 'Start Time',
        type: 'datetime',
        required: true
      },
      { key: 'end_time', label: 'End Time', type: 'datetime', required: true },
      {
        key: 'mark_id',
        label: 'Map Marker ID',
        type: 'text',
        placeholder: 'Enter marker ID'
      }
    ]
  },
  games: {
    collection: 't_game',
    title: 'Games',
    singularTitle: 'Game',
    icon: 'i-lucide-gamepad-2',
    fields: [
      {
        key: 'name',
        label: 'Name',
        type: 'text',
        required: true,
        placeholder: 'Enter game name'
      },
      {
        key: 'description',
        label: 'Description',
        type: 'textarea',
        placeholder: 'Enter game description'
      },
      {
        key: 'game_url',
        label: 'Game URL',
        type: 'url',
        placeholder: 'https://example.com/game'
      },
      {
        key: 'game_level',
        label: 'Difficulty Level',
        type: 'select',
        options: [
          { label: 'Easy', value: 1 },
          { label: 'Medium', value: 2 },
          { label: 'Hard', value: 3 }
        ]
      },
      {
        key: 'event_id',
        label: 'Event ID',
        type: 'number',
        placeholder: 'Enter event ID'
      },
      {
        key: 'area_id',
        label: 'Area ID',
        type: 'number',
        placeholder: 'Enter area ID'
      },
      {
        key: 'mark_id',
        label: 'Map Marker ID',
        type: 'number',
        placeholder: 'Enter marker ID'
      }
    ]
  },
  bulletins: {
    collection: 't_bulletin',
    title: 'Bulletins',
    singularTitle: 'Bulletin',
    icon: 'i-lucide-megaphone',
    fields: [
      {
        key: 'title',
        label: 'Title',
        type: 'text',
        required: true,
        placeholder: 'Enter bulletin title'
      },
      {
        key: 'brief',
        label: 'Brief',
        type: 'textarea',
        placeholder: 'Enter brief description'
      },
      {
        key: 'page_url',
        label: 'Page URL',
        type: 'url',
        placeholder: 'https://example.com/page'
      },
      { key: 'publish_at', label: 'Publish Date', type: 'datetime' },
      {
        key: 'published',
        label: 'Published',
        type: 'boolean'
      },
      {
        key: 'event_id',
        label: 'Event ID',
        type: 'number',
        placeholder: 'Enter event ID'
      }
    ]
  }
}

export function getVenueConfig(type: string): VenueConfig | null {
  return venueConfigs[type] || specialConfigs[type] || null
}

export function formatVenueDate(dateString: string | null): string {
  if (!dateString) return 'Not set'
  return new Date(dateString).toLocaleDateString()
}

export function formatVenueDateTime(dateString: string | null): string {
  if (!dateString) return 'Not set'
  return new Date(dateString).toLocaleString()
}
