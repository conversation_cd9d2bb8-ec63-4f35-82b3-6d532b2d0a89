// Coordinate scaling helpers shared across map editors
// Accept pre-computed scale factors so the caller is responsible for providing them.
export interface ScaleResult {
  x: number
  y: number
  width?: number
  height?: number
}

/**
 * Convert database coordinates → canvas coordinates.
 */
export function scaleToCanvas(
  x: number,
  y: number,
  scaleX: number,
  scaleY: number,
  width?: number,
  height?: number
): ScaleResult {
  return {
    x: x * scaleX,
    y: y * scaleY,
    width: width !== undefined ? width * scaleX : undefined,
    height: height !== undefined ? height * scaleY : undefined
  }
}

/**
 * Convert canvas coordinates → database coordinates.
 */
export function scaleFromCanvas(
  x: number,
  y: number,
  scaleX: number,
  scaleY: number,
  width?: number,
  height?: number
): ScaleResult {
  return {
    x: x / scaleX,
    y: y / scaleY,
    width: width !== undefined ? width / scaleX : undefined,
    height: height !== undefined ? height / scaleY : undefined
  }
}
