export function formatDate(dateString: string | null | undefined) {
  if (!dateString) return 'Not set'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function getEventStatus(event: TEvent) {
  if (!event.start_date || !event.end_date) return 'draft'

  const now = new Date()
  const startDate = new Date(event.start_date)
  const endDate = new Date(event.end_date)

  if (now < startDate) return 'upcoming'
  if (now >= startDate && now <= endDate) return 'active'
  return 'completed'
}

export function getStatusColor(status: string) {
  switch (status) {
    case 'active':
      return 'success'
    case 'upcoming':
      return 'primary'
    case 'completed':
      return 'neutral'
    case 'draft':
      return 'warning'
    default:
      return 'neutral'
  }
}
