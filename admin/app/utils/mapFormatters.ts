import { scaleToCanvas } from '@/utils/coordinate'

// These helpers format Directus data rows into Konva-friendly config objects.
export function formatAreas(
  areaArr: TArea[],
  scaleX: number,
  scaleY: number
) {
  return areaArr.map((area) => {
    const scaled = scaleToCanvas(
      area.x_axis || 0,
      area.y_axis || 0,
      scaleX,
      scaleY,
      area.width || 0,
      area.height || 0
    )

    return {
      visible: true,
      rotation: 0,
      x: scaled.x,
      y: scaled.y,
      width: scaled.width || 0,
      height: scaled.height || 0,
      scaleX: 1,
      scaleY: 1,
      dash: [10, 5],
      stroke: 'red',
      fill: 'rgba(255, 0, 0, 0.1)',
      name: `area-${area.id}`,
      draggable: true,
      text: area.name,
      data: area
    }
  })
}

export function formatMarkers(
  markerArr: TMark[],
  scaleX: number,
  scaleY: number
) {
  return markerArr.map((marker) => {
    const scaled = scaleToCanvas(
      marker.x_axis || 0,
      marker.y_axis || 0,
      scaleX,
      scaleY
    )

    return {
      x: scaled.x,
      y: scaled.y,
      radius: 10,
      fill:
        marker.mark_type === 1
          ? '#8b5cf6' // Game - purple
          : marker.mark_type === 2
            ? '#f59e0b' // Exhibitor - warning/amber
            : marker.mark_type === 3
              ? '#ec4899' // Show - pink
              : marker.mark_type === 4
                ? '#ef4444' // Restaurant - red
                : marker.mark_type === 5
                  ? '#10b981' // Shop - emerald
                  : marker.mark_type === 6
                    ? '#3b82f6' // Hotel - blue
                    : marker.mark_type === 7
                      ? '#84cc16' // Other - lime
                      : '#6b7280', // Unknown - gray
      scaleX: 1,
      scaleY: 1,
      stroke: 'black',
      strokeWidth: 2,
      name: `marker-${marker.id}`,
      draggable: true,
      text: marker.name,
      data: marker
    }
  })
}
