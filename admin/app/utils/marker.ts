export const markerTypeNameMap = {
  1: 'Game',
  2: 'Exhibitor',
  3: 'Show',
  4: 'Restaurant',
  5: 'Shop',
  6: 'Hotel',
  7: 'Other'
} as const

export const markerTypeTableMap = {
  1: 't_game',
  2: 't_exhibitor',
  3: 't_show',
  4: 't_restaurant',
  5: 't_shop',
  6: 't_hotel',
  7: 't_other'
} as const

export const markerTypeOptions = Object.entries(markerTypeNameMap).map(
  ([value, label]) => ({
    value: parseInt(value),
    label
  })
)

export const getMarkerTypeName = (type: number) => {
  return markerTypeNameMap[type as keyof typeof markerTypeNameMap] || 'Unknown'
}
