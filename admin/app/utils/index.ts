export function randomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export function randomFrom<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]!
}

export function xorDecrypt(encryptedText: string, key?: string): string {
  // Use a fixed key if none provided
  const encryptionKey = key ?? '09r4t3pre89n'

  try {
    // First decode from Base64, then URI decode
    const decodedText = decodeURIComponent(atob(encryptedText))
    let result = ''

    // Apply XOR decryption (same as encryption since XOR is symmetric)
    for (let i = 0; i < decodedText.length; i++) {
      const charCode = decodedText.charCodeAt(i) ^ encryptionKey.charCodeAt(i % encryptionKey.length)
      result += String.fromCharCode(charCode)
    }

    return result
  } catch (e) {
    console.error('Decryption failed:', e)
    return ''
  }
}
