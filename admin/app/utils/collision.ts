import type { ScaleResult } from './coordinate'

export interface RectShape extends ScaleResult {
  /** Unique identifier; e.g. `area-12` */
  name: string
  data?: { id?: number }
}

/**
 * Given current rendered area rectangles, return the area id containing the point.
 */
export function getContainingAreaId(
  areas: RectShape[],
  canvasX: number,
  canvasY: number
): number | null {
  const area = areas.find((a) => {
    return (
      canvasX >= a.x
      && canvasX <= a.x + (a.width || 0)
      && canvasY >= a.y
      && canvasY <= a.y + (a.height || 0)
    )
  })
  return area ? (area.data?.id as number) : null
}
