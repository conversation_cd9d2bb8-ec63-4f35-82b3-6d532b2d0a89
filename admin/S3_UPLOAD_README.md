# S3 Upload Implementation

This document describes the AWS S3 direct upload functionality implemented in the admin platform.

## Overview

The S3 upload system allows direct file uploads to AWS S3 buckets without burdening the Directus instance. Files are uploaded directly from the client to S3 and return public URLs for immediate use.

## Features

- ✅ Direct upload to AWS S3
- ✅ File type validation (images only)
- ✅ File size validation (max 10MB)
- ✅ Unique file naming with timestamps
- ✅ Public URL generation
- ✅ Progress tracking
- ✅ Error handling
- ✅ TypeScript support

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```env
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key
AWS_REGION=ap-northeast-1
AWS_S3_BUCKET_NAME=open-portal-expo-prod
AWS_S3_PUBLIC_URL=https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com
```

### 2. AWS S3 Bucket Configuration

Ensure your S3 bucket has:
- Public read access for uploaded files
- CORS configuration to allow uploads from your domain
- Proper IAM permissions for the access keys

Example CORS configuration:
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```

## Usage

### API Endpoint

**POST** `/api/s3/public-upload`

- **Content-Type**: `multipart/form-data`
- **Body**: Form data with `file` field containing the image file

**Response**:
```typescript
interface S3UploadResponse {
  success: boolean
  url?: string      // Public URL of uploaded file
  key?: string      // S3 object key
  error?: string    // Error message if failed
}
```

### Using the Composable

```vue
<script setup>
import { useS3Upload } from '~/composables/useS3Upload'

const { uploading, uploadProgress, error, uploadFile } = useS3Upload()

async function handleUpload(file: File) {
  try {
    const result = await uploadFile(file)
    console.log('Upload successful:', result.url)
  } catch (err) {
    console.error('Upload failed:', err.message)
  }
}
</script>
```

### Direct API Usage

```typescript
async function uploadToS3(file: File) {
  const formData = new FormData()
  formData.append('file', file)
  
  const response = await $fetch<S3UploadResponse>('/api/s3/public-upload', {
    method: 'POST',
    body: formData
  })
  
  return response
}
```

## File Validation

### Allowed File Types
- `image/jpeg`
- `image/jpg`
- `image/png`
- `image/gif`
- `image/webp`
- `image/svg+xml`

### File Size Limit
- Maximum: 10MB per file

## File Naming Convention

Uploaded files are automatically renamed using the pattern:
```
uploads/{timestamp}-{nanoid}.{extension}
```

Example: `uploads/1703123456789-V1StGXR8_Z.jpg`

## Error Handling

The system handles various error scenarios:

- **File validation errors**: Invalid file type or size
- **AWS configuration errors**: Missing credentials or bucket
- **AWS service errors**: Bucket not found, permission denied
- **Network errors**: Upload timeout or connection issues

## Testing

Visit `/test-upload` in your admin interface to test the upload functionality with a simple UI.

## Security Considerations

1. **Server-side validation**: All files are validated on the server before upload
2. **Private credentials**: AWS credentials are only available server-side
3. **Public URLs**: Uploaded files are publicly accessible via the returned URLs
4. **File type restrictions**: Only image files are allowed by default

## Customization

### Adding New File Types

Edit `admin/server/api/s3/public-upload.post.ts`:

```typescript
const ALLOWED_FILE_TYPES = {
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'application/pdf': 'pdf', // Add new types here
  // ...
}
```

### Changing File Size Limit

```typescript
const MAX_FILE_SIZE = 20 * 1024 * 1024 // 20MB
```

### Custom File Naming

Modify the file key generation logic:

```typescript
const fileKey = `custom-folder/${Date.now()}-${nanoid()}.${fileExtension}`
```

## Dependencies

- `@aws-sdk/client-s3`: AWS S3 client
- `@aws-sdk/lib-storage`: Multipart upload support
- `nanoid`: Unique ID generation

## Troubleshooting

### Common Issues

1. **"AWS credentials not configured"**
   - Check environment variables are set correctly
   - Ensure `.env` file is loaded

2. **"S3 bucket not found"**
   - Verify bucket name in environment variables
   - Check bucket exists in the specified region

3. **"Invalid AWS credentials"**
   - Verify access key and secret key
   - Check IAM permissions for S3 operations

4. **CORS errors**
   - Configure S3 bucket CORS policy
   - Allow your domain in AllowedOrigins
