import { S3Client } from '@aws-sdk/client-s3'
import { Upload } from '@aws-sdk/lib-storage'
import { nanoid } from 'nanoid'

interface UploadResponse {
  success: boolean
  url?: string
  key?: string
  error?: string
}

// Allowed file types and their MIME types
const ALLOWED_FILE_TYPES = {
  'image/jpeg': 'jpg',
  'image/jpg': 'jpg',
  'image/png': 'png',
  'image/gif': 'gif',
  'image/webp': 'webp',
  'image/svg+xml': 'svg'
}

// Maximum file size: 10MB
const MAX_FILE_SIZE = 10 * 1024 * 1024

export default defineEventHandler(async (event): Promise<UploadResponse> => {
  try {
    const config = useRuntimeConfig()
    console.log('config:', config)

    // Validate AWS configuration
    if (!config.awsAccessKeyId || !config.awsSecretAccessKey) {
      throw createError({
        statusCode: 500,
        statusMessage: 'AWS credentials not configured'
      })
    }

    // Parse multipart form data
    const form = await readMultipartFormData(event)

    if (!form || form.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No file provided'
      })
    }

    // Find the file in the form data
    const fileData = form.find(item => item.name === 'file')

    if (!fileData || !fileData.data || !fileData.filename) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid file data'
      })
    }

    // Validate file type
    const mimeType = fileData.type || 'application/octet-stream'
    if (!ALLOWED_FILE_TYPES[mimeType as keyof typeof ALLOWED_FILE_TYPES]) {
      throw createError({
        statusCode: 400,
        statusMessage: `File type ${mimeType} not allowed. Allowed types: ${Object.keys(ALLOWED_FILE_TYPES).join(', ')}`
      })
    }

    // Validate file size
    if (fileData.data.length > MAX_FILE_SIZE) {
      throw createError({
        statusCode: 400,
        statusMessage: `File size too large. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB`
      })
    }

    // Generate unique file key
    const fileExtension = ALLOWED_FILE_TYPES[mimeType as keyof typeof ALLOWED_FILE_TYPES]
    const uniqueId = nanoid(10)
    const timestamp = Date.now()
    const fileKey = `uploads/${timestamp}-${uniqueId}.${fileExtension}`

    // Initialize S3 client
    const s3Client = new S3Client({
      region: config.awsRegion,
      credentials: {
        accessKeyId: config.awsAccessKeyId,
        secretAccessKey: config.awsSecretAccessKey
      }
    })

    // Upload to S3 using multipart upload for better reliability
    const upload = new Upload({
      client: s3Client,
      params: {
        Bucket: config.awsS3BucketName,
        Key: fileKey,
        Body: fileData.data,
        ContentType: mimeType,
        // ACL: 'public-read', // Make the file publicly accessible
        CacheControl: 'max-age=31536000', // Cache for 1 year
        Metadata: {
          originalName: fileData.filename,
          uploadedAt: new Date().toISOString()
        }
      }
    })

    // Execute the upload
    await upload.done()

    // Construct the public URL
    const publicUrl = `${config.awsS3PublicUrl}/${fileKey}`

    return {
      success: true,
      url: publicUrl,
      key: fileKey
    }
  } catch (error: any) {
    console.error('S3 upload error:', error)

    // Handle different types of errors
    if (error.statusCode) {
      // This is already a createError, re-throw it
      throw error
    }

    // Handle AWS SDK errors
    if (error.name === 'NoSuchBucket') {
      throw createError({
        statusCode: 500,
        statusMessage: 'S3 bucket not found'
      })
    }

    if (error.name === 'InvalidAccessKeyId' || error.name === 'SignatureDoesNotMatch') {
      throw createError({
        statusCode: 500,
        statusMessage: 'Invalid AWS credentials'
      })
    }

    // Generic error
    throw createError({
      statusCode: 500,
      statusMessage: 'Upload failed'
    })
  }
})
