# Rankings Feature Implementation

## Overview

The Rankings feature displays user leaderboards in the admin interface, showing the top-performing users based on their cumulative ranking scores.

## Implementation Details

### Backend API

The rankings data is fetched from the backend service (port 8082) using the public endpoint:

```
GET /rank/v1/showTopN?n={number}
```

**Features:**
- ✅ Public endpoint (no authentication required)
- ✅ Configurable number of top users to display
- ✅ Returns user rankings with nicknames and scores
- ✅ Uses MySQL-based ranking system (migrated from Redis)

**Response Format:**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "rankResults": [
      {
        "rank": 1,
        "score": 500,
        "nickName": "Ben"
      }
    ]
  },
  "success": true
}
```

### Frontend Implementation

**Location:** `/admin/app/pages/users/rankings.vue`

**Key Features:**
- 🏆 Medal icons for top 3 positions (🥇🥈🥉)
- 📊 Configurable display options (Top 10, 25, 50, 100)
- 🔄 Manual refresh functionality
- ⏰ Last updated timestamp
- 🚨 Error handling with user-friendly messages
- 📱 Responsive table design

**Composable:** `/admin/app/composables/useBackendApi.ts`

This composable handles:
- Backend API configuration
- Type definitions for ranking data
- Error handling and response parsing
- Environment-based URL configuration

### Configuration

The backend API URL can be configured via environment variable:

```bash
NUXT_PUBLIC_BACKEND_API_URL=http://localhost:8082
```

Default: `http://localhost:8082`

## Usage

1. Navigate to **Users > Rankings** in the admin interface
2. Select the number of top users to display (10, 25, 50, or 100)
3. Click **Refresh** to update the rankings
4. View user rankings with medals for top 3 positions

## Technical Notes

- Rankings are calculated in real-time from the `t_user_score` table
- Uses `ranking_score` field (cumulative points) for ranking calculations
- No caching - data is fetched fresh on each request
- Handles empty states and error conditions gracefully

## Future Enhancements

Potential improvements could include:
- Auto-refresh functionality
- Filtering by date ranges
- Export functionality
- User profile links from rankings table
- Real-time updates via WebSocket
