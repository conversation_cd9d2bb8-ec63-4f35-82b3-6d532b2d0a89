#!/bin/bash

# Open Portal Expo Setup Script
# This script sets up all components of the OPE system

set -e

echo "🚀 Open Portal Expo Setup Script"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js not found. Please install Node.js 18+"
        exit 1
    fi
    
    # Check Yarn
    if command -v yarn &> /dev/null; then
        YARN_VERSION=$(yarn --version)
        print_success "Yarn found: $YARN_VERSION"
    else
        print_error "Yarn not found. Please install Yarn"
        exit 1
    fi
    
    # Check Java
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1)
        print_success "Java found: $JAVA_VERSION"
    else
        print_error "Java not found. Please install Java 11+"
        exit 1
    fi
    
    # Check Maven
    if command -v mvn &> /dev/null; then
        MVN_VERSION=$(mvn --version | head -n 1)
        print_success "Maven found: $MVN_VERSION"
    else
        print_error "Maven not found. Please install Maven"
        exit 1
    fi
    
    # Check CocoaPods (for iOS)
    if command -v pod &> /dev/null; then
        POD_VERSION=$(pod --version)
        print_success "CocoaPods found: $POD_VERSION"
    else
        print_warning "CocoaPods not found. iOS build may not work"
    fi
}

# Setup mobile app
setup_mobile_app() {
    print_status "Setting up mobile app..."
    
    cd ope_client
    
    print_status "Installing Node.js dependencies..."
    yarn install
    
    if [ -d "ios" ] && command -v pod &> /dev/null; then
        print_status "Installing iOS dependencies..."
        cd ios
        pod install
        cd ..
    fi
    
    print_success "Mobile app setup complete!"
    cd ..
}

# Setup backend services
setup_backend() {
    print_status "Setting up backend services..."
    
    # Build discover backend
    print_status "Building discover backend..."
    cd backend/massive-discover-backend-develop
    mvn clean compile -DskipTests
    print_success "Discover backend compiled successfully!"
    cd ../..
    
    # Build account service common module
    print_status "Building account service common module..."
    cd backend/massive-account-service-master
    mvn clean install -DskipTests -pl massive-account-common
    print_success "Account service common module installed!"
    cd ../..
}

# Create run scripts
create_run_scripts() {
    print_status "Creating run scripts..."
    
    # Mobile app run script
    cat > run-mobile.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Mobile App"
cd ope_client

echo "Choose platform:"
echo "1) iOS Simulator"
echo "2) Android Emulator"
echo "3) Metro bundler only"
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        echo "Starting iOS..."
        yarn ios
        ;;
    2)
        echo "Starting Android..."
        yarn android
        ;;
    3)
        echo "Starting Metro bundler..."
        yarn start
        ;;
    *)
        echo "Invalid choice. Starting Metro bundler..."
        yarn start
        ;;
esac
EOF

    # Backend run script
    cat > run-backend.sh << 'EOF'
#!/bin/bash
echo "🚀 Starting Backend Services"

echo "Choose service to run:"
echo "1) Discover Backend (Event management, maps, tickets)"
echo "2) Account Service Web (Authentication - Limited functionality)"
echo "3) Both services"
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        echo "Starting Discover Backend..."
        cd backend/massive-discover-backend-develop
        mvn spring-boot:run -pl discover-backend-web
        ;;
    2)
        echo "Starting Account Service..."
        echo "⚠️  WARNING: Account service has limited functionality due to missing user module"
        cd backend/massive-account-service-master
        mvn spring-boot:run -pl massive-account-web
        ;;
    3)
        echo "Starting both services..."
        echo "⚠️  WARNING: Account service has limited functionality"
        echo "Starting Discover Backend in background..."
        cd backend/massive-discover-backend-develop
        mvn spring-boot:run -pl discover-backend-web &
        DISCOVER_PID=$!
        cd ../../backend/massive-account-service-master
        echo "Starting Account Service..."
        mvn spring-boot:run -pl massive-account-web &
        ACCOUNT_PID=$!
        echo "Both services started. Press Ctrl+C to stop."
        wait $DISCOVER_PID $ACCOUNT_PID
        ;;
    *)
        echo "Invalid choice"
        exit 1
        ;;
esac
EOF

    chmod +x run-mobile.sh run-backend.sh
    print_success "Run scripts created!"
}

# Main setup function
main() {
    check_prerequisites
    setup_mobile_app
    setup_backend
    create_run_scripts
    
    print_success "🎉 Setup complete!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Run mobile app: ./run-mobile.sh"
    echo "2. Run backend: ./run-backend.sh"
    echo ""
    echo "📱 Mobile App:"
    echo "   - API Host: https://api-dev.massiveworld.link/"
    echo "   - All dependencies installed ✅"
    echo ""
    echo "🔧 Backend Services:"
    echo "   - Discover Backend: ✅ Fully functional"
    echo "   - Account Service: ⚠️  Limited (missing user module)"
    echo ""
    echo "⚠️  Known Issues:"
    echo "   - Account service authentication may not work"
    echo "   - Some API endpoints will return errors"
    echo "   - Database configuration needs to be set up"
    echo ""
    echo "🔗 Useful URLs (when running):"
    echo "   - Discover Backend: http://localhost:8080"
    echo "   - Account Service: http://localhost:8081"
}

# Run main function
main
