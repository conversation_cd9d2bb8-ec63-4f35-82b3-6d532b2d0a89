# BGM (Background Music) Implementation

## Overview
This implementation adds a simple background music service to the ope_client app that plays a single MP3 file from S3 storage throughout the app experience, with a volume on/off toggle in the side menu.

## Components

### 1. BGM Service (`src/services/bgmService.ts`)
- **Purpose**: Manages background music playback using `react-native-sound`
- **Features**:
  - Loads music from `{RESOURCE_HOST}music/1.mp3`
  - Loops music indefinitely
  - Provides play/pause/stop/toggle functionality
  - Volume control (set to 30% by default)
  - Singleton pattern for global access

### 2. Global State Management
- **Store**: Extended `useAccountStore` in `src/context/store.tsx`
- **New State**: 
  - `isBgmEnabled: boolean` - tracks BGM on/off state
  - `updateBgmEnabled: (enabled: boolean) => void` - updates BGM state
- **Persistence**: BGM preference is saved to device storage using MMKV

### 3. Side Menu Toggle (`src/pages/sideMenu/index.tsx`)
- **New Component**: `ProfileToggleItem` - reusable toggle component
- **Features**:
  - Visual toggle switch (purple when on, grey when off)
  - Animated thumb position
  - Integrated with BGM service

### 4. App Integration (`App.tsx`)
- **Initialization**: BGM service is initialized when the app launches
- **Auto-play**: Music starts automatically if BGM is enabled in settings

## Configuration

### Module Resolution
- **Babel**: Added `@services` alias in `.babelrc`
- **TypeScript**: Added `@services/*` path mapping in `tsconfig.json`

### Translations
- **English**: "Background Music"
- **Korean**: "배경음악"

## Usage

### For Users
1. Open the side menu from the home screen
2. Find the "Background Music" toggle at the bottom
3. Tap to enable/disable background music
4. Setting is automatically saved and persists across app restarts

### For Developers
```typescript
import { useBGM } from '@services/bgmService'

const { isBgmEnabled, toggleBGM, initializeBGM } = useBGM()

// Initialize BGM (usually done in App.tsx)
await initializeBGM()

// Toggle BGM on/off
toggleBGM()

// Check current state
console.log('BGM enabled:', isBgmEnabled)
```

## File Structure
```
src/
├── services/
│   └── bgmService.ts          # BGM service implementation
├── pages/sideMenu/
│   └── index.tsx              # Side menu with BGM toggle
├── context/
│   └── store.tsx              # Global state management
├── i18n/translates/
│   ├── en.json                # English translations
│   └── kr.json                # Korean translations
└── App.tsx                    # App initialization
```

## Dependencies
- `react-native-sound`: Audio playback library (already included)
- `zustand`: State management (already included)
- `react-native-mmkv`: Persistent storage (already included)

## Audio File Requirements
- **Location**: `{RESOURCE_HOST}music/1.mp3`
- **Format**: MP3
- **Recommended**: Optimized for mobile (reasonable file size and quality)
- **Loop-friendly**: Should have seamless loop points for continuous playback

## Testing
- Unit tests included in `__tests__/bgmService.test.ts`
- Tests cover initialization, playback control, and error handling
- Mocked dependencies for reliable testing

## Notes
- BGM plays at 30% volume by default to avoid being intrusive
- Music continues playing across all app screens
- Graceful error handling if audio file fails to load
- Memory management with proper resource cleanup
- Respects device silent mode settings through `react-native-sound` configuration
