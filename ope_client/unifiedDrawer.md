# Unified Drawer Analysis & Implementation Plan

## Analysis Status
✅ **VERIFIED** - This analysis has been verified against the actual codebase as of the latest check.

**Key Findings:**
- All three drawer components (Search, Collections, Filter) are currently working properly
- Line numbers and configurations have been updated to match the current codebase
- The Filter drawer is functional (contrary to initial assumption of being broken)
- The architectural analysis and proposed solution remain valid

## Current Drawer Architecture Analysis

### 1. Search Drawer (Base - Currently Working)
**Type:** `BottomSheet` (always visible, not modal)
**Location:** `src/pages/home/<USER>
**Component:** `src/pages/search/index.tsx`

**Configuration:**
- **Snap Points:** `[searchHeight + insets.bottom, UnistylesRuntime.screen.height - insets.bottom - insets.top - 100]`
- **Initial Index:** `0` (always partially visible)
- **Background:** `backgroundColor: theme.colors.background`
- **Keyboard Behavior:** `keyboardBehavior: 'extend'` and `keyboardBlurBehavior: 'none'`
- **Handle:** `handleComponent: null` (custom handle)

**Content Structure:**
```
├── Background Image (launch_background.png)
├── Handler (custom drag handle)
├── Header (row-reverse layout)
│   ├── Filter Button (search_map.png) - opacity animated
│   ├── Collections Button (search_mark.png) - opacity animated  
│   └── Search Input (animated width)
└── Search Results List (BottomSheetFlatList)
```

**State Management:**
- Search text input with `BottomSheetTextInput`
- Search results from API (`searchWord` mutation)
- Button opacity animations (`useSharedValue` for opacity)
- Width animations for input expansion (`useSharedValue` for width)
- Auto-loads all exhibitors on mount and when dismissed

### 2. Collections Drawer (Currently Working)
**Type:** `BottomSheetModal` (modal overlay)
**Location:** `src/pages/home/<USER>
**Component:** `src/pages/collections/index.tsx`

**Configuration:**
- **Snap Points:** `[UnistylesRuntime.screen.height - insets.bottom - insets.top - 100]`
- **Initial Index:** `0` (but modal, so hidden until presented)
- **Background:** `backgroundColor: theme.colors.background`
- **Keyboard Behavior:** `keyboardBehavior: 'interactive'` and `keyboardBlurBehavior: 'none'`

**Content Structure:**
```
├── Background Image (launch_background.png)
├── Handler (custom drag handle)
├── Header (single search input)
│   └── Search Input (for filtering collections)
├── Filter Tags (horizontal scroll)
│   ├── Mission Tag (purple)
│   ├── Exhibition Tag (orange)
│   ├── Event Tag (pink)
│   ├── Restaurant Tag (red)
│   ├── Shop Tag (green)
│   ├── Hotel Tag (blue)
│   └── Other Tag (yellow-orange)
└── Collections List (BottomSheetFlatList)
```

**State Management:**
- Collections data from API (`collectList` mutation with pagination)
- Exclude types for filtering (`excludeType` state with `MarkType[]`)
- Search text for collections (filters `contentName`)
- Content opacity animation (`useSharedValue` for fade-in)
- Loads data on mount and updates marks on unmount

### 3. Filter Drawer (Currently Working)
**Type:** `BottomSheetModal` (modal overlay)
**Location:** `src/pages/home/<USER>
**Component:** `src/pages/filter/index.tsx`

**Configuration:**
- **Snap Points:** `[400 + insets.bottom]` (fixed height)
- **Initial Index:** `0` (but modal, so hidden until presented)
- **Background:** `backgroundColor: theme.colors.background`
- **Status:** Currently working properly

**Content Structure:**
```
├── BlurView Background (dark blur)
├── Handler (custom drag handle)
└── Horizontal ScrollView
    ├── Mission Filter (toggle with images)
    ├── Exhibition Filter (toggle with images)
    ├── Event Filter (toggle with images)
    ├── Restaurant Filter (toggle with images)
    ├── Shop Filter (toggle with images)
    ├── Hotel Filter (toggle with images)
    ├── Other Filter (toggle with images)
    └── Favorite Filter (toggle with images)
```

**State Management:**
- Global map filter state (`useMapStore` with `MapFilterItem` interface)
- Individual filter toggles for each marker type and favorites
- Affects map marker visibility through `filtMark` function
- Uses haptic feedback on filter changes

## Problems with Current Architecture

### 1. Potential Modal Conflicts
- Multiple `BottomSheetModal` instances could potentially conflict
- Z-index and layering complexity
- Multiple modal management complexity

### 2. Duplicate Functionality
- Both Collections and Filter have similar filtering concepts
- Collections filters by type (exclude), Filter filters by type (include)
- Redundant UI patterns and similar header structures

### 3. Inconsistent UX
- Different interaction patterns for similar functionality
- Filter button becomes invisible when search is focused (opacity animation)
- Separate modals break user flow continuity

### 4. Complex State Management
- Multiple refs and state variables (`searchRef`, `collectionsRef`, `filterRef`)
- Difficult to coordinate between drawers
- Animation state management across multiple components

## Unified Drawer Solution

### Core Concept
Transform the existing Search drawer into a multi-mode unified drawer with three modes:
1. **Search Mode** (default)
2. **Collections Mode** 
3. **Filter Mode**

### Implementation Strategy

#### 1. Drawer Modes Enum
```typescript
enum DrawerMode {
  SEARCH = 'search',
  COLLECTIONS = 'collections', 
  FILTER = 'filter'
}
```

#### 2. Unified State Management
```typescript
const [drawerMode, setDrawerMode] = useState<DrawerMode>(DrawerMode.SEARCH)
const [isExpanded, setIsExpanded] = useState(false)
```

#### 3. Header Button Logic
- **Filter Button:** Switch to Filter mode + expand drawer
- **Collections Button:** Switch to Collections mode + expand drawer  
- **Search Input Focus:** Switch to Search mode + expand drawer

#### 4. Content Rendering
```typescript
const renderDrawerContent = () => {
  switch (drawerMode) {
    case DrawerMode.SEARCH:
      return <SearchContent />
    case DrawerMode.COLLECTIONS:
      return <CollectionsContent />
    case DrawerMode.FILTER:
      return <FilterContent />
  }
}
```

## Required Changes

### 1. Remove Existing Modals
**Files to modify:**
- `src/pages/home/<USER>
  - Remove `collectionsRef` (line 63) and `filterRef` (line 62)
  - Remove Collections BottomSheetModal (lines 426-442) and Filter BottomSheetModal (lines 443-452)
  - Update button handlers in Search component props to change drawer mode instead of presenting modals

### 2. Create Unified Drawer Component
**New file:** `src/pages/unifiedDrawer/index.tsx`
- Combine Search, Collections, and Filter functionality
- Manage mode switching
- Handle animations and transitions

### 3. Extract Content Components
**New files:**
- `src/pages/unifiedDrawer/SearchContent.tsx` (extract from current Search)
- `src/pages/unifiedDrawer/CollectionsContent.tsx` (extract from current Collections)
- `src/pages/unifiedDrawer/FilterContent.tsx` (extract from current Filter)

### 4. Update Button Handlers
**In Search component props (home.tsx lines 386-392):**
```typescript
// Current:
onPressFilter={() => {
  console.log('onPressFilter called, presenting filter modal')
  filterRef.current?.present()
}}
onPressCollections={() => {
  collectionsRef.current?.present()
}}

// New unified approach:
onPressFilter={() => setDrawerMode(DrawerMode.FILTER)}
onPressCollections={() => setDrawerMode(DrawerMode.COLLECTIONS)}
```

### 5. Unified Header Design
Create a consistent header that works across all modes:
- Mode indicator/breadcrumb
- Back button for non-search modes
- Search input (visible in all modes but behaves differently)
- Action buttons (context-sensitive)

## Benefits of Unified Approach

### 1. Eliminates Modal Conflicts
- Single BottomSheet instead of multiple BottomSheetModals
- No z-index or layering issues
- Consistent animation behavior

### 2. Improved UX
- Smooth transitions between modes
- Consistent interaction patterns
- No jarring modal presentations

### 3. Simplified State Management
- Single drawer state
- Unified animation system
- Easier to coordinate functionality

### 4. Better Performance
- Fewer component instances
- Reduced memory usage
- Simpler render tree

### 5. Easier Maintenance
- Single drawer logic to maintain
- Consistent styling and behavior
- Easier to add new modes in future

## Implementation Priority

1. **Phase 1:** Create unified drawer structure and Search mode
2. **Phase 2:** Add Collections mode functionality  
3. **Phase 3:** Add Filter mode functionality
4. **Phase 4:** Remove old modal components
5. **Phase 5:** Polish animations and transitions

This approach will solve the current filter button issue while creating a more maintainable and user-friendly architecture.
