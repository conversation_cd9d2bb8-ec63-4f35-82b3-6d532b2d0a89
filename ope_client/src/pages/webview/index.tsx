import { useAccountStore } from '@context/store'
import Header from '@pure/Header'
import * as RootNavigation from '@pure/RootNavigation'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import { type StackParamList } from '@type/index'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import Toast from 'react-native-root-toast'
import Share from 'react-native-share'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import WebView from 'react-native-webview'
import { ActivityIndicator, View } from 'react-native'

type WebViewProps = NativeStackScreenProps<StackParamList, 'Webview'>
interface BridgeParams {
  id: string
  method: string
  params: string
}
interface CallbackParams {
  id: string
  method: string
  result: string
  status: number
  message: string
}
let injectConsole = `
  const titleDom = document.getElementsByTagName('title')[0];
  const observer = new MutationObserver(() => {
    // window.ReactNativeWebView.postMessage(document.title)
    window.ReactNativeWebView.postMessage(JSON.stringify({method: "title", params: JSON.stringify({title: document.title})}))
  });
  observer.observe(titleDom, {
    childList: true
  });
  window.ReactNativeWebView.postMessage(JSON.stringify({method: "title", params: JSON.stringify({title: document.title})}));
  `
if (__DEV__) {
  injectConsole = `
${injectConsole}
(function () { var script = document.createElement('script'); script.src="//unpkg.com/vconsole@latest/dist/vconsole.min.js"; document.body.appendChild(script); script.onload = function () { var vConsole = new window.VConsole(); } })();
`
}
injectConsole = `${injectConsole} true;`
export default function Webview({ route, navigation }: WebViewProps): React.JSX.Element {
  const { url, hide_navi: hideNavi = true } = route.params
  const [show, setShow] = useState(false)
  const { theme } = useStyles()
  const [title, setTitle] = useState<string>('')
  const { styles } = useStyles(styleSheet)
  const token = useAccountStore((state) => state.token)
  const ref = useRef<WebView>(null)
  useEffect(() => {
    navigation.setOptions({ headerShown: false })
    return () => {
      navigation.setOptions({ headerShown: true })
    }
  }, [])
  const handleMessage = useCallback((message: any) => {
    const params = message as BridgeParams
    const args = JSON.parse(params.params)
    const result: CallbackParams = {
      id: params.id,
      method: params.method,
      status: 200,
      message: 'Success',
      result: '{}'
    }
    // 以后改成注册
    switch (params.method) {
      case 'title':
        setTitle(args.title)
        break
      case 'close':
        _close(result, args, ref)
        break
      case 'toHome':
        _toHome(result, args, ref)
        break
      case 'share':
        _share(result, args, ref)
        break
      case 'toast':
        _toast(result, args, ref)
        break
      default:
        _default(ref, result)
        break
    }
  }, [])

  const runFirst = `
      window.globalProps = { token: "${token}" };
      true;
    `

  return (
    <View style={styles.view}>
      {!hideNavi &&
        <Header title={title} onClickBack={() => { navigation.goBack() }}></Header>
      }
      <WebView
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        webviewDebuggingEnabled={__DEV__}
        ref={ref}
        source={{ uri: url }}
        style={{ flex: 1, backgroundColor: theme.colors.background, display: show ? 'flex' : 'none' }}
        renderLoading={() => <ActivityIndicator color="#FFFFFF" style={styles.loader} />}
        startInLoadingState={true}
        injectedJavaScriptBeforeContentLoaded={runFirst}
        injectedJavaScript={injectConsole}
        onLoad={() => {
          setShow(true)
        }}
        onMessage={(event) => {
          handleMessage(JSON.parse(event.nativeEvent.data))
        }} />
    </View>
  )
}
function _toast(result: CallbackParams, args: any, ref: React.RefObject<WebView>): void {
  // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
  Toast.show(args.message, { position: Toast.positions.CENTER })
}

function _share(result: CallbackParams, args: any, ref: React.RefObject<WebView>): void {
  Share.open({
    url: args.url,
    message: args.message,
    excludedActivityTypes: ['com.apple.UIKit.activity.Message',
      'com.apple.UIKit.activity.CopyToPasteboard',
      'com.apple.UIKit.activity.AirDrop',
      'com.apple.UIKit.activity.Mail',
      'com.apple.UIKit.activity.Print',
      'com.apple.UIKit.activity.AssignToContact'
    ]
  }).then((_res) => {
    result.result = JSON.stringify({ shared: true })
    callback(ref, result)
  })
    .catch((_) => {
      result.result = JSON.stringify({ shared: false })
      callback(ref, result)
    })
}

function _close(result: CallbackParams, _args: any, ref: React.RefObject<WebView>): void {
  useAccountStore.getState().updateScore(100)
  RootNavigation.goBack()
  result.result = JSON.stringify({})
  callback(ref, result)
}

function _toHome(result: CallbackParams, _args: any, ref: React.RefObject<WebView>): void {
  RootNavigation.toHome()
  result.result = JSON.stringify({})
  callback(ref, result)
}

function _default(ref: React.RefObject<WebView>, result: any): void {
  result.status = 400
  result.message = `Can not find method ${result.method}`
  callback(ref, result)
}

function callback(ref: React.RefObject<WebView>, result: any): void {
  ref.current?.injectJavaScript(
    `
      if (window.bridgeCallback !== undefined) {
        window.bridgeCallback(${JSON.stringify(result)});
      }
      true;
    `
  )
}

const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  loader: {
    flex: 1
  }
}))
