import { useMapStore } from '@context/map'
import { hapticTrigger } from '@context/utils'
import { BlurView } from '@react-native-community/blur'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Image, Pressable, ScrollView, Text, TouchableOpacity, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

export default function Filter(): React.JSX.Element {
  const { t } = useTranslation()
  const { styles, theme } = useStyles(styleSheet)
  const updateFilter = useMapStore(state => state.updateFilter)
  const filter = useMapStore(state => state.filter)
  return (
    <BlurView blurType='dark' blurAmount={10} style={{ flex: 1, alignItems: 'center', borderTopLeftRadius: 20, borderTopRightRadius: 20, borderRadius: 20 }}>
      {/* <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%', overflow: 'hidden', borderRadius: 14 }} /> */}
      <View style={styles.handler} hitSlop={{ top: 50, bottom: 50, left: 10, right: 10 }} />
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}
        style={styles.scrollView}
      >
        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, game: !filter.game })
        }}>
          {filter.game
            ? <Image source={require('@images/filter_mission.png')} style={styles.image} />
            : <Image source={require('@images/filter_mission_disable.png')} style={styles.image} />
          }
          <Text style={filter.game ? styles.text : styles.textDisable}>{t('game')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, exhibition: !filter.exhibition })
        }}>
          {filter.exhibition
            ? <Image source={require('@images/filter_exhibition.png')} style={styles.image} />
            : <Image source={require('@images/filter_exhibition_disable.png')} style={styles.image} />
          }
          <Text style={filter.exhibition ? styles.text : styles.textDisable} numberOfLines={1}>{t('exhibition')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, event: !filter.event })
        }}>
          {filter.event
            ? <Image source={require('@images/filter_event.png')} style={styles.image} />
            : <Image source={require('@images/filter_event_disable.png')} style={styles.image} />
          }
          <Text style={filter.event ? styles.text : styles.textDisable}>{t('event')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, restaurant: !filter.restaurant })
        }}>
          {filter.restaurant
            ? <Image source={require('@images/filter_mission.png')} style={styles.image} />
            : <Image source={require('@images/filter_mission_disable.png')} style={styles.image} />
          }
          <Text style={filter.restaurant ? styles.text : styles.textDisable}>{t('restaurant')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, shop: !filter.shop })
        }}>
          {filter.shop
            ? <Image source={require('@images/filter_mission.png')} style={styles.image} />
            : <Image source={require('@images/filter_mission_disable.png')} style={styles.image} />
          }
          <Text style={filter.shop ? styles.text : styles.textDisable}>{t('shop')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, hotel: !filter.hotel })
        }}>
          {filter.hotel
            ? <Image source={require('@images/filter_mission.png')} style={styles.image} />
            : <Image source={require('@images/filter_mission_disable.png')} style={styles.image} />
          }
          <Text style={filter.hotel ? styles.text : styles.textDisable}>{t('hotel')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, other: !filter.other })
        }}>
          {filter.other
            ? <Image source={require('@images/filter_mission.png')} style={styles.image} />
            : <Image source={require('@images/filter_mission_disable.png')} style={styles.image} />
          }
          <Text style={filter.other ? styles.text : styles.textDisable}>{t('other')}</Text>
        </Pressable>

        <Pressable style={styles.button} onPress={() => {
          hapticTrigger()
          updateFilter({ ...filter, favorite: !filter.favorite })
        }}>
          {filter.favorite
            ? <Image source={require('@images/filter_favorite.png')} style={styles.image} />
            : <Image source={require('@images/filter_favorite_disable.png')} style={styles.image} />
          }
          <Text style={filter.favorite ? styles.text : styles.textDisable}>{t('favorite')}</Text>
        </Pressable>
      </ScrollView>
    </BlurView>
  )
}
const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    marginBottom: UnistylesRuntime.insets.bottom
  },
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginVertical: 10
  },
  text: {
    color: theme.colors.typography,
    fontSize: 14,
    fontWeight: '900'
  },
  textDisable: {
    color: theme.colors.lightGrey,
    fontSize: 14,
    fontWeight: '900'
  },
  scrollView: {
    width: '100%',
    marginTop: 20
  },
  scrollContainer: {
    paddingHorizontal: 20,
    gap: 20,
    alignItems: 'center'
  },
  button: {
    alignItems: 'center',
    gap: 12,
    width: 80,
    minWidth: 80
  },
  image: {
    width: 50,
    height: 50
  }
}))
