import { phoneNumberLogin, sendVerifyCode } from '@network/login'
import RNPickerSelect from 'react-native-picker-select'
import { useAccountStore } from '@context/store'
import { Button } from '@pure/Button'
import Loading from '@pure/Loading'
import React, { memo, useCallback, useState } from 'react'
import Toast from 'react-native-root-toast'
import { getCountry } from 'react-native-localize'
import locale from '@static/locale.json'

import { useTranslation } from 'react-i18next'
import {
  Dimensions,
  Image,
  Keyboard,
  Text,
  TextInput,
  View
} from 'react-native'
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { type NaviProps } from '@type/index'
import { useMutation } from '@tanstack/react-query'

enum StepStatus {
  normal,
  error,
  confirm
}
interface StepProps {
  text?: string
  title: string
  placeholder: string
  buttonTitle: string
  content: string
  status?: StepStatus
  onChangeText?: (t: string) => void
  onClickConfirm?: () => void
  disabled?: boolean
  showAreaCode?: boolean
}
function StepInput(props: StepProps): React.JSX.Element {
  const { showAreaCode = false, disabled = false } = props
  const { styles, theme } = useStyles(styleSheet)
  const err = props.status === StepStatus.error ? styles.error : undefined
  const inputError = props.status === StepStatus.error ? styles.inputError : undefined

  const country = getCountry() ?? 'JP'
  const defaultCode = locale.find(item => {
    return item.country === country
  })?.code ?? '+81'
  const [areaCode, setAreaCode] = useState(defaultCode)
  const countryCodes = locale.map(item => {
    return { label: item.code, value: item.code }
  })
  return (
    <View style={styles.step}>
      <Text style={[styles.text, styles.title]}>{props.title}</Text>
      <View style={[styles.inputContainer, { display: 'flex', flexDirection: 'row' }]}>
        {showAreaCode &&
          <RNPickerSelect
            darkTheme={true}
            items={countryCodes}
            onValueChange={(value: string) => {
              setAreaCode(value)
            }}
          >
            <View style={{ width: 83, height: '100%', backgroundColor: '#B8B8B8', display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center', gap: 5 }}>
              <Text style={{ fontSize: 16, color: theme.colors.background }}>{areaCode}</Text>
              <Image style={{ width: 16, height: 10, objectFit: 'fill' }} tintColor={theme.colors.background} source={require('@images/pulldown.png')} />
            </View>
          </RNPickerSelect>
        }
        <TextInput placeholderTextColor={theme.colors.border} style={[styles.input, inputError]} placeholder={props.placeholder} onChangeText={(t) => {
          if (showAreaCode) {
            props.onChangeText?.(`${areaCode}${t}`)
          } else {
            props.onChangeText?.(t)
          }
        }} />
      </View>
      {/* } */}
      <Text style={[styles.text, styles.content, err]}>{props.content}</Text>
      <Button
        disabled={disabled}
        style={[styles.button]} onPress={() => {
          props.onClickConfirm?.()
        }} text={props.buttonTitle}></Button>
    </View >
  )
}
const totalCountDown = 60
const MemoStepInput = memo(StepInput)
function Login({ navigation }: NaviProps): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const [phone, setPhone] = useState('')
  const [code, setCode] = useState('')
  const [phoneButtonDisable, setPhoneButtonDisable] = useState(true)
  const [codeButtonDisable, setCodeButtonDisable] = useState(true)
  const [status, setStatus] = useState(StepStatus.normal)
  const { t } = useTranslation()
  const [countDown, setCountDown] = useState(totalCountDown)
  const backgroundStyle = {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height
  }
  const onChangeText = useCallback((t: string) => {
    setPhoneButtonDisable(validatePhone(t) === null)
    setPhone(t)
    setStatus(StepStatus.normal)
  }, [])
  const updateToken = useAccountStore((state) => state.updateToken)
  const updateNickName = useAccountStore((state) => state.updateNickName)
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: sendVerifyCode,
    onSuccess: (result) => {
      if (result.success) {
        setPhoneButtonDisable(true)
        setCodeButtonDisable(false)
        const interval = setInterval(() => {
          setCountDown(lastCountDown => {
            if (lastCountDown === 0) {
              setPhoneButtonDisable(false)
              setCountDown(totalCountDown)
              clearInterval(interval)
              // your redirection to Quit screen
            } else {
              return lastCountDown - 1
            }
          })
        }, 1000)
        Toast.show(result.msg || 'Verification code sent successfully!', { position: Toast.positions.CENTER })
      } else {
        Toast.show(result.msg || 'Failed to send verification code', { position: Toast.positions.CENTER })
        setStatus(StepStatus.error)
      }
    },
    onError: (error) => {
      console.error('SMS Send Error:', error)
      Toast.show('Failed to send verification code. Please check your phone number and try again.', {
        position: Toast.positions.CENTER,
        duration: Toast.durations.LONG
      })
      setStatus(StepStatus.error)
    }
  })
  const { mutate: loginRun, isPending: loginLoading } = useMutation({
    mutationFn: phoneNumberLogin,
    onSuccess: (result) => {
      if (result.success) {
        updateToken(result.data.authToken as string)
        updateNickName(result.data.nickname as string)
        navigation.navigate('CountDown')
      } else {
        Toast.show(result.msg || 'Login failed. Please check your verification code.', { position: Toast.positions.CENTER })
      }
    },
    onError: (error) => {
      console.error('Login Error:', error)
      Toast.show('Login failed. Please check your verification code and try again.', {
        position: Toast.positions.CENTER,
        duration: Toast.durations.LONG
      })
    }
  })
  return (
    <View style={backgroundStyle}>
      {(loading || loginLoading) &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <KeyboardAwareScrollView style={styles.scroll}
        onScrollEndDrag={Keyboard.dismiss}
        extraScrollHeight={150}
      >
        <MemoStepInput
          showAreaCode={true}
          onChangeText={onChangeText}
          disabled={phoneButtonDisable}
          onClickConfirm={() => {
            Keyboard.dismiss()
            const status = validatePhone(phone) !== null ? StepStatus.confirm : StepStatus.error
            setStatus(status)
            if (status === StepStatus.confirm) {
              run(phone)
            }
          }}
          text={phone} status={status} title={t('phone_number')} placeholder={t('enter_phone')} content={t('enter_phone_hint')} buttonTitle={t('send')}></MemoStepInput>
        <MemoStepInput
          disabled={codeButtonDisable}
          onChangeText={(t) => {
            setCode(t)
          }}
          onClickConfirm={() => {
            loginRun({ phoneNumber: phone, verifyCode: code })
          }}
          title={t('code')} placeholder={t('enter_code')} content={t('refresh_content').replace('{time}', `${countDown}`)} buttonTitle={t('confirm')}></MemoStepInput>
      </KeyboardAwareScrollView>
      <Text style={{ fontSize: 14, color: 'white', paddingHorizontal: 20, paddingBottom: UnistylesRuntime.insets.bottom + 50 }}>{t('login_hint')}</Text>
    </View>
  )
}
const validatePhone = (phoneNumber: string): RegExpMatchArray | null => {
  // E.164 format validation: +[country code][number]
  // Should start with + and be 7-15 digits total
  // More strict validation for AWS SNS compatibility
  return phoneNumber.match(
    /^\+[1-9]\d{6,14}$/
  )
}

const styleSheet = createStyleSheet(theme => ({
  scroll: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
    paddingTop: 100
  },
  step: {
    flex: 1,
    justifyContent: 'center',
    marginHorizontal: 24
  },
  button: {
    borderRadius: 30,
    width: '80%',
    height: 60,
    left: '10%',
    marginBottom: 33
  },
  text: {
    fontSize: 20,
    letterSpacing: 0.25,
    color: 'white'
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 10
  },
  subTitle: {
    fontSize: 16,
    marginBottom: 16
  },
  content: {
    fontSize: 14,
    height: 51
  },
  error: {
    color: 'red'
  },
  inputError: {
    color: 'red',
    backgroundColor: '#FFDBDB',
    borderColor: 'red'
  },
  scrollView: {
  },
  inputContainer: {
    marginBottom: 16,
    backgroundColor: 'white',
    height: 48,
    borderColor: '#B8B8B8',
    borderWidth: 1,
    borderRadius: 5
  },
  input: {
    flexGrow: 1,
    paddingHorizontal: 16,
    fontSize: 16,
    height: '100%'
  }
}))
export default Login
