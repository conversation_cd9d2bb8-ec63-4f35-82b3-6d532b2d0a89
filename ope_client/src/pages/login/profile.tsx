import { updateProfile } from '@network/login'
import { type NaviProps } from '@type/index'
import Lottie<PERSON>iew from 'lottie-react-native'
import React, { useCallback, useRef, useState, type PropsWithChildren } from 'react'
import { Dimensions, Image, Keyboard, Pressable, SafeAreaView, Text, TextInput, TouchableOpacity, View } from 'react-native'
import RNPickerSelect from 'react-native-picker-select'
import Animated, { useSharedValue, withSpring } from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

import Modal, { ModalWrapper } from '@context/modal'
import { usePrefetch } from '@context/utils'
import { Button } from '@pure/Button'
import Loading from '@pure/Loading'
import AlertContent from '@pure/alertContent'
import Bubble, { BubbleCorner } from '@pure/bubble'
import DatePicker from 'react-native-date-picker'
import Toast from 'react-native-root-toast'
import Share from 'react-native-share'
import { useTranslation } from 'react-i18next'
import { useMutation } from '@tanstack/react-query'
import { useBGM } from '@services/bgmService'

interface ProfileInputProps extends PropsWithChildren {
  title: string
  placeHolder?: string
  isSelect?: boolean
  isDate?: boolean
  onChangeText: (t: string) => void
  data?: Array<{ label: string, value: string }>
}

function ProfileInput(props: ProfileInputProps): React.JSX.Element {
  const { t } = useTranslation()
  const { styles, theme } = useStyles(styleSheet)
  const isSelect = props.isSelect ?? false
  const isDate = props.isDate ?? false
  const selectData = props.data ?? []
  const [value, setValue] = useState(null)

  const [date, setDate] = useState<Date | undefined>()
  const [open, setOpen] = useState(false)
  return (
    <View>
      <View style={{ gap: 5, marginTop: 20, display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
        <Text style={styles.inputTitle}>{props.title}</Text>
        {props.children}
      </View>
      {(isSelect && !isDate) &&
        <RNPickerSelect
          darkTheme={true}
          onValueChange={(value) => {
            if (value === 'null') {
              return
            }
            setValue(value)
            props.onChangeText(value)
          }}
          items={selectData}
        >
          <TextInput
            value={value}
            placeholderTextColor={theme.colors.border} style={styles.inputText} placeholder={props.placeHolder ?? t('select')} />
          <Image style={{ width: 16, height: 10, position: 'absolute', right: 18, bottom: 18, objectFit: 'fill' }} source={require('@images/pulldown.png')} />
        </RNPickerSelect>
      }
      {(!isSelect && !isDate) &&
        <TextInput
          onChangeText={(t) => {
            props.onChangeText(t)
          }}
          placeholderTextColor={theme.colors.border} style={styles.inputText} placeholder={props.placeHolder ?? 'Select'} />
      }
      {isDate &&
        <View>
          <Pressable onPressIn={() => {
            Keyboard.dismiss()
            setOpen(true)
          }}>
            <TextInput
              editable={false}
              onPressIn={() => {
                Keyboard.dismiss()
                setOpen(true)
              }}
              onChangeText={(t) => {
                props.onChangeText(t)
              }}
              placeholderTextColor={theme.colors.border} style={styles.inputText} placeholder={props.placeHolder ?? 'Select'}>
              {date?.toLocaleDateString().replaceAll('/', '-') ?? ''}
            </TextInput>
            <Image style={{ width: 16, height: 10, position: 'absolute', right: 18, bottom: 18, objectFit: 'fill' }} source={require('@images/pulldown.png')} />
          </Pressable>
        </View>
      }
      {isDate &&
        <DatePicker
          modal
          open={open}
          mode='date'
          date={date ?? new Date()}
          onConfirm={(date) => {
            setOpen(false)
            setDate(date)
            props.onChangeText(date.toISOString().slice(0, 10))
          }}
          onCancel={() => {
            setOpen(false)
          }}
        />}
    </View>
  )
}

const maleData = [
  { label: 'Male', value: 'Male' },
  { label: 'Female', value: 'Female' }
]

const countryData = [
  { label: 'Japan', value: 'Japan' },
  { label: 'China', value: 'China' }
]
enum ProfileType {
  name,
  gender,
  birthday,
  country
}
function showWelcomeModal(finish: () => void, t: any, changeTrack: (track: number) => Promise<void>): void {
  // Change to track 1 when energy_get modal opens
  changeTrack(1).catch(console.error)

  const modal = Modal.show(
    <ModalWrapper>
      <AlertContent
        onPress={() => {
          Modal.hide(modal)
          // Note: Track will change when navigating to IntroTwo
          finish()
        }}
        title={t('energy_get')} content={t('energy_got')}>
        <View style={{ overflow: 'hidden', display: 'flex', alignItems: 'center', gap: 30 }}>
          <View style={{ height: 218, width: 400 }}>
            <Image source={require('@images/share_bg.png')} style={{ position: 'absolute', overflow: 'hidden', resizeMode: 'contain', height: '100%', width: '100%' }} />
            <Image source={require('@images/polygon.png')} style={{ position: 'absolute', overflow: 'hidden', resizeMode: 'contain', top: '10%', height: '80%', width: '100%' }} />
          </View>
          <Text style={{ fontSize: 20, fontWeight: '900' }}>{t('energy_got')}</Text>
          <TouchableOpacity onPress={() => {
            Share.open({
              url: 'https://supermassiveglobal.co.jp/',
              message: 'Welcome',
              excludedActivityTypes: ['com.apple.UIKit.activity.Message',
                'com.apple.UIKit.activity.CopyToPasteboard',
                'com.apple.UIKit.activity.AirDrop',
                'com.apple.UIKit.activity.Mail',
                'com.apple.UIKit.activity.Print',
                'com.apple.UIKit.activity.AssignToContact'
              ]
            }).then((res) => { console.log(res) })
              .catch((err) => { err && console.log(err) })
          }}>
            <View style={{ backgroundColor: '#222', height: 40, borderRadius: 20 }}>
              <Text style={{ color: 'white', fontWeight: 'bold', paddingHorizontal: 50, height: 40, lineHeight: 40 }}>{t('share')}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </AlertContent>
    </ModalWrapper>
  )
}

function SetProfile({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const insets = useSafeAreaInsets()
  const { changeTrack } = useBGM()

  const name = useRef<string | undefined>()
  const [namePosition, setNamePosition] = useState([0, 0])
  const [nameOpicaty, setNameOpicaty] = useState(0)
  const nameTranslateX = useSharedValue(0)
  const nameTranslateY = useSharedValue(0)
  const nameRef = useRef<Animated.Image>(null)

  const gender = useRef<string | undefined>()
  const [genderPosition, setGenderPosition] = useState([0, 0])
  const [genderOpicaty, setGenderOpicaty] = useState(0)
  const genderTranslateX = useSharedValue(0)
  const genderTranslateY = useSharedValue(0)
  const genderRef = useRef<Animated.Image>(null)

  const birthday = useRef<string | undefined>()
  const [birthdayPosition, setbirthdayPosition] = useState([0, 0])
  const [birthdayOpicaty, setbirthdayOpicaty] = useState(0)
  const birthdayTranslateX = useSharedValue(0)
  const birthdayTranslateY = useSharedValue(0)
  const birthdayRef = useRef<Animated.Image>(null)

  const country = useRef<string | undefined>()
  const [countryPosition, setcountryPosition] = useState([0, 0])
  const [countryOpicaty, setcountryOpicaty] = useState(0)
  const countryTranslateX = useSharedValue(0)
  const countryTranslateY = useSharedValue(0)
  const countryRef = useRef<Animated.Image>(null)

  const portaro = usePrefetch('portaro_loop_01.json')
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: updateProfile,
    onSuccess: (result) => {
      if (result.success) {
        nameTranslateX.value = withSpring(-namePosition[0] + 50, { damping: 100, mass: 2 })
        nameTranslateY.value = withSpring(-namePosition[1] + Dimensions.get('screen').height - 100 - insets.bottom, { damping: 100, mass: 2 })
        genderTranslateX.value = withSpring(-genderPosition[0] + 50, { damping: 100 })
        genderTranslateY.value = withSpring(-genderPosition[1] + Dimensions.get('screen').height - 100 - insets.bottom, { damping: 100 })
        birthdayTranslateX.value = withSpring(-birthdayPosition[0] + 50, { damping: 100 })
        birthdayTranslateY.value = withSpring(-birthdayPosition[1] + Dimensions.get('screen').height - 100 - insets.bottom, { damping: 100 })
        countryTranslateX.value = withSpring(-countryPosition[0] + 50, { damping: 100 })
        countryTranslateY.value = withSpring(-countryPosition[1] + Dimensions.get('screen').height - 100 - insets.bottom, { damping: 100 })
        setTimeout(() => {
          showWelcomeModal(() => {
            setTimeout(() => {
              navigation.navigate('IntroTwo')
            }, 500)
          }, t, changeTrack)
        }, 1500)
      } else {
        Toast.show(result.msg, { position: Toast.positions.CENTER })
      }
    }
  })
  const callback = useCallback((t: string, type: ProfileType) => {
    switch (type) {
      case ProfileType.name:
        setNameOpicaty(t.length <= 0 ? 0 : 1)
        name.current = t
        nameRef.current?.measure((_fx, _fy, _width, _height, px, py) => {
          setNamePosition([px, py])
        })
        break
      case ProfileType.gender:
        gender.current = t
        setGenderOpicaty(1)
        genderRef.current?.measure((_fx, _fy, _width, _height, px, py) => {
          setGenderPosition([px, py])
        })
        break
      case ProfileType.birthday:
        birthday.current = t
        setbirthdayOpicaty(1)
        birthdayRef.current?.measure((_fx, _fy, _width, _height, px, py) => {
          setbirthdayPosition([px, py])
        })
        break
      case ProfileType.country:
        country.current = t
        setcountryOpicaty(1)
        countryRef.current?.measure((_fx, _fy, _width, _height, px, py) => {
          setcountryPosition([px, py])
        })
        break
    }
  }, [])
  return (
    <SafeAreaView style={styles.container}>
      {loading &&
        <Loading />
      }
      <View style={{ width: '100%', padding: 20 }}>
        <Text style={styles.title}>{t('complete_surver')}</Text>
        <ProfileInput
          onChangeText={(t) => {
            callback(t, ProfileType.name)
          }}
          title={t('nickname_input')}
          placeHolder={t('enter_nickname')}>
          <Animated.Image ref={nameRef} style={[{ width: 28, height: 36, objectFit: 'fill', opacity: nameOpicaty }, { transform: [{ translateX: nameTranslateX }, { translateY: nameTranslateY }] }]} source={require('@images/polygon.png')} />
        </ProfileInput>
        <ProfileInput
          onChangeText={(t) => {
            callback(t, ProfileType.gender)
          }}
          data={maleData}
          isSelect title={t('gender_input')}>
          <Animated.Image ref={genderRef} style={[{ width: 28, height: 36, objectFit: 'fill', opacity: genderOpicaty }, { transform: [{ translateX: genderTranslateX }, { translateY: genderTranslateY }] }]} source={require('@images/polygon.png')} />
        </ProfileInput>
        <ProfileInput
          isDate
          onChangeText={(t) => {
            callback(t, ProfileType.birthday)
          }}
          isSelect title={t('birthday_input')}>
          <Animated.Image ref={birthdayRef} style={[{ width: 28, height: 36, objectFit: 'fill', opacity: birthdayOpicaty }, { transform: [{ translateX: birthdayTranslateX }, { translateY: birthdayTranslateY }] }]} source={require('@images/polygon.png')} />
        </ProfileInput>
        <ProfileInput
          onChangeText={(t) => {
            callback(t, ProfileType.country)
          }}
          data={countryData}
          isSelect title={t('country_input')}>
          <Animated.Image ref={countryRef} style={[{ width: 28, height: 36, objectFit: 'fill', opacity: countryOpicaty }, { transform: [{ translateX: countryTranslateX }, { translateY: countryTranslateY }] }]} source={require('@images/polygon.png')} />
        </ProfileInput>
      </View>
      <Button
        text={t('confirm')}
        style={styles.button} onPress={() => {
          if (
            name.current !== undefined &&
            gender.current !== undefined &&
            birthday.current !== undefined &&
            country.current !== undefined
          ) {
            run({ nickname: name.current, gender: gender.current === 'male' ? 0 : 1, birthday: birthday.current, location: country.current })
          } else {
            Toast.show(t('profile_complete'), { position: Toast.positions.CENTER })
          }
        }}>
      </Button>
      <View
        style={{ ...{ bottom: insets.bottom - 20 }, ...styles.image }}
      >
        <LottieView source={portaro} loop autoPlay style={{ width: '100%', height: '100%' }} />
      </View>
      <Bubble
        text={t('tell_us')}
        style={{ ...{ bottom: insets.bottom + 28 }, ...styles.bubble }}
        corner={BubbleCorner.left}
        onClick={() => {
        }} />
    </SafeAreaView >
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    alignItems: 'flex-start',
    backgroundColor: 'black',
    paddingLeft: 20
  },
  title: {
    color: 'white',
    fontSize: 20,
    fontWeight: '900',
    marginTop: 20
  },
  inputTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '900'
  },
  inputText: {
    fontSize: 16,
    paddingLeft: 10,
    marginTop: 8,
    backgroundColor: 'white',
    height: 48,
    borderRadius: 5
  },
  dropdown: {
    marginTop: 8,
    height: 48,
    backgroundColor: 'white',
    borderRadius: 5,
    paddingHorizontal: 8
  },
  placeholderStyle: {
    color: theme.colors.border,
    fontSize: 14

  },
  button: {
    borderRadius: 30,
    marginTop: 20,
    width: Dimensions.get('window').width - 100,
    height: 60,
    left: 50
  },
  text: {
    fontSize: 20,
    letterSpacing: 0.25,
    color: 'white'
  },
  bubble: {
    position: 'absolute',
    left: 140,
    right: 24,
    display: UnistylesRuntime.screen.height > 700 ? 'flex' : 'none'
  },
  image: {
    position: 'absolute',
    left: -10,
    width: 180,
    height: 180
  }
}))
export default SetProfile
