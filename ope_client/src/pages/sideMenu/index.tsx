import { MEDIA_HOST, getLanguageCode } from '@context/utils'
import { updateTicket } from '@pages/home/<USER>'
import Loading from '@pure/Loading'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Image, SafeAreaView, ScrollView, Text, View, type ImageSourcePropType } from 'react-native'
import { TouchableOpacity } from 'react-native-gesture-handler'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import { useBGM } from '@services/bgmService'

export function ProfileItem(props: { icon?: ImageSourcePropType, title: string, onPress?: () => void }): React.JSX.Element {
  const { icon, title, onPress } = props
  const { styles } = useStyles(styleSheet)
  return (
    <TouchableOpacity
      onPress={onPress}
    >
      <View style={styles.item}>
        {icon !== undefined &&
          <Image source={icon} style={styles.itemIcon} />
        }
        <Text style={styles.itemText}>{title}</Text>
        <View style={{ flex: 1, flexGrow: 1, display: 'flex', flexDirection: 'row-reverse' }}>
          <Image source={require('@images/arrow.png')} style={styles.itemCorner} />
        </View>
      </View></TouchableOpacity>
  )
}

export function ProfileToggleItem(props: { icon?: ImageSourcePropType, title: string, isEnabled: boolean, onToggle: () => void }): React.JSX.Element {
  const { icon, title, isEnabled, onToggle } = props
  const { styles } = useStyles(styleSheet)
  return (
    <TouchableOpacity onPress={onToggle}>
      <View style={styles.item}>
        {icon !== undefined &&
          <Image source={icon} style={styles.itemIcon} />
        }
        <Text style={styles.itemText}>{title}</Text>
        <View style={{ flex: 1, flexGrow: 1, display: 'flex', flexDirection: 'row-reverse' }}>
          <View style={[styles.toggle, isEnabled ? styles.toggleOn : styles.toggleOff]}>
            <View style={[styles.toggleThumb, isEnabled ? styles.toggleThumbOn : styles.toggleThumbOff]} />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const SideMenu = (props: { navigation: any, onClickBack: () => void }): React.JSX.Element => {
  const { navigation } = props
  const { styles } = useStyles(styleSheet)
  const { t } = useTranslation()
  const { isBgmEnabled, toggleBGM, changeTrack } = useBGM()
  const { run: runTicket, loading: loadingTicket } = updateTicket((result) => {
    if (result.data === null) {
      navigation.navigate('ActiveTicket')
    } else {
      navigation.navigate('Ticket')
    }
  })

  return (
    <View style={styles.container}>
      {loadingTicket && <Loading />}
      <Image source={require('@images/launch_background.png')} style={styles.image} />
      <SafeAreaView>
        <ScrollView style={{ height: '100%' }}>
          <View style={styles.backContainer}>
            <TouchableOpacity onPress={props.onClickBack}>
              <Image style={styles.back} source={require('@images/profile_back.png')}></Image>
            </TouchableOpacity>
          </View>
          <ProfileItem icon={require('@images/profile_self.png')} title={t('account')} onPress={() => {
            navigation.navigate('Account')
          }} />
          <ProfileItem icon={require('@images/profile_ranking.png')} title={t('ranking')} onPress={() => {
            navigation.navigate('Ranking')
          }} />
          <ProfileItem icon={require('@images/profile_reward.png')} title={t('about_reward')} onPress={() => {
            navigation.navigate('AboutRewards')
          }} />
          <ProfileItem icon={require('@images/profile_ticket.png')} title={t('my_rewards')} onPress={() => {
            navigation.navigate('MyRewards')
          }} />
          <ProfileItem icon={require('@images/profile_information.png')} title={t('information')} onPress={() => {
            navigation.navigate('Information')
          }} />
          {/* <ProfileItem icon={require('@images/profile_ticket.png')} title={t('ticket')} onPress={() => {
            runTicket()
          }} /> */}
          <View style={styles.separator}></View>
          <ProfileItem title={t('rule')} onPress={() => {
            navigation.navigate('Webview', { url: `${MEDIA_HOST}rules-${getLanguageCode()}`, hide_navi: false })
          }} />
          <ProfileItem title={t('privacy_policy')} onPress={() => {
            navigation.navigate('Webview', { url: `${MEDIA_HOST}privacy-policy-${getLanguageCode()}`, hide_navi: false })
          }} />
          <ProfileItem title={'Debug Minigame'} onPress={() => {
            console.log(`Loading debug minigame: ${MEDIA_HOST}game/1?markId=149&lang=${getLanguageCode()}`)
            navigation.navigate('Webview', { url: `${MEDIA_HOST}game/1?markId=149&lang=${getLanguageCode()}`, hide_navi: true })
          }} />
          <View style={styles.separator}></View>
          <ProfileToggleItem
            title={t('bgm')}
            isEnabled={isBgmEnabled}
            onToggle={toggleBGM}
          />
        </ScrollView>
      </SafeAreaView>
    </View >
  )
}

export default SideMenu

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1
  },
  image: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover'
  },
  item: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    color: theme.colors.typography,
    width: '100%',
    paddingHorizontal: 34,
    paddingVertical: 17,
    gap: 17
  },
  itemText: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
  },
  itemIcon: {
    width: 24,
    height: 24
  },
  itemCorner: {
    width: 14,
    height: 14
  },
  separator: {
    width: '100%',
    height: 1,
    backgroundColor: theme.colors.grey,
    marginVertical: 17
  },
  backContainer: {
    display: 'flex',
    flexDirection: 'row-reverse',
    paddingHorizontal: 15
  },
  back: {
    width: 50,
    height: 50
  },
  toggle: {
    width: 50,
    height: 28,
    borderRadius: 14,
    padding: 2,
    justifyContent: 'center'
  },
  toggleOn: {
    backgroundColor: theme.colors.purple
  },
  toggleOff: {
    backgroundColor: theme.colors.grey
  },
  toggleThumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'white'
  },
  toggleThumbOn: {
    alignSelf: 'flex-end'
  },
  toggleThumbOff: {
    alignSelf: 'flex-start'
  }
}))
