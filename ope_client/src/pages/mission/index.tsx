import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { updateMarks, updateCurrentScore } from '@pages/home/<USER>'
import { addMarkScore } from '@network/userScore'
import { type MarkDetail, changeCollect, type GameDetail } from '@network/map'
import React, { useEffect, useState } from 'react'
import { Image, Text, TouchableOpacity, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { useMutation } from '@tanstack/react-query'
interface MissionProps {
  navigation: any
  mark?: MarkDetail
}

export default function Mission({ navigation, mark }: MissionProps): React.JSX.Element {
  const { styles, theme } = useStyles(styleSheet)
  const [collected, setCollected] = useState(mark?.collected ?? false)
  const reloadMarks = updateMarks(undefined)
  const { mutate: change } = useMutation({
    mutationFn: changeCollect,
    onSuccess: (result) => {
      console.log(result)
      if (!result.success) return
      setCollected(!collected)
      reloadMarks()
    }
  })

  const reloadScore = updateCurrentScore(true)
  const { mutate: addMark } = useMutation({
    mutationFn: addMarkScore,
    onSuccess: (_result) => {
      reloadScore()
    }
  })

  useEffect(() => {
    if (mark !== undefined) {
      setCollected(mark?.collected ?? false)
    }
  }, [mark])

  useEffect(() => {
    if (mark?.id === undefined || !(mark?.showPoint)) return
    addMark(mark.id)
    return () => {
      if (mark?.showPoint) {
        reloadMarks()
      }
    }
  }, [])
  // Create fallback content if missing
  const content = mark?.content as GameDetail || {
    name: mark?.name || 'Mission',
    description: 'Mission details not available',
    gameUrl: 'https://via.placeholder.com/300x200?text=Mission+Image'
  }
  return (
    <View style={{ flex: 1, alignItems: 'center' }}>
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%', overflow: 'hidden', borderRadius: 14 }} />
      <View style={styles.handler} hitSlop={{ top: 50, bottom: 50, left: 10, right: 10 }} />
      <TouchableOpacity style={{ position: 'absolute', width: 40, right: 6, top: 6, height: 40 }}
        onPress={() => {
          navigation.navigate('AboutRewards')
        }}
      >
        <Image source={require('@images/mission_about.png')} style={{ width: '100%', height: '100%' }} />
      </TouchableOpacity>
      <BottomSheetScrollView style={styles.container}>
        <View style={styles.event}>
          <Text style={{ color: theme.colors.typography, fontSize: 20, fontWeight: '700', width: '100%' }}>{content.name}</Text>
          <View style={styles.imageContainer}>
            <Image source={require('@images/gasha_border.png')} style={{ position: 'absolute', width: '100%', height: '100%', resizeMode: 'stretch' }} />
            <Image source={{ uri: content.gameUrl }} style={{ position: 'absolute', left: 8, top: 8, right: 8, bottom: 8, borderRadius: 15, backgroundColor: theme.colors.typography }}></Image>
            <TouchableOpacity style={{ position: 'absolute', right: 10, top: -10 }}
              onPress={(_e) => {
                if (mark?.id !== null && mark?.id !== undefined) {
                  change({ collected: !collected, markId: mark?.id })
                }
              }}>
              <Image source={collected ? require('@images/favorite.png') : require('@images/unfavorite.png')} style={{ width: 24, height: 31 }} />
            </TouchableOpacity>
          </View>
          <Text style={styles.eventContent}>{content.description}</Text>
        </View>
      </BottomSheetScrollView>
    </View>
  )
}
const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 40,
    width: UnistylesRuntime.screen.width,
    marginBottom: UnistylesRuntime.insets.bottom
  },
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginVertical: 10
  },
  eventContent: {
    color: theme.colors.typography,
    fontSize: 15,
    fontWeight: '400'
  },
  event: {
    display: 'flex',
    gap: 20,
    marginTop: 20
  },
  imageContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    height: 207,
    borderRadius: 6,
    gap: 6
  }
}))
