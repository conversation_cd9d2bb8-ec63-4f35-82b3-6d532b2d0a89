import { buyReward, type Reward } from '@network/reward'
import { updateProfile, type UserInfo } from '@network/login'
import { useAccountStore } from '@context/store'
import { updateCurrentScore } from '@pages/home/<USER>'
import { Button } from '@pure/Button'
import Modal, { ModalWrapper } from '@context/modal'
import Loading from '@pure/Loading'
import React, { useCallback, useState } from 'react'
import { useFocusEffect } from '@react-navigation/native'
import { Image, Text, TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-root-toast'
import Swiper from 'react-native-swiper'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { ScoreLabel } from './rewards'
import AlertContent from '@pure/alertContent'
import PatternText from '@pure/PatternText'
import { useTranslation } from 'react-i18next'
import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { RESOURCE_HOST } from '@context/utils'
import FastImage from 'react-native-fast-image'
import Bubble, { BubbleCorner } from '@pure/bubble'
import { useMutation } from '@tanstack/react-query'
import { useBGM } from '@services/bgmService'

export default function RewardDetail(props: { navigation?: any, item: Reward, exchangeCallback: (code: number) => void }): React.JSX.Element {
  const score = useAccountStore((state) => state.score)
  const { styles, theme } = useStyles(styleSheet)
  const { t } = useTranslation()
  const [userProfile, setUserProfile] = useState<UserInfo | null>(null)
  const { changeTrack } = useBGM()

  // Score update function
  const reloadScore = updateCurrentScore(true)

  // Fetch user profile on component mount
  const { mutate: fetchProfile } = useMutation({
    mutationFn: updateProfile,
    onSuccess: (result) => {
      if (result.success) {
        setUserProfile(result.data)
      }
    }
  })

  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: buyReward,
    onSuccess: (result) => {
      if (!result.success) {
        Toast.show(result.msg, { position: Toast.positions.CENTER })
        return
      }
      // Update the score in the store after successful purchase
      reloadScore()
      showSuccessModal(() => {
        props.exchangeCallback(result.code)
      })
    }
  })

  const { item } = props

  // Fetch profile when component mounts and when user returns to this screen
  useFocusEffect(
    useCallback(() => {
      fetchProfile({})
    }, [fetchProfile])
  )

  // Check if profile is complete for reward claiming
  const isProfileComplete = useCallback((profile: UserInfo | null): boolean => {
    if (profile === null || profile === undefined) return false

    // Check required fields: nickname, gender, birthday
    const hasNickname = profile.nickname !== null && profile.nickname !== undefined && profile.nickname.trim() !== ''
    const hasGender = profile.gender !== null && profile.gender !== undefined
    const hasBirthday = profile.birthdayStr !== null && profile.birthdayStr !== undefined && profile.birthdayStr.trim() !== ''

    return hasNickname && hasGender && hasBirthday
  }, [])

  const showConfirmModal = useCallback((reward: Reward, finish: () => void) => {
    // Change to track 10 when confirm modal opens
    changeTrack(10).catch(console.error)

    const modal = Modal.show(
      <ModalWrapper>
        <AlertContent
          onPress={() => {
            Modal.hide(modal)
            // Return to track 5 when confirm modal is dismissed
            changeTrack(5).catch(console.error)
          }}
          title={t('exchange')} content="">
          <View style={{ overflow: 'hidden', display: 'flex', alignItems: 'center', gap: 30 }}>
            <PatternText
              style={styles.toastTitle}
              text={t('exchange_reward_toast')}
              pattern={'{price}'}
              replace={<Text key={Date.now()}>{reward.pointPrice.toLocaleString()}</Text>}
            />
            <View style={{ backgroundColor: '#F2F2F2', height: 50, display: 'flex', flexDirection: 'row', borderRadius: 12, alignContent: 'center', alignItems: 'center', gap: 24, marginHorizontal: 20, paddingHorizontal: 30 }}>
              <Text style={[styles.toastPrice, { color: theme.colors.background }]}>{score.toLocaleString()}</Text>
              <Image source={require('@images/change_arrow.png')} style={{ width: 8, height: 16 }} />
              <Text style={[styles.toastPrice, { color: theme.colors.purple }]}>{(score - reward.pointPrice).toLocaleString()}</Text>
            </View>
            <View style={{ height: 40, display: 'flex', flexDirection: 'row', alignContent: 'center', alignItems: 'center', gap: 9 }}>
              <TouchableOpacity onPress={() => {
                Modal.hide(modal)
              }}>
                <View style={[styles.toastButton, { backgroundColor: theme.colors.lightGrey }]}>
                  <Text style={styles.toastButtonText}>Cancel</Text>
                </View>
              </TouchableOpacity>
              <TouchableOpacity onPress={() => {
                Modal.hide(modal)
                finish()
              }}>
                <View style={[styles.toastButton, { backgroundColor: theme.colors.purple }]}>
                  <Text style={styles.toastButtonText}>{t('exchange')}</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </AlertContent>
      </ModalWrapper>

    )
  }, [score, theme.colors.background, theme.colors.purple, theme.colors.lightGrey, styles.toastTitle, styles.toastPrice, styles.toastButton, styles.toastButtonText, t, changeTrack])
  const showSuccessModal = useCallback((finish: () => void) => {
    // Change to track 4 when successful exchange modal opens
    changeTrack(4).catch(console.error)

    const modal = Modal.show(
      <ModalWrapper>
        <AlertContent
          onPress={() => {
            Modal.hide(modal)
            // Return to track 5 when exchange modal closes
            changeTrack(5).catch(console.error)
            finish()
          }}
          title={t('exchange')} content="">
          <View style={{ overflow: 'hidden', display: 'flex', alignItems: 'center', gap: 30 }}>
            <View style={{ width: UnistylesRuntime.screen.width - 96, height: 218, backgroundColor: '#F2F2F2', borderRadius: 20, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <FastImage source={{ uri: `${RESOURCE_HOST}ope_ticket_bg.png` }} style={{ position: 'absolute', width: '100%', height: '100%' }} />
              <FastImage source={{ uri: item.imageUrl }} style={{ width: UnistylesRuntime.screen.width - 176, height: 200, borderRadius: 15 }} resizeMode="cover" />
            </View>
            <View style={{ height: 40, display: 'flex', flexDirection: 'row', alignContent: 'center', alignItems: 'center', gap: 9 }}>
              <TouchableOpacity onPress={() => {
                Modal.hide(modal)
                props.navigation?.replace('MyRewards')
                finish()
              }}>
                <View style={[styles.toastButton, { backgroundColor: theme.colors.purple }]}>
                  <Text style={styles.toastButtonText}>{t('goto_rewards')}</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </AlertContent>
      </ModalWrapper>

    )
  }, [changeTrack])
  const showFailModal = useCallback(() => {
    // Change to track 7 when not enough energy modal opens
    changeTrack(7).catch(console.error)

    const modal = Modal.show(
      <ModalWrapper>
        <AlertContent
          onPress={() => {
            Modal.hide(modal)
            // Return to track 5 when not enough energy modal closes
            changeTrack(5).catch(console.error)
          }}
          title={t('not_enough_energy')} content="">
          <View style={{ overflow: 'hidden', display: 'flex', alignItems: 'center', gap: 30 }}>
            <FastImage source={{ uri: `${RESOURCE_HOST}portaro_sad.png` }} style={{ width: 130, height: 126 }} />
            <Bubble
              text={t('come_back')}
              style={{ width: UnistylesRuntime.screen.width - 76 }}
              corner={BubbleCorner.top}
              onClick={() => {
              }} />

          </View>
        </AlertContent>
      </ModalWrapper>

    )
  }, [changeTrack])
  return (
    <View style={{ flex: 1, alignItems: 'center' }}>
      {loading &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%', overflow: 'hidden', borderRadius: 14 }} />
      <View style={styles.handler} />
      <BottomSheetScrollView contentContainerStyle={{ alignItems: 'center', gap: 10 }} bounces={false}>
        <Swiper
          // https://github.com/leecade/react-native-swiper/issues/1064
          removeClippedSubviews={false}
          style={styles.scroll}
          dot={<View style={[styles.dot, { backgroundColor: '#727272' }]} />}
          activeDot={<View style={[styles.dot, { backgroundColor: '#D9D9D9' }]} />}
        >
          {item.detailImageUrls.map((url, index) => {
            return (
              <View style={styles.imageItem} key={index}>
                <Image source={require('@images/gasha_border.png')} style={{ position: 'absolute', width: '100%', height: '100%', resizeMode: 'stretch' }} />
                <Image source={{ uri: url }} style={{ position: 'absolute', left: 8, top: 8, right: 8, bottom: 8, borderRadius: 15, backgroundColor: theme.colors.typography }}></Image>
              </View>
            )
          })}
        </Swiper>
        <ScoreLabel content={item.pointPrice.toLocaleString()} />

        <Text style={styles.title}>{item.name}</Text>
        <Text style={styles.desc}>{item.detailDescription}</Text>
      </BottomSheetScrollView>
      <Button text={t('exchange')} disabled={item.inventory <= 0} style={styles.button} onPress={() => {
        // First check if profile is complete
        if (!isProfileComplete(userProfile)) {
          Toast.show(t('complete_profile_before_reward'), { position: Toast.positions.CENTER })
          props.navigation?.navigate('Account')
          return
        }

        if (score < item.pointPrice) {
          showFailModal()
        } else {
          showConfirmModal(item, () => {
            run(item.id)
          })
        }
      }}></Button>
    </View >
  )
}

const styleSheet = createStyleSheet(theme => ({
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginVertical: 10
  },
  button: {
    borderRadius: 30,
    width: UnistylesRuntime.screen.width - 100,
    height: 56,
    marginVertical: 30
  },
  scroll: {
    height: (UnistylesRuntime.screen.width - 40) * 207 / 335 + 40,
    marginHorizontal: 20,
    marginTop: 10
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.typography,
    marginHorizontal: 22
  },
  desc: {
    fontSize: 14,
    fontWeight: '400',
    color: theme.colors.typography,
    marginHorizontal: 22
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 19,
    marginBottom: 60,
    width: 200,
    height: 400
  },
  imageItem: {
    width: UnistylesRuntime.screen.width - 40,
    height: (UnistylesRuntime.screen.width - 40) * 207 / 335

  },
  dot: {
    width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 0, marginBottom: -20
  },
  toastTitle: {
    color: theme.colors.background,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '900',
    marginHorizontal: 20
  },
  toastPrice: {
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '900'
  },
  toastButton: {
    paddingHorizontal: 30,
    paddingVertical: 10,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center'
  },
  toastButtonText: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '900',
    color: theme.colors.typography

  }
}))
