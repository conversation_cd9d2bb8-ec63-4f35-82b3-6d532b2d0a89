import { useAccountStore } from '@context/store'
import BottomSheet, { BottomSheetBackdrop } from '@gorhom/bottom-sheet'
import { rewardsList, type Reward } from '@network/reward'
import Header from '@pure/Header'
import Loading from '@pure/Loading'
import { type NaviProps } from '@type/index'
import { cloneDeep } from 'lodash'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { FlatList, Image, Text, View } from 'react-native'
import { RectButton } from 'react-native-gesture-handler'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import RewardDetail from './rewardDetail'
import { useTranslation } from 'react-i18next'
import { useQuery } from '@tanstack/react-query'

import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'

export function ScoreLabel(props: { content?: string }): React.JSX.Element {
  const { theme } = useStyles(styleSheet)
  const score = useAccountStore((state) => state.score)
  return (
    <View style={{ flex: 1, height: '100%', display: 'flex', flexDirection: 'row-reverse', alignItems: 'center' }}>
      <View style={{ backgroundColor: theme.colors.lightGrey, width: 178, height: 30, borderRadius: 30, display: 'flex', alignItems: 'center', flexDirection: 'row', paddingHorizontal: 10 }}>
        <Image style={[{ width: 28, height: 36, marginBottom: 20, objectFit: 'fill' }]} source={require('@images/polygon.png')} />
        <Text style={{ flex: 1, textAlign: 'right', color: theme.colors.typography, fontSize: 20, fontWeight: '900' }}>{props.content ?? score.toLocaleString()}</Text>
      </View>
    </View>
  )
}
export default function Rewards({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const rewardRef = useRef<BottomSheet>(null)
  const { styles } = useStyles(styleSheet)
  const [selectReward, setSelectReward] = useState<Reward | undefined>(undefined)
  const { changeTrack } = useBGM()

  // Backdrop component for bottom sheet
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  )

  useEffect(() => {
    if (selectReward !== undefined) {
      console.log('Opening BottomSheet for reward:', selectReward.id, selectReward.name)
      setTimeout(() => {
        rewardRef.current?.expand()
      }, 100) // Small delay to ensure BottomSheet is ready
    }
  }, [selectReward])

  // Change to track 5 when rewards page comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Rewards screen focused, changing to track 5')
      changeTrack(5).catch(console.error)
    }, [])
  )

  const { data, isPending: loading } = useQuery({ queryKey: ['rewards'], queryFn: async () => await rewardsList(1) })
  return (
    <View style={styles.view}>
      {loading &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <FlatList
        contentContainerStyle={styles.container}
        data={data?.data?.rewards}
        renderItem={({ item }) => <RewardsItem item={item} onPress={(item) => {
          console.log('Reward item tapped:', item.id, item.name)
          setSelectReward(cloneDeep(item))
        }} />}
        keyExtractor={(item) => item.id.toString()}
      >
      </FlatList >
      <Header blur title={t('rewards')} onClickBack={() => { navigation.goBack() }}
        bottom={
          <View style={{ height: 60, display: 'flex', flexDirection: 'row', alignItems: 'center', paddingHorizontal: 53, width: '100%' }}>
            <Text style={{ color: 'white', fontSize: 20, fontWeight: '900' }}>{t('energy')}</Text>
            <ScoreLabel />
          </View>}
      ></Header>
      <BottomSheet
        enablePanDownToClose
        snapPoints={[UnistylesRuntime.screen.height - UnistylesRuntime.insets.top - 100]}
        index={-1}
        ref={rewardRef}
        handleComponent={null}
        backdropComponent={renderBackdrop}
      >
        {selectReward !== undefined &&
          <RewardDetail navigation={navigation} item={selectReward} exchangeCallback={(code) => {
            if (code === 200) {
              rewardRef.current?.close()
            }
          }} />
        }
      </BottomSheet>
    </View >

  )
}

export function RewardsItem(props: { item: Reward, onPress?: (item: Reward) => void }): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const { item } = props
  return (
    <RectButton style={[styles.item, { opacity: item.inventory > 0 ? 1 : 0.5 }]} onPress={() => { props.onPress?.(item) }}>
      <Image source={require('@images/gasha_mask.png')} style={{ position: 'absolute', width: '100%', height: '100%', borderRadius: 20 }} />
      <View style={styles.itemContainer}>
        <View style={styles.itemIcon}>
          <Image source={require('@images/gasha_border.png')} style={{ position: 'absolute', width: '100%', height: '100%', borderRadius: 15, resizeMode: 'stretch' }} />
          <Image source={{ uri: item.imageUrl }} style={{ position: 'absolute', left: 4, top: 4, bottom: 4, right: 4, borderRadius: 12 }} />
        </View>
        <View style={styles.itemContent}>
          <Text style={styles.itemTitle}>{item.name}</Text>
          <Text style={styles.itemSubTitle}>{item.description}</Text>
          <View style={{ flex: 1, display: 'flex', flexDirection: 'row', alignItems: 'flex-end', gap: 5 }}>
            <Text style={{ color: 'white', fontSize: 11, fontWeight: '700' }}>NEED</Text>
            <Image style={[{ width: 20, height: 26, objectFit: 'fill' }]} source={require('@images/polygon.png')} />
            <Text style={{ color: 'white', position: 'absolute', right: 5, bottom: -5, fontSize: 24, fontWeight: '700' }}>{item.pointPrice.toLocaleString()}</Text>
          </View>
        </View>
      </View>
    </RectButton>

  )
}
const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  container: {
    paddingTop: UnistylesRuntime.insets.top + 130,
    paddingBottom: UnistylesRuntime.insets.bottom
  },
  content: {
    height: 40,
    borderRadius: 40,
    overflow: 'hidden',
    backgroundColor: theme.colors.grey,
    marginVertical: 4,
    marginHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 30,
    paddingLeft: 32,
    paddingRight: 8
  },
  icon: {
    position: 'absolute',
    left: 10,
    top: 12,
    width: 26,
    height: 23
  },
  title: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
  },
  item: {
    marginHorizontal: 20,
    marginVertical: 5,
    display: 'flex',
    backgroundColor: '#666666',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#7E7E7E'
  },
  itemContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 20

  },
  itemIcon: {
    width: 100,
    height: 100,
    borderRadius: 5
  },
  itemContent: {
    display: 'flex',
    flex: 1,
    alignSelf: 'flex-start',
    gap: 5

  },
  itemTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '700'
  },
  itemSubTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '400'
  }
}))
