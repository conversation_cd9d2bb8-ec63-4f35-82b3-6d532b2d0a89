import { unregister, updateProfile } from '@network/login'
import { useAccountStore } from '@context/store'
import { ProfileItem } from '@pages/sideMenu'
import Header from '@pure/Header'
import Loading from '@pure/Loading'
import AlertContent from '@pure/alertContent'
import Modal, { ModalWrapper } from '@context/modal'
import { type NaviProps } from '@type/index'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Image, Text, TextInput, TouchableOpacity, View } from 'react-native'
import DatePicker from 'react-native-date-picker'
import RNPickerSelect from 'react-native-picker-select'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import { useTranslation } from 'react-i18next'
import { useMutation } from '@tanstack/react-query'

const countryData = [
  { label: 'Korea', value: 'Korea' },
  { label: 'Japan', value: 'Japan' },
  { label: 'China', value: 'China' }
]

export default function Account(props: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const maleData = [
    { label: t('male'), value: 'Male' },
    { label: t('female'), value: 'Female' }
  ]
  const { navigation } = props
  const { styles } = useStyles(styleSheet)
  const [open, setOpen] = useState(false)
  const [editNick, setEditNick] = useState(false)
  const updateToken = useAccountStore((state) => state.updateToken)
  const updateNickName = useAccountStore((state) => state.updateNickName)
  const country = useRef('')
  const gender = useRef('')
  const nick = useRef('')
  const input = useRef<TextInput | null>(null)
  const { data, isPending: loading, mutate: run } = useMutation({ mutationFn: updateProfile })
  const { isPending: loadingUnregister, mutate: unregisterRun } = useMutation({
    mutationFn: unregister,
    onSuccess: (result) => {
      if (!result.success) return
      updateToken(null)
      updateNickName(null)
      navigation.replace('Launch')
    }
  })

  const showDeleteConfirmModal = useCallback(() => {
    const modal = Modal.show(
      <ModalWrapper>
        <AlertContent
          title={t('delete_account')}
          content=""
          onPress={() => {
            Modal.hide(modal)
          }}
        >
          <View style={{ overflow: 'hidden', display: 'flex', alignItems: 'center', gap: 20, paddingHorizontal: 20 }}>
            <Text style={[styles.confirmText, { textAlign: 'center' }]}>
              {t('delete_account_confirmation')}
            </Text>
            <View style={{ flexDirection: 'row', gap: 15, width: '100%', justifyContent: 'center' }}>
              <TouchableOpacity
                onPress={() => {
                  Modal.hide(modal)
                }}
                style={[styles.confirmButton, styles.cancelButton]}
              >
                <Text style={styles.cancelButtonText}>{t('cancel')}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  Modal.hide(modal)
                  unregisterRun()
                }}
                style={[styles.confirmButton, styles.deleteButton]}
              >
                <Text style={styles.deleteButtonText}>{t('delete')}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </AlertContent>
      </ModalWrapper>
    )
  }, [t, unregisterRun])

  useEffect(() => {
    run({})
  }, [])
  return (
    <View style={styles.view}>
      {(loading || loadingUnregister) &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <Header title={t('account')} onClickBack={() => { navigation.goBack() }}></Header>
      <AccountItem title={t('nickname')} content={editNick ? '' : data?.data?.nickname ?? ''} onPress={() => {
        setEditNick(true)
        input.current?.focus()
      }}>
        <TextInput ref={input} defaultValue={data?.data?.nickname ?? ''} style={[styles.itemContent, { position: 'absolute', width: 100, height: 36, left: 37, top: 20, display: editNick ? 'flex' : 'none' }]}
          onChangeText={value => { nick.current = value }}
          onEndEditing={() => {
            setEditNick(false)
            run({ nickname: nick.current })
          }}
        ></TextInput>
      </AccountItem>
      <RNPickerSelect
        darkTheme={true}
        onValueChange={(value) => {
          gender.current = value
        }}
        onDonePress={() => { run({ gender: gender.current === 'Male' ? 0 : 1 }) }}
        items={maleData}
      >
        <AccountItem title={t('gender')} content={data?.data?.gender === 0 ? t('male') : t('female')} />
      </RNPickerSelect>
      <AccountItem title={t('birthday')} content={data?.data?.birthdayStr ?? ''} onPress={() => {
        setOpen(true)
      }} />
      <RNPickerSelect
        darkTheme={true}
        onValueChange={(value) => {
          country.current = value
        }}
        onDonePress={() => { run({ location: country.current }) }}
        items={countryData}
      >
        <AccountItem title={t('country')} content={data?.data?.location ?? ''} />
      </RNPickerSelect>
      <View style={styles.separator}></View>
      <ProfileItem title={t('my_rewards')} onPress={() => { navigation.navigate('MyRewards') }}></ProfileItem>
      <ProfileItem title={t('my_story')} onPress={() => { navigation.navigate('History') }}></ProfileItem>
      <View style={{ width: '100%', alignItems: 'center', marginTop: 20, gap: 10 }}>
        <TouchableOpacity onPress={() => {
          updateToken(null)
          updateNickName(null)
          navigation.replace('Launch')
        }}>
          <View style={styles.logoutContainer}>
            <Text style={styles.logout}>{t('logout')}</Text>
          </View>
        </TouchableOpacity>

        {__DEV__ &&
          <TouchableOpacity onPress={showDeleteConfirmModal}>
            <View style={styles.deleteContainer}>
              <Text style={styles.deleteText}>Delete Account</Text>
            </View>
          </TouchableOpacity>
        }
      </View>
      <DatePicker
        modal
        open={open}
        mode='date'
        date={new Date()}
        onConfirm={(date) => {
          setOpen(false)
          run({ birthday: date.toISOString().slice(0, 10) })
        }}
        onCancel={() => {
          setOpen(false)
        }}
      />
    </View >
  )
}

function AccountItem(props: { title: string, content?: string, onPress?: () => void, children?: React.ReactNode | undefined }): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  return (
    <View style={styles.itemView}>
      <View style={{ gap: 15 }}>
        <Text style={styles.itemTitle}>{props.title}</Text>
        <Text style={[styles.itemContent]}>{props.content}</Text>
      </View>
      <TouchableOpacity onPress={props.onPress} style={{ flexGrow: 1, display: 'flex', justifyContent: 'center', alignItems: 'flex-end' }}>
        <Image source={require('@images/edit.png')} style={{ width: 36, height: 36 }}></Image>
      </TouchableOpacity>
      {props.children}
    </View >
  )
}

const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  itemView: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: '100%',
    paddingLeft: 37,
    paddingRight: 19,
    marginVertical: 20
  },
  itemTitle: {
    color: theme.colors.lightGrey,
    fontSize: 12
  },
  itemContent: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
    // display: 'none'
    // visibility: 'hidden'
  },
  separator: {
    width: '100%',
    height: 1,
    backgroundColor: theme.colors.grey,
    marginVertical: 17
  },
  logoutContainer: {
    paddingVertical: 10,
    paddingHorizontal: 30,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    borderWidth: 1,
    borderColor: theme.colors.green
  },
  logout: {
    color: theme.colors.green,
    fontSize: 20
  },
  deleteContainer: {
    paddingVertical: 8,
    paddingHorizontal: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    borderWidth: 1,
    borderColor: '#FF4444'
  },
  deleteText: {
    color: '#FF4444',
    fontSize: 16
  },
  confirmText: {
    color: theme.colors.typography,
    fontSize: 16,
    lineHeight: 24
  },
  confirmButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 25,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center'
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: theme.colors.lightGrey
  },
  cancelButtonText: {
    color: theme.colors.lightGrey,
    fontSize: 16,
    fontWeight: '600'
  },
  deleteButton: {
    backgroundColor: '#FF4444',
    borderWidth: 1,
    borderColor: '#FF4444'
  },
  deleteButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  }
}))
