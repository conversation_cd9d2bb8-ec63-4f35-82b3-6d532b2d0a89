import { useMapStore } from '@context/map'
import { usePrefetch } from '@context/utils'
import Bubble, { B<PERSON>bleCorner } from '@pure/bubble'
import { type NaviProps } from '@type/index'
import LottieView from 'lottie-react-native'
import React, { useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'
import { Image, Text, View } from 'react-native'
import Animated from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Svg, { Circle } from 'react-native-svg'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

function CountDown({ navigation }: NaviProps): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const insets = useSafeAreaInsets()
  const data = usePrefetch('portaro_loop_2.json')
  const { t } = useTranslation()
  const dashAngle = (Math.PI * 130 / 15) - 2
  const day = Math.min(useMapStore(state => state.countDown), 30)
  const { changeTrack } = useBGM()

  // Auto-navigate to intro page when countdown reaches 0
  useEffect(() => {
    if (day === 0) {
      navigation.navigate('Intro')
    }
  }, [day, navigation])

  // Change to track 5 when countdown page comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Countdown screen focused, changing to track 5')
      changeTrack(5).catch(console.error)
    }, [])
  )

  const dashArray = []
  for (let i = 0; i < day; i++) {
    dashArray.push(dashAngle)
    if (i < day - 1) {
      dashArray.push(2)
    } else {
      dashArray.push(Math.PI * 1000)
    }
  }

  return (
    <View style={styles.container}>
      <Image
        source={require('@images/dall.png')}
        style={styles.background}
      ></Image>
      <Animated.Image
        source={require('@images/countdown_background.png')}
        style={styles.countdown}
      />
      <Svg height={368} width={368} style={styles.countdown}>
        <Circle
          cx={184}
          cy={184}
          r={130}
          fill='none'
          stroke='#C8C8C8'
          strokeWidth={5}
          strokeDasharray={[dashAngle, 2]}
        ></Circle>
      </Svg>
      <Svg height={368} width={368} style={styles.countdown}>
        <Circle
          cx={184}
          cy={184}
          r={130}
          fill='none'
          stroke='#6400FF'
          strokeWidth={5}
          strokeDasharray={dashArray}
        ></Circle>
      </Svg>
      {/* TODO, replace with generated UI element. Not image */}
      {/* <View style={{ display: 'flex', flexDirection: 'row', justifyContent: 'center', width: '100%', position: 'absolute', top: insets.top + 230, gap: 8 }}>
        {day.toString().split('').map((v, i) => {
          return (
            <Image key={i} source={{ uri: `${RESOURCE_HOST}countdown_${v}.png` }} style={{ overflow: 'visible', objectFit: 'cover', width: 100, height: 123 }} />
          )
        })}
      </View> */}
      <View style={styles.countdownNumber}>
        <Text style={[styles.countdownText, styles.countdownOutline]}>{day}</Text>
        <Text style={[styles.countdownText, styles.countdownFill]}>{day}</Text>
      </View>
      <Image
        source={require('@images/leftdays.png')}
        style={{ width: 132, height: 18, position: 'absolute', top: insets.top + 350 }}
      ></Image>
      <Animated.View
        style={{ ...{ bottom: insets.bottom + 80 }, ...styles.lottieImage }}
      >
        {data !== undefined && (
          <LottieView
            source={data}
            loop={true}
            autoPlay
            style={styles.image}
          />
        )}
      </Animated.View>
      <Bubble
        style={{ ...{ bottom: insets.bottom + 28 }, ...styles.bubble }}
        corner={BubbleCorner.top}
        onClick={() => navigation.navigate('Intro')}
        pattern="{days}"
        replace={<Text key={1}>{day}</Text>}
        text={t('count_down')}
      />
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    alignItems: 'center'
  },
  countdown: {
    position: 'absolute',
    marginLeft: 'auto',
    marginRight: 'auto',
    width: 368,
    height: 368,
    top: UnistylesRuntime.insets.top + 124
  },
  lottieImage: {
    position: 'absolute',
    left: UnistylesRuntime.screen.width / 2 - 100,
    width: 100 * 2,
    height: 93 * 2
  },
  image: {
    position: 'absolute',
    width: 100 * 2,
    height: 93 * 2
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  },
  bubble: {
    position: 'absolute',
    left: 20,
    right: 20
  },
  countdownNumber: {
    position: 'absolute',
    top: UnistylesRuntime.insets.top + 280,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  countdownText: {
    position: 'absolute',
    fontWeight: '900',
    textAlign: 'center',
    includeFontPadding: false,
    textAlignVertical: 'center',
  },
  countdownOutline: {
    fontSize: 110,
    color: '#FFFFFF',
    textShadowColor: '#FFFFFF',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 0,
  },
  countdownFill: {
    fontSize: 100,
    color: '#6400FF',
    textShadowColor: 'transparent',
  },
}))

export default CountDown
