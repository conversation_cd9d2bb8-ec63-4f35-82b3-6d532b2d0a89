import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { updateCurrentScore, updateMarks } from '@pages/home/<USER>'
import { addMarkScore } from '@network/userScore'
import { type ShowDetail, type MarkDetail, changeCollect, type ShowDetailItem } from '@network/map'
import React, { useEffect, useState } from 'react'
import { Image, Text, TouchableOpacity, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import dayjs from 'dayjs'
import { useMutation } from '@tanstack/react-query'
interface ShowProps {
  navigation: any
  mark?: MarkDetail
}

export default function Show({ navigation, mark }: ShowProps): React.JSX.Element {
  const { styles, theme } = useStyles(styleSheet)
  const [collected, setCollected] = useState(mark?.collected ?? false)
  const reloadMarks = updateMarks(undefined)
  const { mutate: change } = useMutation({
    mutationFn: changeCollect,
    onSuccess: (result) => {
      if (!result.success) return
      setCollected(!collected)
      reloadMarks()
    }
  })
  useEffect(() => {
    if (mark !== undefined) {
      setCollected(mark?.collected ?? false)
    }
  }, [mark])

  const reloadScore = updateCurrentScore(true)
  const { mutate: addMark } = useMutation({
    mutationFn: addMarkScore,
    onSuccess: (_result) => {
      reloadScore()
    }
  })

  useEffect(() => {
    if (mark?.id === undefined || !(mark?.showPoint)) return
    addMark(mark.id)
    return () => {
      if (mark?.showPoint) {
        reloadMarks()
      }
    }
  }, [])
  if (mark?.content === null) {
    return <View />
  }
  // Create fallback content if missing
  const content = mark?.content as ShowDetail || {
    shows: [{
      id: 0,
      name: mark?.name || 'Event',
      description: 'Event details not available',
      thumbnailUrl: 'https://via.placeholder.com/300x200?text=Event+Image',
      beginTime: new Date(),
      endTime: new Date(),
      createAt: new Date(),
      updateAt: new Date(),
      markId: mark?.id?.toString() || '0',
      userId: 0
    }]
  }

  return (
    <View style={{ flex: 1, alignItems: 'center' }}>
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%', overflow: 'hidden', borderRadius: 14 }} />
      <View style={styles.handler} hitSlop={{ top: 50, bottom: 50, left: 10, right: 10 }} />
      <BottomSheetScrollView style={styles.container}>
        <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 8 }}>
          <Text style={styles.headerDate}>{mark?.areaName}</Text>
          <View style={{ width: 2, height: 14, backgroundColor: theme.colors.pink }} />
          <Text style={styles.headerDate}>{`${new Date().getMonth()}-${new Date().getDate()}`}</Text>
        </View>
        {content.shows?.map((show, index) => {
          return <Event key={index} show={show} />
        })}
        <TouchableOpacity style={{ position: 'absolute', right: 10, top: 10 }}
          onPress={(_e) => {
            if (mark?.id !== null && mark?.id !== undefined) {
                change({ collected: !collected, markId: mark.id })
            }
          }}>
          <Image source={collected ? require('@images/favorite.png') : require('@images/unfavorite.png')} style={{ width: 24, height: 31 }} />
        </TouchableOpacity>
      </BottomSheetScrollView>
    </View>
  )
}
function Event({ show }: { show: ShowDetailItem }): React.JSX.Element {
  const { styles, theme } = useStyles(styleSheet)
  return (
    <View style={styles.event}>
      <Text style={{ color: theme.colors.typography, fontSize: 20, fontWeight: '700', width: '100%' }}>{show.name}</Text>
      <View style={styles.imageContainer}>
        <Image source={{ uri: show.thumbnailUrl }} style={{ width: '100%', height: '100%' }} />
      </View>
      <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'flex-start' }}>
        <View style={{ backgroundColor: theme.colors.pink, padding: 9, borderRadius: 6 }}>
          <Text style={styles.eventDate}>{`${dayjs(show.beginTime).format('HH:mm')}~${dayjs(show.endTime).format('HH:mm')}`}</Text>
        </View>
      </View>
      <Text style={styles.eventContent}>{show.description}</Text>
    </View>

  )
}
const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    width: UnistylesRuntime.screen.width,
    marginBottom: UnistylesRuntime.insets.bottom
  },
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginVertical: 10
  },
  eventDate: {
    color: theme.colors.typography,
    fontSize: 14,
    fontWeight: '900'
  },
  eventContent: {
    color: theme.colors.typography,
    fontSize: 15,
    fontWeight: '400'
  },
  event: {
    display: 'flex',
    gap: 20,
    marginTop: 20
  },
  imageContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    height: 188,
    borderRadius: 6,
    gap: 6,
    overflow: 'hidden'
  },
  headerDate: {
    fontSize: 14,
    fontWeight: '900',
    color: theme.colors.pink
  }
}))
