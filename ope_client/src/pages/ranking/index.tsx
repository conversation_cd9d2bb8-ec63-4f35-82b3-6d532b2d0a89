import { RESOURCE_HOST } from '@context/utils'
import { getAllUserScoreRankings, getMyUserScoreRankings, type RankResult } from '@network/userScore'
import Header from '@pure/Header'
import LoadMore from '@pure/LoadMore'
import Loading from '@pure/Loading'
import { useMutation, useQuery } from '@tanstack/react-query'
import { type NaviProps } from '@type/index'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useFocusEffect } from '@react-navigation/native'
import { FlatList, Image, RefreshControl, Text, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { useBGM } from '@services/bgmService'

const pageSize = 100
export default function Ranking({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const [data, setData] = useState<RankResult[]>([])
  // const [myRank, setMyRank] = useState<RankResult | undefined>(undefined)
  const [hasMore] = useState(false)
  const page = useRef(0)
  const { changeTrack } = useBGM()
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: getAllUserScoreRankings,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      if (page.current === 0) {
        setData(result.data.rankResults)
      } else {
        setData(prevData => [...prevData, ...result.data.rankResults])
      }
    }
  })
  const { data: myRank } = useQuery({
    queryKey: ['myRank'],
    queryFn: getMyUserScoreRankings
  })

  useEffect(() => {
    run(pageSize)
  }, [])

  // Change to track 5 when rankings page comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Rankings screen focused, changing to track 5')
      changeTrack(5).catch(console.error)
    }, [])
  )
  if (loading && data.length === 0) {
    return (
      <View style={styles.view}>
        <Header blur title={t('ranking')} onClickBack={() => { navigation.goBack() }}></Header>
        <Loading />
      </View>
    )
  }
  return (
    <View style={styles.view}>
      <Image source={require('@images/launch_background.png')}
        style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <FlatList
        style={styles.container}
        contentInset={{ top: 0, left: 0, bottom: UnistylesRuntime.insets.bottom + 220, right: 0 }}
        refreshControl={
          <RefreshControl
            tintColor={'white'}
            progressViewOffset={120}
            onRefresh={() => {
              if (loading) return
              page.current = 0
              run(pageSize)
            }}
            refreshing={loading}>
          </RefreshControl>
        }
        data={data}
        renderItem={({ item }) => <RankingItem item={item} />}
        keyExtractor={(item) => item.rank.toString()}
        onEndReached={() => {
          if (loading) return
          if (hasMore) {
            page.current += 1
            run(pageSize)
          }
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={() => {
          return (<LoadMore loading={loading} hasMore={hasMore} />)
        }}
      >
      </FlatList >
      <Header blur title='Ranking' onClickBack={() => { navigation.goBack() }}></Header>
      {(myRank?.success === true && myRank.data !== null) && <MyRankingItem item={myRank.data} />}
      {(myRank?.success === false) && (
        <View style={[styles.myContent, { backgroundColor: '#ff6b6b' }]}>
          <Text style={[styles.title, { fontSize: 16, color: 'white' }]}>Failed to load ranking</Text>
        </View>
      )}
    </View >
  )
}

function RankingItem(props: { item: RankResult }): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const { item } = props
  let url
  switch (item.rank) {
    case 1:
      url = `${RESOURCE_HOST}1st_place.png`
      break
    case 2:
      url = `${RESOURCE_HOST}2nd_place.png`
      break
    case 3:
      url = `${RESOURCE_HOST}3rd_place.png`
      break
  }
  return (
    <View>
      <View style={styles.content}>
        <Text numberOfLines={1} style={styles.title}>{item.rank}</Text>
        <Text numberOfLines={1} style={[styles.title, { flexShrink: 1 }]}>{item.nickName}</Text>
        <View style={{ flexGrow: 1, display: 'flex', flexDirection: 'row-reverse', flexShrink: 0 }}>
          <View style={styles.score}>
            <Image source={require('@images/polygon.png')}
              style={{ width: 17, height: 22, marginTop: -10, marginLeft: -4 }} />
            <Text style={styles.scoreText}>{item.score.toLocaleString()}</Text>
          </View>
        </View>
      </View>
      {url !== undefined && <Image source={{ uri: url }} style={styles.icon} />}
    </View>
  )
}

function MyRankingItem(props: { item: RankResult }): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const { item } = props
  return (
    <View>
      <View style={styles.myContent}>
        <Text numberOfLines={1} style={[styles.title, { fontSize: 20 }]}>{item.rank}</Text>
        <Text numberOfLines={1} style={[styles.title, { flexShrink: 1, fontSize: 20 }]}>{item.nickName}</Text>
        <View style={{ flexGrow: 1, display: 'flex', flexDirection: 'row-reverse', flexShrink: 0 }}>
          <View style={[styles.score, { backgroundColor: '#595959', height: 28 }]}>
            <Image source={require('@images/polygon.png')}
              style={{ width: 17, height: 22, marginTop: -10, marginLeft: -4 }} />
            <Text style={[styles.scoreText, { fontSize: 16 }]}>{item.score.toLocaleString()}</Text>
          </View>
        </View>

      </View>

    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  container: {
    paddingTop: UnistylesRuntime.insets.top + 80
  },
  content: {
    height: 40,
    borderRadius: 40,
    overflow: 'hidden',
    backgroundColor: theme.colors.grey,
    marginVertical: 4,
    marginHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 30,
    paddingLeft: 32,
    paddingRight: 8
  },
  icon: {
    position: 'absolute',
    left: 10,
    top: 12,
    width: 26,
    height: 23
  },
  title: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
  },
  scoreText: {
    color: theme.colors.border,
    fontSize: 14,
    fontWeight: '900'
  },
  score: {
    height: 24,
    backgroundColor: theme.colors.background,
    borderRadius: 24,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    gap: 5
  },
  myContent: {
    height: 64,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: theme.colors.lightGrey,
    marginVertical: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 30,
    paddingLeft: 32,
    paddingRight: 8,
    position: 'absolute',
    bottom: 20 + UnistylesRuntime.insets.bottom,
    left: 10,
    right: 10,
    borderWidth: 2,
    borderColor: 'rgba(214, 214, 214, 0.20)'
  }

}))
