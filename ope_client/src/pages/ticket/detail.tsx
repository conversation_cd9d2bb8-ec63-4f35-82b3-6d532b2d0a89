import React, { useRef, useState } from 'react'
import { useAccountStore } from '@context/store'
import { checkTicket } from '@network/ticket'
import Header from '@pure/Header'
import { Pressable, Image, View, Text, TouchableOpacity } from 'react-native'
import { Button } from '@pure/Button'
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSpring,
  withTiming,
  cancelAnimation
} from 'react-native-reanimated'

import { Canvas, type DataSourceParam, Fill, Group, Image as SKImage, useImage,  Path, Skia } from '@shopify/react-native-skia'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import polygon from '@images/launch_vector_player.png'
import vectorBack from '@images/launch_vector_back.png'
import vector1 from '@images/launch_vector_1.png'
import { type NaviProps } from '@type/index'
import Loading from '@pure/Loading'
import Toast from 'react-native-root-toast'
import { useTranslation } from 'react-i18next'
import { useMutation } from '@tanstack/react-query'

const width = UnistylesRuntime.screen.width - 60
const ticketRightWidth = 100
const ticketLeftWidth = width - ticketRightWidth
const ticketHeight = 150
const radius = 10

const path = Skia.Path.Make()
path.moveTo(0, radius)
path.addArc({ x: -radius, y: -radius, width: radius * 2, height: radius * 2 }, 90, -90)
path.lineTo(radius, 0)
path.lineTo(ticketLeftWidth, 0)

let step = 8
while (step < ticketHeight) {
  path.lineTo(ticketLeftWidth, step)
  path.lineTo(ticketLeftWidth + 3, step)
  path.lineTo(ticketLeftWidth + 3, step + 4)
  path.lineTo(ticketLeftWidth, step + 4)
  step += 8 + 4
}
path.lineTo(ticketLeftWidth, ticketHeight)
path.lineTo(radius, ticketHeight)
path.addArc({ x: -radius, y: ticketHeight - radius, width: radius * 2, height: radius * 2 }, 0, -90)
path.lineTo(0, ticketHeight - radius)
path.lineTo(0, radius)

export default function Ticket({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const activeTicket = useAccountStore((state) => state.activeTicket)
  const ticketUsed = useRef(activeTicket)
  const fullScreen = useRef(false)
  const { styles, theme } = useStyles(styleSheet)
  const playerX = useSharedValue(-20)
  const vectorFX = useSharedValue(-5)
  const vectorX = useSharedValue(10)
  const rotate = useSharedValue(0)
  const transY = useSharedValue(0)
  const rightTicketRotate = useSharedValue(0)
  const rightTicketOpicaty = useSharedValue(ticketUsed.current ? 0 : 1)
  const boomOpacity = useSharedValue(0)
  const boomScale = useSharedValue(0)
  const scale = useSharedValue(1)
  const opacity = useSharedValue(1)
  const ticketFlash = useSharedValue(1)
  const [pointerEvents, setPointerEvents] = useState<any>('auto')
  const notiOpacity = useSharedValue(0)
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotateZ: `${rotate.value}deg` }, { scale: scale.value }, { translateX: transY.value }]
    }
  })
  const rightTicketanimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: rightTicketOpicaty.value,
      transform: [{ rotate: `${rightTicketRotate.value}deg` }]
    }
  })
  const ticketCode = useAccountStore((state) => state.ticketCode) ?? ''
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: checkTicket,
    onSuccess: (result) => {
      if (result.success) {
        rightTicketRotate.value = withSpring(30)
        boomOpacity.value = withSpring(1)
        boomScale.value = withSpring(1.5, { damping: 100, mass: 2 })
        ticketUsed.current = true
      } else {
        Toast.show(result.msg, { position: Toast.positions.CENTER })
      }
    }
  })
  const backgroundWidth = useSharedValue(UnistylesRuntime.screen.width - 30)
  const backgroundHeight = useSharedValue(278)
  const backgroundRadius = useSharedValue(20)
  return (
    <View style={{ flex: 1 }}>
      {loading && <Loading />}
      <View style={{ flex: 1, backgroundColor: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Image source={require('@images/launch_background_white.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
        <Animated.View style={[{ display: 'flex', gap: 15, justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', height: backgroundHeight, width: backgroundWidth, borderRadius: backgroundRadius, shadowColor: 'black', shadowRadius: 5, shadowOffset: { height: 1, width: 1 }, shadowOpacity: 0.1 }]}>
          <Animated.View style={[{ backgroundColor: 'white', height: ticketHeight, width }, animatedStyle]}>
            <Canvas style={{ position: 'absolute', height: ticketHeight, width: ticketLeftWidth + 4, overflow: 'visible' }}>
              <Group clip={path}>
                <Fill color="#EAEAEA"></Fill>
                <SKImage image={useImage(vectorBack as DataSourceParam)} width={ticketLeftWidth * 2.5} height={ticketHeight * 2.5} fit={'contain'} x={-140} y={vectorFX} />
                <SKImage image={useImage(vector1 as DataSourceParam)} width={ticketLeftWidth * 2.5} height={ticketHeight * 2.5} fit={'contain'} x={-140} y={vectorX} transform={[{ rotate: -0.3 }]} />
                <SKImage image={useImage(polygon as DataSourceParam)} width={ticketLeftWidth * 2.5} height={ticketHeight * 2.5} fit={'contain'} x={-140} y={playerX} />
              </Group>
              <Path path={`M ${ticketLeftWidth} ${ticketHeight - 2} L ${radius + 2} ${ticketHeight - 2} Q ${radius} ${ticketHeight - radius} ${2} ${ticketHeight - radius}L 2 ${ticketHeight - radius - 2}  L 2 ${radius} Q ${radius} ${radius} ${radius + 2} 2 L ${ticketLeftWidth} 2`} style={'stroke'} strokeWidth={4} color={'#D1D1D1'} opacity={0.7}></Path>
            </Canvas >
            <Animated.View style={[rightTicketanimatedStyle, { position: 'absolute', transformOrigin: [0, ticketHeight + 20, 0], left: ticketLeftWidth }]}>
              <Pressable onPress={() => {
                if (fullScreen.current) {
                  run(ticketCode)
                }
              }}>
                <View style={{ width: ticketRightWidth, height: ticketHeight }}>
                  <Image source={require('@images/ticket_right.png')} style={{ position: 'absolute', width: '100%', height: '100%', resizeMode: 'stretch' }} />
                  <Animated.Image source={require('@images/ticket_right_title.png')} style={[{ opacity: ticketFlash }, { position: 'absolute', width: 34, height: 125, left: 30, top: 12 }]} />
                </View>
              </Pressable>
            </Animated.View>
            <Animated.Image source={require('@images/ticket_boom.png')} style={[{ opacity: boomOpacity }, { position: 'absolute', left: 280, top: -40, width: 50, height: 80, resizeMode: 'stretch', transform: [{ rotate: '90deg' }, { scale: boomScale }] }]} />
          </Animated.View>
          <Animated.View style={{ opacity }}>
            <Button
              style={styles.button}
              text={t('admission')}
              onPress={() => {
                opacity.value = withSpring(0)
                setPointerEvents('none')
                transY.value = withSpring(-50)
                notiOpacity.value = withSpring(1)
                rotate.value = withSpring(-90, { damping: 10000, mass: 1 })
                scale.value = withSpring(1.5, { damping: 10000, mass: 1 })
                backgroundWidth.value = withSpring(UnistylesRuntime.screen.width, { damping: 100, mass: 1 })
                backgroundHeight.value = withSpring(UnistylesRuntime.screen.height, { damping: 100, mass: 1 })
                backgroundRadius.value = withSpring(0, { damping: 100, mass: 1 })
                playerX.value = withRepeat(withTiming(0, { duration: 2000 }), -1, true)
                vectorFX.value = withRepeat(withTiming(0, { duration: 2000 }), -1, true)
                vectorX.value = withRepeat(withTiming(0, { duration: 2000 }), -1, true)
                ticketFlash.value = withRepeat(withTiming(0, { duration: 800 }), -1, true)
                fullScreen.current = true
              }}></Button>
          </Animated.View>
        </Animated.View>
      </View>
      <Animated.View style={{ position: 'absolute', top: 0 + UnistylesRuntime.insets.top, left: 0, width: '100%', opacity: notiOpacity, display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'row' }}>
        <TouchableOpacity style={{ position: 'absolute', right: 10, top: 0 }}
          onPress={() => {
            opacity.value = withSpring(1)
            setPointerEvents('auto')
            transY.value = withSpring(0)
            notiOpacity.value = withSpring(0)
            rotate.value = withSpring(0, { damping: 10000, mass: 1 })
            scale.value = withSpring(1, { damping: 10000, mass: 1 })
            backgroundWidth.value = withSpring(UnistylesRuntime.screen.width - 30, { damping: 100, mass: 1 })
            backgroundHeight.value = withSpring(278, { damping: 100, mass: 1 })
            backgroundRadius.value = withSpring(20, { damping: 100, mass: 1 })
            cancelAnimation(playerX)
            playerX.value = withSpring(-20)
            cancelAnimation(vectorFX)
            vectorFX.value = withSpring(-10)
            cancelAnimation(vectorX)
            vectorX.value = withSpring(20)

            cancelAnimation(ticketFlash)
            ticketFlash.value = withSpring(1)
            fullScreen.current = false
            if (ticketUsed.current) {
              boomOpacity.value = withSpring(0)
              rightTicketOpicaty.value = withSpring(0)
            }
          }}
        >
          <Image style={{ width: 48, height: 48 }} source={require('@images/white_close.png')}></Image>
        </TouchableOpacity>
        <View style={{ paddingHorizontal: 34, paddingVertical: 5, backgroundColor: '#F00', borderRadius: 5 }}>
          <Text style={{ color: theme.colors.typography, fontWeight: '700', fontSize: 12 }}>{t('do_not_touch_screen')}</Text>
        </View>
      </Animated.View>
      <Animated.View style={{ position: 'absolute', top: 0, left: 0, width: '100%', opacity, pointerEvents }}>
        <Header mode='light' title={t('ticket_active')} onClickBack={() => {
          navigation.goBack()
        }}></Header>
      </Animated.View>
    </View >

  )
}
const styleSheet = createStyleSheet(_theme => ({
  button: {
    borderRadius: 30,
    width: 172,
    height: 40
  }
}))
