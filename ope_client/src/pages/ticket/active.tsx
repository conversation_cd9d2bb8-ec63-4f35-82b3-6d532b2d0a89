import React, { useRef } from 'react'
import { bindTicket } from '@network/ticket'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { Image, Text, TextInput, View } from 'react-native'
import Header from '@pure/Header'
import { type NaviProps } from '@type/index'
import { Button } from '@pure/Button'
import Toast from 'react-native-root-toast'
import Loading from '@pure/Loading'
import { useTranslation } from 'react-i18next'
import { useMutation } from '@tanstack/react-query'

// import { Container } from './styles';

export default function ActiveTicket({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const text = useRef('')
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: bindTicket,
    onSuccess: (result) => {
      if (!result.success) {
        Toast.show(result.msg, { position: Toast.positions.CENTER })
      } else {
        navigation.replace('Ticket')
      }
    }
  })
  return (
    <View style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
      <Image source={require('@images/launch_background_white.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <Header mode='light' title={t('ticket_active')} onClickBack={() => {
        navigation.goBack()
      }}></Header>
      {loading &&
        <Loading />}
      <Text style={styles.code}>{t('activate_code')}</Text>
      <TextInput secureTextEntry={true} style={styles.input} placeholder={t('enter_code')}
        onChangeText={(e) => {
          text.current = e
        }}
      ></TextInput>
      <Button
        style={styles.button}
        text={t('confirm')}
        onPress={() => {
          run(text.current)
        }}></Button>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  button: {
    borderRadius: 30,
    width: 289,
    height: 56
  },
  code: {
    marginLeft: 16,
    fontWeight: '900',
    fontSize: 16,
    alignSelf: 'flex-start',
    marginTop: 40,
    marginBottom: 10
  },
  input: {
    borderRadius: 5,
    paddingHorizontal: 10,
    width: UnistylesRuntime.screen.width - 32,
    height: 48,
    backgroundColor: 'white',
    marginBottom: 40
  }
}))
