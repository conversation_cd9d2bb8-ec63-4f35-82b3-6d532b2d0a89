import { useAccountStore } from '@context/store'
import React from 'react'
import { Image, TouchableOpacity, View } from 'react-native'
import AnimatedNumbers from 'react-native-animated-numbers'

import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

function Score({ navigation }: { navigation: any }): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const score = useAccountStore((state) => state.score)
  return (
    <View style={[styles.top]}>
      <TouchableOpacity style={[styles.rankBtn]}
        onPress={() => {
          navigation.navigate('Rewards')
        }}
      >
        <Image source={require('@images/polygon.png')} style={{ width: 26, height: 34, marginTop: -14 }} />
        <AnimatedNumbers
          includeComma
          fontStyle={styles.text}
          animateToNumber={score}
        />
      </TouchableOpacity>
    </View>
  )
}
const styleSheet = createStyleSheet(theme => ({
  rankBtn: {
    backgroundColor: theme.colors.typography,
    paddingHorizontal: 8,
    height: 26,
    borderRadius: 21,
    borderWidth: 2,
    borderColor: '#D6D6D6',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  top: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: 5,
    position: 'absolute',
    left: 20,
    top: UnistylesRuntime.insets.top + 24
  },
  text: {
    color: theme.colors.lightGrey,
    fontWeight: '900',
    fontSize: 14

  }

}))

export default Score
