import React from 'react'
import { useAccountStore } from '@context/store'
import { type StyleProp, View, type ViewStyle, Image, Text, TouchableOpacity, type GestureResponderEvent } from 'react-native'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import { useTranslation } from 'react-i18next'

export default function Ticket(props: { style: StyleProp<ViewStyle>, onPress?: ((event: GestureResponderEvent) => void) | undefined }): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const ticketCode = useAccountStore((state) => state.ticketCode) ?? ''
  return (
    <TouchableOpacity style={props.style}
      onPress={props.onPress}
    >
      <View style={styles.container}>
        <Image source={require('@images/ticket.png')} style={{ width: 20, height: 20, overflow: 'visible' }} />
        {ticketCode.length === 0 &&
          <>
            <Text style={styles.text}>{t('ticket_active')}</Text>
            <Image source={require('@images/arrow.png')} style={{ width: 15, height: 15, overflow: 'visible', marginLeft: 10 }} />
          </>
        }
      </View>
    </TouchableOpacity>
  )
}
const styleSheet = createStyleSheet(theme => ({
  container: {
    height: 56,
    borderTopLeftRadius: 56,
    borderBottomLeftRadius: 56,
    backgroundColor: theme.colors.purple,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingHorizontal: 20,
    borderColor: '#5707D3',
    borderWidth: 2
  },
  text: {
    color: theme.colors.typography,
    fontWeight: '900',
    fontSize: 16
  }
}))
