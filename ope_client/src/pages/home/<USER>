import { useMapStore } from '@context/map'
import { useAccountStore } from '@context/store'
import { type MarksResp, getMarks } from '@network/map'
import { type Ticket, getTicket } from '@network/ticket'
import { type OPEData } from '@network/type'
import { getMyUserScoreRankings, getCurrentScore } from '@network/userScore'
import { useMutation } from '@tanstack/react-query'
import { useEffect } from 'react'

export function updateScore(manual = false): () => void {
  const updateScore = useAccountStore((state) => state.updateScore)
  const { mutate: run } = useMutation({
    mutationFn: getMyUserScoreRankings,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      updateScore(result.data.score)
    }
  })
  useEffect(() => {
    if (!manual) {
      run()
    }
  }, [])
  return run
}

export function updateCurrentScore(manual = false): () => void {
  const updateScore = useAccountStore((state) => state.updateScore)
  const { mutate: run } = useMutation({
    mutationFn: getCurrentScore,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      updateScore(result.data)
    }
  })
  useEffect(() => {
    if (!manual) {
      run()
    }
  }, [])
  return run
}

export function updateMarks(onSuccess?: (result: OPEData<MarksResp>) => void): () => void {
  const setMarksResp = useMapStore((state) => state.setMarksResp)
  const { mutate: run } = useMutation({
    mutationFn: getMarks,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      setMarksResp(result.data)
      onSuccess?.(result)
    }
  })
  return run
}

export function updateTicket(onSuccess?: (result: OPEData<Ticket>) => void, manual = true): { run: () => void, loading: boolean } {
  const updateTicket = useAccountStore((state) => state.updateTicketCode)
  const updateActiveTicket = useAccountStore((state) => state.updateActiveTicket)
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: getTicket,
    onSuccess: (result) => {
      updateTicket(result.data === null ? '' : result.data.code)
      updateActiveTicket(result.data === null ? false : result.data.checkedAt !== null)
      onSuccess?.(result)
    }
  })
  return { run, loading }
}
