import BottomSheet, { BottomSheetModal, BottomSheetModalProvider, BottomSheetBackdrop } from '@gorhom/bottom-sheet'
import { ReactNativeZoomableView } from '@openspacelabs/react-native-zoomable-view'

import Exhibition from '@pages/detail/exbitionDetail'
import { cloneDeep } from 'lodash'
import <PERSON><PERSON><PERSON>ie<PERSON> from 'lottie-react-native'
import { Drawer } from 'react-native-drawer-layout'
// import Ticket from './ticket'

import { useMapStore } from '@context/map'
import { hapticTrigger, track, usePrefetch } from '@context/utils'
import { getMark, type Mark, type MarkDetail } from '@network/map'
import { type SearchItem } from '@network/search'
import Show from '@pages/countdown/show'
import Mission from '@pages/mission'
import Search, { type SearchType } from '@pages/search'
import SideMenu from '@pages/sideMenu'
import Loading from '@pure/Loading'
import { Pin, SmallPin, type PinType } from '@pure/Pin'
import Bubble, { BubbleCorner } from '@pure/bubble'
import { type NaviProps } from '@type/index'
import { useMutation } from '@tanstack/react-query'
import React, { useCallback, useEffect, useReducer, useRef, useState } from 'react'
import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'
import {
  Alert,
  Image,
  Keyboard,
  Platform,
  ScrollView,
  TouchableOpacity,
  View
} from 'react-native'
import FastImage from 'react-native-fast-image'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import Score from './Score'
import { updateMarks, updateCurrentScore, updateTicket } from './hooks'
import { useTranslation } from 'react-i18next'

const mapScale = 0.5
const maxZoom = 4
const markerZoomThreshold = 1.1 // Threshold for switching to individual markers
const searchHeight = 70
function Home({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const map = useMapStore((state) => state.map)
  const { styles, theme } = useStyles(styleSheet)
  const insets = UnistylesRuntime.insets
  const portaro = usePrefetch('portaro_ease_1.json')
  const { changeTrack, initializeBGM } = useBGM()
  const scrollView = useRef<ScrollView>(null)
  const zoomView = useRef<ReactNativeZoomableView>(null)
  const contentSize = { width: (map?.mapWidth ?? 0) * mapScale, height: (map?.mapHeight ?? 0) * mapScale }
  const offset = { x: contentSize.width / 2, y: contentSize.height / 2 }

  const openDraw = useRef(false)
  const [, forceUpdate] = useReducer((x) => x + 1, 0)
  const [showPortaro, setShowPortaro] = useState(true)

  const searchRef = useRef<BottomSheetModal>(null)
  const searchContentRef = useRef<SearchType>(null)

  const exhiRef = useRef<BottomSheetModal>(null)
  const [exhibitionDetail, setExhibitionDetail] = useState<MarkDetail>()

  const showRef = useRef<BottomSheetModal>(null)
  const [eventDetail, setEventDetail] = useState<MarkDetail>()

  const missionRef = useRef<BottomSheetModal>(null)
  const [missionDetail, setMissionDetail] = useState<MarkDetail>()

  const pinRefs = useRef<(PinType)[]>([])
  const areas = useMapStore((state) => state.areas)

  const smallPinRefs = useRef<PinType[]>([])
  const marks = useMapStore((state) => state.marks)

  const onPressItem = useCallback((item: SearchItem, marks: Mark[]) => {
    const mark = marks.find((mark) => mark.id === item.id)
    if (mark === undefined) return
    if (Platform.OS === 'ios') {
      scrollView.current?.scrollResponderZoomTo({
        x: mark.xaxis * mapScale - UnistylesRuntime.screen.width / markerZoomThreshold / 2,
        y: mark.yaxis * mapScale - UnistylesRuntime.screen.height / markerZoomThreshold / 2,
        width: UnistylesRuntime.screen.width / markerZoomThreshold,
        height: UnistylesRuntime.screen.height / markerZoomThreshold
      })
    } else {
      run(item.id)
    }
  }, [])

  const prevZoom = useRef(1)
  const onZoomChange = useCallback((zoom: number) => {
    const tempZoom = prevZoom.current
    if (tempZoom < markerZoomThreshold && zoom < markerZoomThreshold) return
    if (tempZoom >= markerZoomThreshold && zoom >= markerZoomThreshold) return
    prevZoom.current = zoom
    pinRefs.current.forEach(item => {
      item?.changeVisiable(zoom >= markerZoomThreshold ? 0 : 1)
    })
    smallPinRefs.current.forEach(item => {
      item?.changeVisiable(zoom >= markerZoomThreshold ? 1 : 0)
    })
  }, [])

  updateCurrentScore()

  const updateMark = updateMarks((result) => {
    if (result.data === null) return
    scrollView.current?.scrollResponderZoomTo({
      x: Math.max(result.data.marks[0].xaxis * mapScale - UnistylesRuntime.screen.width / 2, 0),
      y: Math.max(result.data.marks[0].yaxis * mapScale - UnistylesRuntime.screen.height / 2, 0),
      width: UnistylesRuntime.screen.width,
      height: UnistylesRuntime.screen.height
    })
  })

  useEffect(() => {
    updateMark()
    // Initialize BGM with track 3 on first load
    console.log('Home screen mounted, initializing BGM with track 3')
    initializeBGM(3).catch(console.error)
  }, [])

  // Change to track 3 when home screen comes into focus (after navigation)
  useFocusEffect(
    useCallback(() => {
      console.log('Home screen focused, changing to track 3')
      changeTrack(3).catch(console.error)
    }, [])
  )

  const { loading: loadingTicket } = updateTicket((result) => {
    if (result.data === null) {
      navigation.navigate('ActiveTicket')
    } else {
      navigation.navigate('Ticket')
    }
  })

  const showErrorModal = useCallback((message: string) => {
    Alert.alert(
      'Marker Error',
      message,
      [
        {
          text: 'OK',
          style: 'default'
        }
      ]
    )
  }, [])
  // Backdrop component for bottom sheet modals (dark backdrop that closes completely)
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  )

  // Backdrop component for search bottom sheet (transparent backdrop that collapses to minimized state)
  const renderSearchBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={0}
        appearsOnIndex={1}
        opacity={0}
        pressBehavior="collapse"
      />
    ),
    []
  )

  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: getMark,
    onSuccess: (result) => {
      if (result.data === null) {
        console.warn('Marker data is null:', result)
        showErrorModal('No data available for this marker')
        return
      }

      // Log any missing data but don't block the drawer
      if (!result.data.id || !result.data.name || !result.data.markType) {
        console.warn('Some marker fields are missing:', result.data)
      }

      try {
        switch (result.data.markType) {
          case 1: // Game
            // Log missing content but still show the drawer
            if (!result.data.content) {
              console.warn('Game content is missing, showing with fallback data:', result.data)
            }
            setMissionDetail(cloneDeep(result.data))
            missionRef.current?.present()
            // Change to track 2 when marker drawer opens
            changeTrack(2).catch(console.error)
            break
          case 2: // Exhibition
            if (!result.data.content) {
              console.warn('Exhibition content is missing, showing with fallback data:', result.data)
            }
            setExhibitionDetail(cloneDeep(result.data))
            exhiRef.current?.present()
            // Change to track 2 when marker drawer opens
            changeTrack(2).catch(console.error)
            break
          case 3: // Event
            if (!result.data.content) {
              console.warn('Event content is missing, showing with fallback data:', result.data)
            }
            setEventDetail(cloneDeep(result.data))
            showRef.current?.present()
            // Change to track 2 when marker drawer opens
            changeTrack(2).catch(console.error)
            break
          case 4: // Restaurant
          case 5: // Shop
          case 6: // Hotel
          case 7: // Other
            // For now, show these as exhibitions since they have similar content structure
            if (!result.data.content) {
              console.warn(`Marker type ${result.data.markType} content is missing, showing with fallback data:`, result.data)
            }
            setExhibitionDetail(cloneDeep(result.data))
            exhiRef.current?.present()
            // Change to track 2 when marker drawer opens
            changeTrack(2).catch(console.error)
            break
          default:
            console.warn('Unknown marker type:', result.data.markType)
            showErrorModal(`Unknown marker type: ${result.data.markType}`)
        }
      } catch (error) {
        console.error('Error processing marker data:', error, result.data)
        showErrorModal('Failed to load marker details')
      }
    },
    onError: (error) => {
      console.error('Failed to fetch marker data:', error)
      showErrorModal('Failed to load marker information')
    }
  })
  const wrapper = useCallback((children: React.ReactNode) => {
    if (Platform.OS === 'ios') {
      return (
        <ScrollView
          centerContent={true}
          contentOffset={offset}
          directionalLockEnabled={false}
          ref={scrollView}
          onScroll={(e) => {
            onZoomChange(e.nativeEvent.zoomScale)
          }}
          scrollEventThrottle={16}
          contentContainerStyle={contentSize}
          horizontal={true}
          maximumZoomScale={maxZoom}
          minimumZoomScale={0.5}
          bounces={false}
          bouncesZoom={false}
          style={{ backgroundColor: theme.colors.background, paddingBottom: 100 }}
        >
          {children}
        </ScrollView>
      )
    } else {
      return (
        <ReactNativeZoomableView
          zoomStep={2}
          ref={zoomView}
          minZoom={1}
          maxZoom={maxZoom}
          contentWidth={contentSize.width}
          contentHeight={contentSize.height}
          onTransform={(e) => {
            onZoomChange(e.zoomLevel)
          }}
        >
          {children}
        </ReactNativeZoomableView>
      )
    }
  }, [])

  return (
    <BottomSheetModalProvider>
      <Drawer
        drawerPosition='right'
        open={openDraw.current}
        drawerType='slide'
        onOpen={() => { openDraw.current = true }}
        onClose={() => { openDraw.current = false }}
        renderDrawerContent={() => {
          return <SideMenu navigation={navigation} onClickBack={() => { openDraw.current = false; forceUpdate() }} />
        }}
      >
        {(loading || loadingTicket) &&
          <Loading />
        }
        {wrapper(
          <View>
            <FastImage source={{ uri: map?.mapUrl }} style={contentSize} />
            {areas.map((item, idx) => (
              <Pin
                key={idx}
                opacity={1}
                ref={(ref) => {
                  if (ref !== null) {
                    pinRefs.current[idx] = ref
                  }
                }}
                style={[styles.pin, { left: (item.xaxis + item.width / 2) * mapScale, top: (item.yaxis + item.height / 2) * mapScale }]}
                data={item}
                onPressEvent={(e) => {
                  if (Platform.OS === 'ios') {
                    const centerX = (e.xaxis + e.width / 2) * mapScale
                    const centerY = (e.yaxis + e.height / 2) * mapScale
                    scrollView.current?.scrollResponderZoomTo({
                      x: centerX - UnistylesRuntime.screen.width / markerZoomThreshold / 2,
                      y: centerY - UnistylesRuntime.screen.height / markerZoomThreshold / 2,
                      width: UnistylesRuntime.screen.width / markerZoomThreshold,
                      height: UnistylesRuntime.screen.height / markerZoomThreshold
                    })
                  } else {
                    setTimeout(() => {
                      const centerX = (e.xaxis + e.width / 2) * mapScale
                      const centerY = (e.yaxis + e.height / 2) * mapScale
                      void zoomView.current?._zoomToLocation(centerX, centerY, markerZoomThreshold)
                    }, 0)
                  }
                }}
              />
            ))}
            {marks.map((item, idx) => (
              <SmallPin
                key={idx}
                data={item}
                opacity={0}
                ref={(ref) => {
                  if (ref !== null) {
                    smallPinRefs.current[idx] = ref
                  }
                }}
                style={[styles.pin, { left: item.xaxis * mapScale, top: item.yaxis * mapScale }]}
                onPressEvent={(e) => {
                  run(e.id)
                }}
              />
            ))}
          </View>

        )}
        {/* 积分按钮 */}
        <Score navigation={navigation} />
        {/* <Ticket style={{ position: 'absolute', right: -2, top: UnistylesRuntime.insets.top + 80 }}
          onPress={(_e) => {
            runTicket()
          }}
        /> */}
        <TouchableOpacity style={[styles.right, { top: insets.top + 20 }]}
          onPress={() => {
            openDraw.current = true
            forceUpdate()
          }}
        >
          <Image source={require('@images/home_profile.png')} style={styles.icon} />
        </TouchableOpacity>
        <TouchableOpacity style={[styles.right, { bottom: insets.bottom + searchHeight + 20 }]}
          onPress={() => {
            navigation.navigate('Scan')
          }}
        >
          <Image source={require('@images/home_camera.png')} style={styles.icon} />
        </TouchableOpacity>
        {showPortaro && (
          <>
            <View style={{ ...{ bottom: insets.bottom - 20 + searchHeight }, ...styles.portaro }} >
              {portaro !== undefined &&
                <LottieView source={portaro} loop autoPlay style={{ width: '50%', height: '50%', alignSelf: 'center', marginTop: '30%' }} />
              }
            </View>
            <Bubble
              text={t('tap_area')}
              style={{ ...{ bottom: insets.bottom + 28 + searchHeight }, ...styles.bubble }}
              corner={BubbleCorner.left}
              onClick={() => {
                setShowPortaro(false)
              }}
            />
          </>
        )}
        <BottomSheet
          keyboardBehavior='extend'
          keyboardBlurBehavior='none'
          animateOnMount={false}
          backgroundStyle={{ backgroundColor: theme.colors.background }}
          ref={searchRef}
          onAnimate={(_, t) => {
            hapticTrigger()
            if (t === 0) {
              Keyboard.dismiss()
              searchContentRef.current?.onDismiss()
            }
          }}
          index={0}
          snapPoints={[searchHeight + insets.bottom, UnistylesRuntime.screen.height - insets.bottom - insets.top - 100]}
          handleComponent={null}
          backdropComponent={renderSearchBackdrop}
          onChange={(index) => {
            // Load data when the bottom sheet expands (index 1 = expanded)
            if (index === 1) {
              searchContentRef.current?.loadInitialData()
            }
          }}
        >
          <Search
            ref={searchContentRef}
            onFocus={(() => {
              searchRef.current?.snapToIndex(1) // This will trigger onChange with index=1
              track('action_tap_search')
            })}
            onPressItem={(item) => {
              onPressItem(item, marks)
              searchRef.current?.snapToIndex(0)
            }}
          />
        </BottomSheet>
        <BottomSheetModal
          enablePanDownToClose
          snapPoints={[UnistylesRuntime.screen.height - insets.top - 300]}
          index={0}
          ref={exhiRef}
          handleComponent={null}
          backdropComponent={renderBackdrop}
          onDismiss={() => {
            // Return to track 3 when exhibition modal closes
            changeTrack(3).catch(console.error)
          }}
        >
          <Exhibition navigation={navigation} mark={exhibitionDetail} />
        </BottomSheetModal>
        <BottomSheetModal
          enablePanDownToClose
          snapPoints={[insets.bottom + 500]}
          index={0}
          ref={missionRef}
          handleComponent={null}
          backdropComponent={renderBackdrop}
          onDismiss={() => {
            // Return to track 3 when mission modal closes
            changeTrack(3).catch(console.error)
          }}
        >
          <Mission navigation={navigation} mark={missionDetail} />
        </BottomSheetModal>
        <BottomSheetModal
          enablePanDownToClose
          snapPoints={[UnistylesRuntime.screen.height - insets.top - 300]}
          index={0}
          ref={showRef}
          handleComponent={null}
          backdropComponent={renderBackdrop}
          onDismiss={() => {
            // Return to track 3 when show modal closes
            changeTrack(3).catch(console.error)
          }}
        >
          <Show navigation={navigation} mark={eventDetail} />
        </BottomSheetModal>

      </Drawer>
    </BottomSheetModalProvider >
  )
}

const styleSheet = createStyleSheet(_theme => ({
  right: {
    position: 'absolute',
    right: 20
  },
  icon: {
    width: 48,
    height: 48
  },
  portaro: {
    position: 'absolute',
    left: -50,
    width: 180,
    height: 180
  },
  bubble: {
    position: 'absolute',
    left: 84,
    right: 80
  },
  pin: {
    position: 'absolute'
  }
}))

export default Home
