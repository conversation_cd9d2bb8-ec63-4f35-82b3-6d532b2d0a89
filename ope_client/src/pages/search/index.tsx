import { BottomSheetFlatList, BottomSheetTextInput, BottomSheetView } from '@gorhom/bottom-sheet'
import { MarkType, changeCollect } from '@network/map'
import { searchWord, type SearchItem } from '@network/search'
import { collectList } from '@network/map'
import { updateMarks } from '@pages/home/<USER>'
import { hapticTrigger } from '@context/utils'
import { useMapStore } from '@context/map'
import Loading from '@pure/Loading'
import { useMutation } from '@tanstack/react-query'
import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Image, Pressable, ScrollView, Text, TouchableOpacity, View } from 'react-native'
import Animated, { useSharedValue, withSpring } from 'react-native-reanimated'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

enum DrawerMode {
  SEARCH = 'search',
  COLLECTIONS = 'collections',
  FILTER = 'filter'
}

interface SearchProps {
  onFocus?: () => void
  onPressItem: (item: SearchItem) => void
}
export interface SearchType {
  onDismiss: () => void
  loadInitialData: () => void
}

const Search = forwardRef<SearchType, SearchProps>((props: SearchProps, ref) => {
  const { t } = useTranslation()
  const { onFocus } = props
  const { styles } = useStyles(styleSheet)
  const textInputRef = useRef<any>(null)
  const width = useSharedValue(0)
  const opacity = useSharedValue(1)
  const contentOpacity = useSharedValue(0)

  const [drawerMode, setDrawerMode] = useState<DrawerMode>(DrawerMode.SEARCH)
  const [searchList, setSearchList] = useState<SearchItem[]>([])
  const collectionsContentRef = useRef<{ updateSearch: (text: string) => void }>(null)
  const { mutate: runSearch, isPending: loading } = useMutation({
    mutationFn: searchWord,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      setSearchList(result.data.list)
    }
  })

  // Load all exhibitors on component mount since the component is always partially visible
  useEffect(() => {
    runSearch('') // Empty string will return all exhibitors
  }, [])

  useImperativeHandle(ref, () => ({
    onDismiss() {
      setDrawerMode(DrawerMode.SEARCH)
      contentOpacity.value = withSpring(0, { damping: 20 })
      textInputRef.current?.clear()
      runSearch('') // Reload all exhibitors when dismissing search
    },
    loadInitialData() {
      runSearch('') // Load all exhibitors when search becomes visible
    }
  }))
  return (
    <BottomSheetView style={styles.container}>
      <Image source={require('@images/launch_background.png')} style={styles.image} />
      <View style={styles.handler} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => {
          if (drawerMode === DrawerMode.FILTER) {
            setDrawerMode(DrawerMode.SEARCH)
          } else {
            setDrawerMode(DrawerMode.FILTER)
            onFocus?.() // Expand when switching to Filter mode
          }
        }}>
          <Animated.Image source={require('@images/search_map.png')} style={[styles.button, { opacity }]} />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => {
          if (drawerMode === DrawerMode.COLLECTIONS) {
            setDrawerMode(DrawerMode.SEARCH)
          } else {
            setDrawerMode(DrawerMode.COLLECTIONS)
            // Delay 200ms
            setTimeout(() => {
              onFocus?.() // Expand when switching to Collections mode
            }, 150)
          }
        }}>
          <Animated.Image source={require('@images/search_mark.png')} style={[styles.button, { opacity }]} />
        </TouchableOpacity>
        <Animated.View style={[styles.input, { marginRight: width }]}>
          <Image source={require('@images/search_search.png')} style={{ width: 16, height: 16 }} />
          <BottomSheetTextInput
            ref={textInputRef}
            // blurOnSubmit={false}
            returnKeyType='search'
            enablesReturnKeyAutomatically={true}
            style={{ flex: 1, color: 'white' }}
            onFocus={() => {
              setDrawerMode(DrawerMode.SEARCH)
              onFocus?.() // Always expand when search input is focused
              width.value = withSpring(-120, { damping: 20 })
              opacity.value = withSpring(0, { damping: 20 })
              contentOpacity.value = withSpring(1, { damping: 20 })
            }}
            onBlur={() => {
              width.value = withSpring(0, { damping: 14 })
              opacity.value = withSpring(1, { damping: 20 })
            }}
            onSubmitEditing={(e) => {
              const text = e.nativeEvent.text
              if (drawerMode === DrawerMode.COLLECTIONS) {
                collectionsContentRef.current?.updateSearch(text)
              } else {
                runSearch(text) // Search with text or empty string (shows all)
              }
            }}
            placeholder={t('search_exhibitors')} placeholderTextColor={'#7A7A7A'} />
        </Animated.View>
      </View>
      <View style={{ flex: 1, width: '100%' }}>
        {drawerMode === DrawerMode.SEARCH && (
          <BottomSheetFlatList
            data={searchList}
            renderItem={({ item }) => <SearchItemView showCollections item={item} onPress={() => {
              props.onPressItem(item)
            }} />}
            keyExtractor={(_item, index) => index.toString()}
            style={styles.flat}
            contentContainerStyle={{ flexGrow: 1 }}
          />
        )}
        {drawerMode === DrawerMode.COLLECTIONS && (
          <CollectionsContent ref={collectionsContentRef} onPressItem={props.onPressItem} />
        )}
        {drawerMode === DrawerMode.FILTER && (
          <FilterContent />
        )}
      </View>
      {loading && <Loading />}
    </BottomSheetView >
  )
})

interface SearchItemProps {
  item: SearchItem
  onPress: () => void
  showCollections?: boolean
}

export function SearchItemView(props: SearchItemProps): React.JSX.Element {
  const { styles, theme } = useStyles(styleSheet)
  const { item, showCollections = false } = props
  const [collected, setCollected] = useState(item?.collected ?? false)
  const { mutate: change, isPending: loading } = useMutation({
    mutationFn: changeCollect,
    onSuccess: (result) => {
      if (!result.success) return
      setCollected(!collected)
    }
  })

  // Check if this is a skeleton item
  const isSkeleton = (item as any)?.skeleton === true
  const getMarkTypeColor = (markType: number) => {
    switch (markType) {
      case MarkType.Game: return theme.colors.purple
      case MarkType.Exhibition: return theme.colors.orange
      case MarkType.Event: return theme.colors.pink
      case MarkType.Restaurant: return '#FF4444'
      case MarkType.Shop: return theme.colors.green
      case MarkType.Hotel: return '#4488FF'
      case MarkType.Other: return '#FFAA00'
      default: return theme.colors.purple
    }
  }
  const mainColor = getMarkTypeColor(item.markType)
  return (
    <Pressable style={[styles.headerItem, isSkeleton && { opacity: 0.6 }]} onPress={isSkeleton ? undefined : () => { props.onPress() }}>
      <View style={[styles.headerIcon, { backgroundColor: mainColor }]}>
        <Text style={styles.headerIcon}>{item.name}</Text>
      </View>
      <View style={styles.headerRight}>
        <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 8 }}>
          <Text style={[styles.headerDate, { color: mainColor }]}>{item?.areaName}</Text>
          <View style={{ width: 2, height: 14, backgroundColor: mainColor }} />
          <Text style={[styles.headerDate, { color: mainColor }]}>{`${new Date().getMonth()}-${new Date().getDate()}`}</Text>
        </View>
        <Text style={styles.headerTitle}>{item.contentName}</Text>
      </View>
      <View style={{ flex: 1, flexDirection: 'row-reverse' }}>
        {showCollections && !isSkeleton &&
          <TouchableOpacity
            onPress={(_e) => {
              if (item?.id !== null && item?.id !== undefined) {
                change({ collected: !collected, markId: item.id })
              }
            }}>
            <Image source={collected ? require('@images/favorite.png') : require('@images/unfavorite.png')} style={{ width: 24, height: 31 }} />
          </TouchableOpacity>
        }

      </View>
      {loading && <Loading />}
    </Pressable>
  )
}

Search.displayName = 'Search'

// Minimal Collections Content (no background/handler/header)
const CollectionsContent = forwardRef<{ updateSearch: (text: string) => void }, { onPressItem: (item: SearchItem) => void }>(({ onPressItem }, ref) => {
  const { t } = useTranslation()
  const [collectionList, setCollectionList] = useState<SearchItem[]>([])
  const [excludeType, setExcludeType] = useState<MarkType[]>([])
  const originalCollectionList = useRef<SearchItem[]>([])
  const searchText = useRef<string>('')
  const { styles, theme } = useStyles(styleSheet)

  const { isPending: loading, mutate } = useMutation({
    mutationFn: collectList,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      originalCollectionList.current = result.data.list
      setCollectionList(result.data.list)
    }
  })

  const runUpdateMarks = updateMarks(undefined)
  useEffect(() => {
    mutate({ pageNum: 1, pageSize: 100 })
    return () => runUpdateMarks()
  }, [])

  useEffect(() => {
    setCollectionList(() => {
      return originalCollectionList.current.filter((item) => item.contentName.includes(searchText.current)).filter((item) => !excludeType.includes(item.markType))
    })
  }, [excludeType])

  useImperativeHandle(ref, () => ({
    updateSearch: (text: string) => {
      searchText.current = text
      setCollectionList(() => {
        return originalCollectionList.current.filter((item) => item.contentName.includes(searchText.current)).filter((item) => !excludeType.includes(item.markType))
      })
    }
  }))

  return (
    <View style={{ flex: 1 }}>
      {/* Filter tags - horizontal scrollable */}
      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.tagContainer}
        style={{ maxHeight: 76 }}
      >
        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Game)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Game))
          } else {
            setExcludeType([...excludeType, MarkType.Game])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Game) ? theme.colors.grey : theme.colors.purple }]}>
          <Text style={styles.tagContent}>{t('game')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Exhibition)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Exhibition))
          } else {
            setExcludeType([...excludeType, MarkType.Exhibition])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Exhibition) ? theme.colors.grey : theme.colors.orange }]}>
          <Text style={styles.tagContent}>{t('exhibition')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Event)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Event))
          } else {
            setExcludeType([...excludeType, MarkType.Event])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Event) ? theme.colors.grey : theme.colors.pink }]}>
          <Text style={styles.tagContent}>{t('event')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Restaurant)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Restaurant))
          } else {
            setExcludeType([...excludeType, MarkType.Restaurant])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Restaurant) ? theme.colors.grey : '#FF4444' }]}>
          <Text style={styles.tagContent}>{t('restaurant')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Shop)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Shop))
          } else {
            setExcludeType([...excludeType, MarkType.Shop])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Shop) ? theme.colors.grey : theme.colors.green }]}>
          <Text style={styles.tagContent}>{t('shop')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Hotel)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Hotel))
          } else {
            setExcludeType([...excludeType, MarkType.Hotel])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Hotel) ? theme.colors.grey : '#4488FF' }]}>
          <Text style={styles.tagContent}>{t('hotel')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Other)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Other))
          } else {
            setExcludeType([...excludeType, MarkType.Other])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Other) ? theme.colors.grey : '#FFAA00' }]}>
          <Text style={styles.tagContent}>{t('other')}</Text>
        </Pressable>
      </ScrollView>

      {/* Collections list */}
      <BottomSheetFlatList
        keyboardShouldPersistTaps='always'
        data={loading
          ? Array(3).fill(null).map((_, index) => ({
            id: index,
            contentName: 'Loading...',
            markType: 2 as MarkType,
            collected: false,
            areaId: 0,
            areaName: '',
            description: '',
            eventId: 0,
            showPoint: false,
            markId: 0,
            markName: '',
            name: '',
            thumbnailUrl: '',
            xaxis: 0,
            yaxis: 0,
            skeleton: true
          } as SearchItem & { skeleton: boolean }))
          : collectionList}
        renderItem={({ item }) => <SearchItemView item={item} onPress={() => onPressItem(item)} />}
        keyExtractor={(item, index) => (item as any).skeleton ? `skeleton-${index}` : index.toString()}
        style={styles.flat}
        contentContainerStyle={{ flexGrow: 1 }}
      />
    </View>
  )
})

CollectionsContent.displayName = 'CollectionsContent'

// Minimal Filter Content (no background/handler)
function FilterContent() {
  const { t } = useTranslation()
  const { styles, theme } = useStyles(styleSheet)
  const updateFilter = useMapStore(state => state.updateFilter)
  const filter = useMapStore(state => state.filter)

  return (
    <View style={[styles.filterContainer, { flex: 1 }]}>
      <Pressable style={styles.filterButton} onPress={() => {
        hapticTrigger()
        updateFilter({ ...filter, game: !filter.game })
      }}>
        <Image source={filter.game ? require('@images/filter_mission.png') : require('@images/filter_mission_disable.png')} style={styles.filterImage} />
        <Text style={filter.game ? styles.filterText : styles.filterTextDisable}>{t('game')}</Text>
      </Pressable>

      <Pressable style={styles.filterButton} onPress={() => {
        hapticTrigger()
        updateFilter({ ...filter, exhibition: !filter.exhibition })
      }}>
        <Image source={filter.exhibition ? require('@images/filter_exhibition.png') : require('@images/filter_exhibition_disable.png')} style={styles.filterImage} />
        <Text style={filter.exhibition ? styles.filterText : styles.filterTextDisable}>{t('exhibition')}</Text>
      </Pressable>

      <Pressable style={styles.filterButton} onPress={() => {
        hapticTrigger()
        updateFilter({ ...filter, event: !filter.event })
      }}>
        <Image source={filter.event ? require('@images/filter_event.png') : require('@images/filter_event_disable.png')} style={styles.filterImage} />
        <Text style={filter.event ? styles.filterText : styles.filterTextDisable}>{t('event')}</Text>
      </Pressable>

      <Pressable style={styles.filterButton} onPress={() => {
        hapticTrigger()
        updateFilter({ ...filter, favorite: !filter.favorite })
      }}>
        <Image source={filter.favorite ? require('@images/filter_favorite.png') : require('@images/filter_favorite_disable.png')} style={styles.filterImage} />
        <Text style={filter.favorite ? styles.filterText : styles.filterTextDisable}>{t('favorite')}</Text>
      </Pressable>
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    overflow: 'hidden'
  },
  image: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    right: 0
  },
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginTop: 10
  },
  header: {
    marginTop: 10,
    display: 'flex',
    flexDirection: 'row-reverse',
    alignItems: 'center',
    height: 70,
    paddingHorizontal: 20,
    gap: 20
  },
  input: {
    flex: 1,
    height: 40,
    borderRadius: 5,
    backgroundColor: '#4D4D4D',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingHorizontal: 12,
    color: '#7A7A7A'
  },
  button: {
    width: 40,
    height: 40
  },
  tagContainer: {
    marginVertical: 20,
    paddingHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    height: 36,
    alignItems: 'center'
  },
  tag: {
    paddingHorizontal: 15,
    height: '100%',
    borderRadius: 36,
    borderColor: 'rgba(34, 34, 34, 0.20)',
    borderWidth: 2
  },
  tagContent: {
    color: theme.colors.typography,
    fontSize: 14,
    fontWeight: '900',
    lineHeight: 32
  },
  flat: {
    flex: 1,
    width: UnistylesRuntime.screen.width,
    paddingBottom: UnistylesRuntime.insets.bottom

  },
  headerItem: {
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    marginVertical: 15,
    marginHorizontal: 20
  },
  headerIcon: {
    textAlign: 'center',
    lineHeight: 40,
    color: 'white',
    borderRadius: 20,
    width: 40,
    height: 40,
    fontSize: 20,
    fontWeight: '900'
  },
  headerRight: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
  },
  headerDate: {
    fontSize: 14,
    fontWeight: '900'
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '900',
    color: theme.colors.typography
  },
  filterContainer: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 40,
    justifyContent: 'space-between'
  },
  filterButton: {
    alignItems: 'center',
    gap: 12,
    width: '22%', // 4 columns with some spacing
    minWidth: 70
  },
  filterImage: {
    width: 50,
    height: 50
  },
  filterText: {
    color: theme.colors.typography,
    fontSize: 14,
    fontWeight: '900'
  },
  filterTextDisable: {
    color: theme.colors.lightGrey,
    fontSize: 14,
    fontWeight: '900'
  }
}))

export default Search
