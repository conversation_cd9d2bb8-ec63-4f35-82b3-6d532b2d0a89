import { RESOURCE_HOST } from '@context/utils'
import Header from '@pure/Header'
import PatternText from '@pure/PatternText'
import AlertContent from '@pure/alertContent'
import { type NaviProps } from '@type/index'
import React, { forwardRef, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Dimensions, Text, View, Image } from 'react-native'
import FastImage from 'react-native-fast-image'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Swiper from 'react-native-swiper'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import { create } from 'zustand'

interface AboutRewardsType {
  index: number
  setIndex: (index: number) => void
}
const useAboutRewardsStore = create<AboutRewardsType>((set) => ({
  index: 0,
  setIndex: (index: number) => { set((state: AboutRewardsType) => ({ index })) }
}))

function AboutRewards({ navigation }: NaviProps): React.JSX.Element {
  const swiper = useRef<Swiper>(null)
  const width = Dimensions.get('window').width
  const insets = useSafeAreaInsets()
  const { t } = useTranslation()
  const { styles, theme } = useStyles(styleSheet)
  const setIndex = useAboutRewardsStore((state) => state.setIndex)

  return (
    <View style={styles.container}>
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <Header title={t('about_reward')} onClickBack={() => { navigation.goBack() }}></Header>
      <View style={{ width, height: insets.top }}></View>
      <Swiper style={styles.scroll}
        ref={swiper}
        dot={<View style={{ backgroundColor: '#727272', width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 6, marginBottom: 6 }} />}
        activeDot={<View style={{ backgroundColor: '#D9D9D9', width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 6, marginBottom: 6 }} />}
        onIndexChanged={(index) => {
          setIndex(index)
        }}
      >
        <View style={styles.slide}>
          <AlertContent title={t('about_special_gift')} >
            <FastImage source={{ uri: `${RESOURCE_HOST}yuragi_SDillast03_rough.png` }} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
        <View style={styles.slide}>
          <AlertContent title={t('what_is_energy')}>
            <FastImage source={{ uri: `${RESOURCE_HOST}yuragi_SDillust04_CleanUp_R.png` }} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
        <View style={styles.slide}>
          <AlertContent title={t('about_simple_games')}>
            <FastImage source={{ uri: `${RESOURCE_HOST}yuragi_SDillust05_CleanUp_R.png` }} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
        <View style={styles.slide}>
          <AlertContent title={t('about_reward')}>
            <FastImage source={{ uri: `${RESOURCE_HOST}yuragi_SDillust06_CleanUp_R.png` }} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
      </Swiper>
      <LoopText contents={
        [
          { content: t('about_reward_1'), pattern: '{simple games}', element: <Text key={Date.now()} style={{ color: theme.colors.green }}>{t('simple_game')}</Text> },
          { content: t('about_reward_2'), pattern: '{icon}', element: <Image source={require('@images/mission_marker.png')} key={Date.now()} style={{ width: 12, height: 16 }} /> },
          { content: t('about_reward_3'), pattern: '{icon}', element: <Image source={require('@images/mission_marker.png')} key={Date.now()} style={{ width: 12, height: 16 }} /> },
          { content: t('about_reward_4') }
        ]}
      ></LoopText>
      <View style={{ width, height: 100 }}></View>
    </View >
  )
}
interface LoopTextProps {
  contents: Array<{ content: string, pattern?: string, element?: JSX.Element }>
}
interface LoopTextType {
  changeIndex: (index: number) => void
}

const LoopText = forwardRef<LoopTextType, LoopTextProps>((props: LoopTextProps, ref) => {
  const index = useAboutRewardsStore((state) => state.index)
  const { styles } = useStyles(styleSheet)
  return (
    <PatternText style={styles.bubbleText} text={props.contents[index].content} pattern={props.contents[index].pattern} replace={props.contents[index].element}></PatternText>
  )
})
LoopText.displayName = 'LoopText'

const styleSheet = createStyleSheet(theme => ({
  container: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  scroll: {
    flexGrow: 0
  },
  image: {
    position: 'absolute',
    left: 12,
    width: 108,
    height: 101
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 19,
    marginBottom: 60
  },
  bubbleHighlight: {
    color: theme.colors.green
  },
  bubbleText: {
    color: theme.colors.typography,
    fontSize: 14,
    textAlign: 'center',
    marginHorizontal: 19
  }
}))
export default AboutRewards
