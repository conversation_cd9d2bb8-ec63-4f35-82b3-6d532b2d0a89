import BottomSheet, { BottomSheetBackdrop } from '@gorhom/bottom-sheet'
import { myRewards, type Reward } from '@network/reward'
import Header from '@pure/Header'
import Loading from '@pure/Loading'
import { type NaviProps } from '@type/index'
import { cloneDeep } from 'lodash'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, Image, RefreshControl, Text, TouchableOpacity, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import MyRewardDetail from './myRewardDetail'
import { useQuery } from '@tanstack/react-query'
import { updateCurrentScore } from '@pages/home/<USER>'
import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'

export default function MyRewards({ navigation }: NaviProps): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const { data, isPending: loading, refetch: run } = useQuery({ queryKey: ['myRewards'], queryFn: myRewards })
  const [selectReward, setSelectReward] = useState<Reward | undefined>(undefined)
  const rewardRef = useRef<BottomSheet>(null)
  const { t } = useTranslation()
  const { changeTrack } = useBGM()

  // Backdrop component for bottom sheet
  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  )

  // Get the score update function (hooks must be called at top level)
  const runUpdateScore = updateCurrentScore(true)

  useEffect(() => {
    if (selectReward !== undefined) {
      rewardRef.current?.expand()
    }
  }, [selectReward])

  // Update score and change to track 4 when myRewards page comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('MyRewards screen focused, changing to track 4')
      runUpdateScore()
      changeTrack(4).catch(console.error)
    }, [])
  )
  return (
    <View style={styles.view}>
      {loading &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <Header title={t('my_rewards')} onClickBack={() => { navigation.goBack() }}></Header>
      <Text style={styles.title}>{t('select_prize')}</Text>
      {/* <TouchableOpacity style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', paddingHorizontal: 22, paddingTop: 26, paddingBottom: 10 }}>
        <Text style={styles.exchange}>{t('prize_exchange')}</Text>
        <Image style={styles.icon} source={require('@images/arrow.png')} />
      </TouchableOpacity> */}
      <FlatList
        contentContainerStyle={styles.container}
        refreshControl={
          <RefreshControl
            tintColor={'white'}
            progressViewOffset={20}
            onRefresh={() => {
              if (loading) return
              run()
            }}
            refreshing={loading}>
          </RefreshControl>
        }
        data={data?.data?.myRewards}
        renderItem={({ item }) => <RewardsItem item={item} onPress={() => {
          setSelectReward(cloneDeep(item))
        }} />}
        keyExtractor={(item) => item.id.toString()}
      >
      </FlatList >
      <BottomSheet
        enablePanDownToClose
        snapPoints={[UnistylesRuntime.screen.height - UnistylesRuntime.insets.top - 100]}
        index={-1}
        ref={rewardRef}
        handleComponent={null}
        backdropComponent={renderBackdrop}
      >
        {selectReward !== undefined &&
          <MyRewardDetail item={selectReward} exchangeCallback={(code) => {
            if (code === 200) {
              rewardRef.current?.close()
            }
          }} />
        }

      </BottomSheet>
    </View >
  )
}

function RewardsItem(props: { item: Reward, onPress?: (item: Reward) => void }): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const { item } = props
  const timestamp = new Date(item.boughtAt)
  const dateString = `${timestamp.toLocaleDateString()} ${timestamp.toLocaleTimeString()}`

  return (
    <TouchableOpacity style={styles.item} onPress={() => { props.onPress?.(item) }}>
      <View style={styles.itemContainer}>
        <View style={styles.itemIcon}>
          <Image source={require('@images/gasha_border.png')} style={{ position: 'absolute', width: '100%', height: '100%', borderRadius: 15, resizeMode: 'stretch' }} />
          <Image source={{ uri: item.imageUrl }} style={{ position: 'absolute', left: 4, top: 4, bottom: 4, right: 4, borderRadius: 12 }} />
        </View>
        <View style={styles.itemContent}>
          <Text style={styles.itemTitle}>{item.name}</Text>
          <Text style={styles.itemSubTitle}>{t('acquisition_time')}</Text>
          <Text style={[styles.itemTitle, { fontSize: 14 }]}>{dateString}</Text>
          <Text style={styles.itemSubTitle}>{t('acquisition_point')}</Text>
          <Text style={[styles.itemTitle, { fontSize: 14 }]}>{`${item.pointPrice.toLocaleString()}E`}</Text>
        </View>
      </View>
    </TouchableOpacity>

  )
}

const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  container: {
    paddingTop: 20,
    paddingBottom: UnistylesRuntime.insets.bottom
  },
  content: {
    height: 40,
    borderRadius: 40,
    overflow: 'hidden',
    backgroundColor: theme.colors.grey,
    marginVertical: 4,
    marginHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 30,
    paddingLeft: 32,
    paddingRight: 8
  },
  icon: {
    width: 12,
    height: 12
  },
  exchange: {
    textAlign: 'center',
    flexGrow: 1,
    color: theme.colors.typography,
    fontSize: 12,
    fontWeight: '900'
  },
  title: {
    color: theme.colors.typography,
    fontSize: 14,
    fontWeight: '400',
    paddingHorizontal: 20,
    paddingTop: 30
  },
  item: {
    marginHorizontal: 20,
    marginVertical: 5,
    display: 'flex',
    backgroundColor: '#666666',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#7E7E7E'
  },
  itemContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 20
  },
  itemIcon: {
    width: 100,
    height: 100,
    borderRadius: 5
  },
  itemContent: {
    display: 'flex',
    flex: 1,
    alignSelf: 'center',
    gap: 5

  },
  itemTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '700'
  },
  itemSubTitle: {
    fontSize: 10,
    color: theme.colors.typography,
    fontWeight: '400'
  }
}))
