import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { type Reward } from '@network/reward'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Image, Text, View } from 'react-native'
import QRCode from 'react-native-qrcode-svg'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

export default function MyRewardDetail(props: { item: Reward, exchangeCallback: (code: number) => void }): React.JSX.Element {
  const { styles } = useStyles(styleSheet)
  const { t } = useTranslation()
  const { item } = props
  const timestamp = new Date(item.boughtAt)
  const dateString = `${timestamp.toLocaleDateString()} ${timestamp.toLocaleTimeString()}`

  // Create minimal verification data for staff to scan (admin will query DB for details)
  // Simple XOR encryption function
  const xorEncrypt = (text: string, key: string): string => {
    // Use a fixed key if none provided (in production, use a secure key from environment variables)
    const encryptionKey = key || '09r4t3pre89n';
    let result = '';
    
    for (let i = 0; i < text.length; i++) {
      const charCode = text.charCodeAt(i) ^ encryptionKey.charCodeAt(i % encryptionKey.length);
      result += String.fromCharCode(charCode);
    }
    
    return btoa(encodeURIComponent(result));
  };

  const verificationData = {
    u: item.userId,
    r: item.id,
    t: Date.now() // Adding timestamp to make each code unique
  };

  // Encrypt the data using XOR and encode to Base64
  const qrCodeValue = xorEncrypt(JSON.stringify(verificationData), '09r4t3pre89n');
  return (
    <View style={{ flex: 1, alignItems: 'center' }}>
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%', overflow: 'hidden', borderRadius: 14 }} />
      <View style={styles.handler} />
      <BottomSheetScrollView contentContainerStyle={{ alignItems: 'center', gap: 20, marginTop: 20, marginBottom: UnistylesRuntime.insets.bottom }}>
        <View style={{ width: 217, height: 217, display: 'flex', justifyContent: 'center', alignItems: 'center', backgroundColor: 'white', marginTop: 20 }}>
          <QRCode value={qrCodeValue} size={180}></QRCode>
        </View>
        <View style={styles.itemContainer}>
          <View style={styles.itemIcon}>
            <Image source={require('@images/gasha_border.png')} style={{ position: 'absolute', width: '100%', height: '100%', borderRadius: 15, resizeMode: 'stretch' }} />
            <Image source={{ uri: item.imageUrl }} style={{ position: 'absolute', left: 4, top: 4, bottom: 4, right: 4, borderRadius: 12 }} />
          </View>
          <View style={styles.itemContent}>
            <Text style={styles.itemSubTitle}>{t('acquisition_time')}</Text>
            <Text style={styles.itemTitle}>{dateString}</Text>
            <View style={{ height: 4 }} />
            <Text style={styles.itemSubTitle}>{t('acquisition_point')}</Text>
            <Text style={styles.itemTitle}>{`${item.pointPrice.toLocaleString()}E`}</Text>
          </View>
        </View>
        <Text style={styles.title}>{item.name}</Text>
        <Text style={styles.desc}>{item.detailDescription}</Text>
      </BottomSheetScrollView>
    </View >
  )
}
const styleSheet = createStyleSheet(theme => ({
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginTop: 10
  },
  button: {
    borderRadius: 30,
    width: UnistylesRuntime.screen.width - 100,
    height: 56,
    marginVertical: 30
  },
  scroll: {
    height: (UnistylesRuntime.screen.width - 40) * 207 / 335 + 40,
    marginHorizontal: 20,
    marginTop: 20
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: theme.colors.typography,
    marginHorizontal: 22
  },
  desc: {
    fontSize: 14,
    fontWeight: '400',
    color: theme.colors.typography,
    marginHorizontal: 22
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 19,
    marginBottom: 60,
    width: 200,
    height: 400
  },
  imageItem: {
    width: UnistylesRuntime.screen.width - 40,
    height: (UnistylesRuntime.screen.width - 40) * 207 / 335

  },
  dot: {
    width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 0, marginBottom: -20
  },
  toastTitle: {
    color: theme.colors.background,
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '900',
    marginHorizontal: 20
  },
  toastPrice: {
    textAlign: 'center',
    fontSize: 20,
    fontWeight: '900'
  },
  toastButton: {
    width: 135,
    height: 40,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center'
  },
  toastButtonText: {
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '900',
    color: theme.colors.typography
  },
  itemContainer: {
    width: UnistylesRuntime.screen.width - 44,
    paddingVertical: 10,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 20

  },
  itemIcon: {
    width: 100,
    height: 100,
    borderRadius: 5
  },
  itemContent: {
    display: 'flex',
    flex: 1,
    alignSelf: 'flex-start',
    gap: 5

  },
  itemTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '700'
  },
  itemSubTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '400'
  }
}))
