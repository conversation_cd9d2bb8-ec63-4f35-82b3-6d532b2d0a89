import { BottomSheetScrollView } from '@gorhom/bottom-sheet'
import { type ExhibitionDetail, changeCollect, type MarkDetail } from '@network/map'
import { addMarkScore } from '@network/userScore'
import { updateMarks, updateCurrentScore } from '@pages/home/<USER>'
import Loading from '@pure/Loading'
import { useMutation } from '@tanstack/react-query'
import React, { useEffect, useState } from 'react'
import { Image, Text, TouchableOpacity, View } from 'react-native'
import Share from 'react-native-share'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
interface ExhibitonProps {
  navigation: any
  mark?: MarkDetail
}
function Exbibiton({ navigation, mark }: ExhibitonProps): React.JSX.Element {
  const { styles, theme } = useStyles(styleSheet)
  const [collected, setCollected] = useState(mark?.collected ?? false)
  const runUpadteMarks = updateMarks(undefined)
  const { mutate: change, isPending: loading } = useMutation({
    mutationFn: changeCollect,
    onSuccess: (result) => {
      if (!result.success) return
      setCollected(!collected)
      runUpadteMarks()
    }
  })

  const reloadScore = updateCurrentScore(true)
  const { mutate: addMark } = useMutation({
    mutationFn: addMarkScore, onSuccess: (result) => {
      reloadScore()
    }
  })

  useEffect(() => {
    if (mark?.id === undefined || !(mark?.showPoint)) return
    addMark(mark.id)
    return () => {
      if (mark?.showPoint) {
        runUpadteMarks()
      }
    }
  }, [])

  if (mark?.content === null) {
    return <View />
  }
  // Create fallback content if missing
  const content = mark?.content as ExhibitionDetail || {
    name: mark?.name || 'Exhibition',
    description: 'Exhibition details not available',
    thumbnailUrl: 'https://via.placeholder.com/300x200?text=Exhibition+Image'
  }

  // Get tags from content, fallback to "Others" if not available
  const tags = Array.isArray(content?.tags) && content.tags.length > 0 ? content.tags : ['Others']

  return (
    <View style={{ flex: 1, alignItems: 'center' }}>
      {loading ?? <Loading />}
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%', overflow: 'hidden', borderRadius: 14 }} />
      <View style={styles.handler} />
      <BottomSheetScrollView contentContainerStyle={styles.container}>
        <View style={styles.header}>
          <View style={[styles.headerIcon, { backgroundColor: theme.colors.orange }]}>
            <Text style={styles.headerIconText}>{mark?.name}</Text>
          </View>
          <View style={styles.headerRight}>
            <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <Text style={styles.headerDate}>{mark?.areaName}</Text>
              {mark?.boothNumber && mark.boothNumber.trim() !== '' && (
                <>
                  <View style={{ width: 2, height: 14, backgroundColor: theme.colors.orange }} />
                  <Text style={styles.headerDate}>{mark.boothNumber}</Text>
                </>
              )}
            </View>
            <Text style={styles.headerTitle}>{content.name}</Text>
          </View>
        </View>
        <View>
          <Image source={{ uri: content.thumbnailUrl }} style={{ backgroundColor: theme.colors.grey, width: '100%', height: 188, borderRadius: 14, marginTop: 20 }} />
          <TouchableOpacity style={{ position: 'absolute', right: 10, top: 10 }}
            onPress={(_e) => {
              if (mark?.id !== null && mark?.id !== undefined) {
                change({ collected: !collected, markId: mark.id })
              }
            }}>
            <Image source={collected ? require('@images/favorite.png') : require('@images/unfavorite.png')} style={{ width: 24, height: 31 }} />
          </TouchableOpacity>
        </View>
        <View style={styles.tagsAndShareContainer}>
          <View style={styles.tags}>
            {tags.map((tag, index) => {
              return (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>)
            })}
          </View>

          <TouchableOpacity onPress={() => {
            // Use dynamic website URL from content, fallback to default if not available
            const shareUrl = content.websiteUrl ?? 'https://supermassiveglobal.co.jp/'
            Share.open({
              url: shareUrl,
              message: `Check out ${content.name} at the expo!`,
              excludedActivityTypes: ['com.apple.UIKit.activity.Message',
                'com.apple.UIKit.activity.CopyToPasteboard',
                'com.apple.UIKit.activity.AirDrop',
                'com.apple.UIKit.activity.Mail',
                'com.apple.UIKit.activity.Print',
                'com.apple.UIKit.activity.AssignToContact'
              ]
            }).then((res) => { console.log(res) })
              .catch((err) => { err !== null && console.log(err) })
          }}>
            <Image style={{ width: 36, height: 36 }} source={require('@images/share_sns.png')} />
          </TouchableOpacity>
        </View>

        <Text style={styles.content}>{content.description}</Text>

      </BottomSheetScrollView>
    </View>

  )
}
const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    width: UnistylesRuntime.screen.width
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    marginTop: 10
  },
  headerIcon: {
    textAlign: 'center',
    lineHeight: 40,
    color: 'white',
    borderRadius: 20,
    width: 40,
    height: 40,
    fontSize: 20,
    fontWeight: '900'
  },
  headerIconText: {
    textAlign: 'center',
    lineHeight: 40,
    color: 'white',
    borderRadius: 20,
    width: 36,
    height: 40,
    fontSize: 20,
    fontWeight: '900',
    paddingLeft: 2
  },
  headerRight: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center'
    // gap: 5
  },
  headerDate: {
    fontSize: 14,
    fontWeight: '900',
    color: theme.colors.orange
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '900',
    color: theme.colors.typography
  },
  tagsAndShareContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
    marginBottom: 10,
    minHeight: 36,
    gap: 10
  },
  content: {
    color: theme.colors.typography,
    fontSize: 15

  },
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginVertical: 10
  },
  tags: {
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    flex: 1,
    flexWrap: 'wrap',
    alignItems: 'center'
  },
  tag: {
    backgroundColor: theme.colors.orange,
    paddingHorizontal: 12,
    paddingVertical: 3,
    borderRadius: 6
  },
  tagText: {
    fontSize: 12,
    fontWeight: '900',
    color: theme.colors.typography
  }
}))

export default Exbibiton
