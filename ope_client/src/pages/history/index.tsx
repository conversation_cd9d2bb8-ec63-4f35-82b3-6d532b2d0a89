import { usePrefetch } from '@context/utils'
import { type Reward } from '@network/reward'
import { type HistoryScore, type ScoreRecord, getUserHistoryScore } from '@network/userScore'
import Header from '@pure/Header'
import Loading from '@pure/Loading'
import Bubble, { BubbleCorner } from '@pure/bubble'
import { useMutation } from '@tanstack/react-query'
import { type NaviProps } from '@type/index'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import LottieView from 'lottie-react-native'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Dimensions, FlatList, Image, Text, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

export default function History({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const rewardRef = useRef<BottomSheet>(null)
  const { styles } = useStyles(styleSheet)
  const [selectReward, setSelectReward] = useState<Reward | undefined>(undefined)
  const insets = UnistylesRuntime.insets
  const portaro = usePrefetch('portaro_loop_01.json')

  useEffect(() => {
    if (selectReward !== undefined) {
      rewardRef.current?.expand()
    }
  }, [selectReward])
  const { data, isPending: loading, mutate } = useMutation({ mutationFn: getUserHistoryScore })
  useEffect(() => {
    mutate()
  }, [])
  return (
    <View style={styles.view}>
      {loading &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <FlatList
        contentContainerStyle={styles.container}
        data={data?.data?.historyScores}
        renderItem={({ item }) => <HistoryItem item={item} onPress={(item) => {
          setSelectReward(cloneDeep(item))
        }} />}
        keyExtractor={(_item, index) => index.toString()}
      >
      </FlatList >
      <Header blur title={t('my_story')} onClickBack={() => { navigation.goBack() }}></Header>
      <View style={{ ...{ bottom: insets.bottom - 20 }, ...styles.portaro }} >
        {portaro !== undefined &&
          <LottieView source={portaro} loop autoPlay style={{ width: '50%', height: '50%', alignSelf: 'center', marginTop: '30%' }} />
        }
      </View>
      <Bubble
        text={t('many_memories')}
        style={{ ...{ bottom: insets.bottom + 28 }, ...styles.bubble }}
        corner={BubbleCorner.left}
        onClick={() => {
        }}
      />
    </View >

  )
}

export function HistoryItem(props: { item: HistoryScore, onPress?: (item: Reward) => void }): React.JSX.Element {
  const { theme } = useStyles(styleSheet)
  const { item } = props
  return (
    <>
      <View style={{ marginHorizontal: 20, marginTop: 18 }}>
        {item.scoreRecordList.map((v, index) => <ScoreRecordItem key={index} isLast={index === item.scoreRecordList.length - 1} item={v} />)}
      </View>
      <View style={{ height: 1 / Dimensions.get('screen').scale, width: '100%', backgroundColor: theme.colors.lightGrey, marginTop: 10 }}></View>
    </>
  )
}
function ScoreRecordItem(props: { isLast: boolean, item: ScoreRecord, onPress?: (item: Reward) => void }): React.JSX.Element {
  const { theme } = useStyles(styleSheet)
  const { item, isLast } = props
  return (
    <View style={{ display: 'flex', flexDirection: 'row', gap: 30 }}>
      {!isLast &&
        <View style={{ position: 'absolute', left: 30, top: 0, height: '100%', width: 2, backgroundColor: theme.colors.purple }}></View>
      }
      <View style={{ width: 60, height: 60, borderRadius: 30, backgroundColor: theme.colors.purple, justifyContent: 'center' }}>
        <Text numberOfLines={2} style={{ width: 60, color: theme.colors.typography, fontSize: 12, fontWeight: '900', textAlign: 'center' }}>{`${dayjs(item.finishTime).format('MMM. D \nHH:mm')}`}</Text>
      </View>
      <View style={{ marginTop: 7, marginBottom: 18, flexGrow: 1, backgroundColor: theme.colors.lightGrey, padding: 16, borderRadius: 16, gap: 10 }}>
        <View style={{
          position: 'absolute',
          top: 15,
          left: -15,
          width: 0,
          height: 0,
          borderColor: 'transparent',
          borderTopWidth: 12,
          borderBottomWidth: 12,
          borderRightWidth: 18,
          borderRightColor: theme.colors.lightGrey
        }} />
        {item.sourceName !== null &&
          <Text style={{ color: '#D6D6D6', fontSize: 12, fontWeight: '900' }}>{item.sourceName}</Text>
        }
        <View style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 10 }}>
          <Text style={{ color: theme.colors.typography, fontSize: 16, fontWeight: '900' }}>{item.reasons}</Text>
          {item.score !== null &&
            <View style={{ flexGrow: 1, display: 'flex', flexDirection: 'row-reverse' }}>
              <View style={{ backgroundColor: '#595959', width: 110, height: 24, borderRadius: 24, display: 'flex', flexDirection: 'row', alignItems: 'center', paddingRight: 20, paddingLeft: 10 }}>
                <Image source={require('@images/polygon.png')} style={{ width: 16, height: 27, marginTop: -14 }} />
                <Text style={{ textAlign: 'right', flexGrow: 1, color: theme.colors.typography, fontSize: 16, fontWeight: '900' }}>{item.score}</Text>
              </View>
            </View>
          }
        </View>
      </View>
    </View>
  )
}
const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  container: {
    flex: 1,
    paddingTop: UnistylesRuntime.insets.top + 68,
    paddingBottom: UnistylesRuntime.insets.bottom
  },
  content: {
    height: 40,
    borderRadius: 40,
    overflow: 'hidden',
    backgroundColor: theme.colors.grey,
    marginVertical: 4,
    marginHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 30,
    paddingLeft: 32,
    paddingRight: 8
  },
  icon: {
    position: 'absolute',
    left: 10,
    top: 12,
    width: 26,
    height: 23
  },
  title: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
  },
  item: {
    marginHorizontal: 20,
    marginVertical: 5,
    display: 'flex',
    backgroundColor: '#666666',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#7E7E7E'
  },
  itemContainer: {
    paddingHorizontal: 10,
    paddingVertical: 10,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 20

  },
  itemIcon: {
    width: 100,
    height: 100,
    borderRadius: 5
  },
  itemContent: {
    display: 'flex',
    flex: 1,
    alignSelf: 'flex-start',
    gap: 5

  },
  itemTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '700'
  },
  itemSubTitle: {
    fontSize: 16,
    color: theme.colors.typography,
    fontWeight: '400'
  },
  bubble: {
    position: 'absolute',
    left: 84,
    right: 80
  },
  portaro: {
    position: 'absolute',
    left: -50,
    width: 180,
    height: 180
  }
}))
