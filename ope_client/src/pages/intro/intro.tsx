import { usePrefetch } from '@context/utils'
import Bubble, { BubbleCorner } from '@pure/bubble'
import { type NaviProps } from '@type/index'
import LottieView, { type AnimationObject } from 'lottie-react-native'
import React, { useLayoutEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Image, StyleSheet, View } from 'react-native'
import Animated from 'react-native-reanimated'
function Intro({ navigation }: NaviProps): React.JSX.Element {
  const animationRef = useRef<LottieView>(null)
  const outro = usePrefetch('portaro_loop_01.json') as AnimationObject
  useLayoutEffect(() => {
    animationRef.current?.pause()
  })
  const { t } = useTranslation()

  return (
    <View style={styles.container}>
      <Image source={require('@images/dall.png')} style={styles.background}></Image>
      <View style={styles.contentGroup}>
        <Animated.View style={styles.lottieContainer}>
          {outro !== undefined &&
            <LottieView
              autoPlay={false}
              ref={animationRef}
              source={outro}
              loop={false}
              style={styles.image}
            />
          }
        </Animated.View>
        <Bubble
          style={styles.bubble}
          corner={BubbleCorner.top}
          onClick={() => {
            animationRef.current?.play()
            setTimeout(() => {
              navigation.navigate('IntroOne')
            }, 2500)
          }}
          text={t('welcome_portaro')} />
      </View>
    </View>
  )
}

const LOTTIE_WIDTH = 310
const LOTTIE_HEIGHT = 288

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center'
  },
  contentGroup: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: 0
  },
  lottieContainer: {
    width: LOTTIE_WIDTH,
    height: LOTTIE_HEIGHT,
    alignItems: 'center',
    justifyContent: 'center'
  },
  image: {
    width: LOTTIE_WIDTH,
    height: LOTTIE_HEIGHT
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  },
  bubble: {
    marginHorizontal: 24
  }
})
export default Intro
