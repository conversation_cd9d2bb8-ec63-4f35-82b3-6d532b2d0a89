import React, { useRef, useCallback } from 'react'
import Animated from 'react-native-reanimated'
import { View, StyleSheet, Dimensions, Image } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Swiper from 'react-native-swiper'
import Bubble, { BubbleCorner } from '@pure/bubble'
import AlertContent from '@pure/alertContent'
import { useTranslation } from 'react-i18next'
import LottieView from 'lottie-react-native'
import { usePrefetch } from '@context/utils'
import { type NaviProps } from '@type/index'
import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'

function IntroTwo({ navigation }: NaviProps): React.JSX.Element {
  const index = useRef(1)
  const swiper = useRef<Swiper>(null)
  const width = Dimensions.get('window').width
  const insets = useSafeAreaInsets()
  const { t } = useTranslation()
  const portaro1 = usePrefetch('portaro_loop_1.json')
  const { changeTrack } = useBGM()

  // Change to track 5 when IntroTwo page comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('IntroTwo screen focused, changing to track 5')
      changeTrack(5).catch(console.error)
    }, [changeTrack])
  )

  return (
    <View style={styles.container}>
      <View style={{ width, height: insets.top }}></View>
      <Image source={require('@images/dall.png')} style={styles.background}></Image>
      <View style={[styles.background, { backgroundColor: 'rgba(0, 0, 0, 0.4)' }]} />
      <Swiper style={styles.scroll}
        ref={swiper}
        dot={<View style={{ backgroundColor: '#727272', width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 6, marginBottom: 6 }} />}
        activeDot={<View style={{ backgroundColor: '#D9D9D9', width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 6, marginBottom: 6 }} />}
      >
        <View style={styles.slide}>
          <AlertContent title={t('what_is_energy')} content={'Image 04 About Energy'}>
            <Image source={require('@images/intro_5.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
        <View style={styles.slide}>
          <AlertContent title={t('what_is_energy')} content={'Image 05 About Energy'}>
            <Image source={require('@images/intro_5.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
      </Swiper>
      <View style={{ width, height: 200 }}></View>
      <Animated.View
        style={{ ...{ bottom: insets.bottom + 28 }, ...styles.portaro }}
      >
        {portaro1 !== undefined &&
          <LottieView
            autoPlay
            loop
            source={portaro1}
            style={styles.portaro}
          />
        }
      </Animated.View>
      <Bubble
        text=''
        style={{ ...{ bottom: insets.bottom + 28 }, ...styles.bubble }}
        corner={BubbleCorner.left}
        onClick={() => {
          if (index.current > 1) {
            navigation.replace('Home')
            return
          }
          index.current = index.current + 1
          swiper.current?.scrollTo(index.current, true)
        }}

        pattern='{portaro}'
        replace={<Image key={1} source={require('@images/polygon.png')} style={{ width: 15, height: 20 }}></Image>}
        contents={[t('intro_4'), t('intro_5')]} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center'
  },
  scroll: {
    flexGrow: 0
    // height: 300
  },
  portaro: {
    position: 'absolute',
    left: 0,
    bottom: 5,
    width: 150,
    height: 150
  },
  image: {
    position: 'absolute',
    left: 12,
    width: 108,
    height: 101
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 19,
    marginBottom: 60
  },
  bubble: {
    position: 'absolute',
    left: 140,
    right: 24
  }
})
export default IntroTwo
