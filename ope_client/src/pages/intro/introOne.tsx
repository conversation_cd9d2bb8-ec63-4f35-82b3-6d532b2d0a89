import { usePrefetch } from '@context/utils'
import { useAccountStore } from '@context/store'
import AlertContent from '@pure/alertContent'
import Bubble, { BubbleCorner } from '@pure/bubble'
import { type NaviProps } from '@type/index'
import <PERSON><PERSON>View from 'lottie-react-native'
import React, { forwardRef, useRef, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Dimensions, Image, Text, View } from 'react-native'
import Animated from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Swiper from 'react-native-swiper'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'
import useIntroOneStore from './store'

function IntroOne({ navigation }: NaviProps): React.JSX.Element {
  const index = useRef(1)
  const swiper = useRef<Swiper>(null)
  const width = Dimensions.get('window').width
  const bubble = useRef<Bubble>(null)
  const insets = useSafeAreaInsets()
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const setIndex = useIntroOneStore((state) => state.setIndex)
  const portaro1 = usePrefetch('portaro_loop_01.json')
  const portaro2 = usePrefetch('portaro_loop_01.json')
  const portaro3 = usePrefetch('portaro_loop_01.json')
  const nickName = useAccountStore((state) => state.nickName)
  const { changeTrack } = useBGM()

  // Change to track 5 when IntroOne page comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('IntroOne screen focused, changing to track 5')
      changeTrack(5).catch(console.error)
    }, [changeTrack])
  )

  return (
    <View style={styles.container}>
      <View style={{ width, height: insets.top }}></View>
      <Image source={require('@images/dall.png')} style={styles.background}></Image>
      <View style={[styles.background, { backgroundColor: 'rgba(0, 0, 0, 0.4)' }]} />
      <Swiper style={styles.scroll}
        ref={swiper}
        dot={<View style={{ backgroundColor: '#727272', width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 6, marginBottom: 6 }} />}
        activeDot={<View style={{ backgroundColor: '#D9D9D9', width: 8, height: 8, borderRadius: 7.5, marginLeft: 6, marginRight: 6, marginTop: 6, marginBottom: 6 }} />}
        onMomentumScrollEnd={(e, state, context) => {
          if (state.index === 2) {
            if (nickName !== null) {
              navigation.navigate('IntroTwo')
            } else {
              navigation.navigate('SetProfile')
            }
          }
        }}
        onIndexChanged={(index) => {
          bubble.current?.changeIndex(index)
          setIndex(index)
        }}
      >
        <View style={styles.slide}>
          <AlertContent title={t('about_app')} content={''} >
            <Image source={require('@images/yuragi_SDillast01_01.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
        <View style={styles.slide}>
          <AlertContent title={t('about_simple_games')} content={''}>
            <Image source={require('@images/yuragi_SDillast01_02.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
        <View style={styles.slide}>
          <AlertContent title={t('about_special_gift')} content={''}>
            <Image source={require('@images/yuragi_SDillast01_03.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain' }} />
          </AlertContent>
        </View>
      </Swiper>
      <View style={{ width, height: 200 }}></View>
      <LoopImage images={[portaro1, portaro2, portaro3]} />
      <Bubble
        ref={bubble}
        text=''
        style={{ ...{ bottom: insets.bottom + 28 }, ...styles.bubble }}
        corner={BubbleCorner.left}
        onClick={() => {
          if (index.current > 2) {
            if (nickName !== null) {
              navigation.navigate('IntroTwo')
            } else {
              navigation.navigate('SetProfile')
            }
            return
          }
          index.current = index.current + 1
          swiper.current?.scrollTo(index.current, true)
          // bubble.current?.changeIndex(index.current)
        }}
        pattern='{simple games}'
        replace={<Text key={'intro'} style={styles.bubbleHighlight}>{t('simple_games')}</Text>}
        contents={[t('intro_1'), t('intro_2'), t('intro_3')]} />
    </View>
  )
}

interface LoopImageProps {
  images: any[]
}
interface LoopImageType {
  changeIndex: (index: number) => void
}

const LoopImage = forwardRef<LoopImageType, LoopImageProps>((props: LoopImageProps, ref) => {
  const index = useIntroOneStore((state) => state.index)

  const { images } = props
  const insets = useSafeAreaInsets()
  const { styles } = useStyles(styleSheet)
  return (
    <Animated.View
      style={{ ...{ bottom: insets.bottom + 28 }, ...styles.portaro }}
    >
      {images[index] !== undefined &&
        <LottieView
          autoPlay
          loop
          source={images[index]}
          style={styles.portaro}
        />
      }
    </Animated.View>
  )
})
LoopImage.displayName = 'LoopImage'

const styleSheet = createStyleSheet(_theme => ({
  container: {
    flex: 1,
    alignItems: 'center'
  },
  scroll: {
    flexGrow: 0
  },
  image: {
    position: 'absolute',
    left: 12,
    width: 108,
    height: 101
  },
  portaro: {
    position: 'absolute',
    left: 0,
    bottom: 5,
    width: 150,
    height: 150
  },
  background: {
    position: 'absolute',
    width: '100%',
    height: '100%'
  },
  slide: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 19,
    marginBottom: 60
  },
  bubble: {
    position: 'absolute',
    left: 140,
    right: 24
  },
  bubbleHighlight: {
    color: _theme.colors.green
  }
}))
export default IntroOne
