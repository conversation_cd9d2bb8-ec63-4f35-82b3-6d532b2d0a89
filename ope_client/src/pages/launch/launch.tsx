import { useMapStore } from '@context/map'
import { usePrefetch } from '@context/utils'
import { event } from '@network/login'
import { Button } from '@pure/Button'
import Loading from '@pure/Loading'
import { useMutation } from '@tanstack/react-query'
import { type NaviProps } from '@type/index'
import React, { useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useFocusEffect } from '@react-navigation/native'
import { useBGM } from '@services/bgmService'
import {
  Dimensions,
  Image,
  Text,
  View
} from 'react-native'
import FastImage from 'react-native-fast-image'
import Animated, { useAnimatedStyle, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated'
import Toast from 'react-native-root-toast'
import { createStyleSheet, useStyles } from 'react-native-unistyles'

const preloads = ['portaro_intro.json', 'portaro_outro.json', 'portaro_loop_01.json', 'portaro_loop_02.json', 'portaro_loop_03.json', 'portaro_loop_04.json', 'portaro_loop_05.json']
function Launch({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const duration = 5000
  const { styles } = useStyles(styleSheet)
  const scale = useSharedValue(0)
  const sizeScale = useSharedValue(1)
  const updateMap = useMapStore(state => state.updateMap)
  const setCountDown = useMapStore(state => state.setCountdown)
  const { changeTrack } = useBGM()

  const backOffset = useSharedValue(15)
  const backStyles = useAnimatedStyle(() => ({
    transform: [{ translateY: backOffset.value }]
  }))
  const frontOffset = useSharedValue(-30)
  const frontStyles = useAnimatedStyle(() => ({
    transform: [{ translateY: frontOffset.value }]
  }))

  const frontVectorOffset = useSharedValue(100)
  const frontVectorStyles = useAnimatedStyle(() => ({
    transform: [{ translateY: frontVectorOffset.value }]
  }))
  const playerOffset = useSharedValue(-15)
  const playerStyles = useAnimatedStyle(() => ({
    transform: [{ rotateX: `${scale.value}deg` }, { scale: sizeScale.value }, { rotateZ: '30deg' }, { translateY: playerOffset.value }, { translateX: playerOffset.value / 3 + 20 }]
  }))
  const backVectorStyles = useAnimatedStyle(() => ({
    transform: [{ rotateX: `${scale.value}deg` }, { scale: sizeScale.value }, { translateY: playerOffset.value }]
  }))
  preloads.forEach((p) => usePrefetch(p))
  useEffect(() => {
    backOffset.value = withRepeat(
      withTiming(-backOffset.value, { duration }),
      -1,
      true
    )
    frontOffset.value = withRepeat(
      withTiming(-frontOffset.value, { duration }),
      -1,
      true
    )
    frontVectorOffset.value = withRepeat(
      withTiming(2 * frontVectorOffset.value, { duration }),
      -1,
      true
    )
    playerOffset.value = withRepeat(
      withTiming(-playerOffset.value, { duration }),
      -1,
      true
    )
    scale.value = withRepeat(
      withTiming(10, { duration }),
      -1,
      true
    )
    sizeScale.value = withRepeat(
      withTiming(1.1, { duration }), -1, true)
  }, [])

  // Change to track 9 when launch screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('Launch screen focused, changing to track 9')
      changeTrack(9).catch(console.error)
    }, [])
  )
  const { mutate: run, isPending: loading } = useMutation({
    mutationFn: event,
    onSuccess: (result) => {
      console.log('Event API Success:', result)
      if (!result.success) {
        Toast.show(result.msg, { position: Toast.positions.CENTER })
        return
      }
      if (result.data === null) {
        Toast.show('No event data received', { position: Toast.positions.CENTER })
        return
      }
      updateMap(result.data.map)
      setCountDown(result.data.countdown)
      FastImage.preload([{ uri: result.data.map.mapUrl }])
      navigation.navigate('Login')
    },
    onError: (error) => {
      console.error('Event API Error:', error)
      Toast.show(`Network error: ${error.message}`, { position: Toast.positions.CENTER })
    }
  })
  return (
    <View style={styles.view}>
      {loading &&
        <Loading />
      }
      <Animated.Image source={require('@images/launch_vector_back.png')} style={[{ overflow: 'visible', position: 'absolute', width: '100%', height: '100%', top: -50, zIndex: 0 }, backStyles]} />
      <Animated.Image source={require('@images/launch_vector_3.png')} style={[{ position: 'absolute', width: '100%', height: 300, zIndex: 100 }, backVectorStyles]} />
      <Animated.Image source={require('@images/launch_vector_1.png')} style={[{ position: 'absolute', width: '100%', height: '100%', zIndex: 100 }, backVectorStyles]} />
      <Animated.Image source={require('@images/launch_vector_player.png')} style={[{ transformOrigin: 'center center 120', zIndex: 1000, position: 'absolute', top: 150, width: 625, height: 549, transform: [{ rotate: '40deg' }] }, playerStyles]} />
      <Animated.Image source={require('@images/launch_vector_2.png')} style={[{ zIndex: 2000, position: 'absolute', width: '100%', height: '100%' }, frontVectorStyles]} />
      <Animated.Image source={require('@images/launch_vector_front.png')} style={[{ zIndex: 2000, overflow: 'visible', position: 'absolute', width: '100%', height: '100%', left: 10, top: -50 }, frontStyles]} />
      <Text style={[styles.text, { marginBottom: 80, zIndex: 2001 }]}>Copyright © Super Massive Global Co., Ltd. All Rights Reserved.</Text>
      <Text style={[styles.text, { zIndex: 2001 }]}>ver 1.0.0</Text>
      <View style={{ zIndex: 20000 }}>
        <Button text={t('start')} style={styles.button} onPress={() => {
          run()
        }} />
      </View>
      <Image source={require('@images/launch_logo.png')} style={{ zIndex: 3000, width: 232, height: 88, marginLeft: -100, marginBottom: 20 }} />
    </View>
  )
}

const styleSheet = createStyleSheet(theme => ({
  view: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column-reverse',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#EAEAEA'
  },
  button: {
    borderRadius: 30,
    width: Dimensions.get('window').width - 100,
    height: 60,
    marginBottom: 30
  },
  text: {
    color: theme.colors.background,
    fontSize: 10
  }
}))
export default Launch
