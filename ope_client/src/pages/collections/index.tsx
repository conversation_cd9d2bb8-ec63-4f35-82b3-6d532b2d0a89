import { hapticTrigger } from '@context/utils'
import { BottomSheetFlatList, BottomSheetTextInput, BottomSheetView } from '@gorhom/bottom-sheet'
import { MarkType, collectList } from '@network/map'
import { type SearchItem } from '@network/search'
import { updateMarks } from '@pages/home/<USER>'
import { SearchItemView } from '@pages/search'
import Loading from '@pure/Loading'
import { useMutation } from '@tanstack/react-query'
import React, { useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Image, Keyboard, Pressable, Text, View } from 'react-native'
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'

interface CollectionsProps {
  onPressItem: (item: SearchItem) => void
}

export default function Collections(props: CollectionsProps): React.JSX.Element {
  const { t } = useTranslation()
  const [collectionList, setCollectionList] = useState<SearchItem[]>([])
  const [excludeType, setExcludeType] = useState<MarkType[]>([])
  const originalCollectionList = useRef<SearchItem[]>([])
  const searchText = useRef<string>('')
  const contentOpacity = useSharedValue(0)
  const { isPending: loading, mutate } = useMutation({
    mutationFn: collectList,
    onSuccess: (result) => {
      if (!result.success || result.data === null) return
      originalCollectionList.current = result.data.list
      setCollectionList(result.data.list)
    }
  })
  const runUpdateMarks = updateMarks(undefined)
  useEffect(() => {
    mutate({ pageNum: 1, pageSize: 100 })
    contentOpacity.value = withSpring(1, { damping: 20 })
    return () => {
      runUpdateMarks()
    }
  }, [])
  useEffect(() => {
    setCollectionList(() => {
      return originalCollectionList.current.filter((item) => item.contentName.includes(searchText.current)).filter((item) => !excludeType.includes(item.markType))
    })
  }, [excludeType])

  const contentAnimatedStyle = useAnimatedStyle(() => ({
    opacity: contentOpacity.value
  }))

  const { styles, theme } = useStyles(styleSheet)
  return (
    <BottomSheetView style={styles.container}>
      <Image source={require('@images/launch_background.png')} style={styles.image} />
      <View style={styles.handler} />
      <View style={styles.header}>
        <Animated.View style={styles.input}>
          <Image source={require('@images/search_search.png')} style={{ width: 16, height: 16 }} />
          <BottomSheetTextInput
            returnKeyType='search'
            enablesReturnKeyAutomatically={false}
            blurOnSubmit={false}
            style={{ flex: 1, color: 'white' }}
            onChangeText={(text) => {
              searchText.current = text
              setCollectionList(() => {
                return originalCollectionList.current.filter((item) => item.contentName.includes(searchText.current)).filter((item) => !excludeType.includes(item.markType))
              })
            }}
            onSubmitEditing={(e) => {
              const text = e.nativeEvent.text
              if (text.length > 0) {
                // runSearch(text)
              }
            }}
            placeholder={t('search_exhibitors')} placeholderTextColor={'#7A7A7A'} />
        </Animated.View>
      </View>
      <Animated.ScrollView
        style={[{ maxHeight: 76 }, contentAnimatedStyle]}
        horizontal={true}
        contentContainerStyle={styles.tagContainer}
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        decelerationRate="fast"
        bounces={false}
      >
        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Game)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Game))
          } else {
            setExcludeType([...excludeType, MarkType.Game])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Game) ? theme.colors.grey : theme.colors.purple }]}>
          <Text style={[styles.tagContent]}>{t('mission')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Exhibition)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Exhibition))
          } else {
            setExcludeType([...excludeType, MarkType.Exhibition])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Exhibition) ? theme.colors.grey : theme.colors.orange }]}>
          <Text style={[styles.tagContent]}>{t('exhibition')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Event)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Event))
          } else {
            setExcludeType([...excludeType, MarkType.Event])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Event) ? theme.colors.grey : theme.colors.pink }]}>
          <Text style={[styles.tagContent]}>{t('event')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Restaurant)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Restaurant))
          } else {
            setExcludeType([...excludeType, MarkType.Restaurant])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Restaurant) ? theme.colors.grey : '#FF4444' }]}>
          <Text style={[styles.tagContent]}>{t('restaurant')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Shop)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Shop))
          } else {
            setExcludeType([...excludeType, MarkType.Shop])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Shop) ? theme.colors.grey : theme.colors.green }]}>
          <Text style={[styles.tagContent]}>{t('shop')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Hotel)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Hotel))
          } else {
            setExcludeType([...excludeType, MarkType.Hotel])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Hotel) ? theme.colors.grey : '#4488FF' }]}>
          <Text style={[styles.tagContent]}>{t('hotel')}</Text>
        </Pressable>

        <Pressable onPress={() => {
          hapticTrigger()
          if (excludeType.includes(MarkType.Other)) {
            setExcludeType(excludeType.filter((item) => item !== MarkType.Other))
          } else {
            setExcludeType([...excludeType, MarkType.Other])
          }
        }} style={[styles.tag, { backgroundColor: excludeType.includes(MarkType.Other) ? theme.colors.grey : '#FFAA00' }]}>
          <Text style={[styles.tagContent]}>{t('other')}</Text>
        </Pressable>
      </Animated.ScrollView>
      <BottomSheetFlatList
        keyboardShouldPersistTaps='always'
        data={collectionList}
        renderItem={({ item }) => <SearchItemView item={item} onPress={() => {
          Keyboard.dismiss()
          props.onPressItem(item)
        }} />}
        keyExtractor={(_item, index) => index.toString()}
        style={styles.flat}
      />
      {loading && <Loading />}
    </BottomSheetView >
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    overflow: 'hidden',
    minHeight: '100%'
  },
  image: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: '100%',
    height: '100%'
    // objectFit: 'cover'
  },
  handler: {
    width: 76,
    height: 6,
    backgroundColor: theme.colors.lightGrey,
    borderRadius: 6,
    marginTop: 10
  },
  eventContent: {
    color: theme.colors.typography,
    fontSize: 15,
    fontWeight: '400'
  },
  event: {
    display: 'flex',
    gap: 20,
    marginTop: 20
  },
  imageContainer: {
    display: 'flex',
    flexWrap: 'wrap',
    flexDirection: 'row',
    height: 207,
    borderRadius: 6,
    gap: 6
  },
  flat: {
    flex: 1,
    width: UnistylesRuntime.screen.width,
    paddingBottom: UnistylesRuntime.insets.bottom

  },
  input: {
    flex: 1,
    height: 40,
    borderRadius: 5,
    backgroundColor: '#4D4D4D',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    paddingHorizontal: 12,
    color: '#7A7A7A'
  },
  header: {
    marginTop: 10,
    display: 'flex',
    flexDirection: 'row-reverse',
    alignItems: 'center',
    height: 70,
    paddingHorizontal: 20,
    gap: 20
  },
  tagContainer: {
    marginVertical: 20,
    paddingHorizontal: 20,
    display: 'flex',
    flexDirection: 'row',
    gap: 10,
    height: 36,
    alignItems: 'center'
  },
  tag: {
    paddingHorizontal: 15,
    height: '100%',
    borderRadius: 36,
    borderColor: 'rgba(34, 34, 34, 0.20)',
    borderWidth: 2
  },
  tagContent: {
    color: theme.colors.typography,
    fontSize: 14,
    fontWeight: '900',
    lineHeight: 32
  }
}))
