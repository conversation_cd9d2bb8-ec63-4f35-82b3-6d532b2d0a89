import { But<PERSON> } from '@pure/Button'
import PatternText from '@pure/PatternText'
import { type NaviProps } from '@type/index'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Alert,
  Dimensions,
  Image,
  Linking,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { Camera, useCameraDevice, useCameraPermission, useCodeScanner } from 'react-native-vision-camera'

function Scan({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const { theme } = useStyles(styleSheet)
  const device = useCameraDevice('back')
  const { styles } = useStyles(styleSheet)
  const { hasPermission, requestPermission } = useCameraPermission()
  const codeScanner = useCodeScanner({
    codeTypes: ['qr', 'ean-13'],
    onCodeScanned: (codes) => {
      setSearching(false)
      if ((codes[0].value?.startsWith('http')) ?? false) {
        setUrl(codes[0].value)
      }
    }
  })
  const [searching, setSearching] = useState(true)
  const [url, setUrl] = useState<string | undefined>(undefined)
  useEffect(() => {
    async function request(): Promise<void> {
      if (!hasPermission) {
        const has = await requestPermission()
        if (!has) {
          Alert.alert('Open settings?', '', [
            { text: t('cancel'), style: 'cancel' },
            {
              text: 'OK',
              onPress: () => {
                void Linking.openSettings()
              }
            }
          ])
        }
      }
    }
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    request()
  }, [])

  console.log(device)
  return (
    <View style={{ flex: 1 }}>
      {device !== undefined &&
        <View
          style={[StyleSheet.absoluteFill, styles.container]}
        >
          <Camera
            style={{ position: 'absolute', left: 0, right: 0, top: 0, bottom: 0, backgroundColor: theme.colors.background }}
            device={device}
            isActive={searching}
            codeScanner={codeScanner}
          />
          <View style={styles.header}>
            <PatternText
              style={styles.textIntro}
              text={t('scan_intro')}
              pattern='{portaro}'
              replace={<Image key={1} source={require('@images/mission_marker.png')} style={{ width: 20, height: 27, objectFit: 'contain' }}></Image>}
            />
            <Text style={styles.textTips}>{t('scan_tips_1')}</Text>
          </View>
          <View style={styles.scan}>
            <View style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)', height: 288, flexGrow: 1 }}></View>
            <Image source={require('@images/scan.png')} style={{ width: 288, height: 288 }} />
            <View style={{ backgroundColor: 'rgba(0, 0, 0, 0.8)', height: 288, flexGrow: 1 }}></View>
          </View>
          <View style={styles.bottom}>
            <Button text={t('mission_start')} style={styles.button} disabled={url === undefined} onPress={() => {
              if (url !== undefined) {
                navigation.navigate('Webview', { url })
              }
            }} />
          </View>
          <View style={styles.close}>
            <TouchableOpacity onPress={() => navigation.pop()}>
              <Image source={require('@images/close.png')} style={{ width: 48, height: 48 }} />
            </TouchableOpacity>
          </View>
        </View>
      }
    </View >
  )
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  header: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    paddingHorizontal: 19,
    gap: 20,
    paddingTop: UnistylesRuntime.insets.top + 60,
    paddingBottom: 40
  },
  close: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: UnistylesRuntime.insets.bottom + 20,
    paddingLeft: 20

  },
  scan: {
    height: 288,
    width: Dimensions.get('window').width,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60
  },
  bottom: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    width: '100%',
    display: 'flex',
    alignItems: 'stretch',
    paddingTop: 40
  },
  button: {
    marginHorizontal: 30,
    height: 56,
    borderRadius: 28,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  textIntro: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
  },
  textTips: {
    color: theme.colors.typography,
    fontSize: 12,
    fontWeight: '400'
  }
}))
export default Scan
