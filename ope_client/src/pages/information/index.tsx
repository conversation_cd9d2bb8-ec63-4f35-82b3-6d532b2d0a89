import { storage } from '@context/storage'
import { getBulletins, type Bulletins } from '@network/bulletins'
import Header from '@pure/Header'
import Loading from '@pure/Loading'
import { useMutation } from '@tanstack/react-query'
import { type NaviProps } from '@type/index'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, Image, RefreshControl, Text, TouchableOpacity, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { create } from 'zustand'

export default function Information({ navigation }: NaviProps): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const { data, mutate: run, isPending: loading } = useMutation({
    mutationFn: getBulletins
  })
  useEffect(() => {
    run({pageNum: 1, pageSize: 100})
  }, [])
  return (
    <View style={styles.view}>
      {loading &&
        <Loading />
      }
      <Image source={require('@images/launch_background.png')} style={{ position: 'absolute', width: '100%', height: '100%' }} />
      <Header title={t('information')} onClickBack={() => { navigation.goBack() }}></Header>
      <FlatList
        contentContainerStyle={styles.container}
        refreshControl={
          <RefreshControl
            tintColor={'white'}
            progressViewOffset={20}
            onRefresh={() => {
              if (loading) return
              run(1, 100)
            }}
            refreshing={loading}>
          </RefreshControl>
        }
        data={data?.data.list}
        renderItem={({ item }) => <InformationView item={item} onPress={() => {
          navigation.navigate('Webview', { url: item.pageUrl, hide_navi: false })
        }} />}
        keyExtractor={(_item, index) => index.toString()}
      >
      </FlatList >
    </View >
  )
}

function InformationView(props: { item: Bulletins, onPress?: (item: Bulletins) => void }): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  const { item } = props
  const updateReadList = useInformationStore((state) => state.updateReadList)
  const readList = useInformationStore((state) => state.readList)
  return (
    <TouchableOpacity style={styles.item} onPress={() => {
      updateReadList(item.pageUrl)
      props.onPress?.(item)
    }}>
      <View style={styles.itemContainer}>
        <View style={{ flex: 1, gap: 14 }}>
          <View style={{ display: 'flex', flexDirection: 'row', gap: 10, alignItems: 'center' }}>
            <Text style={styles.date}>{dayjs(item.publishAt).format('mm-DD-YYYY')}</Text>
            {!readList.includes(item.pageUrl) &&
              <View style={styles.newTag}>
                <Text style={{ lineHeight: 11, fontSize: 10, fontWeight: '900' }}>{t('new')}</Text>
              </View>
            }
          </View>
          <Text numberOfLines={2} style={styles.title}>{item.title}</Text>

        </View>
        <Image source={require('@images/arrow.png')} style={styles.itemCorner} />
      </View>
    </TouchableOpacity>

  )
}

const styleSheet = createStyleSheet(theme => ({
  view: {
    backgroundColor: theme.colors.background,
    flex: 1
  },
  container: {
    paddingTop: 20,
    paddingBottom: UnistylesRuntime.insets.bottom
  },
  item: {
    paddingHorizontal: 20,
    paddingVertical: 5,
    width: '100%'
  },
  itemContainer: {
    backgroundColor: theme.colors.grey,
    borderColor: 'rgba(214, 214, 214, 0.10)',
    borderWidth: 2,
    borderRadius: 20,
    height: 105,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20
  },
  itemCorner: {
    width: 18,
    height: 18
  },
  date: {
    color: theme.colors.lightGrey,
    fontSize: 14,
    fontWeight: '900'
  },
  title: {
    color: theme.colors.typography,
    fontSize: 16,
    fontWeight: '900'
  },
  newTag: {
    backgroundColor: '#00FF5B',
    height: 12,
    borderRadius: 12,
    paddingHorizontal: 9
  }
}))
interface InformationType {
  readList: string[]
  updateReadList: (url: string) => void
}

const useInformationStore = create<InformationType>(((set, get) => ({
  readList: JSON.parse(storage.getString('information_read') ?? '[]'),
  updateReadList: (url) => {
    const list = get().readList
    list.push(url)
    storage.set('information_read', JSON.stringify(list))
    set({ readList: cloneDeep(list) })
  }
})))
