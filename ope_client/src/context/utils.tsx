import { storage } from '@context/storage'
import { getLocales } from 'react-native-localize'
import ReactNativeHapticFeedback from 'react-native-haptic-feedback'
import analytics from '@react-native-firebase/analytics'
import { Platform } from 'react-native'
import { useQuery } from '@tanstack/react-query'
const options = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false
}

export function hapticTrigger(): void {
  ReactNativeHapticFeedback.trigger('impactLight', options)
}

async function prefetch(url: string): Promise<any> {
  const data = storage.getString(url)
  if (data !== undefined) return await Promise.resolve(JSON.parse(data))
  return await fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  }).then(async response => await response.json()).then(res => {
    const r = JSON.stringify(res)
    storage.set(url, r)
    return res
  })
}

export function usePrefetch(path: string): any | undefined {
  const url = `${RESOURCE_HOST}${path}`
  const { data } = useQuery({
    queryKey: [path],
    queryFn: async () => await prefetch(url)
  })
  return data
}

export function track(name: string, params?: Record<string, any>): void {
  if (Platform.OS === 'ios') {
    void analytics().logEvent(name, params)
  }
}

export function getLanguageCode(): string {
  return getLocales()[0].languageCode ?? 'en'
}

// Local development configuration
// export const RESOURCE_HOST = 'https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com/mobile-app/' // Static resources from Discover Web
// export const API_HOST = 'http://localhost:8082/' // Main API endpoints (Discover Web Service)
// export const ACCOUNT_API_HOST = 'http://localhost:8082/' // Account/Auth endpoints (FIXED: SMS endpoints are in Discover Web Service, not Account Service)
// export const MEDIA_HOST = 'https://6ce0-2405-6580-22e0-3300-8129-c5c6-a4c0-242d.ngrok-free.app/' // Media host
// export const MEDIA_HOST = 'http://localhost:3000/' // Media host

// Staging development configuration
// export const RESOURCE_HOST = 'https://open-portal-expo-staging.s3.ap-northeast-1.amazonaws.com/mobile-app/'
export const API_HOST = 'https://ope-backend-stg.zafar.dev/'
export const ACCOUNT_API_HOST = 'https://ope-backend-stg.zafar.dev/'
export const MEDIA_HOST = 'https://ope-ghost-stg.zafar.dev/'

// Production configuration
export const RESOURCE_HOST = 'https://open-portal-expo-prod.s3.ap-northeast-1.amazonaws.com/mobile-app/' // Static resources from Discover Web
// export const API_HOST = 'https://backend.openportalexpo.com/'
// export const ACCOUNT_API_HOST = 'https://backend.openportalexpo.com/'
// export const MEDIA_HOST = 'https://media.openportalexpo.com/'
