import Geolocation, { type GeolocationResponse } from '@react-native-community/geolocation'
import { useEffect, useState } from 'react'
import DeviceInfo from 'react-native-device-info'
import { magnetometer, setUpdateIntervalForType, SensorTypes, type SensorData } from 'react-native-sensors'

export function watchPosition(): [GeolocationResponse | undefined, SensorData | undefined] {
  const [havePermission, setHavePermission] = useState(false)
  const [position, setPosition] = useState<GeolocationResponse | undefined>(undefined)
  const [sensorData, setSensorData] = useState<SensorData | undefined>(undefined)
  setUpdateIntervalForType(SensorTypes.magnetometer, 400) // defaults to 100ms
  useEffect(() => {
    Geolocation.requestAuthorization(() => {
      setHavePermission(true)
    }, (error) => {
      console.log(error)
    })
  }, [])
  useEffect(() => {
    if (havePermission) {
      void DeviceInfo.isEmulator().then((isEmulator) => {
        if (!isEmulator) {
          magnetometer.subscribe((data) => {
            setSensorData(data)
          })
        }
      })

      Geolocation.watchPosition((position) => {
        setPosition(position)
      }, (error) => {
        console.log(error)
      }, { enableHighAccuracy: true, distanceFilter: 0 })
      return () => {
        Geolocation.clearWatch(0)
      }
    }
  }, [havePermission])
  return [position, sensorData]
}
