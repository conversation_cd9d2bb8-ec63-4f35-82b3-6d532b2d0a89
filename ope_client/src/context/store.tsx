import { storage } from '@context/storage'
import { Appearance } from 'react-native'
import { create } from 'zustand'

export interface AccountType {
  token: string | null
  updateToken: (token: string | null) => void
  nickName: string | null
  updateNickName: (nickName: string | null) => void
  isDarkMode: boolean
  score: number
  updateScore: (score: number) => void
  ticketCode?: string
  updateTicketCode: (ticket: string) => void
  activeTicket: boolean
  updateActiveTicket: (active: boolean) => void
  isBgmEnabled: boolean
  updateBgmEnabled: (enabled: boolean) => void
}

export const useAccountStore = create<AccountType>((set) => ({
  isDarkMode: Appearance.getColorScheme() === 'dark',
  score: storage.getNumber('score') ?? 0,
  token: storage.getString('token') ?? null,
  nickName: storage.getString('nick_name') ?? null,
  updateScore: (score: number) => {
    storage.set('score', score)
    set((state) => ({ score }))
  },
  updateToken: (token: string | null) => {
    if (token === null) {
      storage.delete('token')
      set((state) => ({ token: null }))
    } else {
      storage.set('token', `Bearer ${token}`)
      set((state) => ({ token: `Bearer ${token}` }))
    }
  },
  updateNickName: (nickName: string | null) => {
    if (nickName === null) {
      storage.delete('nick_name')
      set((state) => ({ nickName: null }))
    } else {
      storage.set('nick_name', nickName)
      set((state) => ({ nickName }))
    }
  },
  ticketCode: storage.getString('ticket_code'),
  updateTicketCode: (ticket: string) => {
    storage.set('ticket_code', ticket)
    set((state) => ({ ticketCode: ticket }))
  },
  activeTicket: storage.getBoolean('ticket_active') ?? false,
  updateActiveTicket: (active: boolean) => {
    storage.set('ticket_active', active)
    set((state) => ({ activeTicket: active }))
  },
  isBgmEnabled: storage.getBoolean('bgm_enabled') ?? true,
  updateBgmEnabled: (enabled: boolean) => {
    storage.set('bgm_enabled', enabled)
    set((state) => ({ isBgmEnabled: enabled }))
  }
}))
