import { storage } from '@context/storage'
import { create } from 'zustand'
import { type Map } from '@network/login'
import { type MarksResp, type EventArea, type Mark, MarkType } from '@network/map'
export interface MapFilterItem {
  game: boolean
  exhibition: boolean
  event: boolean
  restaurant: boolean
  shop: boolean
  hotel: boolean
  other: boolean
  favorite: boolean
}
export interface MapType {
  map?: Map
  updateMap: (map: Map) => void
  countDown: number
  setCountdown: (countDown: number) => void
  filter: MapFilterItem
  updateFilter: (filter: MapFilterItem) => void
  areas: EventArea[]
  setMarksResp: (marksResp: MarksResp) => void
  marksResp: MarksResp
  marks: Mark[]
}

export const useMapStore = create<MapType>((set, get) => ({
  map: JSON.parse(storage.getString('map') ?? '{}'),
  updateMap: (map: Map) => {
    storage.set('map', JSON.stringify(map))
    set((state) => ({ map }))
  },
  countDown: 30,
  setCountdown: (countDown: number) => {
    set((state) => ({ countDown }))
  },
  filter: { game: true, exhibition: true, event: true, restaurant: true, shop: true, hotel: true, other: true, favorite: true },
  updateFilter: (filter: MapFilterItem) => {
    set((state) => ({ filter, marks: filtMark(get().marksResp.marks, filter) }))
  },
  areas: [],
  marks: [],
  setMarksResp: (marksResp: MarksResp) => {
    const filter = get().filter
    set(() => ({ marksResp, areas: marksResp.areas, marks: filtMark(marksResp.marks, filter) }))
  },
  marksResp: { areas: [], marks: [] }
}))

function filtMark(marks: Mark[], filter: MapFilterItem): Mark[] {
  const result: Mark[] = []
  marks.forEach((item) => {
    item.hidden = false
    switch (item.markType) {
      case MarkType.Game:
        item.hidden = !filter.game
        break
      case MarkType.Exhibition:
        item.hidden = !filter.exhibition
        break
      case MarkType.Event:
        item.hidden = !filter.event
        break
      case MarkType.Restaurant:
        item.hidden = !filter.restaurant
        break
      case MarkType.Shop:
        item.hidden = !filter.shop
        break
      case MarkType.Hotel:
        item.hidden = !filter.hotel
        break
      case MarkType.Other:
        item.hidden = !filter.other
        break
      default:
        // Unknown marker types are visible by default
        item.hidden = false
        break
    }
    if (!filter.favorite && item.collected) {
      item.hidden = true
    }
    result.push(item)
  })
  return result
}
