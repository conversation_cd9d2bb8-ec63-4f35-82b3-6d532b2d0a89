import React, { useEffect, type ReactNode } from 'react'
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated'
import RootSiblings from 'react-native-root-siblings'
import { UnistylesRuntime } from 'react-native-unistyles'

export default class Modal {
  static show(chilidren: ReactNode): { destroy: () => void } {
    let instance = { destroy: () => { } }

    instance = new RootSiblings(chilidren)
    return instance
  }

  static hide(toast: { destroy: () => void }): void {
    if (toast instanceof RootSiblings) {
      toast.destroy()
    } else {
      console.warn(`Toast.hide expected a \`RootSiblings\` instance as argument.\nBut got \`${typeof toast}\` instead.`)
    }
  }
}

export function ModalWrapper(props: { children: ReactNode }): React.JSX.Element {
  const offset = useSharedValue(0)

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: offset.value
  }))

  useEffect(() => {
    offset.value = withSpring(1, { duration: 400 })
    return () => {
      offset.value = withSpring(0, { duration: 400 })
    }
  }, [])

  return (<Animated.View style={[{
    paddingVertical: (UnistylesRuntime.screen.height - 444) / 2,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center'
  }, animatedStyle]}>{props.children}</Animated.View>
  )
}
