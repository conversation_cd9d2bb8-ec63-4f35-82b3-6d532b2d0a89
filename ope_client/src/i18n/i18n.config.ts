import i18next from 'i18next'
import { initReactI18next } from 'react-i18next'
import { findBestLanguageTag, getLocales } from 'react-native-localize'
import { en, kr } from './translates'

const codes = getLocales().flatMap(locale => locale.languageCode)
const language = findBestLanguageTag(codes)?.languageTag ?? 'en'
const resources = {
  en: {
    translation: en
  },
  kr: {
    translation: kr
  }
}

void i18next.use(initReactI18next).init({
  lng: language,
  compatibilityJSON: 'v3',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false
  },
  resources
})

export default i18next
