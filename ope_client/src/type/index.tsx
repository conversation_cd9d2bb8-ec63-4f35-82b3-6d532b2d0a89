import { type RouteProp } from '@react-navigation/native'
// type ParamListBase = Record<string, object | undefined>

export interface NaviProps {
  navigation: any
  route: RouteProp<any>
}

export type StackParamList = {
  Webview: { url: string, hide_navi?: boolean }
  Home: undefined
  Account: undefined
  Launch: undefined
  Login: undefined
  CountDown: undefined
  Intro: undefined
  History: undefined
  IntroOne: undefined
  IntroTwo: undefined
  SetProfile: undefined
  Rewards: undefined
  Scan: undefined
  Ranking: undefined
  MyRewards: undefined
  AboutRewards: undefined
  Information: undefined
  Ticket: undefined
  ActiveTicket: undefined
}

export const EVENT_ID = 1
