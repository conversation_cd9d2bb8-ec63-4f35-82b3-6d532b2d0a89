import React from 'react'
import { useTranslation } from 'react-i18next'
import { Text, View } from 'react-native'
import { createStyleSheet, useStyles } from 'react-native-unistyles'

enum LoadMoreStatus {
  normal,
  loading,
  end
}

export default function LoadMore(props: { loading: boolean, hasMore: boolean }): React.JSX.Element {
  const { t } = useTranslation()
  const { styles } = useStyles(styleSheet)
  let status = props.loading ? LoadMoreStatus.loading : LoadMoreStatus.normal
  if (!props.hasMore) {
    status = LoadMoreStatus.end
  }
  switch (status) {
    case LoadMoreStatus.end:
      return (
        <View style={styles.container}>
          <Text style={styles.text}>{t('no_more')}</Text>
        </View>
      )
    case LoadMoreStatus.loading:
      return (
        <View>
          <Text style={styles.text}>{t('loading')}</Text>
        </View>
      )
    default:
      return (
        <View>
          <Text style={styles.text}>{t('pull_to_load')}</Text>
        </View>
      )
  }
}

const styleSheet = createStyleSheet(theme => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60
  },
  text: {
    color: theme.colors.typography
  }
}))
