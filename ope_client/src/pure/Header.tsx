import React, { useCallback } from 'react'
import { Image, Text, TouchableOpacity, View } from 'react-native'
import { UnistylesRuntime, createStyleSheet, useStyles } from 'react-native-unistyles'
import { BlurView } from '@react-native-community/blur'

export default function Header(props: { title: string, onClickBack: () => void, mode?: 'dark' | 'light', blur?: boolean, bottom?: React.ReactNode }): React.JSX.Element {
  const { styles, theme } = useStyles(styleSheet)
  const { mode = 'dark', blur = false } = props
  const wrapper = useCallback((children: React.ReactNode) => {
    if (blur) {
      return <BlurView blurType='dark' blurAmount={10} style={styles.blur}>
        {children}
      </BlurView>
    }
    return <View style={[styles.container, mode === 'light' ? { backgroundColor: theme.colors.typography } : undefined]}>
      {children}
    </View>
  }, [])
  return wrapper(
    <View>
      <View style={styles.content}>
        <TouchableOpacity onPress={props.onClickBack} style={{ flex: 1, flexGrow: 1, display: 'flex', flexDirection: 'row' }}>
          <View>
            <Image source={require('@images/left_back.png')} style={styles.back} tintColor={mode === 'light' ? theme.colors.lightGrey : theme.colors.typography} />
          </View>
        </TouchableOpacity>
        <Text style={[styles.title, mode === 'light' ? { color: theme.colors.lightGrey } : undefined]}>{props.title}</Text>
        <View style={{ flex: 1, flexGrow: 1, display: 'flex', flexDirection: 'row-reverse' }}>
        </View>
      </View>
      {props.bottom}
    </View>
  )
}
const styleSheet = createStyleSheet(theme => ({
  container: {
    width: '100%',
    paddingTop: UnistylesRuntime.insets.top,
    backgroundColor: theme.colors.grey
  },
  blur: {
    position: 'absolute',
    width: '100%',
    paddingTop: UnistylesRuntime.insets.top

  },
  content: {
    height: 60,
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 22
  },
  title: {
    color: theme.colors.typography,
    fontSize: 20,
    fontWeight: '900'
  },
  back: {
    width: 17,
    height: 17
  }
}))
