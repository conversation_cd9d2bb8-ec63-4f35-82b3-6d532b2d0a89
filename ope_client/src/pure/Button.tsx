import { hapticTrigger } from '@context/utils'
import React from 'react'

import { Image, StyleSheet, Text, View, type TouchableOpacityProps } from 'react-native'
import { TouchableOpacity } from 'react-native-gesture-handler'

interface ButtonProps extends TouchableOpacityProps {
  text: string
}

function Button(props: ButtonProps): React.JSX.Element {
  const { text, style, onPress, ...otherProps } = props
  const disabled = props.disabled ?? false
  return (
    <TouchableOpacity
      {...otherProps}
      onPress={(e) => {
        hapticTrigger()
        onPress?.(e)
      }}
      activeOpacity={0.8}>
      <View
        style={[styles.button, disabled && styles.buttonDisable, style]}
      >
        {disabled &&
          <Image source={require('@images/button_mask_disable.png')} style={[{ position: 'absolute', width: '100%', height: '100%' }]} />
        }
        {!disabled &&
          <Image source={require('@images/button_mask.png')} style={[{ position: 'absolute', width: '100%', height: '100%' }]} />
        }
        <Text style={[styles.text, disabled && styles.textDisable]}>{text}</Text>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#6400FF',
    borderWidth: 2,
    borderColor: '#5707D3',
    overflow: 'hidden'
  },
  buttonDisable: {
    backgroundColor: 'rgb(102,102,102)',
    borderColor: 'rgb(92,92,92)'
  },
  text: {
    fontSize: 20,
    fontWeight: '900',
    letterSpacing: 0.25,
    color: 'white'
  },
  textDisable: {
    color: 'rgb(128,128,128)'
  }
})
export { Button }
