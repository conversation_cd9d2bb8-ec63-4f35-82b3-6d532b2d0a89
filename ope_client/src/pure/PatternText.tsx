import React from 'react'
import { Text, type ViewProps } from 'react-native'

interface PatternTextProps extends ViewProps {
  text: string
  pattern?: string
  replace?: React.JSX.Element
}

const PatternText = (props: PatternTextProps): React.JSX.Element => {
  const { text, pattern, replace } = props
  if (pattern == null || text == null) {
    return <Text style={props.style}>{text}</Text>
  }
  const parts = text.split(new RegExp(`(${pattern})`, 'g'))

  return (
    <Text style={props.style}>
      {parts.map((part, index) => (
        part === pattern
          ? replace
          : <Text key={index}>{part}</Text>
      ))}
    </Text>
  )
}

export default PatternText
