import React from 'react'
import { ActivityIndicator, StyleSheet, Text, View } from 'react-native'
function Loading(): React.JSX.Element {
  return (
    <>
      <LoadingContainer />
    </>
  )
}
function LoadingContainer(): React.JSX.Element {
  return (
    <View
      style={[styles.sibling]}
      pointerEvents='none'
    >
      <View style={styles.container}>
        <ActivityIndicator />
        <Text style={{ color: 'white' }}>Loading...</Text>
      </View>
    </View>
  )
}

export default Loading

const styles = StyleSheet.create({
  sibling: {
    zIndex: 10000,
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center'
  },
  container: {
    padding: 20,
    paddingHorizontal: 40,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    display: 'flex',
    gap: 20
  }
})
