import React, { type PropsWithChildren } from 'react'
import { Image, Pressable, StyleSheet, Text, View } from 'react-native'

interface SwiperViewProps extends PropsWithChildren {
  title: string
  content: string
  onPress?: () => void
}
export default function AlertContent(props: SwiperViewProps): React.JSX.Element {
  return (
    <View style={styles.slideContent}>
      <Image style={{ position: 'absolute', width: '100%', height: '100%' }} source={require('@images/mask.png')}></Image>
      <View style={{ width: '100%', height: 97 }}>
        {(props.onPress != null) &&
          <Pressable onPress={props.onPress}>
            <Image style={{ position: 'absolute', right: 10, top: 10, width: 48, height: 48, zIndex: 99 }} source={require('@images/white_close.png')}></Image>
          </Pressable>
        }
        <Text minimumFontScale={0.1} allowFontScaling={true} adjustsFontSizeToFit={true} style={styles.title} numberOfLines={1}>{props.title}</Text>
      </View>
      <View style={styles.slideTextContent}>
        {props.children}
        {props.children === undefined &&
          <Text style={styles.text}>{props.content}</Text>
        }
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  slideContent: {
    display: 'flex',
    borderRadius: 30,
    alignItems: 'center',
    width: '100%',
    height: '100%',
    backgroundColor: '#F2F2F2',
    overflow: 'hidden'
  },
  slideTextContent: {
    position: 'absolute',
    borderRadius: 30,
    left: 8,
    right: 8,
    bottom: 8,
    top: 58,
    backgroundColor: 'white',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'

  },
  text: {
    textAlign: 'center',
    color: 'black',
    fontSize: 30,
    fontWeight: 'bold'
  },
  title: {
    marginHorizontal: 60,
    paddingTop: 15,
    textAlign: 'center',
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    height: 50
  }
})
