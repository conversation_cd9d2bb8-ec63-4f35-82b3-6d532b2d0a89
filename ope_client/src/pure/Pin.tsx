import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react'
import { createStyleSheet, useStyles } from 'react-native-unistyles'
import { Image, Pressable, Text, TouchableOpacity, View, type PressableProps } from 'react-native'
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring
} from 'react-native-reanimated'
import { type Mark, type EventArea, MarkType } from '@network/map'
import { useMapStore } from '@context/map'

interface PinProps extends PressableProps {
  ref: any
  opacity: number
  data: EventArea
  onPressEvent: (event: EventArea) => void
}
interface SmallPinProps extends PressableProps {
  ref: any
  opacity: number
  data: Mark
  onPressEvent: (event: Mark) => void
}
export interface PinType {
  changeVisiable: (opacity: number) => void
}

const icons = [
  require('@images/icon_mission.png'),
  require('@images/icon_exhibition.png'),
  require('@images/icon_event.png'),
  require('@images/restraurant_marker.png'),
  require('@images/shop_marker.png'),
  require('@images/hotel_marker.png'),
  require('@images/ope_marker.png')
]
const Pin = forwardRef<PinType, PinProps>((props: PinProps, ref) => {
  const { styles } = useStyles(styleSheet)
  const { opacity, data, ...otherProps } = props
  const filter = useMapStore((state) => state.filter)
  const display = useSharedValue(props.opacity)
  const enableEvents = useSharedValue<any>(props.opacity === 1 ? 'auto' : 'none')
  const opacityAnimte = useAnimatedStyle(() => {
    return {
      opacity: display.value,
      pointerEvents: enableEvents.value
    }
  })
  useImperativeHandle(ref, () => ({
    changeVisiable(opacity: number) {
      display.value = withSpring(opacity)
      enableEvents.value = opacity === 1 ? 'auto' : 'none'
    }
  }))

  // Filter out disabled marker types
  const visibleTypes = data.types.filter((markType) => {
    switch (markType) {
      case MarkType.Game:
        return filter.game
      case MarkType.Exhibition:
        return filter.exhibition
      case MarkType.Event:
        return filter.event
      case MarkType.Restaurant:
        return filter.restaurant
      case MarkType.Shop:
        return filter.shop
      case MarkType.Hotel:
        return filter.hotel
      case MarkType.Other:
        return filter.other
      default:
        // Unknown marker types are visible by default
        return true
    }
  })

  // Hide entire area pin if no types are visible
  if (visibleTypes.length === 0) {
    return null
  }

  return (
    <Animated.View {...otherProps} style={[otherProps.style, styles.container, opacityAnimte]}>
      <Pressable
        onPress={(_e) => {
          props.onPressEvent(data)
        }}
      >
        <View style={styles.containerView}>
          {visibleTypes.map((item) => {
            const iconIndex = item - 1
            const iconSource = icons[iconIndex] !== undefined ? icons[iconIndex] : icons[0] // Fallback to first icon if index is out of bounds
            return <Image key={item} source={iconSource} style={[{ width: 26, height: 26 }]} />
          })}
          <Text style={styles.text}>{data.markCount}</Text>
        </View>
      </Pressable>
      {data.showPoint &&
        <Animated.Image source={require('@images/polygon.png')} style={[{ position: 'absolute', width: 19, height: 24, left: '50%', right: '50%', top: -20 }]} />
      }
      {data.collected &&
        <Image source={require('@images/favorite.png')} style={{ position: 'absolute', left: -5, top: -8, width: 16, height: 21 }} />
      }
    </Animated.View>
  )
})
Pin.displayName = 'Pin'

const SmallPin = forwardRef<PinType, SmallPinProps>((props: SmallPinProps, ref) => {
  const { styles, theme } = useStyles(styleSheet)
  const { opacity, data, ...otherProps } = props
  const display = useSharedValue(props.opacity)
  const [enableEvents, setEnableEvents] = useState('none')
  const opacityAnimte = useAnimatedStyle(() => {
    return {
      opacity: display.value
    }
  })
  const polygonRef = useRef<Image>(null)
  useImperativeHandle(ref, () => ({
    changeVisiable(opacity: number) {
      display.value = withSpring(opacity)
      setEnableEvents(opacity === 1 ? 'auto' : 'none')
    }
  }))
  const translateX = useSharedValue(0)
  const translateY = useSharedValue(0)
  const translateAlpha = useSharedValue(1)

  const polygonAnimatedStyle = useAnimatedStyle(() => ({
    opacity: translateAlpha.value,
    transform: [{ translateX: translateX.value }, { translateY: translateY.value }]
  }))

  const animatePolygon = useCallback(() => {
    if (!data.showPoint) {
      return
    }
    polygonRef.current?.measure((_x, _y, _width, _height, pageX, pageY) => {
      if (translateAlpha.value === 0) {
        return
      }
      translateX.value = withSpring(-(pageX / 2 - 30), { damping: 100, mass: 2 })
      translateY.value = withSpring(-(pageY / 2 - 30), { damping: 100, mass: 2 })
      translateAlpha.value = withSpring(0, { duration: 5000 })
    })
  }, [])
  if (data.hidden) {
    return null
  }
  // Helper function to get marker image source
  const getMarkerImage = (markType: number) => {
    switch (markType) {
      case MarkType.Game:
        return require('@images/mission_marker.png')
      case MarkType.Exhibition:
        return require('@images/exhibition_marker.png')
      case MarkType.Event:
        return require('@images/microphone_marker.png')
      case MarkType.Restaurant:
        return require('@images/restraurant_marker.png')
      case MarkType.Shop:
        return require('@images/shop_marker.png')
      case MarkType.Hotel:
        return require('@images/hotel_marker.png')
      case MarkType.Other:
        return require('@images/ope_marker.png')
      default:
        return require('@images/pin_small.png')
    }
  }

  // Render marker with specific icon for each type
  if (data.markType >= MarkType.Game && data.markType <= MarkType.Other) {
    return (
      <Animated.View style={[otherProps.style, styles.markerContainer, opacityAnimte, { pointerEvents: enableEvents as 'auto' | 'none' }]}>
        <TouchableOpacity
          style={{ pointerEvents: enableEvents as 'auto' | 'none' }}
          onPress={(_e) => {
            animatePolygon()
            props.onPressEvent(data)
          }}
        >
          <View style={styles.markerPill}>
            <Image
              source={getMarkerImage(data.markType)}
              style={styles.markerImage} />
            {data.boothNumber && (
              <Text style={styles.boothNumberText}>{data.boothNumber}</Text>
            )}
          </View>
          {data.collected &&
            <Image source={require('@images/favorite.png')} style={{ position: 'absolute', left: -5, top: -8, width: 16, height: 21 }} />
          }
          {data.showPoint &&
            <Animated.Image ref={polygonRef} source={require('@images/polygon.png')} style={[{ position: 'absolute', width: 19, height: 24, left: '50%', top: -20, marginLeft: -9.5 }, polygonAnimatedStyle]} />
          }
        </TouchableOpacity>
      </Animated.View>
    )
  }
  return (
    <Animated.View {...otherProps} style={[otherProps.style, styles.smallContainer, opacityAnimte, { pointerEvents: enableEvents as 'auto' | 'none' }]}>
      <TouchableOpacity
        style={{ pointerEvents: enableEvents as 'auto' | 'none' }}
        onPress={() => {
          animatePolygon()
          props.onPressEvent(data)
        }}
      >
        <View style={styles.smallContainerView}>
          <Image source={require('@images/pin_small_corner.png')} style={[{ position: 'absolute', width: 16, height: 23, left: 12, top: 30 }]} />
          <Image source={require('@images/pin_small.png')} style={[{ position: 'absolute', width: 40, height: 40 }]} />
          <Text style={[styles.text, { color: theme.colors.typography, textAlign: 'center' }]}>{data.name}</Text>
          {data.showPoint &&
            <Animated.Image ref={polygonRef} source={require('@images/polygon.png')} style={[{ position: 'absolute', width: 19, height: 24, left: 10, top: -20 }, polygonAnimatedStyle]} />
          }
        </View>
      </TouchableOpacity>
      {data.collected &&
        <Image source={require('@images/favorite.png')} style={{ position: 'absolute', left: -5, top: -8, width: 16, height: 21 }} />
      }
    </Animated.View>
  )
})
SmallPin.displayName = 'SmallPin'
const styleSheet = createStyleSheet(theme => ({
  container: {
    height: 38,
    transform: [{ translateX: -19 }, { translateY: -19 }],
    borderRadius: 34,
    borderWidth: 2,
    borderColor: theme.colors.border,
    backgroundColor: 'white',
    paddingLeft: 5,
    paddingRight: 8

  },
  containerView: {
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    gap: 4
  },
  text: {
    fontWeight: '900',
    fontSize: 20,
    color: '#666'
  },
  smallContainer: {
    width: 40,
    height: 40,
    marginLeft: -20,
    marginTop: -20,
    transform: [{ scale: 0.5 }]
  },
  smallContainerView: {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  imageContainer: {
    width: 30,
    height: 40,
    overflow: 'visible'
  },
  markerContainer: {
    marginLeft: -25,
    marginTop: -15,
    transform: [{ scale: 0.8 }]
  },
  markerPill: {
    backgroundColor: 'white',
    borderRadius: 20,
    paddingHorizontal: 8,
    paddingVertical: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  markerImage: {
    width: 20,
    height: 20,
  },
  markerImageNoMargin: {
    marginRight: 0,
  },
  markerPillImageOnly: {
    minWidth: 'auto',
    justifyContent: 'center',
  },
  boothNumberText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  }
}))

export { Pin, SmallPin }
