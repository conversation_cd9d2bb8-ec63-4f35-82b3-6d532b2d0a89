import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react'
import { StyleSheet, type ViewProps, Pressable, Image, View } from 'react-native'

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withRepeat,
  withSequence
} from 'react-native-reanimated'
import PatternText from './PatternText'

export enum BubbleCorner {
  left,
  top
}
interface BubbleProps extends ViewProps {
  text: string
  pattern?: string
  replace?: React.JSX.Element
  corner?: BubbleCorner
  onClick: () => void
  index?: number
  contents?: string[]
}

export interface BubbleType {
  changeIndex: (index: number) => void
}

const Bubble = forwardRef<BubbleType, BubbleProps>((props: BubbleProps, ref) => {
  const offset = useSharedValue(5)
  const [index, setIndex] = useState(props.index ?? 0)
  const corner = props.corner ?? BubbleCorner.left
  const { contents = [] } = props

  const alpha = useSharedValue(0)
  const alphaAnimatedStyles = useAnimatedStyle(() => {
    return {
      opacity: alpha.value
    }
  })
  useEffect(() => {
    alpha.value = withSequence(withTiming(0, { duration: 0 }), withTiming(1, { duration: 400 }))
  }, [index])

  const animatedStyles = useAnimatedStyle(() => ({
    transform: [{ translateY: offset.value }]
  }))
  useEffect(() => {
    offset.value = withRepeat(
      withTiming(-offset.value, { duration: 400 }),
      -1,
      true
    )
  }, [])
  useImperativeHandle(ref, () => ({
    changeIndex(index: number) {
      setIndex(index)
    }
  }))

  return (
    <Pressable style={[styles.container, props.style]}
      onPress={() => {
        if (contents.length > index + 1) {
          setIndex(index + 1)
        }
        props.onClick()
      }}
    >
      <View style={styles.blackView}>
        <Animated.View style={alphaAnimatedStyles}>
          <PatternText
            style={[styles.text]}
            text={props.contents !== undefined ? props.contents[index] : props.text}
            pattern={props.pattern}
            replace={props.replace}
          >
          </PatternText>
        </Animated.View>
        {corner === BubbleCorner.left &&
          <Image style={styles.leftCorner} source={require('@images/left_corner.png')} />
        }
        {corner === BubbleCorner.top &&
          <Image style={styles.topCorner} source={require('@images/top_corner.png')} />
        }
        <Animated.Image source={require('@images/pulldown_white.png')} style={[styles.corner, animatedStyles]}></Animated.Image>
      </View>
    </Pressable>
  )
})

Bubble.displayName = 'Bubble'

const styles = StyleSheet.create({
  container: {
    borderRadius: 16,
    backgroundColor: '#666666'
  },
  blackView: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 5,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 16,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    backgroundColor: '#222222',
    left: 0,
    top: 0
  },
  text: {
    textAlignVertical: 'center',
    textAlign: 'left',
    color: 'white',
    fontSize: 18,
    marginVertical: 5,
    marginHorizontal: 5
  },
  corner: {
    position: 'absolute',
    right: 10,
    bottom: 10,
    width: 8,
    height: 6
  },
  topCorner: {
    position: 'absolute',
    left: 32,
    top: -7,
    width: 18,
    height: 15

  },
  leftCorner: {
    position: 'absolute',
    left: 0,
    bottom: -9,
    width: 18,
    height: 15

  }
})

export default Bubble
