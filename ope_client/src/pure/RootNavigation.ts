import { createNavigationContainerRef } from '@react-navigation/native'

export const navigationRef = createNavigationContainerRef()

export function navigate(name: never, params: never): void {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params)
  }
}
export function goBack(): void {
  if (navigationRef.isReady()) {
    navigationRef.goBack()
  }
}
export function toHome(): void {
  if (navigationRef.isReady()) {
    navigationRef.navigate('Home' as never)
  }
}
