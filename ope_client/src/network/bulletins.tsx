import { API_HOST } from '@context/utils'
import { commonHeaders, generateUrl } from '@network/basic'
import { type OPEData } from '@network/type'
import { EVENT_ID } from '@type/index'

async function getBulletins(props: { pageNum: number, pageSize: number }): Promise<OPEData<BulletinsResp>> {
  const { pageNum, pageSize } = props
  return await fetch(generateUrl(`${API_HOST}bulletin/v1/bulletins`, { pageNum, pageSize, eventId: EVENT_ID }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

export interface BulletinsResp {
  endRow: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  list: Bulletins[]
  navigateFirstPage: number
  navigateLastPage: number
  navigatePages: number
  navigatepageNums: number[]
  nextPage: number
  pageNum: number
  pageSize: number
  pages: number
  prePage: number
  size: number
  startRow: number
  total: number
}

export interface Bulletins {
  brief: string
  createAt: Date
  eventId: number
  id: number
  pageUrl: string
  publishAt: Date
  published: boolean
  title: string
  updateAt: Date
  userId: number
}

export { getBulletins }
