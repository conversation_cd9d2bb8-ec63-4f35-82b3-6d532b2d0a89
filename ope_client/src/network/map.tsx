import { API_HOST } from '@context/utils'
import { type OPEData } from '@network/type'
import { commonHeaders, generateUrl } from './basic'
import { type SearchDetail } from './search'
import { EVENT_ID } from '@type/index'

async function getMarks(): Promise<OPEData<MarksResp>> {
  return await fetch(generateUrl(`${API_HOST}mark/v1/marks`, { eventId: EVENT_ID }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}
async function getMark(markId: number): Promise<OPEData<MarkDetail>> {
  return await fetch(generateUrl(`${API_HOST}mark/v1/mark`, { markId }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

async function changeCollect(props: { collected: boolean, markId: number }): Promise<OPEData<any>> {
  const { collected, markId } = props
  return await fetch(`${API_HOST}mark/v1/mark/collect`, {
    method: 'PUT',
    headers: commonHeaders(),
    body: JSON.stringify({ collected, markId })
  }).then(async response => await response.json())
}

async function collectList(props: { pageNum: number, pageSize: number }): Promise<OPEData<SearchDetail>> {
  const { pageNum, pageSize } = props
  return await fetch(generateUrl(`${API_HOST}mark/v1/mark/collect`, { pageNum, pageSize }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

export interface MarksResp {
  areas: EventArea[]
  marks: Mark[]
}

export interface EventArea {
  id: number
  name: string
  height: number
  width: number
  types: number[]
  userId: number
  xaxis: number
  yaxis: number
  markCount: number
  showPoint: boolean
  collected: boolean
  hidden: boolean
}

export enum MarkType {
  Game = 1,
  Exhibition = 2,
  Event = 3,
  Restaurant = 4,
  Shop = 5,
  Hotel = 6,
  Other = 7
}

export interface Mark {
  eventId: number
  id: number
  areaId: number
  name: string
  markType: number
  showPoint: boolean
  collected: boolean
  areaName: string
  shows: Show[]
  xaxis: number
  yaxis: number
  hidden: boolean
  boothNumber?: string
}

export interface Exhibitor {
  detail: string
  id: number
  name: string
  thumbnailUrl: string
  hasUserCompletedGame?: boolean
}

export interface Show {
  beginTime: number
  detail: string
  endTime: number
  id: number
  name: string
  thumbnailUrl: string
}

export interface MarkDetail {
  areaId: number
  eventId: number
  showPoint: boolean
  id: number
  markType: number
  name: string
  areaName: string
  content?: ExhibitionDetail | ShowDetail | GameDetail
  collected: boolean
  boothNumber?: string
}

export interface ExhibitionDetail {
  createAt: Date
  description: string
  id: number
  markId: number
  name: string
  thumbnailUrl: string
  updateAt: Date
  userId: number
  tags: string[] // Array of tags from backend
  websiteUrl?: string // Website URL for exhibition content
}

export interface ShowDetail {
  shows: ShowDetailItem[]
}

interface GameDetail {
  createAt: Date
  description: string
  id: number
  markId: number
  name: string
  gameUrl: string
  gameLevel: number
  updateAt: Date
  userId: number
}

export interface ShowDetailItem {
  beginTime: Date
  createAt: Date
  description: string
  endTime: Date
  id: number
  markId: string
  name: string
  thumbnailUrl: string
  updateAt: Date
  userId: number
}

export { getMarks, getMark, changeCollect, collectList, type GameDetail }
