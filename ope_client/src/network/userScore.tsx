import { API_HOST } from '@context/utils'
import { type OPEData } from '@network/type'
import { commonHeaders, generateUrl } from './basic'

async function getAllUserScoreRankings(n: number): Promise<OPEData<ScoreRankingResponse>> {
  return await fetch(generateUrl(`${API_HOST}rank/v1/showTopN`, { n }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

async function getMyUserScoreRankings(): Promise<OPEData<RankResult>> {
  return await fetch(`${API_HOST}rank/v1/rank`, {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

async function getCurrentScore(): Promise<OPEData<number>> {
  return await fetch(`${API_HOST}userScore/v1/currentScore`, {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}
export interface ScoreRankingResponse {
  rankResults: RankResult[]
}

export interface RankResult {
  nickName: string
  rank: number
  score: number
}

async function addMarkScore(markId: number): Promise<OPEData<any>> {
  return await fetch(`${API_HOST}mark/v1/addMarkScore`, {
    method: 'PUT',
    headers: commonHeaders(),
    body: JSON.stringify({ markId })
  }).then(async response => await response.json())
}

async function getUserHistoryScore(): Promise<OPEData<HistoryScoreResponse>> {
  return await fetch(`${API_HOST}userScore/v1/getUserHistoryScore`, {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

export interface HistoryScoreResponse {
  historyScores: HistoryScore[]
}

export interface HistoryScore {
  date: Date
  scoreRecordList: ScoreRecord[]
}

export interface ScoreRecord {
  score?: string
  sourceName?: string
  finishTime: number
  reasons: string
}

export { getAllUserScoreRankings, getMyUserScoreRankings, addMarkScore, getUserHistoryScore, getCurrentScore }
