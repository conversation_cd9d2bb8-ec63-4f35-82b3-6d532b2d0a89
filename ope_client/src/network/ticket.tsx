import { API_HOST } from '@context/utils'
import { commonHeaders, generateUrl } from './basic'
import { type OPEData } from './type'
import { EVENT_ID } from '@type/index'

async function getTicket(): Promise<OPEData<Ticket>> {
  return await fetch(generateUrl(`${API_HOST}ticket/v1/ticket`, { eventId: EVENT_ID }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

async function bindTicket(code: string): Promise<OPEData<Ticket>> {
  return await fetch(`${API_HOST}ticket/v1/ticket`, {
    method: 'POST',
    headers: commonHeaders(),
    body: JSON.stringify({ code, eventId: EVENT_ID })
  }).then(async response => await response.json())
}

async function checkTicket(code: string): Promise<OPEData<Ticket>> {
  return await fetch(`${API_HOST}ticket/v1/ticket/check`, {
    method: 'POST',
    headers: commonHeaders(),
    body: JSON.stringify({ code, eventId: EVENT_ID })
  }).then(async response => await response.json())
}

export interface Ticket {
  boundAt: Date
  checkedAt: Date
  code: string
  createAt: Date
  eventId: number
  id: number
  name: string
  updateAt: Date
  userId: number
}

export { getTicket, bindTicket, checkTicket }
