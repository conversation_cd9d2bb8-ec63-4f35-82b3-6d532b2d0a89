import { API_HOST } from '@context/utils'
import { type OPEData } from './type'
import { commonHeaders, generateUrl } from './basic'

async function searchWord(word: string): Promise<OPEData<SearchDetail>> {
  return await fetch(generateUrl(`${API_HOST}exhibitor/v1/search`, { pageNum: 1, pageSize: 100, word }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

export interface SearchDetail {
  endRow: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  list: SearchItem[]
  navigateFirstPage: number
  navigateLastPage: number
  navigatePages: number
  navigatepageNums: number[]
  nextPage: number
  pageNum: number
  pageSize: number
  pages: number
  prePage: number
  size: number
  startRow: number
  total: number
}

export interface SearchItem {
  areaId: number
  areaName: string
  collected: boolean
  description: string
  eventId: number
  showPoint: boolean
  id: number
  markId: number
  markName: string
  name: string
  thumbnailUrl: string
  xaxis: number
  yaxis: number
  contentName: string
  markType: number
}

export { searchWord }
