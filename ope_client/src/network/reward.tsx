import { API_HOST } from '@context/utils'
import { commonHeaders, generateUrl } from './basic'
import { type OPEData } from './type'

async function myRewards(): Promise<OPEData<MyRewards>> {
  return await fetch(`${API_HOST}reward/v1/my/rewards`, {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

async function buyReward(rewardId: number): Promise<OPEData<Rewards>> {
  return await fetch(`${API_HOST}reward/v1/my/reward`, {
    method: 'POST',
    headers: commonHeaders(),
    body: JSON.stringify({ rewardId })
  }).then(async response => await response.json())
}

async function rewardsList(eventId: number): Promise<OPEData<Rewards>> {
  return await fetch(generateUrl(`${API_HOST}reward/v1/rewards`, { eventId }), {
    method: 'GET',
    headers: commonHeaders()
  }).then(async response => await response.json())
}

export interface Rewards {
  rewards: Reward[]
}

export interface MyRewards {
  myRewards: Reward[]
}

export interface Reward {
  boughtAt: number
  description: string
  detailDescription: string
  detailImageUrls: string[]
  exchangedAt: number
  id: number
  imageUrl: string
  name: string
  pointPrice: number
  userId: number
  inventory: number
}

export { myRewards, rewardsList, buyReward }
