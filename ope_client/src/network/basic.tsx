import { useAccountStore } from '@context/store'

export function generateUrl(host: string, args: object): string {
  return `${host}?${Object.entries(args).map(([key, value]) => `${key}=${value}`).join('&')}`
}

export function commonParams(): Record<string, any> {
  // const state = useAccountStore.getState()
  return {
  }
}
export function commonHeaders(): Record<string, string> {
  const state = useAccountStore.getState()
  return {
    Authorization: state.token ?? '',
    // Authorization: state.token ? `Bearer ${state.token}` : '',
    'Content-Type': 'application/json'
  }
}
