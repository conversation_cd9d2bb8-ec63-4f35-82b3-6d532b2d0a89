import { type OPEData } from '@network/type'
import { API_HOST, ACCOUNT_API_HOST } from '@context/utils'
import { merge } from 'lodash'
import { commonHeaders, commonParams, generateUrl } from './basic'
import { EVENT_ID } from '@type/index'
import dayjs from 'dayjs'
async function sendVerifyCode(phoneNumber: string): Promise<OPEData<any>> {
  const path = 'user/login/sendVerifyCode'
  // if (__DEV__) {
  //   path = 'user/login/sendVerifyCodeMock'
  // }

  console.log('📱 Sending SMS to:', phoneNumber)

  try {
    const response = await fetch(`${ACCOUNT_API_HOST}${path}`, {
      method: 'POST',
      headers: commonHeaders(),
      body: JSON.stringify({ phoneNumber })
    })

    const result = await response.json()
    console.log('📱 SMS API Response:', result)

    if (!response.ok) {
      console.error('📱 SMS API Error - HTTP Status:', response.status)
      throw new Error(`HTTP ${response.status}: ${result.msg || 'Failed to send SMS'}`)
    }

    return result
  } catch (error) {
    console.error('📱 SMS Network Error:', error)
    throw error
  }
}

async function phoneNumberLogin(props: {phoneNumber: string, verifyCode: string}): Promise<OPEData<any>> {
  const { phoneNumber, verifyCode } = props

  console.log('🔐 Attempting login with phone:', phoneNumber, 'code:', verifyCode)

  try {
    const response = await fetch(`${ACCOUNT_API_HOST}user/login/phoneNumberLogin`, {
      method: 'POST',
      headers: commonHeaders(),
      body: JSON.stringify({ phoneNumber, verifyCode })
    })

    const result = await response.json()
    console.log('🔐 Login API Response:', result)

    if (!response.ok) {
      console.error('🔐 Login API Error - HTTP Status:', response.status)
      throw new Error(`HTTP ${response.status}: ${result.msg || 'Login failed'}`)
    }

    return result
  } catch (error) {
    console.error('🔐 Login Network Error:', error)
    throw error
  }
}
async function unregister(): Promise<OPEData<any>> {
  return await fetch(`${ACCOUNT_API_HOST}user/unregister`, {
    method: 'POST',
    headers: commonHeaders()
  }).then(async response => await response.json())
}
async function updateProfile(profile: { birthday?: string, gender?: number, location?: string, nickname?: string }): Promise<OPEData<UserInfo>> {
  return await fetch(`${ACCOUNT_API_HOST}user/updateUserInfo`, {
    method: 'POST',
    headers: commonHeaders(),
    body: JSON.stringify(merge(profile, commonParams()))
  }).then(async response => await response.json())
}

async function event(): Promise<OPEData<EventResponse>> {
  return await fetch(`${API_HOST}event/v1/event`, {
    method: 'POST',
    headers: commonHeaders(),
    body: JSON.stringify({ eventId: EVENT_ID })
  }).then(async response => await response.json())
}

export interface EventResponse {
  countdown: number
  eventInfo: EventInfo
  map: Map
}

export interface EventInfo {
  createAt: Date
  description: string
  endDate: Date
  id: number
  name: string
  startDate: Date
  updateAt: Date
  userId: number
}

export interface Map {
  createAt: Date
  description: string
  eventId: number
  id: number
  mapHeight: number
  mapUrl: string
  mapWidth: number
  name: string
  updateAt: Date
  userId: number
}

export interface UserInfo {
  authToken: string
  birthdayStr: string
  gender: number
  id: number
  location: string
  nickname: string
  point: number
  profileBgImg: string
  profileImg: string
  registerTime: number
  shopId: number
  signature: string
}

export { sendVerifyCode, phoneNumberLogin, updateProfile, event, unregister }
