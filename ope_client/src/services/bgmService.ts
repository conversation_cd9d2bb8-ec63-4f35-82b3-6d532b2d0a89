/* eslint-disable @typescript-eslint/strict-boolean-expressions */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import Sound from 'react-native-sound'
import { RESOURCE_HOST } from '@context/utils'
import { useAccountStore } from '@context/store'

class BGMService {
  private sound: Sound | null = null
  private isInitialized = false
  private currentTrack = 3

  constructor() {
    // Enable playback in silence mode
    Sound.setCategory('Playback')
  }

  async initialize(trackNumber: number = 1): Promise<void> {
    // Release previous sound if exists
    if (this.sound !== null) {
      this.sound.release()
    }

    this.currentTrack = Math.max(1, Math.min(10, trackNumber))
    this.isInitialized = false

    await new Promise((resolve, reject) => {
      const bgmUrl = `${RESOURCE_HOST}music/${this.currentTrack}.mp3`

      this.sound = new Sound(bgmUrl, '', (error) => {
        if (error) {
          console.error('Failed to load BGM:', error)
          reject(error)
          return
        }

        if (this.sound) {
          this.sound.setNumberOfLoops(-1) // Loop indefinitely
          this.sound.setVolume(0.3) // Set volume to 30%
        }

        this.isInitialized = true
        console.log(`BGM track ${this.currentTrack} initialized successfully`)
        resolve(undefined)
      })
    })
  }

  async changeTrack(trackNumber: number): Promise<void> {
    const targetTrack = Math.max(1, Math.min(10, trackNumber))

    // Don't reinitialize if we're already on the correct track
    if (this.currentTrack === targetTrack && this.isInitialized) {
      console.log(`Already on track ${targetTrack}, skipping initialization`)
      return
    }

    const wasPlaying = this.sound?.isPlaying() ?? false
    await this.initialize(targetTrack)

    if (wasPlaying) {
      this.play()
    }
  }

  getCurrentTrack(): number {
    return this.currentTrack
  }

  play(): void {
    if (!this.isInitialized || !this.sound) {
      console.log('BGM play failed: not initialized or no sound object')
      return
    }
    console.log(`Playing BGM track ${this.currentTrack}`)
    this.sound.play()
  }

  pause(): void {
    if (!this.sound) {
      return
    }

    this.sound.pause()
  }

  setVolume(volume: number): void {
    if (!this.sound) {
      return
    }

    this.sound.setVolume(Math.max(0, Math.min(1, volume)))
  }

  release(): void {
    if (this.sound) {
      this.sound.release()
      this.sound = null
      this.isInitialized = false
    }
  }
}

// Create a singleton instance
export const bgmService = new BGMService()

// Hook to manage BGM state with the global store
export const useBGM = () => {
  const { isBgmEnabled, updateBgmEnabled } = useAccountStore()

  const initializeBGM = async (trackNumber: number = 1) => {
    try {
      // Only initialize if BGM is enabled
      if (!isBgmEnabled) {
        console.log(`BGM is disabled, skipping initialization of track ${trackNumber}`)
        return
      }

      await bgmService.initialize(trackNumber)
      // Auto-play since BGM is enabled
      bgmService.play()
    } catch (error) {
      console.error('Failed to initialize BGM:', error)
    }
  }

  const toggleBGM = async () => {
    const newState = !isBgmEnabled
    updateBgmEnabled(newState)

    if (newState) {
      // If enabling BGM, initialize with a default track (home screen track 3)
      console.log('BGM enabled, initializing with track 3 (home default)')
      try {
        await bgmService.initialize(3)
        bgmService.play()
      } catch (error) {
        console.error('Failed to initialize BGM when toggling on:', error)
      }
    } else {
      bgmService.pause()
    }
  }

  const changeTrack = async (trackNumber: number) => {
    try {
      // Only change track if BGM is enabled
      if (!isBgmEnabled) {
        console.log(`BGM is disabled, skipping track change to ${trackNumber}`)
        return
      }

      await bgmService.changeTrack(trackNumber)
      // Auto-play since BGM is enabled
      bgmService.play()
    } catch (error) {
      console.error('Failed to change BGM track:', error)
    }
  }

  return {
    isBgmEnabled,
    initializeBGM,
    toggleBGM,
    changeTrack,
    bgmService
  }
}
