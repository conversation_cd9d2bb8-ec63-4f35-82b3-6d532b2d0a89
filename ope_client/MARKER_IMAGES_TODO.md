# Marker Images TODO

The mobile app has been updated to support all 7 marker types, but placeholder marker images need to be created for the new types.

## Current Images (Available)
- ✅ `mission_marker.png` - Used for Game markers (type 1)
- ✅ `microphone_marker.png` - Used for Event markers (type 3)

## Images Needed (Create These)
- 🔲 `exhibition_marker.png` - For Exhibition/Booth markers (type 2)
  - Suggested icon: Booth/stand/display icon
  - Size: 30x40px (same as existing markers)
  - Style: Match existing marker design

- 🔲 `restaurant_marker.png` - For Restaurant markers (type 4)
  - Suggested icon: Fork and knife, or plate icon
  - Size: 30x40px
  - Style: Match existing marker design

- 🔲 `shop_marker.png` - For Shop markers (type 5)
  - Suggested icon: Shopping bag or cart icon
  - Size: 30x40px
  - Style: Match existing marker design

- 🔲 `hotel_marker.png` - For Hotel markers (type 6)
  - Suggested icon: Bed or building icon
  - Size: 30x40px
  - Style: Match existing marker design

- 🔲 `other_marker.png` - For Other/Facility markers (type 7)
  - Suggested icon: Information "i" or question mark icon
  - Size: 30x40px
  - Style: Match existing marker design

## Filter Images Needed (Optional Enhancement)
For better filter UI, create specific filter icons:
- 🔲 `filter_game.png` & `filter_game_disable.png`
- 🔲 `filter_restaurant.png` & `filter_restaurant_disable.png`
- 🔲 `filter_shop.png` & `filter_shop_disable.png`
- 🔲 `filter_hotel.png` & `filter_hotel_disable.png`
- 🔲 `filter_other.png` & `filter_other_disable.png`

## Current Fallback Behavior
Until these images are created, the app will use `mission_marker.png` as a fallback for all new marker types. This ensures the app works correctly but all markers will look the same.

## Design Guidelines
- Match the style and color scheme of existing markers
- Use clear, recognizable icons for each category
- Ensure icons are visible at small sizes
- Consider using consistent color coding:
  - Game: Purple
  - Exhibition: Orange
  - Event: Pink
  - Restaurant: Red
  - Shop: Green
  - Hotel: Blue
  - Other: Gray/Yellow

## Implementation Status
- ✅ Backend: All marker types supported
- ✅ Admin Interface: All marker types supported with colors
- ✅ Mobile App Logic: Updated to handle all 7 types
- ✅ Mobile App Filtering: Updated for all types
- ✅ Mobile App Collections: Updated for all types
- ✅ Translations: Added for all new types
- 🔲 Mobile App Images: Need to create placeholder images

## Usage Examples for "Other" Markers
The "Other" category is designed for general venue facilities:
- Restrooms/Toilets
- Information booths
- First aid stations
- ATMs
- Lost & found
- Emergency exits
- Phone charging stations
- Wi-Fi help desks
- Security checkpoints
- Accessibility services
