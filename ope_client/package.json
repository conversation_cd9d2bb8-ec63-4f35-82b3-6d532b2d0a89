{"name": "OPE_Client", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start --experimental-debugger", "startClear": "react-native start --experimental-debugger --reset-cache", "prepare": "cd ./ios && bundle exec pod install && cd ..", "prepareNew": "cd ./ios && RCT_NEW_ARCH_ENABLED=1 bundle exec pod install && cd ..", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.6", "@openspacelabs/react-native-zoomable-view": "2.1.6", "@react-native-community/blur": "^4.4.0", "@react-native-community/geolocation": "^3.2.1", "@react-native-firebase/analytics": "^20.1.0", "@react-native-firebase/app": "^20.1.0", "@react-native-picker/picker": "^2.7.2", "@react-navigation/native": "^6.0.8", "@react-navigation/native-stack": "^6.9.17", "@shopify/react-native-skia": "^1.3.2", "@tanstack/react-query": "^5.51.1", "dayjs": "^1.11.11", "i18next": "^23.10.1", "lodash": "^4.17.21", "lottie-react-native": "^6.7.0", "react": "^18.2.0", "react-i18next": "^14.1.0", "react-native": "^0.75.0", "react-native-animated-numbers": "^0.6.2", "react-native-date-picker": "^4.3.5", "react-native-device-info": "^11.1.0", "react-native-drawer-layout": "3.3.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.18.1", "react-native-haptic-feedback": "^2.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-localize": "^3.0.6", "react-native-mmkv": "^2.11.0", "react-native-pager-view": "^6.2.3", "react-native-picker-select": "^9.1.3", "react-native-qrcode-svg": "^6.3.1", "react-native-reanimated": "^3.15.0", "react-native-root-toast": "^3.5.1", "react-native-safe-area-context": "^4.10.5", "react-native-screens": "^3.34.0", "react-native-sensors": "^7.3.6", "react-native-share": "^10.2.0", "react-native-sound": "^0.11.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.6.0", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^3.5.2", "react-native-unistyles": "^2.4.0", "react-native-vision-camera": "4.0.4", "react-native-webview": "^13.12.2", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.75.0", "@react-native/eslint-config": "^0.74.0", "@react-native/metro-config": "^0.75.0", "@react-native/typescript-config": "^0.74.0", "@types/lodash": "^4.17.0", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.4.0", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.0.1", "eslint-config-standard-with-typescript": "^43.0.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.2.1", "react-devtools": "^5.0.0", "react-test-renderer": "^18.2.0", "reactotron-react-native": "^5.1.6", "reactotron-react-native-mmkv": "^0.2.6", "typescript": "*"}, "engines": {"node": ">=18"}, "extends": "@react-native", "packageManager": "yarn@4.1.1"}