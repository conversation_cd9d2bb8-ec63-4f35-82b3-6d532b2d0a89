{
  "presets": ['module:@react-native/babel-preset'
  ],
  "plugins": [
    "react-native-reanimated/plugin",
    [
      "module-resolver",
      {
        "root": [
          "./src"
        ],
        "alias": {
          "@images": "./src/images/",
          "@pages": "./src/pages/",
          "@context": "./src/context/",
          "@type": "./src/type/",
          "@pure": "./src/pure/",
          "@network": "./src/network/",
          "@static": "./src/static/",
          "@services": "./src/services/"
        }
      }
    ]
  ]
}
