import { bgmService } from '@services/bgmService'

// Mock react-native-sound
jest.mock('react-native-sound', () => {
  const mockSound = {
    setNumberOfLoops: jest.fn(),
    setVolume: jest.fn(),
    play: jest.fn((callback) => callback(true)),
    pause: jest.fn(),
    stop: jest.fn(),
    release: jest.fn()
  }

  const Sound = jest.fn((url, basePath, callback) => {
    // Simulate successful loading
    setTimeout(() => callback(null), 0)
    return mockSound
  })

  Sound.setCategory = jest.fn()
  
  return Sound
})

// Mock the context utils
jest.mock('@context/utils', () => ({
  RESOURCE_HOST: 'https://test-host.com/'
}))

// Mock the store
jest.mock('@context/store', () => ({
  useAccountStore: () => ({
    isBgmEnabled: true,
    updateBgmEnabled: jest.fn()
  })
}))

describe('BGMService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    bgmService.release()
  })

  test('should initialize successfully', async () => {
    await expect(bgmService.initialize()).resolves.toBeUndefined()
  })

  test('should play music when enabled', async () => {
    await bgmService.initialize()
    bgmService.play()
    expect(bgmService.getIsPlaying()).toBe(true)
  })

  test('should pause music', async () => {
    await bgmService.initialize()
    bgmService.play()
    bgmService.pause()
    expect(bgmService.getIsPlaying()).toBe(false)
  })

  test('should stop music', async () => {
    await bgmService.initialize()
    bgmService.play()
    bgmService.stop()
    expect(bgmService.getIsPlaying()).toBe(false)
  })

  test('should toggle music playback', async () => {
    await bgmService.initialize()
    
    // Start playing
    bgmService.toggle()
    expect(bgmService.getIsPlaying()).toBe(true)
    
    // Pause
    bgmService.toggle()
    expect(bgmService.getIsPlaying()).toBe(false)
  })

  test('should set volume correctly', async () => {
    await bgmService.initialize()
    bgmService.setVolume(0.5)
    // Volume setting is tested through the mock
  })

  test('should handle volume bounds correctly', async () => {
    await bgmService.initialize()
    
    // Test upper bound
    bgmService.setVolume(1.5)
    
    // Test lower bound
    bgmService.setVolume(-0.5)
    
    // These should not throw errors and should clamp values
  })

  test('should release resources properly', async () => {
    await bgmService.initialize()
    bgmService.release()
    expect(bgmService.getIsPlaying()).toBe(false)
  })
})
