/**
 * @format
 */

import { type UserInfo } from '../src/network/login'

// Mock profile validation function (extracted from RewardDetail component)
const isProfileComplete = (profile: UserInfo | null): boolean => {
  if (profile === null || profile === undefined) return false
  
  // Check required fields: nickname, gender, birthday
  const hasNickname = profile.nickname !== null && profile.nickname !== undefined && profile.nickname.trim() !== ''
  const hasGender = profile.gender !== null && profile.gender !== undefined
  const hasBirthday = profile.birthdayStr !== null && profile.birthdayStr !== undefined && profile.birthdayStr.trim() !== ''
  
  return hasNickname && hasGender && hasBirthday
}

describe('Profile Validation for Reward Claiming', () => {
  // Test that profile validation works correctly for reward claiming
  // This ensures users must have complete profiles (nickname, gender, birthday) before claiming rewards
  it('should return false for null profile', () => {
    expect(isProfileComplete(null)).toBe(false)
  })

  it('should return false for profile with missing nickname', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '1990-01-01',
      gender: 0,
      id: 1,
      location: 'Japan',
      nickname: '', // Empty nickname
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(false)
  })

  it('should return false for profile with missing gender', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '1990-01-01',
      gender: null as any, // Missing gender
      id: 1,
      location: 'Japan',
      nickname: 'TestUser',
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(false)
  })

  it('should return false for profile with missing birthday', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '', // Empty birthday
      gender: 0,
      id: 1,
      location: 'Japan',
      nickname: 'TestUser',
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(false)
  })

  it('should return true for complete profile', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '1990-01-01',
      gender: 0,
      id: 1,
      location: 'Japan',
      nickname: 'TestUser',
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(true)
  })

  it('should return true for profile with gender 1 (female)', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '1990-01-01',
      gender: 1, // Female
      id: 1,
      location: 'Japan',
      nickname: 'TestUser',
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(true)
  })

  it('should handle whitespace-only nickname', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '1990-01-01',
      gender: 0,
      id: 1,
      location: 'Japan',
      nickname: '   ', // Whitespace only
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(false)
  })

  it('should handle whitespace-only birthday', () => {
    const profile: UserInfo = {
      authToken: 'token',
      birthdayStr: '   ', // Whitespace only
      gender: 0,
      id: 1,
      location: 'Japan',
      nickname: 'TestUser',
      point: 100,
      profileBgImg: '',
      profileImg: '',
      registerTime: Date.now(),
      shopId: 1,
      signature: ''
    }
    expect(isProfileComplete(profile)).toBe(false)
  })

  // Note: In the actual RewardDetail component, profile data is refetched using useFocusEffect
  // whenever the user returns to the screen (e.g., after completing profile in Account page).
  // This ensures the validation always uses the most up-to-date profile information.
})
