{"env": {"browser": true, "es2021": true}, "extends": ["plugin:react/recommended", "standard-with-typescript"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["react", "react-native"], "overrides": [{"files": ["*.tsx", "*.jsx", "*.ts", "*.js"], "rules": {"@typescript-eslint/space-before-function-paren": ["off", "never"], "@typescript-eslint/consistent-type-definitions": ["off", "never"]}, "extends": ["standard-with-typescript"]}]}