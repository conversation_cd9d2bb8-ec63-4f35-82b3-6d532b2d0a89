{"version": "0.2.0", "configurations": [{"name": "Debug iOS", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "launch", "platform": "ios", "target": "simulator", "iosSimulatorTarget": "iPhone 16 Pro", "sourceMaps": true, "outDir": "${workspaceFolder}/.vscode/.react"}, {"name": "Debug Android", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "launch", "platform": "android", "sourceMaps": true, "outDir": "${workspaceFolder}/.vscode/.react"}, {"name": "Attach to packager", "cwd": "${workspaceFolder}", "type": "reactnative", "request": "attach", "sourceMaps": true, "outDir": "${workspaceFolder}/.vscode/.react"}]}