import React, { useEffect } from 'react'
import {
  QueryClient,
  QueryClientProvider
} from '@tanstack/react-query'
import { RootSiblingParent } from 'react-native-root-siblings'

import { navigationRef } from '@pure/RootNavigation'
import { NavigationContainer } from '@react-navigation/native'

import { createNativeStackNavigator } from '@react-navigation/native-stack'

import { useAccountStore } from '@context/store'
import Account from '@pages/account'
import CountDown from '@pages/countdown/countdown.tsx'
import Home from '@pages/home/<USER>'
import Intro from '@pages/intro/intro'
import IntroOne from '@pages/intro/introOne'
import IntroTwo from '@pages/intro/introTwo'
import Launch from '@pages/launch/launch.tsx'
import Login from '@pages/login/login.tsx'
import SetProfile from '@pages/login/profile'
import MyRewards from '@pages/myRewards'
import Ranking from '@pages/ranking'
import Rewards from '@pages/rewards/rewards'
import Scan from '@pages/scan/Scan'
import Webview from '@pages/webview'
import Ticket from '@pages/ticket/detail'
import ActiveTicket from '@pages/ticket/active'
import History from '@pages/history'
import Information from '@pages/information'
import AboutRewards from '@pages/myRewards/aboutRewards'
import { type StackParamList } from '@type/index'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import SplashScreen from 'react-native-splash-screen'
import { track } from '@context/utils'

const Stack = createNativeStackNavigator<StackParamList>()
const queryClient = new QueryClient()

function App(): React.JSX.Element {
  const token = useAccountStore((state) => state.token)

  useEffect(() => {
    console.log('🚀 App launched successfully!')
    console.log('📱 Token status:', token ? 'Logged in' : 'Not logged in')
    SplashScreen.hide()
    track('app_launch')
  }, [])
  return (
    <NavigationContainer ref={navigationRef}>
      <Stack.Navigator initialRouteName={token !== null ? 'Home' : 'Launch'}>
        <Stack.Screen name="Home" component={Home} options={{ headerShown: false }} />
        <Stack.Screen name="Launch" component={Launch} options={{ headerShown: false }} />
        <Stack.Screen name="Login" component={Login} options={{ headerShown: false }} />
        <Stack.Screen name="CountDown" component={CountDown} options={{ headerShown: false }} />
        <Stack.Screen name="Intro" component={Intro} options={{ headerShown: false, animation: 'fade' }} />
        <Stack.Screen name="IntroOne" component={IntroOne} options={{ headerShown: false, animation: 'fade' }} />
        <Stack.Screen name="IntroTwo" component={IntroTwo} options={{ headerShown: false }} />
        <Stack.Screen name="SetProfile" component={SetProfile} options={{ headerShown: false }} />
        <Stack.Screen name="Rewards" component={Rewards} options={{ headerShown: false, animation: 'slide_from_bottom' }} />
        <Stack.Screen name="Scan" component={Scan} options={{ headerShown: false, animation: 'slide_from_bottom' }} />
        <Stack.Screen name="Webview" component={Webview} options={{ headerShown: false }} />
        <Stack.Screen name="Ranking" component={Ranking} options={{ headerShown: false }} />
        <Stack.Screen name="Account" component={Account} options={{ headerShown: false }} />
        <Stack.Screen name="MyRewards" component={MyRewards} options={{ headerShown: false }} />
        <Stack.Screen name="AboutRewards" component={AboutRewards} options={{ headerShown: false }} />
        <Stack.Screen name="Information" component={Information} options={{ headerShown: false }} />
        <Stack.Screen name="History" component={History} options={{ headerShown: false }} />
        <Stack.Screen name="Ticket" component={Ticket} options={{ headerShown: false }} />
        <Stack.Screen name="ActiveTicket" component={ActiveTicket} options={{ headerShown: false }} />
      </Stack.Navigator>
    </NavigationContainer>
  )
}

function WrapApp(): React.JSX.Element {
  return (
    <QueryClientProvider client={queryClient}>
      <RootSiblingParent>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <App />
        </GestureHandlerRootView>
      </RootSiblingParent>
    </QueryClientProvider>
  )
}

export default WrapApp
