graph TD
    A["🚀 App Launch<br/>Splash screen with loading<br/>State: useAccountStore.token<br/>No API calls"] --> B{User Authenticated?}
    
    B -->|No| C["📱 Launch Screen<br/>Event loading & map preload<br/>API: POST /event/v1/event {eventId}<br/>State: useMapStore.updateMap()"]
    B -->|Yes| D["🗺️ Home Screen<br/>Interactive map with pins<br/>API: GET /mark/v1/marks?eventId=1<br/>State: useMapStore.marks, useAccountStore.score"]
    
    C --> E["🔐 Login Screen<br/>Phone number & verification input<br/>API: POST /user/login/sendVerifyCode {phoneNumber}<br/>State: phone, code, countDown"]
    E --> F["📞 Phone Input<br/>Country code picker & phone field<br/>No API calls<br/>State: phone, areaCode"]
    F --> G["🔢 Verification Code<br/>6-digit code input field<br/>API: POST /user/login/phoneNumberLogin {phoneNumber, verifyCode}<br/>State: useAccountStore.updateToken()"]
    G --> H["⏰ CountDown Screen<br/>Circular timer with days remaining<br/>No API calls<br/>State: useMapStore.countDown"]
    H --> I{Event Started?}
    I -->|No| H
    I -->|Yes| J["👋 Intro Screen<br/>Welcome with Portaro character<br/>No API calls<br/>State: Lottie animation"]
    
    J --> K["📖 Intro One<br/>Tutorial slides with swiper<br/>No API calls<br/>State: useIntroOneStore.index"]
    K --> L{Has Nickname?}
    L -->|No| M["👤 Set Profile<br/>Nickname, country, birthday form<br/>API: POST /user/updateUserInfo {nickname, location, birthday}<br/>State: useAccountStore.updateNickName()"]
    L -->|Yes| N["✅ Intro Two<br/>Final onboarding step<br/>No API calls<br/>State: Navigation completion"]
    M --> N
    N --> D
    
    D --> O["📋 Side Menu Drawer<br/>Profile navigation menu<br/>No API calls<br/>State: openDraw.current"]
    D --> P["📷 QR Scanner<br/>Camera view with scan overlay<br/>No API calls<br/>State: url, searching"]
    D --> Q["🔍 Search Bottom Sheet<br/>Multi-mode search interface<br/>API: POST /search/v1/search {keyword}<br/>State: searchList, drawerMode"]
    D --> R["📍 Map Interactions<br/>Pin taps and zoom controls<br/>API: GET /mark/v1/mark?markId=X<br/>State: selectedPin, zoomLevel"]
    
    O --> S["⚙️ Account Management<br/>Profile editing interface<br/>API: GET /user/profile<br/>State: userProfile data"]
    O --> T["🏆 Ranking<br/>Leaderboard display<br/>API: GET /rank/v1/showTopN?n=100<br/>State: rankData, myRank"]
    O --> U["🎁 About Rewards<br/>Rewards catalog browser<br/>API: GET /reward/v1/rewards?eventId=1<br/>State: rewardsList"]
    O --> V["🎫 My Rewards<br/>Owned rewards list<br/>API: GET /reward/v1/myRewards<br/>State: myRewardsList"]
    O --> W["ℹ️ Information<br/>App info and help content<br/>No API calls<br/>State: Static content"]
    O --> X["📄 Rules/Privacy<br/>WebView with external content<br/>No API calls<br/>State: WebView URL"]
    O --> Y["🎮 Debug Minigame<br/>Test game launcher<br/>No API calls<br/>State: WebView navigation"]
    
    S --> Z["✏️ Edit Profile<br/>Inline editing fields<br/>API: POST /user/updateUserInfo {field: value}<br/>State: editNick, input values"]
    S --> AA["🎫 My Rewards List<br/>Navigate to rewards<br/>No API calls<br/>State: Navigation"]
    S --> BB["📚 My Story/History<br/>User activity history<br/>API: GET /user/history<br/>State: historyData"]
    S --> CC["🚪 Logout<br/>Clear tokens and return to launch<br/>No API calls<br/>State: useAccountStore.updateToken(null)"]
    
    T --> DD["📊 Leaderboard View<br/>Scrollable ranking list<br/>API: GET /rank/v1/showTopN?n=100<br/>State: rankResults, pagination"]
    
    U --> EE["🛍️ Rewards Catalog<br/>Available rewards grid<br/>API: GET /reward/v1/rewards?eventId=1<br/>State: rewards array"]
    EE --> FF["🎁 Reward Detail Modal<br/>Reward info and exchange button<br/>No API calls<br/>State: selectReward"]
    FF --> GG["💰 Exchange Reward<br/>Confirmation and processing<br/>API: POST /reward/v1/exchange {rewardId}<br/>State: useAccountStore.updateScore()"]
    
    V --> HH["📋 My Rewards List<br/>Owned rewards with refresh<br/>API: GET /reward/v1/myRewards<br/>State: myRewards array"]
    HH --> II["🎫 My Reward Detail<br/>QR code generation modal<br/>No API calls<br/>State: QR code data"]
    II --> JJ["📱 QR Code Display<br/>Encrypted QR for redemption<br/>No API calls<br/>State: qrCodeValue"]
    
    P --> KK{QR Code Valid?}
    KK -->|Yes| LL["🎮 Mini-game WebView<br/>Game interface with bridge<br/>Bridge: window.globalProps.token<br/>State: WebView with injected token"]
    KK -->|No| MM["❌ Error Message<br/>Invalid QR code toast<br/>No API calls<br/>State: Toast display"]
    
    LL --> NN["🏁 Game Completion<br/>Game result processing<br/>Bridge: close() method<br/>State: Game completion callback"]
    NN --> OO["⭐ Score Update<br/>Points added to account<br/>API: POST /userScore/v1/addScore {score}<br/>State: useAccountStore.updateScore()"]
    NN --> PP["🏠 Return to Home<br/>Navigate back to map<br/>No API calls<br/>State: Navigation.goBack()"]
    
    Q --> QQ["🔍 Search Mode<br/>Text search for locations<br/>API: POST /search/v1/search {keyword}<br/>State: searchList, textInput"]
    Q --> RR["📚 Collections Mode<br/>Collected items display<br/>API: GET /mark/v1/collectList<br/>State: collectionList, excludeType"]
    Q --> SS["🎛️ Filter Mode<br/>Pin type visibility toggles<br/>No API calls<br/>State: useMapStore.filter"]
    
    QQ --> TT["📍 Search Results<br/>List of matching locations<br/>No API calls<br/>State: searchList display"]
    TT --> UU["📌 Select Location<br/>Tap to focus on map<br/>No API calls<br/>State: Map focus coordinates"]
    UU --> VV["🗺️ Map Focus<br/>Zoom to selected location<br/>No API calls<br/>State: Map zoom/pan"]
    
    RR --> WW["📋 Collected Items<br/>User's collected markers<br/>No API calls<br/>State: collectionList filtered"]
    WW --> XX["👁️ Collection Detail<br/>View collected item info<br/>No API calls<br/>State: Selected collection"]
    
    SS --> YY["🎛️ Filter Options<br/>Toggle switches for pin types<br/>No API calls<br/>State: MapFilterItem toggles"]
    YY --> ZZ["✅ Apply Filters<br/>Update filter state<br/>No API calls<br/>State: useMapStore.updateFilter()"]
    ZZ --> AAA["🗺️ Updated Map<br/>Filtered pins display<br/>No API calls<br/>State: filtMark() result"]
    
    R --> BBB{Pin Type}
    BBB -->|Mission| CCC["🎯 Mission Detail Modal<br/>Game info and start button<br/>API: GET /mark/v1/mark?markId=X<br/>State: missionDetail, collected"]
    BBB -->|Exhibition| DDD["🏢 Exhibition Detail Modal<br/>Exhibitor information display<br/>API: GET /mark/v1/mark?markId=X<br/>State: exhibitionDetail"]
    BBB -->|Event| EEE["🎪 Event Detail Modal<br/>Event information and actions<br/>API: GET /mark/v1/mark?markId=X<br/>State: eventDetail"]
    
    CCC --> FFF["🚀 Start Mission<br/>Launch game from mission<br/>No API calls<br/>State: Game URL preparation"]
    FFF --> GGG["🎮 Mini-game Launch<br/>Navigate to game WebView<br/>No API calls<br/>State: WebView navigation"]
    GGG --> LL
    
    DDD --> HHH["📋 Exhibition Info<br/>Booth details and description<br/>No API calls<br/>State: Exhibition data display"]
    HHH --> III["❤️ Collect/Share<br/>Add to collections or share<br/>API: POST /mark/v1/collect {markId}<br/>State: collected status"]
    
    EEE --> JJJ["📅 Event Info<br/>Event schedule and details<br/>No API calls<br/>State: Event data display"]
    JJJ --> KKK["🎬 Event Actions<br/>Event-specific interactions<br/>Varies by event type<br/>State: Event-specific state"]
    
    W --> LLL["📖 Information Pages<br/>Static help content<br/>No API calls<br/>State: Static content display"]
    X --> MMM["🌐 WebView Content<br/>External web pages<br/>No API calls<br/>State: WebView URL loading"]
    Y --> NNN["🎮 Debug Game Launch<br/>Test minigame access<br/>No API calls<br/>State: Debug WebView"]
    NNN --> LL
    
    style A fill:#e1f5fe
    style D fill:#c8e6c9
    style LL fill:#fff3e0
    style O fill:#f3e5f5
    style Q fill:#e8f5e8
