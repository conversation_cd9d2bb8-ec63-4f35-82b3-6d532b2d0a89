# Open Portal Expo (OPE) - System Topology & Status Overview

## Mermaid Diagram

```mermaid
graph TB
    %% Users
    subgraph "👥 End Users"
        MobileUsers[📱 Mobile App Users]
        AdminUsers[👨‍💼 Admin Users]
    end

    %% Client Layer
    subgraph "📱 Client Applications"
        ReactNativeApp["📱 React Native App<br/>✅ FULLY WORKING<br/>All dependencies available"]
    end

    %% API Gateway
    subgraph "🌐 Load Balancer & Routing"
        ALB["🔄 AWS Load Balancer<br/>✅ CONFIGURED<br/>ope-api-dev.massiveworld.link"]
        NginxIngress["🔄 Nginx Ingress<br/>✅ CONFIGURED<br/>Kubernetes ready"]
    end

    %% Backend Services
    subgraph "🔧 Backend Services"
        DiscoverService["🎯 Event/Discover Service<br/>⚠️ CANNOT START<br/>Missing: Database connections<br/>Issues: Private Docker images"]
        AdminService["👨‍💼 Admin API Service<br/>⚠️ CANNOT START<br/>Missing: Database connections<br/>Issues: Private Docker images"]
        AccountService["👤 Authentication Service<br/>❌ CANNOT BUILD<br/>Missing: massive-account-common module<br/>Issues: Wrong entity config"]
    end

    %% Data Layer
    subgraph "🗄️ Data Layer"
        MySQL["🗄️ MySQL Database<br/>⚠️ DEPLOYMENT NEEDED<br/>Referenced: massive-mysql-headless:3306<br/>Schema: Available, needs deployment"]
        Redis["⚡ Redis Cache<br/>⚠️ DEPLOYMENT NEEDED<br/>Referenced: massive-redis-headless:6379<br/>Used for: Sessions, caching"]
    end

    %% Infrastructure
    subgraph "☁️ Infrastructure"
        K8sCluster["⚙️ Kubernetes Cluster<br/>✅ CONFIGURED<br/>AWS EKS ready"]
        DockerRegistry["📦 Container Registry<br/>❌ PRIVATE ACCESS ONLY<br/>Blocks standalone deployment"]
        FileStorage["🗂️ File Storage<br/>✅ CONFIGURED<br/>AWS S3 for static assets"]
    end

    %% External Services
    subgraph "🌐 External Services"
        Firebase["🔥 Firebase<br/>⚠️ CREDENTIALS EXPOSED<br/>Push notifications, analytics"]
        LineOAuth["💬 LINE OAuth<br/>⚠️ ENVIRONMENT SPECIFIC<br/>Social login integration"]
        EmailSMS["📧📱 Email & SMS<br/>⚠️ HARDCODED CREDENTIALS<br/>QQ Mail, AWS Pinpoint, YunPian"]
    end

    %% Monitoring
    subgraph "📊 Monitoring Stack"
        Monitoring["📈 Prometheus + Grafana<br/>⚠️ CHARTS CONFIGURED<br/>+ ELK Stack, Loki<br/>Status: Not deployed yet"]
    end

    %% Connections
    MobileUsers --> ReactNativeApp
    AdminUsers --> ALB

    ReactNativeApp --> ALB
    ALB --> NginxIngress

    NginxIngress -.-> |"❌ Cannot connect"| DiscoverService
    NginxIngress -.-> |"❌ Cannot connect"| AdminService
    NginxIngress -.-> |"❌ Cannot build"| AccountService

    DiscoverService -.-> |"❌ Service not found"| MySQL
    DiscoverService -.-> |"❌ Service not found"| Redis
    DiscoverService --> FileStorage
    DiscoverService -.-> |"⚠️ Exposed credentials"| Firebase
    DiscoverService -.-> |"⚠️ May not work"| LineOAuth
    DiscoverService -.-> |"⚠️ Hardcoded creds"| EmailSMS

    AdminService -.-> |"❌ Service not found"| MySQL
    AdminService -.-> |"❌ Service not found"| Redis
    AdminService --> FileStorage

    AccountService -.-> |"❌ Service not found"| MySQL
    AccountService -.-> |"❌ Service not found"| Redis
    AccountService -.-> |"⚠️ Hardcoded creds"| EmailSMS

    DiscoverService --> K8sCluster
    AdminService --> K8sCluster
    AccountService -.-> |"❌ Cannot deploy"| K8sCluster
    DiscoverService -.-> |"❌ Private images"| DockerRegistry
    AdminService -.-> |"❌ Private images"| DockerRegistry
    AccountService -.-> |"❌ Private images"| DockerRegistry

    K8sCluster --> Monitoring

    %% Status Colors
    classDef working fill:#d4edda,stroke:#155724,stroke-width:2px
    classDef broken fill:#f8d7da,stroke:#721c24,stroke-width:2px
    classDef warning fill:#fff3cd,stroke:#856404,stroke-width:2px
    classDef unknown fill:#e2e3e5,stroke:#6c757d,stroke-width:2px
    classDef external fill:#cce5ff,stroke:#004085,stroke-width:2px

    class ReactNativeApp,ALB,NginxIngress,K8sCluster,FileStorage working
    class AccountService,DockerRegistry broken
    class DiscoverService,AdminService,MySQL,Redis,Monitoring,Firebase,LineOAuth,EmailSMS warning
    class Firebase,LineOAuth,EmailSMS external
```

## Component Status Summary

### ✅ Working Components

- **Mobile App**: React Native app with all public dependencies
- **Load Balancer**: AWS ALB and Nginx Ingress configured
- **Kubernetes**: EKS cluster ready for deployment
- **File Storage**: S3 configured for static assets

### ❌ Broken Components

- **Authentication Service**: Missing massive-account-common module

- **Container Registry**: Private ECR blocks standalone deployment

### ⚠️ Warning Components (Need Deployment/Configuration)

- **Event/Discover Service**: Code complete but cannot start without databases
- **Admin API Service**: Code complete but cannot start without databases
- **MySQL Database**: Schema available, deployment needed
- **Redis Cache**: Configuration ready, deployment needed
- **Monitoring Stack**: Helm charts configured but not deployed yet
- **External Services**: Credentials exposed or environment-specific

## Critical Blockers

1. **Missing Database Deployments**: MySQL and Redis services need deployment
2. **Missing Account Module**: massive-account-common module not provided
3. **Private Docker Images**: Cannot build containers without ECR access
4. **Exposed Credentials**: Security vulnerability in configuration files

## Next Steps

1. Deploy MySQL and Redis (manually or via containers)
2. Request/implement missing account module
3. Replace private Docker images with public ones
4. Move credentials to environment variables
