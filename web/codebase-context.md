

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run generate` - Generate static site
- `npm run preview` - Preview the built application

## Architecture Overview

This is a Nuxt 3 web application that serves as the web version of a React Native Open-Portal Expo app. The app uses:

- **Framework**: Nuxt 3 with SSR disabled (`ssr: false`)
- **Styling**: Inline Tailwind CSS with NuxtUI Pro components. No scoped styles.
- **Internationalization**: Nuxt i18n with English, Japanese, and Korean support
- **Map Rendering**: Konva.js with vue-konva for interactive map visualization
- **State Management**: Vue 3 composables instead of Zustand/Pinia

## Key Technical Implementation

### Interactive Map System
The core feature is an interactive map (`components/InteractiveMap.vue`) built with Konva.js that displays:
- Background map images with coordinate-based pin overlays
- Pin types: Mission (1), Exhibition (2), Event (3), Restaurant (4), Shop (5), Hotel (6), Other (7)
- Zoom, pan, and filtering capabilities
- Coordinate scaling utilities in `utils/coordinate.ts` and `utils/mapFormatters.ts`

### State Management Pattern
Uses Vue 3 composables (not Pinia) for state management:
- `composables/useMap.ts` - Map state, pins, areas, filters
- `composables/useAccount.ts` - User authentication and profile
- `composables/useUser.ts` - User data management
- `composables/useIntro.ts` - Onboarding flow management

### Application Flow
1. **Entry**: `pages/index.vue` (splash/launch screen)
2. **Authentication**: `pages/login.vue` (phone number auth)
3. **Onboarding**: `pages/intro.vue` → `pages/set-profile.vue`
4. **Main App**: `pages/home.vue` (interactive map interface)
5. **Features**: QR scanner, rewards system, ranking, account management

### Type System
Comprehensive TypeScript types in `types/index.ts` covering:
- Map pins, areas, and event map structures
- User authentication and profile types
- API response types and UI state types
- Filter and modal state management

### Multi-language Support
Configured for English (default), Japanese, and Korean with locale files in `i18n/locales/` and routing handled by Nuxt i18n module.

## Important Implementation Notes

- The app is a direct web port of a React Native application
- Uses Konva.js for map rendering (same approach as admin interface)
- Map coordinates require scaling from database coordinates to canvas coordinates
- Pin filtering system maintains state across map interactions
- WebView integration for mini-games uses iframe with postMessage bridge
- Mock data system in `utils/mockData.ts` for development

## File Structure Conventions

- **Pages**: Auto-routed Nuxt pages with detailed documentation headers
- **Components**: Vue SFC components with purpose-specific organization
- **Composables**: Vue 3 reactive state management replacing Zustand
- **Utils**: Pure functions for API, constants, coordinate transformation
- **Types**: Centralized TypeScript definitions matching API structures