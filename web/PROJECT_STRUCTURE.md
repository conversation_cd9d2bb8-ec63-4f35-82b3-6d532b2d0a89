# Open Portal Expo - Web Project Structure

This document outlines the complete file structure created based on the target UX sitemap document.

## Project Structure Overview

```
web/app/
├── pages/                          # Nuxt.js pages (auto-routing)
├── components/                     # Vue components
├── layouts/                        # Nuxt.js layouts
├── composables/                    # Vue composables (state management)
├── utils/                          # Utility functions and configurations
└── types/                          # TypeScript type definitions
```

## Pages Structure

### App Entry & Initialization
- `pages/index.vue` - Splash/Launch screen (app entry point)
- `pages/event-selection.vue` - Event selection when multiple events available

### Authentication & Onboarding Flow
- `pages/launch.vue` - App initialization and authentication check (deprecated - now handled by index)
- `pages/login.vue` - Phone number authentication
- `pages/countdown.vue` - Event countdown display
- `pages/intro.vue` - Welcome screen with Portaro character
- `pages/intro-one.vue` - Tutorial slides
- `pages/set-profile.vue` - User profile setup
- `pages/intro-two.vue` - Final onboarding step

### Main Application
- `pages/home.vue` - Home screen with interactive Konva.js map (primary interface)
- `pages/qr-scanner.vue` - QR code scanning functionality

### Account & Profile Management
- `pages/account.vue` - Profile management and settings
- `pages/my-story.vue` - User activity history

### Rewards System
- `pages/rewards.vue` - Available rewards catalog
- `pages/my-rewards.vue` - Owned rewards and QR codes

### Social & Competition
- `pages/ranking.vue` - Global leaderboard

### Information & Content
- `pages/information.vue` - App information and help
- `pages/privacy-policy.vue` - Privacy policy and terms

### Development Tools
- `pages/debug-minigame.vue` - Development mini-game testing (dev only)

## Components Structure

### Main Application Components
- `components/InteractiveMap.vue` - Konva.js-based map with pins and areas
- `components/SideMenuDrawer.vue` - Navigation drawer
- `components/SearchBottomSheet.vue` - Multi-mode search interface

### Integration Components
- `components/MinigameWebView.vue` - Mini-game integration with bridge
- `components/WebViewContent.vue` - External content display
- `components/SystemModal.vue` - System modals and overlays

### Modal Components
- `components/modals/MissionModal.vue` - Mission/game information
- `components/modals/ExhibitionModal.vue` - Exhibitor details
- `components/modals/EventModal.vue` - Event information

### Reward Components
- `components/RewardDetailModal.vue` - Reward details and exchange

## Layouts

- `layouts/default.vue` - Main app layout for authenticated pages
- `layouts/auth.vue` - Authentication flow layout

## State Management (Composables)

- `composables/useAccountStore.ts` - User authentication and profile
- `composables/useMapStore.ts` - Map state and data management
- `composables/useIntroOneStore.ts` - Tutorial slide management

## Utilities & Configuration

- `utils/api.ts` - API client and endpoint definitions
- `utils/constants.ts` - App-wide constants and configuration
- `utils/coordinate.ts` - Coordinate scaling utilities for Konva.js
- `utils/mapFormatters.ts` - Map data formatters for Konva rendering
- `types/index.ts` - TypeScript type definitions

## Key Features by File

### App Flow
1. **Index (Splash)** → **Event Selection** (if needed) → **Login** (if not authenticated) → **Countdown** → **Intro** → **Intro One** → **Set Profile** (if needed) → **Intro Two** → **Home**

### Main App Features
- **Interactive Map**: Konva.js-based zoomable map with multiple pin types (Mission, Exhibition, Event)
- **QR Scanner**: Camera-based QR scanning for mini-game access
- **Search**: Multi-mode bottom sheet (search, collections, filters)
- **Side Menu**: Navigation to all major app sections
- **Event Selection**: Choose from available events

### User Management
- **Account**: Profile editing and management
- **My Story**: Activity history and achievements
- **Rewards**: Browse and exchange rewards
- **My Rewards**: View owned rewards and generate QR codes
- **Ranking**: Global leaderboard and competition

## Technical Considerations

### State Migration (Mobile → Web)
- **Zustand** → **Pinia/Vue Composables**
- **React Navigation** → **Nuxt Router**
- **React Native WebView** → **iframe with postMessage**
- **React Native Camera** → **Web Camera APIs + vue-qrcode-reader**
- **React Native Map** → **Web Map Library**

### API Integration
- Maintains same endpoint structure as mobile app
- Authentication token handling
- Error handling and retry logic
- Request/response transformation

### Pin Types & Colors
- **Mission (1)**: Purple - Interactive games/challenges
- **Exhibition (2)**: Orange/Warning - Exhibitor information
- **Event (3)**: Pink - Event information

### Technical Implementation
- **Map Rendering**: Uses Konva.js for map rendering (same approach as admin)
- **Background**: Background image with interactive pin overlays
- **Scaling**: Coordinate scaling utilities for responsive design
- **Integration**: vue-konva for Vue.js integration
- **Data Flow**: Backend API → Formatters → Konva rendering

## Next Steps

1. **Implement Authentication Flow**: Start with login and token management
2. **Set up State Management**: Implement Pinia stores or composables
3. **Integrate Map Library**: Choose and integrate web map solution
4. **Implement QR Scanner**: Set up vue-qrcode-reader
5. **API Integration**: Connect to backend endpoints
6. **UI Implementation**: Build out component interfaces
7. **Testing**: Implement comprehensive testing strategy

## Development Notes

- All files contain detailed documentation headers with:
  - Purpose and features
  - Navigation patterns
  - API endpoints
  - Global state usage
  - User journey context
- No functional code implemented yet - pure skeleton structure
- Ready for incremental development and feature implementation
- Maintains consistency with mobile app architecture and user flows
