{"about_app": "About this App", "about_reward": "About Reward", "about_reward_1": "Points accumulated through {simple_games} can be exchanged for coupons and limited-edition goods.", "about_reward_2": "You grow when you find the \" {icon} \" symbol on a page or screen in the app.", "about_reward_3": "QR code panels are located at the \" {icon} \" marks marked in the MAP.", "about_reward_4": "You can spin the \"gacha\" (a special kind of gacha) near the event exit", "about_simple_games": "About simple games", "about_special_gift": "About the special gift", "account": "Account", "acquisition_point": "Acquisition point", "acquisition_time": "Acquisition time", "activate_code": "Activate Code", "admission": "Admission", "bgm": "Background Music", "birthday": "Birthday", "birthday_input": "birthday", "cancel": "Cancel", "code": "Code", "come_back": "Come back to exchange after you collect more energies!", "complete_profile_before_reward": "Please complete your profile to claim rewards.", "complete_surver": "Please complete the survey", "confirm": "CONFIRM", "count_down": "Only {days} days to go until the event!", "country": "Country", "country_input": "country input", "delete": "Delete", "delete_account": "Delete Account", "delete_account_confirmation": "Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently removed.", "do_not_touch_screen": "Please do not touch the screen \n and show it to the staff as it is.", "energy": "Energy", "energy_get": "Energy GET!!!", "energy_got": "I got the Energy!", "enter_code": "Please enter the verification code", "enter_nickname": "Please enter your name", "enter_phone": "Please enter your phone number", "enter_phone_hint": "A 6-digit verification code will be sent to your registered phone number", "event": "Event", "exchange": "Exchange", "exchange_reward_toast": "Do you want to spend {price} points to redeem?", "exhibition": "Exhibition", "favorite": "Favorite", "female": "FEMALE", "game": "Game", "gender": "Gender", "gender_input": "gender input", "goto_rewards": "<PERSON>o My Rewards", "hotel": "Hotel", "information": "Information", "intro_1": "This application allows you to You can check the event venue MAP, stall information, ", "intro_2": "You can play {simple_games} to earn points before and during the event.", "intro_3": "Points accumulated through {simple_games} can be exchanged for coupons and limited-edition goods.", "intro_4": "You grow when you find the \" {portaro} \" symbol on a page or screen in the app.", "intro_5": "\"Please help me grow and collect more points! Good things will happen at the event!\"", "loading": "Loading...", "login_hint": "If you have already purchased tickets and have not yet registered as an app member, please register from this screen.", "logout": "Logout", "male": "MALE", "many_memories": "You have made many memories!", "mission": "Mission", "mission_start": "Mission Start", "my_rewards": "My Rewards", "my_story": "My Story", "new": "new", "nickname": "Nickname", "nickname_input": "nickname input", "no_more": "No more", "not_enough_energy": "Not enough energy", "other": "Other", "phone_number": "Phone number ", "privacy_policy": "Privacy Policy", "prize_exchange": "Click here for the location of the prize exchange", "profile_complete": "Please complete your profile", "pull_to_load": "Pull to load", "ranking": "Ranking", "refresh_content": "Refreshed in {time} seconds", "restaurant": "Restaurant", "rewards": "Rewards", "rule": "Rule", "scan_intro": "Please superimpose the 2D code for \" {portaro} \" in the frame installed in the venue.", "scan_tips_1": "・Please wait 30 minutes before reading the same 2D code.\n・Energy accumulated by completing missions can be redeemed for prizes later.", "scan_tips_2": "・Energy accumulated by completing missions can be redeemed for prizes later.", "search_exhibitors": "Search Exhibitors", "select": "Select", "select_prize": "Select the prize you wish to exchange and generate a QR code. Please show the generated QR code to the staff at the designated prize exchange.", "send": "SEND", "share": "Share", "shop": "Shop", "simple_game": "simple games", "simple_games": "simple games", "start": "START", "tap_area": "Tap the area you are interested in.", "tell_us": "Please tell us about yourself", "ticket": "Ticket", "ticket_active": "Ticket Activate", "welcome_portaro": "Welcome! I'm <PERSON><PERSON>.", "what_is_energy": "What is energy?"}