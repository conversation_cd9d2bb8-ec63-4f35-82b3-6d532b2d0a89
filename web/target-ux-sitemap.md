# Open Portal Expo - UX Sitemap & User Journey Analysis

## Overview
This sitemap documents the complete user experience flow of the Open Portal Expo mobile app, serving as a blueprint for building the new Nuxt.js web version.

## 1. Authentication & Onboarding Flow

### 1.1 App Launch
- **Entry Point**: App initialization
- **Logic**: Checks for existing authentication token
- **Routes**:
  - If authenticated → Home Screen
  - If not authenticated → Launch Screen
- **APIs**: None
- **Global State**: `useAccountStore.token` (check authentication status)

### 1.2 Launch Screen
- **Purpose**: Initial app entry point for new users
- **Features**:
  - Event data loading
  - Map preloading
  - Background initialization
- **Navigation**: Automatically proceeds to Login Screen
- **APIs**: `POST /event/v1/event { eventId: 1 }`
- **Global State**: `useMapStore.updateMap()`, `useMapStore.setCountdown()`

### 1.3 Login Screen
- **Purpose**: User authentication via phone number
- **User Actions**:
  - Enter phone number with country code selection
  - Request verification code
  - Enter verification code
  - Submit for authentication
- **Validation**: Phone number format validation
- **Navigation**: Success → CountDown Screen
- **APIs**:
  - `POST /user/login/sendVerifyCode { phoneNumber }`
  - `POST /user/login/phoneNumberLogin { phoneNumber, verifyCode }`
- **Global State**: `useAccountStore.updateToken()`, `useAccountStore.updateNickName()`

### 1.4 CountDown Screen
- **Purpose**: Event countdown display
- **Features**:
  - Circular countdown timer
  - Days remaining display
  - Animated Portaro character
- **Logic**: Auto-navigates to Intro when countdown reaches 0
- **Navigation**: When event starts → Intro Screen
- **APIs**: None
- **Global State**: `useMapStore.countDown` (read countdown value)

### 1.5 Intro Sequence
#### 1.5.1 Intro Screen
- **Purpose**: Welcome screen with Portaro character
- **Features**:
  - Lottie animation
  - Welcome speech bubble
  - Tap to continue
- **Navigation**: Tap → Intro One
- **APIs**: None
- **Global State**: Lottie animation state (local)

#### 1.5.2 Intro One (Tutorial Slides)
- **Purpose**: App tutorial with swipeable slides
- **Features**:
  - 3 tutorial slides with dot indicators
  - Portaro character with speech bubbles
  - Interactive tutorial content
- **Logic**: Checks if user has nickname
- **Navigation**:
  - Has nickname → Intro Two
  - No nickname → Set Profile
- **APIs**: None
- **Global State**: `useIntroOneStore.index`, `useAccountStore.nickName`

#### 1.5.3 Set Profile Screen
- **Purpose**: User profile setup
- **User Actions**:
  - Enter nickname
  - Select country/location
  - Set date of birth
- **Navigation**: Complete → Intro Two
- **APIs**: `POST /user/updateUserInfo { nickname, location, birthday }`
- **Global State**: `useAccountStore.updateNickName()`

#### 1.5.4 Intro Two Screen
- **Purpose**: Final onboarding step
- **Navigation**: Complete → Home Screen
- **APIs**: None
- **Global State**: Navigation completion state (local)

## 2. Main Application Flow

### 2.1 Home Screen (Interactive Map)
- **Purpose**: Primary app interface - interactive event map
- **Core Features**:
  - Zoomable/pannable map view
  - Multiple pin types (Mission, Exhibition, Event)
  - Score display
  - Profile access
  - QR scanner access
  - Search functionality
- **APIs**: `GET /mark/v1/marks?eventId=1`
- **Global State**: `useMapStore.marks`, `useMapStore.areas`, `useAccountStore.score`

#### 2.1.1 Map Interactions
- **Pin Types**:
  - **Mission Pins** (Purple): Interactive games/challenges
  - **Exhibition Pins** (Orange): Exhibitor information
  - **Event Pins** (Pink): Event information
- **User Actions**:
  - Tap pins to view details
  - Zoom in/out
  - Pan around map
  - Filter pin visibility
- **APIs**: `GET /mark/v1/mark?markId=X` (when pin tapped)
- **Global State**: `selectedPin`, `zoomLevel`, `useMapStore.filter`

#### 2.1.2 UI Elements
- **Top Right**: Profile menu button
- **Top Left**: Score display (tappable)
- **Bottom Right**: QR scanner button
- **Bottom**: Search bottom sheet
- **Right Drawer**: Side menu
- **APIs**: None (UI elements only)
- **Global State**: `openDraw.current`, `useAccountStore.score`

### 2.2 Side Menu Drawer
- **Access**: Swipe from right or tap profile button
- **Menu Items**:
  - Account Management
  - Ranking
  - About Rewards
  - My Rewards
  - Information
  - Rules & Privacy Policy
  - Debug Minigame (dev only)
  - BGM Toggle
- **APIs**: None (navigation only)
- **Global State**: `useAccountStore.isBgmEnabled`

### 2.3 Search Bottom Sheet
- **Purpose**: Find and navigate to specific locations
- **Modes**:
  - **Search Mode**: Text search for exhibitors/locations
  - **Collections Mode**: View collected items
  - **Filter Mode**: Toggle pin type visibility
- **Features**:
  - Expandable bottom sheet
  - Real-time search results
  - Tap to focus map on location
- **APIs**:
  - `POST /search/v1/search { keyword }` (Search Mode)
  - `GET /mark/v1/collectList` (Collections Mode)
- **Global State**: `searchList`, `drawerMode`, `collectionList`, `useMapStore.filter`

## 3. QR Code & Mini-game Flow

### 3.1 QR Scanner
- **Access**: Tap QR button on home screen
- **Features**:
  - Camera-based QR code scanning
  - Permission handling
  - URL validation
- **Flow**:
  - Scan QR code → Validate URL → Launch mini-game
- **APIs**: None (camera only)
- **Global State**: `url`, `searching` (local state)

### 3.2 Mini-game Integration
- **Technology**: WebView with JavaScript bridge
- **Features**:
  - Token injection for authentication
  - Score tracking
  - Game completion callbacks
- **Bridge Methods**:
  - `close`: Return to app
  - `toHome`: Navigate to home
  - `toast`: Show notifications
  - `share`: Share content
- **Navigation**: Game completion → Score update → Return to home
- **APIs**:
  - Bridge: `window.globalProps.token` (token injection)
  - `POST /userScore/v1/addScore { score }` (score update)
- **Global State**: `useAccountStore.token`, `useAccountStore.updateScore()`

## 4. Account & Profile Management

### 4.1 Account Screen
- **Access**: Side menu → Account
- **Features**:
  - Edit nickname
  - Change country/location
  - Date of birth selection
  - Navigation to sub-sections
- **Sub-navigation**:
  - My Rewards
  - My Story/History
  - Logout option
- **APIs**: `GET /user/profile`
- **Global State**: `userProfile`, `editNick` (local state)

### 4.2 Profile Data Management
- **Editable Fields**:
  - Nickname (inline editing)
  - Country (picker selection)
  - Date of birth (date picker)
- **Actions**:
  - Save changes automatically
  - Logout (clears token, returns to launch)
- **APIs**:
  - `POST /user/updateUserInfo { nickname?, location?, birthday? }`
  - `POST /user/unregister` (account deletion)
- **Global State**: `useAccountStore.updateToken()`, `useAccountStore.updateNickName()`

## 5. Rewards System

### 5.1 Rewards Catalog
- **Access**: Side menu → About Rewards
- **Features**:
  - Browse available rewards
  - View reward details
  - Check energy requirements
  - Exchange rewards
- **APIs**: `GET /reward/v1/rewards?eventId=1`
- **Global State**: `rewardsList`, `selectReward`

### 5.2 My Rewards
- **Access**: Side menu → My Rewards or Account → My Rewards
- **Features**:
  - View owned rewards
  - Generate QR codes for redemption
  - Refresh to update status
- **APIs**: `GET /reward/v1/myRewards`
- **Global State**: `myRewardsList`, `selectReward`

### 5.3 Reward Detail Modals
- **Features**:
  - Reward information
  - Exchange functionality
  - QR code generation
  - Energy cost display
- **APIs**: `POST /reward/v1/exchange { rewardId }`
- **Global State**: `useAccountStore.updateScore()`, `qrCodeValue` (local)

## 6. Social & Competition Features

### 6.1 Ranking System
- **Access**: Side menu → Ranking
- **Features**:
  - Global leaderboard
  - User ranking display
  - Score comparison
  - Refresh functionality
- **APIs**:
  - `GET /rank/v1/showTopN?n=100` (global rankings)
  - `GET /rank/v1/rank` (user's rank)
- **Global State**: `rankResults`, `myRank`, pagination state

### 6.2 History/Story
- **Access**: Account → My Story
- **Purpose**: User activity history
- **Features**: Track user progress and achievements
- **APIs**: `GET /user/history`
- **Global State**: `historyData` (local state)

## 7. Information & Content

### 7.1 Information Pages
- **Access**: Side menu → Information
- **Content**: App information and help content
- **APIs**: None (static content)
- **Global State**: None (static content display)

### 7.2 WebView Content
- **Purpose**: Display external content
- **Features**:
  - Rules and privacy policy
  - External web content
  - Navigation controls
  - Token injection for authenticated content
- **APIs**: None (external URLs)
- **Global State**: `useAccountStore.token` (for injection), WebView URL state

## 8. Modal & Overlay Components

### 8.1 Pin Detail Modals
- **Mission Modal**: Game information and launch
- **Exhibition Modal**: Exhibitor details and collection
- **Event Modal**: Event information and actions
- **APIs**: `GET /mark/v1/mark?markId=X`, `POST /mark/v1/collect { markId }`
- **Global State**: `missionDetail`, `exhibitionDetail`, `eventDetail`, `collected` status

### 8.2 Bottom Sheets
- **Search Bottom Sheet**: Multi-mode search interface
- **Reward Detail**: Reward information and exchange
- **APIs**: Various (see Search and Rewards sections)
- **Global State**: Bottom sheet snap states, modal visibility

### 8.3 System Modals
- **Loading Overlays**: Progress indicators
- **Alert Dialogs**: Confirmations and errors
- **Toast Messages**: Brief notifications
- **APIs**: None (UI feedback only)
- **Global State**: Loading states, modal instances (RootSiblings)

## 9. Key User Journeys

### 9.1 First-Time User Journey
1. Launch → Login → CountDown → Intro Sequence → Home
2. Profile setup during intro if needed
3. Tutorial completion → Main app access
- **APIs**: Event, Login, Profile setup APIs
- **Global State**: Full authentication and onboarding state flow

### 9.2 Returning User Journey
1. Launch → Home (if authenticated)
2. Direct access to all features
- **APIs**: Map data loading
- **Global State**: `useAccountStore.token` validation

### 9.3 Mini-game Journey
1. Home → QR Scanner → Scan Code → Mini-game → Score Update → Home
- **APIs**: Score update API, WebView bridge
- **Global State**: Score tracking, game completion state

### 9.4 Reward Journey
1. Home → Side Menu → Rewards → Browse/Exchange → QR Code → Redemption
- **APIs**: Rewards catalog, exchange, my rewards
- **Global State**: Rewards data, score deduction

### 9.5 Social Journey
1. Home → Side Menu → Ranking → View Leaderboard
2. Compete for higher scores through mini-games
- **APIs**: Ranking APIs, score tracking
- **Global State**: Ranking data, user position

## 10. Technical Considerations for Web Version

### 10.1 Navigation Patterns
- Replace React Navigation with Nuxt routing
- Maintain modal/overlay patterns with Vue components
- Implement bottom sheet behavior with CSS/Vue transitions
- **State Migration**: Convert navigation state to Vue Router
- **API Integration**: Maintain same endpoint structure

### 10.2 WebView Integration
- Replace React Native WebView with iframe or direct integration
- Maintain JavaScript bridge functionality
- Handle authentication token passing
- **State Migration**: Convert bridge methods to postMessage API
- **API Integration**: Maintain token injection pattern

### 10.3 Camera/QR Functionality
- Replace React Native camera with web camera APIs
- Use vue-qrcode-reader for QR scanning
- Handle permission requests appropriately
- **State Migration**: Convert camera permissions to web permissions
- **API Integration**: Same QR validation flow

### 10.4 State Management
- Replace Zustand with Pinia or Vue composables
- Maintain authentication state
- Handle map state and user preferences
- **State Migration**:
  - `useAccountStore` → Pinia store with same structure
  - `useMapStore` → Pinia store with same filter/map logic
- **API Integration**: Maintain same API call patterns

### 10.5 Map Implementation
- Replace React Native map with web map library
- Maintain zoom, pan, and pin interaction patterns
- Implement responsive design for different screen sizes
- **State Migration**: Convert map state to web map library state
- **API Integration**: Same marker data structure and pin APIs
