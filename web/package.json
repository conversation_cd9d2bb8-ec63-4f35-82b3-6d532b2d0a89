{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/fonts": "0.11.4", "@nuxt/icon": "1.15.0", "@nuxt/ui-pro": "^3.3.0", "@nuxtjs/i18n": "10.0.3", "konva": "^9.3.22", "nuxt": "^4.0.1", "vue": "^3.5.18", "vue-konva": "^3.2.2", "vue-router": "^4.5.1"}}