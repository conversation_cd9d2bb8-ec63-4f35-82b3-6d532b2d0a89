// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  modules: ['@nuxt/fonts', '@nuxt/icon', '@nuxtjs/i18n', '@nuxt/ui-pro'],
  ssr: false,

  i18n: {
    defaultLocale: 'en',
    locales: [
      {
        code: 'en',
        iso: 'en-US',
        language: 'English',
        name: 'English',
        file: 'en.json',
      },
      {
        code: 'jp',
        iso: 'ja-JP',
        language: 'Japanese',
        name: '日本語',
        file: 'ja.json',
      },
      {
        code: 'kr',
        iso: 'ko-KR',
        language: 'Korean',
        name: '한국어',
        file: 'ko.json',
      },
    ],
  },
  
  css: ['~/assets/css/main.css']
})