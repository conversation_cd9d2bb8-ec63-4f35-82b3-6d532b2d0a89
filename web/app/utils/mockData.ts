/**
 * Mock Data for Testing Map Functionality
 * 
 * Purpose: Provide test data for map rendering, zoom, and pin interactions
 */

import type { MapPin, MapArea, EventMap } from '~/types'

// Mock event map data
export const mockEventMap: EventMap = {
  id: '1',
  event_id: '1',
  map_url: 'http://ope-directus-stg.zafar.dev/assets/81492942-b929-4d75-8fab-e167649fbb62',
  original_width: 3840,
  original_height: 2160
}

// Mock areas data
export const mockAreas: MapArea[] = [
  {
    id: 'area-1',
    name: 'Main Hall',
    x_axis: 100,
    y_axis: 100,
    width: 300,
    height: 200,
    event_id: '1'
  },
  {
    id: 'area-2',
    name: 'Exhibition Hall A',
    x_axis: 500,
    y_axis: 150,
    width: 250,
    height: 180,
    event_id: '1'
  },
  {
    id: 'area-3',
    name: 'Exhibition Hall B',
    x_axis: 800,
    y_axis: 120,
    width: 280,
    height: 220,
    event_id: '1'
  },
  {
    id: 'area-4',
    name: 'Food Court',
    x_axis: 200,
    y_axis: 400,
    width: 400,
    height: 150,
    event_id: '1'
  },
  {
    id: 'area-5',
    name: 'Conference Rooms',
    x_axis: 700,
    y_axis: 450,
    width: 300,
    height: 200,
    event_id: '1'
  }
]

// Mock markers/pins data
export const mockMarkers: MapPin[] = [
  // Main Hall markers
  {
    id: 'marker-1',
    mark_type: 1, // Mission
    name: 'VR Experience',
    description: 'Virtual Reality gaming experience',
    x_axis: 480,
    y_axis: 400,
    area_id: 'area-1',
    event_id: '1'
  },
  {
    id: 'marker-2',
    mark_type: 2, // Exhibition
    name: 'Tech Showcase',
    description: 'Latest technology demonstrations',
    x_axis: 800,
    y_axis: 500,
    area_id: 'area-1',
    event_id: '1'
  },
  {
    id: 'marker-3',
    mark_type: 3, // Event
    name: 'Opening Ceremony',
    description: 'Event opening ceremony',
    x_axis: 1120,
    y_axis: 600,
    area_id: 'area-1',
    event_id: '1'
  },

  // Exhibition Hall A markers
  {
    id: 'marker-4',
    mark_type: 2, // Exhibition
    name: 'AI Booth',
    description: 'Artificial Intelligence showcase',
    x_axis: 1760,
    y_axis: 540,
    area_id: 'area-2',
    event_id: '1'
  },
  {
    id: 'marker-5',
    mark_type: 2, // Exhibition
    name: 'Robotics Demo',
    description: 'Interactive robotics demonstration',
    x_axis: 2080,
    y_axis: 675,
    area_id: 'area-2',
    event_id: '1'
  },
  {
    id: 'marker-6',
    mark_type: 1, // Mission
    name: 'Robot Challenge',
    description: 'Programming challenge with robots',
    x_axis: 2240,
    y_axis: 540,
    area_id: 'area-2',
    event_id: '1'
  },

  // Exhibition Hall B markers
  {
    id: 'marker-7',
    mark_type: 2, // Exhibition
    name: 'Startup Alley',
    description: 'Innovative startup presentations',
    x_axis: 2720,
    y_axis: 480,
    area_id: 'area-3',
    event_id: '1'
  },
  {
    id: 'marker-8',
    mark_type: 3, // Event
    name: 'Pitch Competition',
    description: 'Startup pitch competition',
    x_axis: 3040,
    y_axis: 590,
    area_id: 'area-3',
    event_id: '1'
  },
  {
    id: 'marker-9',
    mark_type: 1, // Mission
    name: 'Innovation Quest',
    description: 'Interactive innovation challenge',
    x_axis: 3200,
    y_axis: 750,
    area_id: 'area-3',
    event_id: '1'
  },

  // Food Court markers
  {
    id: 'marker-10',
    mark_type: 4, // Restaurant
    name: 'Sushi Bar',
    description: 'Fresh sushi and Japanese cuisine',
    x_axis: 250,
    y_axis: 450,
    area_id: 'area-4',
    event_id: '1'
  },
  {
    id: 'marker-11',
    mark_type: 4, // Restaurant
    name: 'Pizza Corner',
    description: 'Wood-fired pizza',
    x_axis: 350,
    y_axis: 480,
    area_id: 'area-4',
    event_id: '1'
  },
  {
    id: 'marker-12',
    mark_type: 5, // Shop
    name: 'Tech Store',
    description: 'Latest gadgets and accessories',
    x_axis: 450,
    y_axis: 460,
    area_id: 'area-4',
    event_id: '1'
  },
  {
    id: 'marker-13',
    mark_type: 4, // Restaurant
    name: 'Coffee Hub',
    description: 'Premium coffee and pastries',
    x_axis: 550,
    y_axis: 500,
    area_id: 'area-4',
    event_id: '1'
  },

  // Conference Rooms markers
  {
    id: 'marker-14',
    mark_type: 3, // Event
    name: 'Keynote Speech',
    description: 'Industry leader keynote presentation',
    x_axis: 750,
    y_axis: 500,
    area_id: 'area-5',
    event_id: '1'
  },
  {
    id: 'marker-15',
    mark_type: 3, // Event
    name: 'Panel Discussion',
    description: 'Expert panel on future tech',
    x_axis: 850,
    y_axis: 550,
    area_id: 'area-5',
    event_id: '1'
  },
  {
    id: 'marker-16',
    mark_type: 3, // Event
    name: 'Workshop',
    description: 'Hands-on development workshop',
    x_axis: 950,
    y_axis: 520,
    area_id: 'area-5',
    event_id: '1'
  },

  // Some standalone markers (not in areas)
  {
    id: 'marker-17',
    mark_type: 6, // Hotel
    name: 'Information Desk',
    description: 'Event information and assistance',
    x_axis: 50,
    y_axis: 50,
    event_id: '1'
  },
  {
    id: 'marker-18',
    mark_type: 7, // Other
    name: 'First Aid',
    description: 'Medical assistance station',
    x_axis: 1100,
    y_axis: 50,
    event_id: '1'
  },
  {
    id: 'marker-19',
    mark_type: 7, // Other
    name: 'Lost & Found',
    description: 'Lost and found service',
    x_axis: 50,
    y_axis: 700,
    event_id: '1'
  },
  {
    id: 'marker-20',
    mark_type: 5, // Shop
    name: 'Souvenir Shop',
    description: 'Event merchandise and souvenirs',
    x_axis: 1100,
    y_axis: 700,
    event_id: '1'
  }
]

// Helper function to get mock data
export const getMockMapData = () => ({
  eventMap: mockEventMap,
  areas: mockAreas,
  markers: mockMarkers
})

// Helper function to get markers by area
export const getMarkersByArea = (areaId: string) => {
  return mockMarkers.filter(marker => marker.area_id === areaId)
}

// Helper function to get area center points for area pins
export const getAreaCenterPoints = () => {
  return mockAreas.map(area => ({
    ...area,
    centerX: area.x_axis + (area.width / 2),
    centerY: area.y_axis + (area.height / 2),
    markerCount: getMarkersByArea(area.id).length
  }))
}

// Mock filter state
export const mockFilterState = {
  mission: true,
  exhibition: true,
  event: true,
  restaurant: true,
  shop: true,
  hotel: true,
  other: true
}
