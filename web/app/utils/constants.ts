/**
 * Constants - App-wide Constants and Configuration
 * 
 * Purpose: Centralized constants and configuration values
 * 
 * Categories:
 * - Pin Types and Colors
 * - API Configuration
 * - UI Constants
 * - Navigation Routes
 * - Event Configuration
 * 
 * Pin Types:
 * - Mission = 1 (Purple)
 * - Exhibition = 2 (Orange/Warning)
 * - Event = 3 (Pink)
 * 
 * Technical Considerations:
 * - Maintain consistency with mobile app values
 * - Centralized configuration management
 * - Type safety for constants
 */

// Pin type constants
export const PIN_TYPES = {
  MISSION: 1,
  EXHIBITION: 2,
  EVENT: 3
} as const

// Pin colors
export const PIN_COLORS = {
  [PIN_TYPES.MISSION]: 'purple',
  [PIN_TYPES.EXHIBITION]: 'warning',
  [PIN_TYPES.EVENT]: 'pink'
} as const

// API configuration
export const API_CONFIG = {
  BASE_URL: '',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3
} as const

// UI constants
export const UI_CONSTANTS = {
  BOTTOM_SHEET_SNAP_POINTS: ['25%', '50%', '90%'],
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500
} as const

// Navigation routes
export const ROUTES = {
  SPLASH: '/', // Splash/Launch screen (index)
  HOME: '/home', // Main map interface
  LOGIN: '/login',
  LAUNCH: '/launch', // Deprecated - now handled by index
  COUNTDOWN: '/countdown',
  INTRO: '/intro',
  INTRO_ONE: '/intro-one',
  INTRO_TWO: '/intro-two',
  SET_PROFILE: '/set-profile',
  QR_SCANNER: '/qr-scanner',
  ACCOUNT: '/account',
  MY_STORY: '/my-story',
  REWARDS: '/rewards',
  MY_REWARDS: '/my-rewards',
  RANKING: '/ranking',
  INFORMATION: '/information',
  PRIVACY_POLICY: '/privacy-policy',
  DEBUG_MINIGAME: '/debug-minigame'
} as const

// Event configuration
export const EVENT_CONFIG = {
  EVENT_ID: 1,
  DEFAULT_ZOOM_LEVEL: 15,
  MAX_ZOOM_LEVEL: 20,
  MIN_ZOOM_LEVEL: 10
} as const
