/**
 * Coordinate Scaling Utilities
 * 
 * Purpose: Handle coordinate transformations between database and canvas coordinates
 * 
 * Based on admin implementation for consistency
 * Used for Konva.js map rendering with responsive scaling
 */

export interface ScaleResult {
  x: number
  y: number
  width?: number
  height?: number
}

/**
 * Convert database coordinates → canvas coordinates.
 * Used when rendering pins/areas on the Konva canvas
 */
export function scaleToCanvas(
  x: number,
  y: number,
  scaleX: number,
  scaleY: number,
  width?: number,
  height?: number
): ScaleResult {
  return {
    x: x * scaleX,
    y: y * scaleY,
    width: width !== undefined ? width * scaleX : undefined,
    height: height !== undefined ? height * scaleY : undefined
  }
}

/**
 * Convert canvas coordinates → database coordinates.
 * Used when saving pin positions or handling interactions
 */
export function scaleFromCanvas(
  x: number,
  y: number,
  scaleX: number,
  scaleY: number,
  width?: number,
  height?: number
): ScaleResult {
  return {
    x: x / scaleX,
    y: y / scaleY,
    width: width !== undefined ? width / scaleX : undefined,
    height: height !== undefined ? height / scaleY : undefined
  }
}

/**
 * Calculate scaling factors based on original and target dimensions
 */
export function calculateScalingFactors(
  originalWidth: number,
  originalHeight: number,
  targetWidth: number,
  targetHeight: number
) {
  return {
    scaleX: targetWidth / originalWidth,
    scaleY: targetHeight / originalHeight
  }
}

/**
 * Get responsive scaling factors for current viewport
 */
export function getResponsiveScaling(
  originalWidth: number,
  originalHeight: number,
  containerElement: HTMLElement
) {
  const rect = containerElement.getBoundingClientRect()
  return calculateScalingFactors(
    originalWidth,
    originalHeight,
    rect.width,
    rect.height
  )
}
