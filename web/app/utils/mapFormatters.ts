/**
 * Map Formatters - Convert API data to Konva-friendly formats
 * 
 * Purpose: Format map data for Konva.js rendering
 * Based on admin implementation for consistency
 */

import { scaleToCanvas } from './coordinate'

/**
 * Format areas data for Konva rendering
 */
export function formatAreas(
  areaArr: any[],
  scaleX: number,
  scaleY: number
) {
  return areaArr.map((area) => {
    const scaled = scaleToCanvas(
      area.x_axis || 0,
      area.y_axis || 0,
      scaleX,
      scaleY,
      area.width || 0,
      area.height || 0
    )

    return {
      id: area.id,
      visible: true,
      rotation: 0,
      x: scaled.x,
      y: scaled.y,
      width: scaled.width || 0,
      height: scaled.height || 0,
      scaleX: 1,
      scaleY: 1,
      dash: [15, 8], // More prominent dashed lines
      stroke: '#3b82f6', // Blue stroke for better visibility
      strokeWidth: 2,
      fill: 'rgba(59, 130, 246, 0.15)', // Light blue fill
      name: `area-${area.id}`,
      text: area.name,
      data: area
    }
  })
}

/**
 * Format markers/pins data for Konva rendering
 */
export function formatMarkers(
  markerArr: any[],
  scaleX: number,
  scaleY: number
) {
  return markerArr.map((marker) => {
    const scaled = scaleToCanvas(
      marker.x_axis || 0,
      marker.y_axis || 0,
      scaleX,
      scaleY
    )

    return {
      id: marker.id,
      x: scaled.x,
      y: scaled.y,
      radius: 16, // Larger radius for better visibility
      fill: getMarkerColor(marker.mark_type),
      scaleX: 1,
      scaleY: 1,
      stroke: '#fff', // White stroke for better contrast
      strokeWidth: 3,
      shadowColor: 'black',
      shadowBlur: 6,
      shadowOffset: { x: 3, y: 3 },
      shadowOpacity: 0.4,
      name: `marker-${marker.id}`,
      text: marker.name,
      data: marker
    }
  })
}

/**
 * Get marker color based on type
 * Consistent with admin implementation
 */
export function getMarkerColor(markType: number): string {
  switch (markType) {
    case 1: return '#8b5cf6' // Mission/Game - purple
    case 2: return '#f59e0b' // Exhibition - warning/amber
    case 3: return '#ec4899' // Event/Show - pink
    case 4: return '#ef4444' // Restaurant - red
    case 5: return '#10b981' // Shop - emerald
    case 6: return '#3b82f6' // Hotel - blue
    case 7: return '#84cc16' // Other - lime
    default: return '#6b7280' // Unknown - gray
  }
}

/**
 * Get marker type name
 */
export function getMarkerTypeName(markType: number): string {
  switch (markType) {
    case 1: return 'Mission'
    case 2: return 'Exhibition'
    case 3: return 'Event'
    case 4: return 'Restaurant'
    case 5: return 'Shop'
    case 6: return 'Hotel'
    case 7: return 'Other'
    default: return 'Unknown'
  }
}

/**
 * Filter markers based on filter state
 */
export function filterMarkers(markers: any[], filter: any) {
  return markers.filter(marker => {
    switch (marker.mark_type) {
      case 1: return filter.mission
      case 2: return filter.exhibition
      case 3: return filter.event
      case 4: return filter.restaurant || true // Default visible
      case 5: return filter.shop || true // Default visible
      case 6: return filter.hotel || true // Default visible
      case 7: return filter.other || true // Default visible
      default: return true
    }
  })
}

/**
 * Group nearby markers for zoomed-out view
 */
export function groupNearbyMarkers(markers: any[], groupingDistance: number = 50) {
  if (!markers.length) return []

  const groups: any[] = []
  const processed = new Set<number>()

  markers.forEach((marker, index) => {
    if (processed.has(index)) return

    // Find all markers within grouping distance
    const nearbyMarkers = [marker]
    processed.add(index)

    markers.forEach((otherMarker, otherIndex) => {
      if (processed.has(otherIndex) || index === otherIndex) return

      const distance = Math.sqrt(
        Math.pow(marker.x - otherMarker.x, 2) +
        Math.pow(marker.y - otherMarker.y, 2)
      )

      if (distance <= groupingDistance) {
        nearbyMarkers.push(otherMarker)
        processed.add(otherIndex)
      }
    })

    // Create group marker
    if (nearbyMarkers.length === 1) {
      // Single marker - show as individual
      groups.push({
        ...marker,
        id: `single-${marker.id}`,
        text: marker.text
      })
    } else {
      // Multiple markers - show as group
      const centerX = nearbyMarkers.reduce((sum, m) => sum + m.x, 0) / nearbyMarkers.length
      const centerY = nearbyMarkers.reduce((sum, m) => sum + m.y, 0) / nearbyMarkers.length

      groups.push({
        id: `group-${marker.id}`,
        x: centerX,
        y: centerY,
        radius: 20 + Math.min(nearbyMarkers.length * 2, 15), // Larger for more markers
        fill: '#3b82f6', // Blue for groups
        stroke: '#fff',
        strokeWidth: 3,
        shadowColor: 'black',
        shadowBlur: 6,
        shadowOffset: { x: 3, y: 3 },
        shadowOpacity: 0.4,
        text: `${nearbyMarkers.length}`,
        data: nearbyMarkers, // Store all markers in the group
        isGroup: true
      })
    }
  })

  return groups
}
