<!--
  Side Menu Drawer - Navigation Menu
  
  Access: Swipe from right or tap profile button
  
  Menu Items:
  - Account Management
  - Ranking
  - About Rewards
  - My Rewards
  - Information
  - Rules & Privacy Policy
  - Debug Minigame (dev only)
  - BGM Toggle
  
  Features:
  - Slide-out drawer animation
  - Menu item navigation
  - BGM toggle control
  - User profile display
  - Development tools (conditional)
  
  APIs:
  - None (navigation only)
  
  Global State:
  - useAccountStore.isBgmEnabled
  - openDraw.current
  
  User Journey:
  - Primary navigation hub
  - Access to all major app sections
  - User settings and preferences
-->

<template>
  <div class="side-menu-drawer">
    <!-- Drawer overlay -->
    <!-- Drawer container -->
    <!-- User profile section -->
    <!-- Menu items list -->
    <!-- BGM toggle -->
    <!-- Development tools (conditional) -->
  </div>
</template>

<script setup lang="ts">
// Drawer state management
// Menu item navigation
// BGM toggle functionality
// Development mode detection
// User profile data
</script>

<style scoped>
/* Drawer animation */
/* Menu item styling */
/* Profile section layout */
/* Toggle control styling */
</style>
