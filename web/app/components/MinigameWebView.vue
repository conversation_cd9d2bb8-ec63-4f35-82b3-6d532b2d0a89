<!--
  Mini-game WebView - Game Integration Component
  
  Technology: WebView with JavaScript bridge (iframe for web)
  
  Features:
  - Token injection for authentication
  - Score tracking
  - Game completion callbacks
  - JavaScript bridge communication
  
  Bridge Methods:
  - close: Return to app
  - toHome: Navigate to home
  - toast: Show notifications
  - share: Share content
  
  Navigation:
  - Game completion → Score update → Return to home
  
  APIs:
  - Bridge: window.globalProps.token (token injection)
  - POST /userScore/v1/addScore { score } (score update)
  
  Global State:
  - useAccountStore.token
  - useAccountStore.updateScore()
  
  Technical Considerations:
  - Replace React Native WebView with iframe or direct integration
  - Maintain JavaScript bridge functionality
  - Handle authentication token passing
  - Convert bridge methods to postMessage API
  - Maintain token injection pattern
  
  User Journey:
  - Interactive game experience
  - Score earning mechanism
  - Return to main app with updated score
-->

<template>
  <div class="minigame-webview">
    <!-- Game iframe container -->
    <!-- Loading overlay -->
    <!-- Bridge communication handler -->
    <!-- Score update feedback -->
  </div>
</template>

<script setup lang="ts">
// iframe management
// JavaScript bridge setup
// Token injection
// Score update handling
// Game completion callbacks
// Navigation control
</script>

<style scoped>
/* iframe container styling */
/* Loading overlay styling */
/* Full-screen game layout */
</style>
