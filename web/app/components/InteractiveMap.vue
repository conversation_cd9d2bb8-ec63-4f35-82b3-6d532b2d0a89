<!--
  InteractiveMap Component - Konva.js-based Interactive Map

  Purpose: Primary map interface with zoom, pan, and pin interactions

  Features:
  - Konva.js-based rendering for performance
  - Zoom and pan controls (mouse wheel + touch gestures)
  - Single layer marker system
  - Filter integration
  - Pin click interactions

  Props:
  - class: CSS classes for styling

  Events:
  - pin-selected: Emitted when a pin is clicked
  - map-interaction: Emitted on zoom/pan changes

  Based on admin implementation with simplified marker system
-->

<script setup lang="ts">
import { useImage } from 'vue-konva'
import { useElementSize } from '@vueuse/core'
import type { MapPin } from '~/types'
import { formatMarkers } from '~/utils/mapFormatters'

// Props
interface Props {
  class?: string
  mapUrl: string
  mapWidth: number
  mapHeight: number
  markers: readonly MapPin[]
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  class: '',
  isLoading: false
})

// Events
const emit = defineEmits<{
  'pin-selected': [pin: MapPin]
  'map-interaction': [interaction: { zoom: number, pan: { x: number, y: number } }]
}>()

// Create reactive map data from props
const mapData = computed(() => ({
  id: '1',
  event_id: '1',
  map_url: props.mapUrl,
  original_width: props.mapWidth,
  original_height: props.mapHeight
}))

const marks = computed(() => props.markers)

// Filter state (keep local for now)
const { filter } = useMap()

// Stage reference and dimensions
const stageRef = ref<any>(null)
const containerRef = ref<HTMLElement>()
const { width, height } = useElementSize(containerRef)

// Local zoom and pan state (reactive)
const stageScale = ref({ x: 1, y: 1 })
const stagePos = ref({ x: 0, y: 0 })

// Touch gesture state
const lastCenter = ref<{ x: number, y: number } | null>(null)
const lastDist = ref(0)
const dragStopped = ref(false)
const isDraggingDisabled = ref(false)


// Calculate initial scale to fill viewport vertically
const initialScale = computed(() => {
  if (!mapData.value || !width.value || !height.value) return 1

  const mapWidth = mapData.value.original_width
  const mapHeight = mapData.value.original_height
  const containerWidth = width.value
  const containerHeight = height.value

  // Calculate scale to fit map in container (maintaining aspect ratio)
  const scaleX = containerWidth / mapWidth
  const scaleY = containerHeight / mapHeight

  // Use the larger scale to fill the screen (prioritize vertical filling)
  return Math.max(scaleX, scaleY)
})

// Watch for initial scale changes and update stage scale
watch(initialScale, (newScale) => {
  if (newScale && stageScale.value.x === 1 && stageScale.value.y === 1) {
    // Only set initial scale if we're at default zoom
    stageScale.value = { x: newScale, y: newScale }

    // Center the map in the viewport
    if (mapData.value) {
      const mapWidth = mapData.value.original_width * newScale
      const mapHeight = mapData.value.original_height * newScale
      stagePos.value = {
        x: (width.value - mapWidth) / 2,
        y: (height.value - mapHeight) / 2
      }
    }
  }
}, { immediate: true })

// Zoom and pan constants
const minZoom = 0.5  // Allow zooming out to 10% to fit large images
const maxZoom = 4

// Pan boundary buffer (pixels beyond image edge)
const panBuffer = 0



// Background image loading - make it reactive to prop changes like admin
const backgroundImageUrl = computed(() => props.mapUrl || '')
const [backgroundImage] = useImage(backgroundImageUrl)

// Debug image loading
watch(() => props.mapUrl, (newUrl) => {
  console.log('Background image URL:', newUrl)
}, { immediate: true })

watch(backgroundImage, (newImage) => {
  console.log('Background image loaded:', newImage)
  if (newImage) {
    console.log('Image dimensions:', newImage.width, 'x', newImage.height)
  }
}, { immediate: true })

// Stage configuration (reactive)
const stageConfig = computed(() => ({
  width: width.value,
  height: height.value,
  scaleX: stageScale.value.x,
  scaleY: stageScale.value.y,
  x: stagePos.value.x,
  y: stagePos.value.y,
  draggable: !isDraggingDisabled.value
}))

// Use a consistent base size for the map (don't fit to container)
const mapDimensions = computed(() => {
  if (!mapData.value) {
    return {
      mapWidth: 1000, // Default base width
      mapHeight: 1000, // Default base height
      offsetX: 0,
      offsetY: 0
    }
  }

  // Use the original dimensions as the base size
  // Stage scaling will handle the actual display size
  return {
    mapWidth: mapData.value.original_width,
    mapHeight: mapData.value.original_height,
    offsetX: 0,
    offsetY: 0
  }
})

// Background image configuration using original dimensions
const backgroundConfig = computed(() => {
  if (!backgroundImage.value || !mapData.value) return {}

  const { mapWidth, mapHeight } = mapDimensions.value
  return {
    x: 0,
    y: 0,
    image: backgroundImage.value,
    width: mapWidth,
    height: mapHeight
  }
})

// Scaling factors for coordinate conversion (1:1 since we use original dimensions)
const scalingFactors = computed(() => {
  if (!mapData.value) return { scaleX: 1, scaleY: 1 }

  // Since we're using original dimensions, scaling factors are 1:1
  return { scaleX: 1, scaleY: 1 }
})

// Filtered markers based on filter state
const filteredMarkers = computed(() => {
  if (!marks.value.length) return []

  return marks.value.filter(marker => {
    switch (marker.mark_type) {
      case 1: return filter.value.mission
      case 2: return filter.value.exhibition
      case 3: return filter.value.event
      case 4: return filter.value.restaurant
      case 5: return filter.value.shop
      case 6: return filter.value.hotel
      case 7: return filter.value.other
      default: return true
    }
  })
})

// Formatted markers for Konva rendering (only depends on data and dimensions, not zoom)
const formattedMarkers = computed(() => {
  if (!filteredMarkers.value.length || !scalingFactors.value) return []
  const { scaleX, scaleY } = scalingFactors.value

  return formatMarkers([...filteredMarkers.value], scaleX, scaleY)
})

// Single layer markers with viewport culling and zoom-independent sizing
const visibleMarkers = computed(() => {
  // Viewport culling for performance
  const viewportBounds = {
    x: -stagePos.value.x / stageScale.value.x,
    y: -stagePos.value.y / stageScale.value.y,
    width: width.value / stageScale.value.x,
    height: height.value / stageScale.value.y
  }

  // Add padding to viewport for smooth scrolling
  const padding = 100
  const culledMarkers = formattedMarkers.value.filter(marker => {
    return marker.x >= viewportBounds.x - padding &&
           marker.x <= viewportBounds.x + viewportBounds.width + padding &&
           marker.y >= viewportBounds.y - padding &&
           marker.y <= viewportBounds.y + viewportBounds.height + padding
  })

  // Scale marker sizes inversely with zoom but less aggressively
  const baseMarkerRadius = 15 // Base radius in pixels
  const scaleDamping = 0.5 // Damping factor (0 = no scaling, 1 = full inverse scaling)
  const scaleAdjusted = 1 + (stageScale.value.x - 1) * scaleDamping
  const markerRadius = baseMarkerRadius / scaleAdjusted
  
  return culledMarkers.map(marker => ({
    ...marker,
    radius: markerRadius,
    strokeWidth: Math.max(1, 2 / scaleAdjusted) // Less aggressive stroke scaling
  }))
})

// Label configurations with less aggressive scaling
const labelConfigs = computed(() => {
  const scaleDamping = 0.5 // Same damping factor as markers
  const scaleAdjusted = 1 + (stageScale.value.x - 1) * scaleDamping
  
  return visibleMarkers.value.map(marker => ({
    id: marker.id,
    x: marker.x + (20 / scaleAdjusted),
    y: marker.y,
    text: marker.text,
    fontSize: Math.max(10, 12 / scaleAdjusted),
    fill: 'white',
    fontFamily: 'Arial, sans-serif',
    fontStyle: 'bold', // Make text bold for better readability
    verticalAlign: 'middle',
    offsetY: Math.max(4, 6 / scaleAdjusted),
    shadowColor: 'black',
    shadowBlur: Math.max(2, 4 / scaleAdjusted),
    shadowOffset: { x: Math.max(1, 2 / scaleAdjusted), y: Math.max(1, 2 / scaleAdjusted) },
    shadowOpacity: 0.8
  }))
})

// Pin visibility is now handled directly in the computed properties above

// Pan boundary constraint function
const constrainPan = (newPos: { x: number, y: number }, scale: number) => {
  if (!mapData.value) return newPos

  const mapWidth = mapData.value.original_width * scale
  const mapHeight = mapData.value.original_height * scale
  
  // Calculate boundaries with buffer
  const minX = width.value - mapWidth - panBuffer
  const maxX = panBuffer
  const minY = height.value - mapHeight - panBuffer
  const maxY = panBuffer

  return {
    x: Math.max(minX, Math.min(maxX, newPos.x)),
    y: Math.max(minY, Math.min(maxY, newPos.y))
  }
}

// Touch gesture utility functions
const getDistance = (p1: { x: number, y: number }, p2: { x: number, y: number }) => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2))
}

const getCenter = (p1: { x: number, y: number }, p2: { x: number, y: number }) => {
  return {
    x: (p1.x + p2.x) / 2,
    y: (p1.y + p2.y) / 2,
  }
}

// Wheel zoom handler (following Konva Vue pattern)
const handleWheel = (e: any) => {
  e.evt.preventDefault()

  const stage = stageRef.value?.getNode()
  if (!stage) return

  const pointer = stage.getPointerPosition()
  if (!pointer) return

  // Calculate zoom
  const scaleBy = 1.1
  const oldScale = stageScale.value.x
  const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy
  const clampedScale = Math.max(minZoom, Math.min(maxZoom, newScale))

  // Calculate new position to zoom towards pointer
  const mousePointTo = {
    x: (pointer.x - stagePos.value.x) / oldScale,
    y: (pointer.y - stagePos.value.y) / oldScale,
  }

  const newPos = {
    x: pointer.x - mousePointTo.x * clampedScale,
    y: pointer.y - mousePointTo.y * clampedScale,
  }

  // Apply pan constraints
  const constrainedPos = constrainPan(newPos, clampedScale)

  // Update reactive state (Vue will handle the rest)
  stageScale.value = { x: clampedScale, y: clampedScale }
  stagePos.value = constrainedPos
}

// Drag move handler for real-time constraints
const handleDragMove = (e: any) => {
  const stage = e.target
  const newPos = {
    x: stage.x(),
    y: stage.y()
  }
  
  // Apply pan constraints in real-time
  const constrainedPos = constrainPan(newPos, stageScale.value.x)
  
  // Update stage position if constrained
  if (constrainedPos.x !== newPos.x || constrainedPos.y !== newPos.y) {
    stage.position(constrainedPos)
  }
  
  // Update reactive state
  stagePos.value = constrainedPos
}

// Drag end handler
const handleDragEnd = (e: any) => {
  const stage = e.target
  const newPos = {
    x: stage.x(),
    y: stage.y()
  }
  
  // Apply pan constraints (final safety check)
  const constrainedPos = constrainPan(newPos, stageScale.value.x)
  stagePos.value = constrainedPos
  
  // Update stage position if constrained
  if (constrainedPos.x !== newPos.x || constrainedPos.y !== newPos.y) {
    stage.position(constrainedPos)
  }
}

// Touch move handler for multi-touch zoom and pan
const handleTouchMove = (e: any) => {
  e.evt.preventDefault()

  const touch1 = e.evt.touches[0]
  const touch2 = e.evt.touches[1]
  const stage = e.target.getStage()

  if (!stage) return

  // Restore dragging if it was cancelled by multi-touch
  if (touch1 && !touch2 && !stage.isDragging() && dragStopped.value) {
    stage.startDrag()
    dragStopped.value = false
  }

  if (touch1 && touch2) {
    // Stop Konva's drag and implement our own pan logic with two pointers
    if (stage.isDragging()) {
      stage.stopDrag()
      dragStopped.value = true
    }

    const p1 = {
      x: touch1.clientX,
      y: touch1.clientY,
    }
    const p2 = {
      x: touch2.clientX,
      y: touch2.clientY, 
    }

    if (!lastCenter.value) {
      lastCenter.value = getCenter(p1, p2)
      return
    }

    const newCenter = getCenter(p1, p2)
    const dist = getDistance(p1, p2)

    if (!lastDist.value) {
      lastDist.value = dist
      return
    }

    // Local coordinates of center point
    const pointTo = {
      x: (newCenter.x - stagePos.value.x) / stageScale.value.x,
      y: (newCenter.y - stagePos.value.y) / stageScale.value.x,
    }

    // Calculate new scale with limits
    const newScale = stageScale.value.x * (dist / lastDist.value)
    const clampedScale = Math.max(minZoom, Math.min(maxZoom, newScale))

    stageScale.value = { x: clampedScale, y: clampedScale }

    // Calculate new position of the stage
    const dx = newCenter.x - lastCenter.value.x
    const dy = newCenter.y - lastCenter.value.y

    const newPos = {
      x: newCenter.x - pointTo.x * clampedScale + dx,
      y: newCenter.y - pointTo.y * clampedScale + dy,
    }

    // Apply pan constraints
    stagePos.value = constrainPan(newPos, clampedScale)

    lastDist.value = dist
    lastCenter.value = newCenter
  }
}

// Touch start handler to disable dragging with two fingers
const handleTouchStart = (e: any) => {
  const touches = e.evt.touches
  if (touches && touches.length === 2) {
    // Disable dragging when two fingers detected
    isDraggingDisabled.value = true
  }
}

// Touch end handler
const handleTouchEnd = () => {
  lastDist.value = 0
  lastCenter.value = null
  // Re-enable dragging when touch ends
  isDraggingDisabled.value = false
}

// Pin click handler
const handleMarkerClick = (marker: any) => {
  // Emit selection event for the marker
  emit('pin-selected', marker.data || marker)
}

// Auto-zoom and focus methods
const zoomToLocation = (x: number, y: number, targetZoom: number = 2) => {
  const centerX = width.value / 2
  const centerY = height.value / 2

  const newPos = {
    x: centerX - x * targetZoom,
    y: centerY - y * targetZoom
  }

  stageScale.value = { x: targetZoom, y: targetZoom }
  stagePos.value = newPos

  emit('map-interaction', { zoom: targetZoom, pan: newPos })
}

const focusOnPin = (pin: MapPin) => {
  if (!scalingFactors.value) return

  const { scaleX, scaleY } = scalingFactors.value
  const scaledX = pin.x_axis * scaleX
  const scaledY = pin.y_axis * scaleY

  // Zoom to a reasonable level
  const targetZoom = 2.0
  zoomToLocation(scaledX, scaledY, targetZoom)
}

const zoomToFitAll = () => {
  if (!formattedMarkers.value.length) return

  // Calculate bounds of all markers
  const bounds = formattedMarkers.value.reduce((acc, marker) => ({
    minX: Math.min(acc.minX, marker.x),
    maxX: Math.max(acc.maxX, marker.x),
    minY: Math.min(acc.minY, marker.y),
    maxY: Math.max(acc.maxY, marker.y)
  }), {
    minX: Infinity,
    maxX: -Infinity,
    minY: Infinity,
    maxY: -Infinity
  })

  // Calculate zoom to fit all markers with padding
  const padding = 50
  const boundsWidth = bounds.maxX - bounds.minX + padding * 2
  const boundsHeight = bounds.maxY - bounds.minY + padding * 2

  const scaleX = width.value / boundsWidth
  const scaleY = height.value / boundsHeight
  const targetZoom = Math.min(scaleX, scaleY, maxZoom)

  // Center on bounds
  const centerX = (bounds.minX + bounds.maxX) / 2
  const centerY = (bounds.minY + bounds.maxY) / 2

  zoomToLocation(centerX, centerY, targetZoom)
}

const resetView = () => {
  const scale = initialScale.value
  stageScale.value = { x: scale, y: scale }

  // Center the map in the viewport
  if (mapData.value) {
    const mapWidth = mapData.value.original_width * scale
    const mapHeight = mapData.value.original_height * scale
    stagePos.value = {
      x: (width.value - mapWidth) / 2,
      y: (height.value - mapHeight) / 2
    }
  } else {
    stagePos.value = { x: 0, y: 0 }
  }

  emit('map-interaction', { zoom: scale, pan: stagePos.value })
}

// Expose methods for parent component
defineExpose({
  zoomToLocation,
  focusOnPin,
  zoomToFitAll,
  resetView
})

// Performance monitoring
const performanceStats = ref({
  renderCount: 0,
  lastRenderTime: 0,
  avgRenderTime: 0,
  visibleMarkers: 0
})

// Track render performance
watch([visibleMarkers], () => {
  const startTime = performance.now()

  nextTick(() => {
    const endTime = performance.now()
    const renderTime = endTime - startTime

    performanceStats.value.renderCount++
    performanceStats.value.lastRenderTime = renderTime
    performanceStats.value.avgRenderTime =
      (performanceStats.value.avgRenderTime * (performanceStats.value.renderCount - 1) + renderTime) /
      performanceStats.value.renderCount
    performanceStats.value.visibleMarkers = visibleMarkers.value.length

    // Log performance warnings
    if (renderTime > 16) { // More than one frame at 60fps
      console.warn(`Slow render detected: ${renderTime.toFixed(2)}ms`)
    }
  })
})

// Enable Konva hit detection for touch events
onMounted(() => {
  try {
    // Enable all events on Konva, even when dragging a node
    // This is required for touch events to work properly
    if (typeof window !== 'undefined' && (window as any).Konva) {
      (window as any).Konva.hitOnDragEnabled = true
    }
  } catch (err) {
    console.error('Failed to initialize map:', err)
  }
})
</script>

<template>
  <div ref="containerRef" :class="props.class" class="relative overflow-hidden bg-black w-full h-full">
    <!-- Loading state -->
    <div v-if="props.isLoading" class="fixed inset-0 flex items-center justify-center bg-gray-900 z-50">
      <div class="text-center">
        <div class="animate-spin rounded-full h-16 w-16 border-4 border-gray-700 border-t-white mx-auto mb-4"></div>
        <p class="text-gray-300 text-lg">Loading map...</p>
      </div>
    </div>

    <!-- No data state -->
    <div v-else-if="!props.mapUrl || !props.markers.length" class="absolute inset-0 flex items-center justify-center bg-gray-50">
      <div class="text-center p-6">
        <div class="text-gray-400 text-6xl mb-4">📍</div>
        <h3 class="text-lg font-semibold text-gray-700 mb-2">No Map Data</h3>
        <p class="text-gray-500 mb-4">No markers or map data available to display.</p>
      </div>
    </div>

    <!-- Konva Stage -->
    <v-stage
      v-else
      ref="stageRef"
      :config="stageConfig"
      class="w-full h-full bg-gray-100"
      @wheel="handleWheel"
      @dragmove="handleDragMove"
      @dragend="handleDragEnd"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <!-- Background Image Layer -->
      <v-layer>
        <!-- Background image -->
        <v-image
          v-if="backgroundImage"
          :config="backgroundConfig"
        />
      </v-layer>

      <!-- Markers Layer with Caching -->
      <v-layer ref="markersLayerRef">
        <!-- Single layer markers -->
        <v-circle
          v-for="marker in visibleMarkers"
          :key="`marker-${marker.id}`"
          :config="{
            ...marker,
            perfectDrawEnabled: false,
            listening: true
          }"
          @click="handleMarkerClick(marker)"
          @tap="handleMarkerClick(marker)"
        />
      </v-layer>

      <!-- Labels Layer -->
      <v-layer>
        <!-- Single layer marker labels with zoom-independent sizing -->
        <v-text
          v-for="labelConfig in labelConfigs"
          :key="`label-${labelConfig.id}`"
          :config="labelConfig"
        />
      </v-layer>
    </v-stage>
  </div>
</template>
