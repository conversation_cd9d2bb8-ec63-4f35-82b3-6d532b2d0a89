<!--
  Mission Modal - Game Information and Launch
  
  Purpose: Display mission/game information and launch functionality
  
  Features:
  - Mission details display
  - Game launch button
  - Score information
  - Collection status
  - Mission requirements
  
  APIs:
  - GET /mark/v1/mark?markId=X
  - POST /mark/v1/collect { markId }
  
  Global State:
  - missionDetail
  - collected status
  
  User Journey:
  - View mission information
  - Launch associated mini-game
  - Track completion status
  - Collect mission rewards
-->

<template>
  <div class="mission-modal">
    <!-- Modal overlay -->
    <!-- Mission information -->
    <!-- Game launch button -->
    <!-- Score/reward display -->
    <!-- Collection status -->
    <!-- Close button -->
  </div>
</template>

<script setup lang="ts">
// Mission data loading
// Game launch functionality
// Collection handling
// Modal state management
</script>

<style scoped>
/* Modal overlay styling */
/* Mission detail layout */
/* Launch button styling */
/* Collection status styling */
</style>
