<!--
  Event Modal - Event Information and Actions
  
  Purpose: Display event information and related actions
  
  Features:
  - Event details display
  - Schedule information
  - Location details
  - Registration/participation
  - Event reminders
  
  APIs:
  - GET /mark/v1/mark?markId=X
  - POST /mark/v1/collect { markId }
  
  Global State:
  - eventDetail
  - collected status
  
  User Journey:
  - View event information
  - Check event schedule
  - Register for events
  - Set event reminders
-->

<template>
  <div class="event-modal">
    <!-- Modal overlay -->
    <!-- Event information -->
    <!-- Schedule details -->
    <!-- Location information -->
    <!-- Registration button -->
    <!-- Close button -->
  </div>
</template>

<script setup lang="ts">
// Event data loading
// Registration functionality
// Schedule management
// Modal state management
</script>

<style scoped>
/* Modal overlay styling */
/* Event detail layout */
/* Schedule styling */
/* Registration button styling */
</style>
