<!--
  Exhibition Modal - Exhibitor Details and Collection
  
  Purpose: Display exhibitor information and collection functionality
  
  Features:
  - Exhibitor details display
  - Company information
  - Collection functionality
  - Contact information
  - Booth location
  
  APIs:
  - GET /mark/v1/mark?markId=X
  - POST /mark/v1/collect { markId }
  
  Global State:
  - exhibitionDetail
  - collected status
  
  User Journey:
  - View exhibitor information
  - Learn about companies/products
  - Collect exhibition stamps
  - Access contact information
-->

<template>
  <div class="exhibition-modal">
    <!-- Modal overlay -->
    <!-- Exhibitor information -->
    <!-- Company details -->
    <!-- Collection button -->
    <!-- Contact information -->
    <!-- Close button -->
  </div>
</template>

<script setup lang="ts">
// Exhibition data loading
// Collection functionality
// Contact information handling
// Modal state management
</script>

<style scoped>
/* Modal overlay styling */
/* Exhibition detail layout */
/* Company information styling */
/* Collection button styling */
</style>
