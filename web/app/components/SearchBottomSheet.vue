<!--
  Search Bottom Sheet - Multi-mode Search Interface
  
  Purpose: Find and navigate to specific locations
  
  Modes:
  - Search Mode: Text search for exhibitors/locations
  - Collections Mode: View collected items
  - Filter Mode: Toggle pin type visibility
  
  Features:
  - Always visible at bottom with expandable content
  - Real-time search results
  - Tap to focus map on location
  - Mode switching (search/collections/filter)
  - Smooth animations and transitions
  
  APIs:
  - POST /search/v1/search { keyword } (Search Mode)
  - GET /mark/v1/collectList (Collections Mode)
  
  Global State:
  - searchList
  - drawerMode
  - collectionList
  - useMapStore.filter
  
  User Journey:
  - Primary search interface
  - Collection management
  - Map filtering controls
-->

<template>
  <div class="search-bottom-sheet">
    <!-- Bottom Sheet Container -->
    <div 
      class="fixed bottom-0 left-0 right-0 z-50 bg-white shadow-2xl transition-all duration-300 ease-in-out"
      :class="{
        'translate-y-0': true,
        'h-16': !isExpanded,
        'h-96': isExpanded
      }"
    >
      <!-- Drag Handle & Header (Always Visible) -->
      <div 
        class="flex items-center justify-between p-4 border-b border-gray-200 cursor-pointer"
        @click="toggleExpanded"
      >
        <!-- Drag Handle -->
        <div class="flex items-center space-x-3">
          <div class="w-8 h-1 bg-gray-300 rounded-full" />
          <span class="text-sm font-medium text-gray-700">
            {{ modes.find(m => m.key === currentMode)?.label || 'Search' }}
          </span>
        </div>
        
        <!-- Expand/Collapse Icon -->
        <Icon 
          :name="isExpanded ? 'i-heroicons-chevron-down' : 'i-heroicons-chevron-up'" 
          class="w-5 h-5 text-gray-400 transition-transform duration-200"
        />
      </div>

      <!-- Expanded Content -->
      <div 
        v-show="isExpanded"
        class="flex flex-col h-full overflow-hidden"
      >
        <!-- Mode Toggle Buttons -->
        <div class="flex space-x-1 p-4 pb-2 bg-gray-50">
          <button
            v-for="mode in modes"
            :key="mode.key"
            class="flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-lg text-sm font-medium transition-all"
            :class="{
              'bg-blue-500 text-white shadow-md': currentMode === mode.key,
              'bg-white text-gray-600 hover:bg-gray-100': currentMode !== mode.key
            }"
            @click="setMode(mode.key)"
          >
            <Icon :name="mode.icon" class="w-4 h-4" />
            <span>{{ mode.label }}</span>
          </button>
        </div>

        <!-- Content Area -->
        <div class="flex-1 overflow-y-auto p-4">
          <!-- Search Mode Content -->
          <div v-if="currentMode === 'search'" class="space-y-4">
            <!-- Search Input -->
            <div class="relative">
              <Icon name="i-heroicons-magnifying-glass" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Search locations..."
                class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                @input="handleSearch"
              />
              <button
                v-if="searchQuery"
                class="absolute right-3 top-1/2 transform -translate-y-1/2"
                @click="clearSearch"
              >
                <Icon name="i-heroicons-x-mark" class="w-5 h-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>

            <!-- Search Results -->
            <div v-if="searchResults.length > 0" class="space-y-2">
              <div
                v-for="result in searchResults"
                :key="result.id"
                class="p-3 bg-white border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                @click="selectLocation(result)"
              >
                <div class="flex items-start space-x-3">
                  <div class="flex-shrink-0">
                    <span 
                      class="inline-block px-2 py-1 text-xs font-medium rounded-full"
                      :class="getPinTypeClasses(result.mark_type)"
                    >
                      {{ getPinTypeName(result.mark_type) }}
                    </span>
                  </div>
                  <div class="flex-1 min-w-0">
                    <h3 class="text-sm font-medium text-gray-900 truncate">
                      {{ result.name }}
                    </h3>
                    <p v-if="result.description" class="text-xs text-gray-500 truncate">
                      {{ result.description }}
                    </p>
                  </div>
                  <Icon name="i-heroicons-chevron-right" class="w-4 h-4 text-gray-400 flex-shrink-0" />
                </div>
              </div>
            </div>

            <!-- No Results Message -->
            <div v-else-if="searchQuery && !isSearching" class="text-center py-8">
              <Icon name="i-heroicons-magnifying-glass" class="w-12 h-12 text-gray-300 mx-auto mb-2" />
              <p class="text-gray-500">No locations found</p>
            </div>

            <!-- Loading State -->
            <div v-else-if="isSearching" class="text-center py-8">
              <div class="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p class="text-gray-500">Searching...</p>
            </div>
          </div>

          <!-- Collections Mode Content -->
          <div v-else-if="currentMode === 'collections'" class="space-y-4">
            <div v-if="collectionList.length > 0" class="grid grid-cols-2 gap-3">
              <div
                v-for="item in collectionList"
                :key="item.id"
                class="p-3 bg-white border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                @click="selectLocation(item)"
              >
                <div class="text-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Icon name="i-heroicons-star" class="w-6 h-6 text-blue-600" />
                  </div>
                  <h3 class="text-xs font-medium text-gray-900 truncate mb-1">
                    {{ item.name }}
                  </h3>
                  <span 
                    class="inline-block px-2 py-1 text-xs font-medium rounded-full"
                    :class="getPinTypeClasses(item.mark_type)"
                  >
                    {{ getPinTypeName(item.mark_type) }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- No Collections Message -->
            <div v-else class="text-center py-8">
              <Icon name="i-heroicons-star" class="w-12 h-12 text-gray-300 mx-auto mb-2" />
              <p class="text-gray-500">No collected items yet</p>
            </div>
          </div>

          <!-- Filter Mode Content -->
          <div v-else-if="currentMode === 'filter'" class="space-y-3">
            <div
              v-for="filterOption in filterOptions"
              :key="filterOption.key"
              class="flex items-center justify-between py-3 px-2"
            >
              <div class="flex items-center space-x-3">
                <span 
                  class="inline-block px-3 py-1 text-sm font-medium rounded-full"
                  :class="filterOption.classes"
                >
                  {{ filterOption.name }}
                </span>
                <span class="text-sm text-gray-700">{{ filterOption.count }} items</span>
              </div>
              <label class="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  class="sr-only peer"
                  :checked="filter[filterOption.key as keyof typeof filter]"
                  @change="toggleFilter(filterOption.key as keyof typeof filter)"
                />
                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { MapPin } from '~/types'

// Component props and emits
interface Props {
  open?: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'location-selected', location: MapPin): void
}

const props = withDefaults(defineProps<Props>(), {
  open: false
})

const emit = defineEmits<Emits>()

// Bottom sheet state management
const isExpanded = ref(false)
const currentMode = ref<'search' | 'collections' | 'filter'>('search')

// Mode configuration
const modes = [
  { key: 'search', label: 'Search', icon: 'i-heroicons-magnifying-glass' },
  { key: 'collections', label: 'Collections', icon: 'i-heroicons-star' },
  { key: 'filter', label: 'Filter', icon: 'i-heroicons-funnel' }
] as const

// Search state
const searchQuery = ref('')
const searchResults = ref<MapPin[]>([])
const isSearching = ref(false)

// Collections state
const collectionList = ref<MapPin[]>([])

// Get map state and filter
const { marks, filter, toggleFilter } = useMap()

// Filter options configuration
const filterOptions = computed(() => [
  { 
    key: 'mission', 
    name: 'Mission', 
    classes: 'bg-purple-100 text-purple-800', 
    count: marks.value.filter(m => m.mark_type === 1).length 
  },
  { 
    key: 'exhibition', 
    name: 'Exhibition', 
    classes: 'bg-orange-100 text-orange-800', 
    count: marks.value.filter(m => m.mark_type === 2).length 
  },
  { 
    key: 'event', 
    name: 'Event', 
    classes: 'bg-pink-100 text-pink-800', 
    count: marks.value.filter(m => m.mark_type === 3).length 
  },
  { 
    key: 'restaurant', 
    name: 'Restaurant', 
    classes: 'bg-green-100 text-green-800', 
    count: marks.value.filter(m => m.mark_type === 4).length 
  },
  { 
    key: 'shop', 
    name: 'Shop', 
    classes: 'bg-blue-100 text-blue-800', 
    count: marks.value.filter(m => m.mark_type === 5).length 
  },
  { 
    key: 'hotel', 
    name: 'Hotel', 
    classes: 'bg-cyan-100 text-cyan-800', 
    count: marks.value.filter(m => m.mark_type === 6).length 
  },
  { 
    key: 'other', 
    name: 'Other', 
    classes: 'bg-gray-100 text-gray-800', 
    count: marks.value.filter(m => m.mark_type === 7).length 
  }
])

// Bottom sheet management
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// Mode switching logic
const setMode = (mode: 'search' | 'collections' | 'filter') => {
  currentMode.value = mode
  if (!isExpanded.value) {
    isExpanded.value = true
  }
}

// Search functionality
const handleSearch = debounce(async () => {
  const query = searchQuery.value
  if (!query.trim()) {
    searchResults.value = []
    return
  }

  isSearching.value = true
  try {
    // Filter marks based on search query
    searchResults.value = marks.value.filter(mark => 
      mark.name.toLowerCase().includes(query.toLowerCase()) ||
      (mark.description && mark.description.toLowerCase().includes(query.toLowerCase()))
    )
  } catch (error) {
    console.error('Search failed:', error)
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}, 300)

const clearSearch = () => {
  searchQuery.value = ''
  searchResults.value = []
}

// Collections data loading
const loadCollections = async () => {
  try {
    // Filter collected items from marks
    collectionList.value = marks.value.filter(mark => mark.collected === true)
  } catch (error) {
    console.error('Failed to load collections:', error)
    collectionList.value = []
  }
}

// Map focus navigation
const selectLocation = (location: MapPin) => {
  emit('location-selected', location)
  isExpanded.value = false
}

// Utility functions
const getPinTypeClasses = (markType: number): string => {
  const classMap = {
    1: 'bg-purple-100 text-purple-800',   // Mission
    2: 'bg-orange-100 text-orange-800',   // Exhibition  
    3: 'bg-pink-100 text-pink-800',       // Event
    4: 'bg-green-100 text-green-800',     // Restaurant
    5: 'bg-blue-100 text-blue-800',       // Shop
    6: 'bg-cyan-100 text-cyan-800',       // Hotel
    7: 'bg-gray-100 text-gray-800'        // Other
  }
  return classMap[markType as keyof typeof classMap] || 'bg-gray-100 text-gray-800'
}

const getPinTypeName = (markType: number): string => {
  const nameMap = {
    1: 'Mission',
    2: 'Exhibition',
    3: 'Event',
    4: 'Restaurant',
    5: 'Shop',
    6: 'Hotel',
    7: 'Other'
  }
  return nameMap[markType as keyof typeof nameMap] || 'Unknown'
}

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeoutId: ReturnType<typeof setTimeout>
  return ((...args: any[]) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), wait)
  }) as T
}

// Watch for mode changes
watch(currentMode, (newMode) => {
  if (newMode === 'collections') {
    loadCollections()
  }
})

// Load collections on mount
onMounted(() => {
  loadCollections()
})
</script>
