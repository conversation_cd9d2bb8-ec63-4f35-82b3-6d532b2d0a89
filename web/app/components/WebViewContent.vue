<!--
  WebView Content - External Content Display
  
  Purpose: Display external content
  
  Features:
  - Rules and privacy policy
  - External web content
  - Navigation controls
  - Token injection for authenticated content
  - Loading states
  
  APIs:
  - None (external URLs)
  
  Global State:
  - useAccountStore.token (for injection)
  - WebView URL state
  
  User Journey:
  - Access external content
  - View rules and policies
  - Navigate external resources
  - Authenticated content access
-->

<template>
  <div class="webview-content">
    <!-- iframe container -->
    <!-- Navigation controls -->
    <!-- Loading overlay -->
    <!-- Error handling -->
  </div>
</template>

<script setup lang="ts">
// iframe management
// Token injection for authenticated content
// Navigation control
// Loading state management
// Error handling
</script>

<style scoped>
/* iframe container styling */
/* Navigation controls styling */
/* Loading overlay styling */
/* Error state styling */
</style>
