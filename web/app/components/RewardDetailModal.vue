<!--
  Reward Detail Modal - Reward Information and Exchange
  
  Features:
  - Reward information display
  - Exchange functionality
  - QR code generation
  - Energy cost display
  - Exchange confirmation
  
  APIs:
  - POST /reward/v1/exchange { rewardId }
  
  Global State:
  - useAccountStore.updateScore()
  - qrCodeValue (local)
  
  User Journey:
  - Detailed reward information
  - Exchange confirmation and processing
  - QR code generation for redemption
  - Score/energy deduction
-->

<template>
  <div class="reward-detail-modal">
    <!-- Modal overlay -->
    <!-- Reward information -->
    <!-- Energy cost display -->
    <!-- Exchange button -->
    <!-- QR code display -->
    <!-- Close button -->
  </div>
</template>

<script setup lang="ts">
// Modal state management
// Reward exchange logic
// QR code generation
// Score update handling
// Modal close functionality
</script>

<style scoped>
/* Modal overlay styling */
/* Reward detail layout */
/* QR code styling */
/* Exchange button styling */
</style>
