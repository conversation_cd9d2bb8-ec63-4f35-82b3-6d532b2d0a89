/**
 * Account Store - User Authentication and Profile Management
 * 
 * Purpose: Manage user authentication state and profile data
 * 
 * State Management:
 * - Authentication token
 * - User profile information
 * - Nickname management
 * - Score tracking
 * - BGM preferences
 * 
 * APIs:
 * - Authentication endpoints
 * - Profile management endpoints
 * - Score update endpoints
 * 
 * Technical Considerations:
 * - Replace Zustand with Pinia or Vue composables
 * - Maintain authentication state
 * - Handle token persistence
 * - Score and profile synchronization
 * 
 * Migration Notes:
 * - Convert from useAccountStore (Zustand) to Pinia store
 * - Maintain same structure and API patterns
 * - Handle authentication state persistence
 */

// Authentication state management
// Profile data management
// Score tracking
// Token handling
// BGM preferences
// API integration

export const useUser = () => {
  // Authentication state
  const token = ref<string | null>(null)
  const isAuthenticated = ref(false)

  // Profile state
  const nickname = ref<string>('Test User')
  const score = ref<number>(1250) // Mock score for testing

  // BGM state
  const isBgmEnabled = ref(true)

  // Authentication methods
  const updateToken = (newToken: string | null) => {
    token.value = newToken
    isAuthenticated.value = !!newToken
  }

  const updateNickName = (newNickname: string) => {
    nickname.value = newNickname
  }

  const updateScore = (newScore: number) => {
    score.value = newScore
  }

  const addScore = (points: number) => {
    score.value += points
  }

  // BGM methods
  const toggleBgm = () => {
    isBgmEnabled.value = !isBgmEnabled.value
  }

  return {
    // State properties
    token: readonly(token),
    isAuthenticated: readonly(isAuthenticated),
    nickname: readonly(nickname),
    score: readonly(score),
    isBgmEnabled: readonly(isBgmEnabled),

    // Action methods
    updateToken,
    updateNickName,
    updateScore,
    addScore,
    toggleBgm
  }
}
