/**
 * Account Store - User Authentication and Profile Management
 * 
 * Purpose: Manage user authentication state and profile data
 * 
 * State Management:
 * - Authentication token
 * - User profile information
 * - Nickname management
 * - Score tracking
 * - BGM preferences
 * 
 * APIs:
 * - Authentication endpoints
 * - Profile management endpoints
 * - Score update endpoints
 * 
 * Technical Considerations:
 * - Replace Zustand with Pinia or Vue composables
 * - Maintain authentication state
 * - Handle token persistence
 * - Score and profile synchronization
 * 
 * Migration Notes:
 * - Convert from useAccountStore (Zustand) to Pinia store
 * - Maintain same structure and API patterns
 * - Handle authentication state persistence
 */

import type { User, AuthState } from '~/types'

export const useAccount = () => {
  // Authentication state
  const token = ref<string | null>(null)
  const isAuthenticated = ref(false)
  
  // Profile state
  const user = ref<User | null>(null)
  const nickname = ref<string>('')
  const score = ref<number>(1250) // Mock score for testing
  
  // BGM state
  const isBgmEnabled = ref(true)
  
  // Authentication methods
  const updateToken = (newToken: string | null) => {
    token.value = newToken
    isAuthenticated.value = !!newToken
    
    // Store in cookie for persistence
    const tokenCookie = useCookie('authToken', {
      default: () => null,
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })
    tokenCookie.value = newToken
  }
  
  const updateNickName = (newNickname: string) => {
    nickname.value = newNickname
    if (user.value) {
      user.value.nickname = newNickname
    }
  }
  
  const updateScore = (newScore: number) => {
    score.value = newScore
    if (user.value) {
      user.value.score = newScore
    }
  }
  
  const addScore = (points: number) => {
    score.value += points
    if (user.value) {
      user.value.score = score.value
    }
  }
  
  // BGM methods
  const toggleBgm = () => {
    isBgmEnabled.value = !isBgmEnabled.value
  }
  
  const setBgm = (enabled: boolean) => {
    isBgmEnabled.value = enabled
  }
  
  // Profile methods
  const updateProfile = (profileData: Partial<User>) => {
    if (user.value) {
      user.value = { ...user.value, ...profileData }
    }
  }
  
  // Logout
  const logout = () => {
    token.value = null
    isAuthenticated.value = false
    user.value = null
    nickname.value = ''
    score.value = 0
    
    // Clear cookie
    const tokenCookie = useCookie('authToken')
    tokenCookie.value = null
  }
  
  // Initialize from cookie on mount
  const initializeAuth = () => {
    const tokenCookie = useCookie('authToken')
    if (tokenCookie.value) {
      updateToken(tokenCookie.value)
      // In a real app, you'd validate the token and load user data
      // For now, just set some mock data
      user.value = {
        id: '1',
        nickname: 'Test User',
        phoneNumber: '+1234567890',
        location: 'Tokyo',
        birthday: '1990-01-01',
        score: score.value,
        token: tokenCookie.value
      }
      nickname.value = user.value.nickname
    }
  }
  
  return {
    // State properties
    token: readonly(token),
    isAuthenticated: readonly(isAuthenticated),
    user: readonly(user),
    nickname: readonly(nickname),
    score: readonly(score),
    isBgmEnabled: readonly(isBgmEnabled),
    
    // Action methods
    updateToken,
    updateNickName,
    updateScore,
    addScore,
    toggleBgm,
    setBgm,
    updateProfile,
    logout,
    initializeAuth
  }
}
