/**
 * Map Store - Map State and Data Management
 *
 * Purpose: Manage map state, markers, and filtering
 *
 * State Management:
 * - Map markers and areas
 * - Filter states
 * - Countdown data
 * - Map view state
 * - Selected pins
 *
 * APIs:
 * - Map data endpoints
 * - Marker detail endpoints
 * - Collection endpoints
 *
 * Technical Considerations:
 * - Replace Zustand with Pinia or Vue composables
 * - Handle map state and user preferences
 * - Maintain filter and map logic
 * - Convert map state to web map library state
 *
 * Migration Notes:
 * - Convert from useMapStore (Zustand) to Pinia store
 * - Maintain same filter/map logic
 * - Same marker data structure and pin APIs
 */

import type { MapPin, MapArea, EventMap } from '~/types'
import { getMockMapData, mockFilterState } from '~/utils/mockData'

export const useMap = () => {
  // Map state
  const marks = ref<MapPin[]>([])
  const areas = ref<MapArea[]>([])
  const mapData = ref<EventMap | null>(null)
  const selectedPin = ref<MapPin | null>(null)
  const isLoading = ref(false)

  // Filter state
  const filter = ref(mockFilterState)

  // Map view state
  const zoomLevel = ref(1)
  const panPosition = ref({ x: 0, y: 0 })

  // Countdown state
  const countDown = ref<number | null>(null)

  // Load mock data
  const loadMapData = async () => {
    try {
      isLoading.value = true

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500))

      const mockData = getMockMapData()
      marks.value = mockData.markers
      areas.value = mockData.areas
      mapData.value = mockData.eventMap

    } catch (error) {
      console.error('Failed to load map data:', error)
    } finally {
      isLoading.value = false
    }
  }

  // Filter management
  const updateFilter = (filterType: keyof typeof mockFilterState, value: boolean) => {
    filter.value[filterType] = value
  }

  const toggleFilter = (filterType: keyof typeof mockFilterState) => {
    filter.value[filterType] = !filter.value[filterType]
  }

  const resetFilters = () => {
    filter.value = { ...mockFilterState }
  }

  // Map view management
  const updateZoom = (newZoom: number) => {
    zoomLevel.value = newZoom
  }

  const updatePan = (newPosition: { x: number, y: number }) => {
    panPosition.value = newPosition
  }

  // Pin selection
  const selectPin = (pin: MapPin | null) => {
    selectedPin.value = pin
  }

  // Countdown management
  const setCountdown = (value: number) => {
    countDown.value = value
  }

  const updateMap = () => {
    // Refresh map data
    loadMapData()
  }

  return {
    // State properties
    marks: readonly(marks),
    areas: readonly(areas),
    mapData: readonly(mapData),
    selectedPin,
    filter,
    zoomLevel: readonly(zoomLevel),
    panPosition: readonly(panPosition),
    countDown: readonly(countDown),
    isLoading: readonly(isLoading),

    // Action methods
    loadMapData,
    updateFilter,
    toggleFilter,
    resetFilters,
    updateZoom,
    updatePan,
    selectPin,
    setCountdown,
    updateMap
  }
}
