/**
 * Type Definitions - TypeScript Type Definitions
 * 
 * Purpose: Centralized type definitions for the application
 * 
 * Categories:
 * - User and Authentication Types
 * - Map and Pin Types
 * - Reward Types
 * - API Response Types
 * - UI State Types
 * 
 * Technical Considerations:
 * - Maintain type safety across the application
 * - Consistent with API response structures
 * - Support for state management types
 */

// User and Authentication Types
export interface User {
  id: string
  nickname: string
  phoneNumber: string
  location: string
  birthday: string
  score: number
  token: string
}

export interface AuthState {
  isAuthenticated: boolean
  token: string | null
  user: User | null
}

// Map and Pin Types (based on backend structure)
export interface MapPin {
  id: string
  mark_type: number // 1=Mission, 2=Exhibition, 3=Event, 4=Restaurant, 5=Shop, 6=Hotel, 7=Other
  name: string
  description?: string
  x_axis: number // Database coordinates
  y_axis: number // Database coordinates
  area_id?: string
  event_id: string
  collected?: boolean
}

export interface MapArea {
  id: string
  name: string
  x_axis: number
  y_axis: number
  width: number
  height: number
  event_id: string
}

export interface EventMap {
  id: string
  event_id: string
  map_url: string
  original_width: number
  original_height: number
}

export interface MapState {
  pins: MapPin[]
  areas: MapArea[]
  mapData: EventMap | null
  selectedPin: MapPin | null
  filter: {
    mission: boolean
    exhibition: boolean
    event: boolean
    restaurant: boolean
    shop: boolean
    hotel: boolean
    other: boolean
  }
  zoomLevel: number
  panPosition: {
    x: number
    y: number
  }
}

// Reward Types
export interface Reward {
  id: string
  name: string
  description: string
  energyCost: number
  imageUrl: string
  available: boolean
}

export interface MyReward {
  id: string
  rewardId: string
  reward: Reward
  acquiredAt: string
  used: boolean
  qrCode: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
}

// UI State Types
export interface BottomSheetState {
  isOpen: boolean
  snapPoint: string
  mode: 'search' | 'collections' | 'filter'
}

export interface ModalState {
  isOpen: boolean
  type: 'mission' | 'exhibition' | 'event' | 'reward' | null
  data: any
}
