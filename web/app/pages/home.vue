<!--
  Home Screen - Interactive Map (Primary App Interface)
  
  Purpose: Primary app interface - interactive event map
  
  Core Features:
  - Zoomable/pannable map view using Konva.js (similar to admin)
  - Multiple pin types (Mission, Exhibition, Event)
  - Score display
  - Profile access
  - QR scanner access
  - Search functionality
  
  Pin Types:
  - Mission Pins (Purple): Interactive games/challenges
  - Exhibition Pins (Orange): Exhibitor information  
  - Event Pins (Pink): Event information
  
  User Actions:
  - Tap pins to view details
  - Zoom in/out
  - Pan around map
  - Filter pin visibility
  
  UI Elements:
  - Top Right: Profile menu button
  - Top Left: Score display (tappable)
  - Bottom Right: QR scanner button
  - Bottom: Search bottom sheet
  - Right Drawer: Side menu
  
  APIs:
  - GET /mark/v1/marks?eventId=1
  - GET /mark/v1/mark?markId=X (when pin tapped)
  
  Global State:
  - useMap.marks
  - useMap.areas
  - useAccount.score
  - selectedPin
  - zoomLevel
  - useMap.filter
  - openDraw.current
  
  User Journey:
  - Main hub for all app activities
  - Central navigation point
  - Primary interaction interface
  
  Technical Implementation:
  - Uses Konva.js for map rendering (same as admin)
  - Background image with interactive pin overlays
  - Coordinate scaling for responsive design
-->

<template>
  <div class="relative w-full h-screen overflow-hidden">
    <!-- Top UI Bar -->
    <div class="absolute top-0 left-0 right-0 z-10 flex justify-between items-center p-4">
      <!-- Score display (top left) -->
      <div class="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg">
        <span class="text-sm font-medium">{{ score }} pts</span>
      </div>

      <!-- Profile menu button (top right) -->
      <UButton
        color="neutral"
        variant="soft"
        size="sm"
        square
        @click="isMenuOpen = true"
      >
        <Icon name="i-heroicons-user-circle" />
      </UButton>
    </div>

    <!-- Interactive Map Container -->
    <!-- <InteractiveMap
      class="w-full h-full"
      :map-url="mapData?.map_url || ''"
      :map-width="mapData?.original_width || 3840"
      :map-height="mapData?.original_height || 2160"
      :markers="marks"
      :is-loading="isLoading"
      @pin-selected="handlePinSelection"
      @map-interaction="handleMapInteraction"
    /> -->

    <!-- Bottom UI Elements -->
    <div class="absolute bottom-4 right-4 z-10">
      <!-- QR scanner button (bottom right) -->
      <UButton
        color="primary"
        size="lg"
        square
        @click="openQRScanner"
      >
        <Icon name="i-heroicons-qr-code" />
      </UButton>
    </div>

    <!-- Search bottom sheet (bottom) -->
    <SearchBottomSheet
      @location-selected="focusMapOnLocation"
    />

    <!-- Side Menu Drawer -->
    <SideMenuDrawer
      v-model:open="isMenuOpen"
    />

    <!-- Pin Detail Modals -->
    <MissionModal
      v-if="selectedPin?.mark_type === 1"
      v-model:open="isPinModalOpen"
      :pin="selectedPin"
    />
    <ExhibitionModal
      v-if="selectedPin?.mark_type === 2"
      v-model:open="isPinModalOpen"
      :pin="selectedPin"
    />
    <EventModal
      v-if="selectedPin?.mark_type === 3"
      v-model:open="isPinModalOpen"
      :pin="selectedPin"
    />

  </div>
</template>

<script setup lang="ts">
// Map data fetching
const {
  marks,
  mapData,
  isLoading,
  loadMapData,
  selectedPin
} = useMap()
const { score } = useUser()

// UI state
const isMenuOpen = ref(false)
const isPinModalOpen = ref(false)


// Pin selection handler
const handlePinSelection = (pin: any) => {
  selectedPin.value = pin
  isPinModalOpen.value = true
}

// Map interaction handler
const handleMapInteraction = (interaction: any) => {
  // Handle zoom, pan, etc.
}


// QR scanner navigation
const openQRScanner = () => {
  navigateTo('/qr-scanner')
}

// Map focus handler
const focusMapOnLocation = (location: any) => {
  // Focus map on selected location
}

// Back gesture protection
const preventBackNavigation = () => {
  // Add a dummy history entry to prevent back navigation
  window.history.pushState(null, '', window.location.href)
}

const handlePopState = (event: PopStateEvent) => {
  // Prevent back navigation by pushing state again
  preventBackNavigation()
}

// Load map data on mount
onMounted(async () => {
  await loadMapData()
  
  // Set up back gesture protection
  preventBackNavigation()
  window.addEventListener('popstate', handlePopState)
})

// Clean up event listener on unmount
onUnmounted(() => {
  window.removeEventListener('popstate', handlePopState)
})
</script>
