<!--
  Event Selection - Choose Event to Participate
  
  Purpose: Allow users to select which event they want to participate in
  
  Features:
  - List available events
  - Event details display
  - Event selection
  - Cookie/session management for selected event
  
  Flow:
  - Display available events
  - Show event details (dates, location, description)
  - Allow user to select an event
  - Store selection in cookie/session
  - Navigate to appropriate next screen (countdown/home)
  
  APIs:
  - GET /event/v1/events (list available events)
  - POST /event/v1/event { eventId } (select event)
  
  Global State:
  - Selected event ID
  - Available events list
  - Event selection state
  
  User Journey:
  - Shown when no event is selected
  - Gateway to event-specific experience
  - Sets up event context for entire app
-->

<template>
  <div class="event-selection-screen">
    <!-- Header -->
    <div class="header">
      <h1 class="title">Select Event</h1>
      <p class="subtitle">Choose which event you'd like to participate in</p>
    </div>
    
    <!-- Events List -->
    <div class="events-container">
      <div 
        v-for="event in availableEvents" 
        :key="event.id"
        class="event-card"
        :class="{ 'selected': selectedEventId === event.id }"
        @click="selectEvent(event)"
      >
        <!-- Event Image -->
        <div class="event-image">
          <img 
            v-if="event.image_url" 
            :src="event.image_url" 
            :alt="event.name"
            class="image"
          />
          <div v-else class="placeholder-image">
            <span class="placeholder-text">{{ event.name.charAt(0) }}</span>
          </div>
        </div>
        
        <!-- Event Details -->
        <div class="event-details">
          <h3 class="event-name">{{ event.name }}</h3>
          <p class="event-description">{{ event.description }}</p>
          
          <!-- Event Dates -->
          <div class="event-dates">
            <span class="date-label">Dates:</span>
            <span class="date-range">
              {{ formatEventDates(event.start_date, event.end_date) }}
            </span>
          </div>
          
          <!-- Event Location -->
          <div v-if="event.location" class="event-location">
            <span class="location-label">Location:</span>
            <span class="location-text">{{ event.location }}</span>
          </div>
          
          <!-- Event Status -->
          <div class="event-status">
            <span 
              class="status-badge"
              :class="getEventStatusClass(event)"
            >
              {{ getEventStatus(event) }}
            </span>
          </div>
        </div>
        
        <!-- Selection Indicator -->
        <div v-if="selectedEventId === event.id" class="selection-indicator">
          <span class="checkmark">✓</span>
        </div>
      </div>
    </div>
    
    <!-- Continue Button -->
    <div class="actions">
      <button 
        class="continue-button"
        :disabled="!selectedEventId || isLoading"
        @click="confirmSelection"
      >
        <span v-if="isLoading">Selecting...</span>
        <span v-else>Continue to Event</span>
      </button>
    </div>
    
    <!-- Loading Overlay -->
    <div v-if="isLoadingEvents" class="loading-overlay">
      <div class="loading-spinner">Loading events...</div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Event selection state
const availableEvents = ref<any[]>([])
const selectedEventId = ref<string | null>(null)
const isLoadingEvents = ref(true)
const isLoading = ref(false)

// Load available events
const loadEvents = async () => {
  try {
    isLoadingEvents.value = true
    // API call to get available events
    // const response = await $fetch('/api/events')
    // availableEvents.value = response.data
    
    // Mock data for now
    availableEvents.value = [
      {
        id: '1',
        name: 'Open Portal Expo 2024',
        description: 'The premier technology and innovation expo',
        start_date: '2024-08-01',
        end_date: '2024-08-03',
        location: 'Tokyo Big Sight',
        image_url: null,
        status: 'upcoming'
      }
    ]
  } catch (error) {
    console.error('Failed to load events:', error)
  } finally {
    isLoadingEvents.value = false
  }
}

// Event selection
const selectEvent = (event: any) => {
  selectedEventId.value = event.id
}

// Confirm selection and navigate
const confirmSelection = async () => {
  if (!selectedEventId.value) return
  
  try {
    isLoading.value = true
    
    // Store selected event in cookie/session
    const eventCookie = useCookie('selectedEventId', {
      default: () => null,
      maxAge: 60 * 60 * 24 * 30 // 30 days
    })
    eventCookie.value = selectedEventId.value
    
    // API call to select event
    // await $fetch('/api/event/select', {
    //   method: 'POST',
    //   body: { eventId: selectedEventId.value }
    // })
    
    // Navigate based on event status
    const selectedEvent = availableEvents.value.find(e => e.id === selectedEventId.value)
    if (selectedEvent) {
      const status = getEventStatus(selectedEvent)
      if (status === 'upcoming') {
        await navigateTo('/countdown')
      } else if (status === 'active') {
        await navigateTo('/home')
      } else {
        await navigateTo('/home') // Default to home
      }
    }
  } catch (error) {
    console.error('Failed to select event:', error)
  } finally {
    isLoading.value = false
  }
}

// Event status helpers
const getEventStatus = (event: any) => {
  const now = new Date()
  const startDate = new Date(event.start_date)
  const endDate = new Date(event.end_date)
  
  if (now < startDate) return 'upcoming'
  if (now > endDate) return 'ended'
  return 'active'
}

const getEventStatusClass = (event: any) => {
  const status = getEventStatus(event)
  return {
    'status-upcoming': status === 'upcoming',
    'status-active': status === 'active',
    'status-ended': status === 'ended'
  }
}

// Date formatting
const formatEventDates = (startDate: string, endDate: string) => {
  const start = new Date(startDate).toLocaleDateString()
  const end = new Date(endDate).toLocaleDateString()
  return start === end ? start : `${start} - ${end}`
}

// Load events on mount
onMounted(() => {
  loadEvents()
})
</script>
