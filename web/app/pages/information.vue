<!--
  Information Pages - App Information and Help
  
  Access: Side menu → Information
  
  Content: App information and help content
  
  Features:
  - App information display
  - Help documentation
  - FAQ section
  - Contact information
  - Version information
  
  APIs:
  - None (static content)
  
  Global State:
  - None (static content display)
  
  User Journey:
  - Access app information
  - Get help and support
  - Understand app features
  - Contact support if needed
-->

<template>
  <div class="information-screen">
    <!-- App information section -->
    <!-- Help documentation -->
    <!-- FAQ section -->
    <!-- Contact information -->
    <!-- Version information -->
  </div>
</template>

<script setup lang="ts">
// Static content management
// Version information display
// Contact information handling
</script>

<style scoped>
/* Information layout */
/* Section styling */
/* FAQ styling */
/* Contact information styling */
</style>
