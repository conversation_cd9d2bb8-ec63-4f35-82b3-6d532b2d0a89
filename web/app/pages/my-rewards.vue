<!--
  My Rewards - Owned Rewards
  
  Access: Side menu → My Rewards or Account → My Rewards
  
  Features:
  - View owned rewards
  - Generate QR codes for redemption
  - Refresh to update status
  - Reward usage tracking
  
  APIs:
  - GET /reward/v1/myRewards
  
  Global State:
  - myRewardsList
  - selectReward
  
  User Journey:
  - View acquired rewards
  - Generate redemption QR codes
  - Track reward usage
  - Manage reward inventory
-->

<template>
  <div class="my-rewards-screen">
    <!-- Owned rewards grid -->
    <!-- Reward status indicators -->
    <!-- QR code generation -->
    <!-- Refresh functionality -->
    <!-- Usage history -->
  </div>
</template>

<script setup lang="ts">
// My rewards data loading
// QR code generation
// Reward status management
// Refresh functionality
// Usage tracking
</script>

<style scoped>
/* My rewards grid layout */
/* Reward status styling */
/* QR code display */
/* Refresh button styling */
</style>
