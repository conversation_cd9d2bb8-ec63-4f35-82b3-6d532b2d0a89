<!--
  Index Page - Splash/Launch Screen

  Purpose: App entry point and initialization

  Features:
  - Splash screen display
  - Authentication check
  - Event data loading
  - Cookie/session management
  - Event selection (if multiple events)
  - App initialization

  Flow:
  1. Show splash screen
  2. Check authentication status
  3. Load event data
  4. Handle event selection (if needed)
  5. Initialize app state
  6. Route to appropriate screen:
     - If not authenticated → Login
     - If authenticated but no event selected → Event Selection
     - If authenticated and event selected → Home/Map

  APIs:
  - POST /event/v1/event { eventId: 1 }
  - Authentication check endpoints

  Global State:
  - useAccountStore.token (authentication)
  - useMapStore.updateMap()
  - useMapStore.setCountdown()
  - Event selection state

  User Journey:
  - App entry point
  - Handles initialization and routing
  - Sets up app state for user session
-->

<template>
  <div class="splash-screen">
    <!-- Splash logo/branding -->
    <!-- Loading indicators -->
    <!-- Event selection (if multiple events) -->
    <!-- Start button -->
    <!-- Background initialization -->
  </div>
</template>

<script setup lang="ts">
// Authentication check
// Event data loading
// Cookie/session management
// Event selection logic
// App initialization
// Routing logic based on auth/event state
</script>
