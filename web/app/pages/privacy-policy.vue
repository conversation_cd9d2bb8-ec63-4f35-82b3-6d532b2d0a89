<!--
  Privacy Policy - Rules and Privacy Policy
  
  Access: Side menu → Rules & Privacy Policy
  
  Purpose: Display privacy policy and app rules
  
  Features:
  - Privacy policy content
  - Terms of service
  - App usage rules
  - Legal information
  
  APIs:
  - None (static content or external URL)
  
  Global State:
  - None (static content)
  
  User Journey:
  - Review privacy policy
  - Understand terms of service
  - Legal compliance information
-->

<template>
  <div class="privacy-policy-screen">
    <!-- Privacy policy content -->
    <!-- Terms of service -->
    <!-- App usage rules -->
    <!-- Legal information -->
    <!-- Back navigation -->
  </div>
</template>

<script setup lang="ts">
// Static content display
// Navigation handling
// External link handling
</script>

<style scoped>
/* Privacy policy layout */
/* Content section styling */
/* Legal text styling */
/* Navigation styling */
</style>
