<!--
  Rewards Catalog - Available Rewards
  
  Access: Side menu → About Rewards
  
  Features:
  - Browse available rewards
  - View reward details
  - Check energy requirements
  - Exchange rewards
  - Reward filtering and search
  
  APIs:
  - GET /reward/v1/rewards?eventId=1
  
  Global State:
  - rewardsList
  - selectReward
  
  User Journey:
  - Browse available rewards
  - Check energy costs
  - Exchange energy for rewards
  - Plan reward acquisition strategy
-->

<template>
  <div class="rewards-screen">
    <!-- Rewards grid/list -->
    <!-- Reward cards -->
    <!-- Energy cost display -->
    <!-- Exchange buttons -->
    <!-- Filter controls -->
  </div>
</template>

<script setup lang="ts">
// Rewards data loading
// Reward selection handling
// Exchange functionality
// Energy cost validation
// Filter and search logic
</script>

<style scoped>
/* Rewards grid layout */
/* Reward card styling */
/* Energy cost display */
/* Exchange button styling */
</style>
