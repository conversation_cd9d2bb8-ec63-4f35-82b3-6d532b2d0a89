<!--
  Login Screen - User Authentication
  
  Purpose: User authentication via phone number
  
  User Actions:
  - Enter phone number with country code selection
  - Request verification code
  - Enter verification code
  - Submit for authentication
  
  Features:
  - Phone number format validation
  - Country code selection
  - Verification code input
  - Authentication flow
  
  Validation:
  - Phone number format validation
  - Verification code validation
  
  Navigation:
  - Success → CountDown Screen
  - Error → Stay on login with error message
  
  APIs:
  - POST /user/login/sendVerifyCode { phoneNumber }
  - POST /user/login/phoneNumberLogin { phoneNumber, verifyCode }
  
  Global State:
  - useAccountStore.updateToken()
  - useAccountStore.updateNickName()
  
  User Journey:
  - Primary authentication method
  - Phone number + verification code flow
  - Gateway to main app experience
-->

<template>
  <div class="login-screen">
    <!-- Phone number input with country code selector -->
    <!-- Verification code input -->
    <!-- Submit button -->
    <!-- Loading states -->
    <!-- Error messages -->
  </div>
</template>

<script setup lang="ts">
// Phone number validation
// Country code selection
// Verification code handling
// Authentication API calls
// Error handling
// Navigation logic
</script>

<style scoped>
/* Login form styling */
/* Input field styling */
/* Button styling */
/* Error message styling */
</style>
