<!--
  Account Screen - Profile Management
  
  Access: Side menu → Account
  
  Features:
  - Edit nickname
  - Change country/location
  - Date of birth selection
  - Navigation to sub-sections
  - Profile data display
  
  Sub-navigation:
  - My Rewards
  - My Story/History
  - Logout option
  
  Editable Fields:
  - Nickname (inline editing)
  - Country (picker selection)
  - Date of birth (date picker)
  
  Actions:
  - Save changes automatically
  - Logout (clears token, returns to launch)
  - Account deletion option
  
  APIs:
  - GET /user/profile
  - POST /user/updateUserInfo { nickname?, location?, birthday? }
  - POST /user/unregister (account deletion)
  
  Global State:
  - userProfile
  - editNick (local state)
  - useAccountStore.updateToken()
  - useAccountStore.updateNickName()
  
  User Journey:
  - Profile management and customization
  - Access to user-specific features
  - Account control and settings
-->

<template>
  <div class="account-screen">
    <!-- Profile header -->
    <!-- Editable profile fields -->
    <!-- Sub-navigation menu -->
    <!-- Logout button -->
    <!-- Account deletion option -->
  </div>
</template>

<script setup lang="ts">
// Profile data loading
// Inline editing functionality
// Profile update API calls
// Logout functionality
// Navigation to sub-sections
</script>

<style scoped>
/* Account screen layout */
/* Profile field styling */
/* Navigation menu styling */
/* Action button styling */
</style>
